package com.meta.common.constant;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "meta_captcha_codes:";

    /**
     * 验证码(注册账号) redis key
     */
    public static final String CAPTCHA_REGISTER_KEY = "meta_captcha_register:";

    public static final String TG_BIND_EMAIL_KEY = "tg_bind_email_key:";
    public static final String TG_TRADE_EMAIL_KEY = "tg_trade_email_key:";

    /**
     * 验证码(邮箱登录验证) redis key
     */
    public static final String CAPTCHA_EMAIL_LOGIN_KEY = "meta_captcha_email_login:";

    /**
     * 登录失败(密码) redis key
     */
    public static final String LOGIN_FAIL_PASS_KEY = "meta_pass_login_fail:";

    /**
     * 登录失败(密码) redis key
     */
    public static final String LOGIN_FAIL_SAME_IP_KEY = "meta_pass_login_same_ip_fail:";

    /**
     * 登录失败(邮箱验证码) redis key
     */
    public static final String LOGIN_FAIL_EMAIL_KEY = "meta_email_login_fail:";

    /**
     * 谷歌身份验证器(验证码) redis key
     */
    public static final String LOGIN_FAIL_GOOGLE_KEY = "meta_google_login_fail:";


    public static final String ES_REQUEST_NO = "es_request_no:";

    /**
     * 谷歌身份验证器key
     */
    public static final String GENERATE_GOOGLE_KEY = "meta_generate_google_key:";

    public static final String RESET_GOOGLE_KEY_CODE = "reset_google_key_code:";

    /**
     * 重置密码的Key
     */
    public static final String CAPTCHA_RESET_KEY = "meta_captcha_reset:";

    /**
     * 重置交易密码的Key
     */
    public static final String CAPTCHA_RESET_TRADE_KEY = "meta_captcha_trade_reset:";

    /**
     * 交易密码错误次数
     */
    public static final String TRADE_ERROR_KEY = "meta_trade_error_key:";

    /**
     * 五分钟内 提现金额相同风控规则
     */
    public static final String WALLET_LAST_WITHDRAWAL_AMOUNT = "meta_wallet_last_withdrawal_amount:";

    /**
     * 30分钟内 连续提现不超过3笔 风控规则
     */
    public static final String WALLET_CONTINUOUS_PAYOUTS_30 = "meta_wallet_continuous_payouts30:";

    /**
     * IP黑名单Key
     */
    public static final String IP_BLACK_KEY = "meta_ip_black:";

    /**
     * 账户锁定时长
     */
    public static final Integer ACCOUNT_LOCK_TIME = 120;

    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "meta_login_tokens:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "meta_repeat_submit:";

    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";

    /**
     * 登录用户编号 redis key
     */
    public static final String LOGIN_USERID_KEY = "meta_login_userId:";

    /**
     * 过期的Tokens redis key
     */
    public static final String USER_EXPIRED_TOKENS_KEY = "meta_user_expired_tokens";

    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = "sub";

    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * KUN汇率调整
     */
    public static final String KUN_RATE = "KUN_RATE";

    /**
     * 币种
     */
    public static final String TRADE_COIN = "TRADE_COIN:";

    /**
     * kyc审核时间
     */
    public static final String KYC_CHECK_TIME = "KYC_CHECK_TIME:";
    /**
     * kyc等待次数
     */
    public static final String KYC_WAIT_COUNT = "KYC_WAIT_COUNT:";


    /**
     * 邮箱正则验证
     */
    public static final String EMAIL_REGULAR = "^[\\w.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";


    public static class procedure {
        //节点升级返佣
        public static final String p_rel_node_apply_benefit = "p_rel_node_apply_benefit";
        //充值费返佣
        public static final String p_rel_card_recharge_benefit = "p_rel_card_recharge_benefit";
        //开卡费返佣
        public static final String p_rel_card_open_benefit = "p_rel_card_open_benefit";
        //节点升级返佣COIN
        public static final String p_rel_node_apply_benefit_coin = "p_rel_node_apply_benefit_coin";
        //充值费返佣COIN
        public static final String p_rel_card_recharge_benefit_coin = "p_rel_card_recharge_benefit_coin";
        //开卡费返佣COIN
        public static final String p_rel_card_open_benefit_coin = "p_rel_card_open_benefit_coin";


    }

    /**
     * 加密卡号的key
     */
    public static final String card_key = "5+oz1OlcMKRoM58zvQjyxg==";

    public static final String moonbank_card_key = "5+oz1OlcMKjnRoM5";


    /**
     * mq队列
     */
    public static final String queue_transaction_code = "queue.transaction.code";
    public static final String queue_card_recharge = "queue.card.recharge";
    public static final String queue_card_3ds = "queue.card.3ds";
    public static final String queue_card_kyc = "queue.user.kyc";
    public static final String queue_card_status = "queue.card.status";
    public static final String queue_transaction_created = "queue.transaction.created";
    public static final String queue_card_activation_code = "queue.card.activation.code";
    public static final String queue_h5_listening = "queue.h5.listening";
    public static final String queue_card_close = "queue.card.close";
    public static final String queue_delayed = "queue.delayed";
    public static final String queue_main = "queue.main";
    public static final String queue_dlx = "queue.dlx";
    public static final String queue_dlx_amount_alert = "queue.dlx.amount.alert";
    public static final String queue_amount_alert = "queue.amount.alert";

    //开卡队列
    public static final String queue_kz_open_card = "queue.kz.open.card";
    //充值队列
    public static final String queue_kz_recharge_card = "queue.kz.recharge.card";

    //开卡队列-B端
    public static final String queue_kz_open_card_B = "queue.kz.open.card.toB";
    //充值队列-B端
    public static final String queue_kz_recharge_card_B = "queue.kz.recharge.card.toB";

    /**
     * 交换机
     */
    public static final String exchange_name_notify = "kazepay.direct";

    //延迟队列交换机
    public static final String exchange_name_delayed = "kazepay.delayed";
    //死信交换机
    public static final String exchange_name_dlx = "kazepay.dlx";


    /**
     * 交换机绑定队列
     */
    public static final String routing_key_transaction_code = "routing.transaction.code";
    public static final String routing_key_card_recharge = "routing.card.recharge";
    public static final String routing_key_card_3ds = "routing.card.3ds";
    public static final String routing_key_card_kyc = "routing.card.kyc";
    public static final String routing_key_card_status = "routing.card.status";
    public static final String routing_transaction_created = "routing.transaction.created";
    public static final String routing_card_activation_code = "routing.card.activation.code";
    public static final String routing_key_h5_listening = "routing.h5.listening";
    public static final String routing_key_card_close = "routing.card.close";
    public static final String routing_key_amount_alert = "routing.amount.alert";

    public static final String routing_key_delayed = "routing.delayed";
    public static final String routing_key_dlx = "routing.dlx.delayed";

    public static final String routing_key_dlx_amount_alert = "routing.dlx.amount.alert";

    //开卡
    public static final String routing_key_kz_open_card = "routing.kz.open.card";
    //充值
    public static final String routing_key_kz_recharge_card = "routing.kz.recharge.card";


    //开卡-B端
    public static final String routing_key_kz_open_card_B  = "routing.kz.open.card.toB";
    //充值-B端
    public static final String routing_key_kz_recharge_card_B  = "routing.kz.recharge.card.toB";

}
