package com.meta.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/01/02/16:50
 */
@Component
public class LockCfg {

    @Autowired
    private StringRedisTemplate redisTemplate;

    private static final String LOCK_PREFIX = "lock:";

    // 获取锁
    public boolean tryLock(String lockKey, String requestId, long timeout, TimeUnit unit) {
        // 尝试获取锁
        Boolean success = redisTemplate.opsForValue().setIfAbsent(LOCK_PREFIX + lockKey, requestId, timeout, unit);
        return success != null && success;
    }

    // 释放锁
    public boolean unlock(String lockKey, String requestId) {
        String currentValue = redisTemplate.opsForValue().get(LOCK_PREFIX + lockKey);
        if (requestId.equals(currentValue)) {
            redisTemplate.delete(LOCK_PREFIX + lockKey);
            return true;
        }
        return false;
    }
}
