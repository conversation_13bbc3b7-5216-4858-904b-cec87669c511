package com.meta.common.enums.app;

public enum TxnStatus{
    //moonbank
    DECLINED("DECLINED",'0'),
    APPROVED("APPROVED",'1'),
    CONFIRM("CONFIRM",'1'),
    PENDING("PENDING",'2'),
    pending("pending",'2'),
    posted("posted",'1'),
    declined("declined",'0'),
    vodi("vodi",'0'),
    CANCEL("CANCEL",'0'),
    OTHER("OTHER",'1'),


    //vcc
    FAIL("fail",'0'),
    SUCCESS("success",'1'),
    PADDING("processing",'2');
    String name;
    Character value;

    TxnStatus(String name,Character value) {
        this.name = name;
        this.value = value;
    }

    public static Character getValueByName(String name){
        for(TxnStatus ts: TxnStatus.values()){
            if(ts.name.equals(name))
                return ts.value;
        }
        return null;
    }

    public String getName() {
        return this.name;
    }

    public Character getValue() {
        return value;
    }
}
