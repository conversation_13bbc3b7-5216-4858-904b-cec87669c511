package com.meta.common.enums.app;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/06/20/17:42
 */
public enum PartnerTxnType {

    CardApplication("Card application", "卡申请", "CA"),
    CardTopup("Card recharge", "卡充值", "CR"),
    CardWithdrawal("Card withdrawal", "卡提现", "CW"),
    MerchantTopup("Merchant recharge", "商户充值", "MT"),
    MerchantWithdrawal("Merchant withdrawal", "商户提现", "MW"),
    MerchantRefund("Merchant refund", "商户退款", "MR"),
    ManualEntry("Manual entry", "人工入账", "ME"),
    CommissionReturn("Commission return", "佣金返还", "COR"),
    TransactionAuthorization("Transaction authorization fee for card transactions", "卡交易授权费", "TA"),
    FeeForCardTransactions30("Fee for card transactions below 30 USD", "卡消费低于30U手续费", "FT");

    PartnerTxnType(String ename, String cname, String value) {
        this.ename = ename;
        this.cname = cname;
        this.value = value;
    }


    private final String ename;
    private final String cname;
    private final String value;

    public String getEname() {
        return ename;
    }

    public String getCname() {
        return cname;
    }

    public String getValue() {
        return value;
    }

    public static PartnerTxnType toType(String value) {
        return Stream.of(PartnerTxnType.values())
                .filter(p -> p.value.equals(value))
                .findAny()
                .orElse(null);
    }

}
