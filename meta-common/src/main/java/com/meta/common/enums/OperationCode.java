package com.meta.common.enums;

import com.meta.common.utils.MessageUtils;

/**
 * 异常代码
 */
public enum OperationCode implements BaseEnum<OperationCode> {
    SUCCESS(MessageUtils.message("operation.success")),
    FAIL(MessageUtils.message("operation.fail")),
    ACCOUNT_BALANCE_NO_ENOUGH(MessageUtils.message("wallet.account.insufficient.balance")),
    ACTIVITI_CODE_NUM_NO_ENOUGH(MessageUtils.message("wallet.activitiCode.insufficient.num")),
    SILVER_ACTIVITI_CODE_NUM_NO_ENOUGH(MessageUtils.message("wallet.activitiCode.silver.insufficient.num")),
    GOLDEN_ACTIVITI_CODE_NUM_NO_ENOUGH(MessageUtils.message("wallet.activitiCode.golden.insufficient.num")),
    BLACKGOLDEN_ACTIVITI_CODE_NUM_NO_ENOUGH(MessageUtils.message("wallet.activitiCode.goldenBlack.insufficient.num"));

    String msg;
    OperationCode(String msg) {
        this.msg = msg;
    }
    @Override
    public String getOrdinal() {
        return String.valueOf(this.ordinal());
    }
}




