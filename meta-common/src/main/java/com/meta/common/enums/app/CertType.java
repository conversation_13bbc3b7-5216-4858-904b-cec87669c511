package com.meta.common.enums.app;

import com.meta.common.enums.BaseEnum;

/**
 * 证件类型
 */
public enum CertType implements BaseEnum<CertType> {
    ID_CARD("0", "身份证"),
    PASSPORT("1", "护照");
    String value;
    String name;
    CertType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    @Override
    public String getOrdinal() {
        return getValue();
    }

    @Override
    public String toString() {
        return getValue();
    }
}
