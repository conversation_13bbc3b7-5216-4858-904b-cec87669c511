package com.meta.common.enums;

/**
 * <AUTHOR>
 * @date 2025/02/12/11:38
 */
public enum PhysicalCardType implements BaseEnum<PhysicalCardType>{
    /**
     * 欧洲实体卡
     */
    EuroCard( "11"),
    /**
     * 新加坡实体卡-kyc不容易通过
     */
    SingaporeUCard_01( "12"),

    /**
     * 新加坡实体卡-kyc容易通过
     */
    SingaporeUCard_10( "22"),

    ;

    private final String type;

    PhysicalCardType(String name) {
        this.type = name;
    }

    public String getType() {
        return type;
    }

    @Override
    public String getOrdinal() {
        return String.valueOf(this.ordinal());
    }
}
