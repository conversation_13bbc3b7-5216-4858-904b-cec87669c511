package com.meta.common.enums.app;

import com.meta.common.enums.BaseEnum;

/**
 *  冻结的类型
 */
public enum FreezeType implements BaseEnum<FreezeType> {
    /**
     * 冻结
     */
    FREEZE,
    /**
     * 释放
     */
    FREE_FREEZE,
    /**
     *  回退
     */
    FALLBACK,

    /**
     * 不涉及冻结
     */
    NO,
    /**
     * 不处理金额,即日记状态为processing
     */
    NORMAL,
    /**
     * 不处理金额,,即日记状态为FAIL
     */
    NORMAL_FALLBACK;


    @Override
    public String getOrdinal() {
        return String.valueOf(this.ordinal());
    }

}
