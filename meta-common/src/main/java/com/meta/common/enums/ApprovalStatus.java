package com.meta.common.enums;

/**
 * 申请状态
 */
public enum ApprovalStatus implements BaseEnum<ApprovalStatus>{
    /**
     * 审批中
     */
    PENDING( "审批中"),
    /**
     * 审批通过
     */
    PASS( "审批通过"),
    /**
     * 审批不通过
     * */
    REJECT( "审批不通过");

    private final String name;

    ApprovalStatus(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    @Override
    public String getOrdinal() {
        return String.valueOf(this.ordinal());
    }
}
