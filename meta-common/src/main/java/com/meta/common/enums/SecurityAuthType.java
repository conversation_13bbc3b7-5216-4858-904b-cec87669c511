package com.meta.common.enums;

import com.meta.common.constant.HttpStatus;

public enum SecurityAuthType {
    TRADE(HttpStatus.ERROR_TRADE_NOT_AUTH, "wallet.error.trade.not.auth"),// 902
    FACE(HttpStatus.ERROR_FACE_NOT_AUTH, "wallet.error.face.not.auth"),// 901
    ALL(HttpStatus.ERROR_NOT_AUTH, "wallet.error.not.auth");// 900

    private int code;
    private String msg;

    SecurityAuthType(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
