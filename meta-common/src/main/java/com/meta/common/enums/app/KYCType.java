package com.meta.common.enums.app;

import com.meta.common.enums.BaseEnum;

/**
 * 实名类型
 */
public enum KYCType implements BaseEnum<KYCType> {

    /**
     * 个人
     */
    PERSON("0", "个人"),
    /**
     * 公司
     */
    COMPANY("1", "公司");

    private String value;
    private String name;

    KYCType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    @Override
    public String getOrdinal() {
        return this.value;
    }
}
