package com.meta.common.enums;

interface ErrorEnum {
  int type();
  String value();
}

public enum BizError implements ErrorEnum {

  NotNull("not.null"),
  /**
   * 系统
   */

  SYS_SUCCESS("sys.success"),//操作成功
  SYS_ERROR("sys.error"),//操作失败
  SYS_PHONE_FORMAT_ERROR("sys.phone.format.error"), //请输入正确的手机号码
  SYS_NUMBER_FORMAT_ERROR("sys.number.format.error"),  //数字格式错误
  SYS_PARAMETER_EXCEPTION("sys.param.exception"), //参数错误
  SYS_TRC_CODE_MISS("sys.trc.code.miss"), //请联系管理员设置TRC20-USDT收款码
  SYS_LOGIN_LIMIT_COUNT("sys.login.limit.count"),//本账号今日登录失败已达上限...请明日再试
  SYS_PWD_SIX_NUM_CHAR("sys.pwd.six.num.char"), //  密码至少包含 数字和英文，长度6-20

  SYS_LINE_CHART("sys.error.line.chart"),//操作频繁，请稍后再试...


  SYS_WITHDRAW_BUSY("sys.withdraw.busy"),//提币系统繁忙，请稍后再试...
  SYS_RECHARGE_BUSY("sys.recharge.busy"),//充值系统繁忙，请稍后再试...

  /**
   * 用户
   */
  USER_LOCKED("user.locked"), //用户已被冻结,请联系管理员解冻后再试!
  USER_NOT_FOUND("user.not.found"), //查无此客户信息，请联系管理员！
  USER_FIL_LOCK("user.fil.lock"), //FIL用户已被冻结
  USER_USDT_LOCK("user.usdt.lock"), //USDT用户已被冻结
  USER_XCH_LOCK("user.xch.lock"), //XCH用户已被冻结
  USER_PHONE_NOT_REGISTER("user.phone.not.register"), //手机号未在平台上注册，请重新输入
  USER_INTERNET_ERROR("user.internet.error"), //该号码未注册，请注册后登录！

  USER_FIL_NOT_ENOUGH("user.fil.not.enough"), //FIL余额不足
  USER_USDT_NOT_ENOUGH("user.usdt.not.enough"), //USDT余额不足
  USER_XCH__NOT_ENOUGH("user.xch.not.enough"), //XCH余额不足

  USER_FIL_BAL("user.fil.bal"), //您当前FIL余额: {0}
  USER_USDT_BAL("user.usdt.bal"), //您当前USDT余额: {0}
  USER_XCH_BAL("user.xch.bal"), //您当前XCH余额: {0}

  USER_FIL_FENRUN_LOCK("user.fil.fenrun.lock"), //分润收益: {0}已被冻结，请提升等级进行解锁,当前最多可转金额为{1}FIL

  USER_NAME_NOT_AUTH("user.name.not.auth"), //请先到系统设置进行实名认证或者等待后台审核
  USER_TO_NAME_NOT_AUTH("user.to.name.not.auth"), //对方用户未进行实名认证

  USER_WITHDRAW_PASSWORD_NOT_SET("user.withdraw.password.not.set"),//请先到系统设置进行提币密码的设置
  USER_WITHDRAW_PASSWORD_FALSE("user.withdraw.password.false"),//提币密码错误

  USER_PHONE_BINDED("user.phone.binded"), //手机号已经被绑定,请更换手机号码

  USER_AUTH_NAME_EMPTY("user.auth.name.empty"), //姓名不能为空
  USER_AUTH_CARDNO_INVALID("user.auth.cardno.invalid"), //身份证号码无效,请重新输入.
  USER_AUTH_CARDNO_UNDER_REVIEW("user.auth.cardno.under.review"),//此身份证号码已提交认证过
  USER_AUTH_CARD_FRONT_IMG_MISS("user.auth.card.front.img.miss"),//请上传身份证正面照片
  USER_AUTH_CARD_BACK_IMG_MISS("user.auth.card.back.img.miss"),//请上传身份证反面照片

  USER_GUDO_BELONG_NOT_MATCH("user.gudo.belong.not.match"), //归属股东信息不匹配
  USER_PHONE_CODE("user.phone.code"), //验证码已发送

  /**
   * 交易
   */


  TRADE_TRANSACTIONID_EMPTY("trade.transactionid.empty"), //交易号不能为空
  TRADE_AMOUNT_EMPTY("trade.amount.empty"), //交易金额不能为空
  TRADE_RECHARGE_MISS_VOUCHER_IMG("trade.recharge.miss.voucher.img"), //请上传交易凭证截图

  TRADE_AMOUNT_ILLEGAL("trade.amount.illegal"),//请输入正确的金额
  TRADE_TRANSACTIONID_SUBMITED("trade.transactionid.submited"), //此交易号已下发过余额,请勿重复提交.
  TRADE_TRANSACTIONID_UNDER_REVIEW("trade.transactionid.under.review"), //此交易号已在审核列表中,请勿重复提交.
  TRADE_TRANSACTIONID_NOT_FOUND("trade.transactionid.not.found"), //查无此交易信息,请核对后 再试！
  TRADE_TRANSACTIONID_ERROR("trade.transactionid.error"), //此交易号信息有误,请核对后 再试！
  TRADE_RECHARGE_AMOUNT_ERROR("trade.recharge.amount.error"), //充值金额有误,请核对后再试！
  TRADE_TRANSACTION_ERROR("trade.transaction.error"), //此交易未成功

  TRADE_TRANSFERS_TO_MYSELF("trade.transfers.to.myself"), //不能给自己转账

  TRADE_TRANSFERS_FIL_MIN("trade.transfers.fil.min"), //{0}FIL起转
  TRADE_TRANSFERS_USDT_MIN("trade.transfers.usdt.min"), //{0}USDT起转
  TRADE_TRANSFERS_USDT("trade.transfers.usdt"), // USDT转账必须大于0
  TRADE_TRANSFERS_XCH_MIN("trade.transfers.xch.min"), //{0}XCH起转
  TRADE_TRANSFERS_BUY_MIN("trade.transfers.buy.min"),//至少购买{0}T算力才能转账

  TRADE_WITHDRAW_ADDR_EMPTY("withdraw.addr.empty"), //提币地址不能为空
  TRADE_WITHDRAW_PWD_EMPTY("withdraw.pwd.empty"), //提币密码不能为空

  TRADE_WITHDRAW_FIL_MIN("withdraw.fil.min"), //1FIL提币起步,请重新输入数值！
  TRADE_WITHDRAW_USDT_MIN("withdraw.usdt.min"), //10USDT提币起步,请重新输入数值！
  TRADE_WITHDRAW_USDT_MIN_BAL("withdraw.usdt.min.bal"), //{0}USDT提币起步,请重新输入数值！
  TRADE_WITHDRAW_XCH_MIN("withdraw.xch.min"), //1XCH提币起步,请重新输入数值！

  TRADE_WITHDRAW_BUY_MIN("trade.withdraw.u.buy.min"),//至少购买{0}T算力才能提币

  TRADE_WITHDRAW_U_TRC("trade.withdraw.u.trc"), //请使用TRC20地址进行提币
  TRADE_WITHDRAW_U_FEE_LOW("trade.withdraw.u.fee.low"), //提币的金额不足以支付手续费



  /**
   * 产品
   */
  PRODUCT_NOT_FOUND("product.notfound"), //产品未找到
  PRODUCT_NOT_ONSALE("product.not.onsale"), //产品已下架
  PRODUCT_STOCK_EMPTY("product.stock.empty"), //库存不足
  PRODUCT_SALE_TIME_NOTSTART("product.sale.time.notstart"), //产品售卖时间为: {0}
  PRODUCT_FIVEANTD_ONLY("product.fiveantd.only"), //此产品仅五星蚂蚁用户才能购买
  PRODUCT_WHOLESALE_NOTSET("product.wholesale.notset"), //管理员未设置批发价格
  PRODUCT_NEW_ONLY("product.new.only"), //此产品为新用户专享,您已购买过."

  /**
   * 订单
   */
  ORDER_BUY_NOT_GUDO("order.buy.not.gudo"), //未有所有股东不能购买
  ORDER_FIL_ADVANCE("order.fil.advance"), //产品需预缴{0}FIL 余额仅剩：{1},请先充值！
  ORDER_FULLPOW_COUPON_NOT("order.fullpow.coupon.not"), //满存算力不能使用优惠劵
  ORDER_USE_COUPON_MAX("order.coupon.max"), //此订单最多能使用{0}张优惠劵
  ORDER_COUPON_BAL("order.coupon.bal"),//优惠劵仅剩{0}张
  ORDER_NOT_FOUND("order.not.found"),//此订单不存在


  /**
   * 矿池信息
   */
  POOL_NOT_FOUND("pool.not.found"), //矿池信息不存在,请联系管理员.

  /**
   * 优惠劵
   */
  COUPON_EMPTY("coupon.empty"),//您未曾拥有优惠劵
  COUPON_COUNT_NOT_ENOUGH("coupon.count.not.enough"),//优惠劵数量不足


  /**
   * 账单信息
   */
  BILL_RECOMM_FEE_NOT_FOUND("bill.recomm.fee.not.found"), //返佣比例信息不存在，请联系管理员！
  BILL_CLOUD_NOT_FOUND("bill.cloud.not.found"), //查无此账单信息,请联系管理员！
  BILL_RC_USDT_WAIT_NOT_ENOUGH("bill.rc.usdt.wait.not.enough"), //待分配USDT余额不足

  /**
   * 验证
   */
  CAPTCHA_EMPTY("captcha.empty"), //请输入验证码
  CAPTCHA_EXPT("captcha.expt"), //验证码失效,请重新获取.
  CAPTCHA_INVALID("captcha.invlid"),//验证码不正确
  CAPTCHA_DAY_LIMIT("captcha.day.limit"),//验证码次数超出日频率限制
  EMAIL_INVALID("email.invalid"), //请输入正确的邮箱号码


  OVER("over");

  int type;
  String value;
  BizError(int type, String value) {
    this.type = type;

    this.value = value;
  }

  BizError(String value) {
    this(500, value);
  }

  @Override
  public int type() {
    return type;
  }

  @Override
  public String value() {
    return value;
  }
}
