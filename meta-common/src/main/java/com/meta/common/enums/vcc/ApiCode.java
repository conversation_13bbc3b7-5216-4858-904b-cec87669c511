package com.meta.common.enums.vcc;

import com.meta.common.enums.BaseEnum;

import java.util.stream.Stream;

/**
 *  vcc接口的apicode
 */
public enum ApiCode implements BaseEnum<ApiCode> {

    CreateCard("createCard"),
    QueryCardDetail("queryCardDetail"),
    CloseCard("closeCard"),
    RechargeCard("rechargeCard"),
    WithdrawCard("withdrawCard"),
    SuspendCard("suspendCard"),
    UnSuspendCard("unSuspendCard"),
    QueryTransactionPage("queryTransactionPage"),

    //*********** 通知接口 ******************************
    createCardNotify("createCardNotify"),
    closeCardNotify("closeCardNotify"),
    suspendCardNotify("suspendCardNotify"),
    unsuspendCardNotify("unsuspendCardNotify"),
    transactionNotify("transactionNotify"),
    rechargeCardNotify("rechargeCardNotify"),
    withdrawCardNotify("withdrawCardNotify");

    String name;

    ApiCode(String name) {
        this.name = name;
    }

    public String getName(){
        return this.name;
    }
    @Override
    public String getOrdinal() {
        return String.valueOf(this.ordinal());
    }

    public static ApiCode toType(String name) {
        return Stream.of(ApiCode.values())
                .filter(p -> p.name.equals(name))
                .findAny()
                .orElse(null);
    }

    public static boolean checkNotify(String code){
        ApiCode apicode = toType(code);
        if(apicode != null && apicode.getName().endsWith("Notify")){
            return true;
        }
        return false;
    }
}
