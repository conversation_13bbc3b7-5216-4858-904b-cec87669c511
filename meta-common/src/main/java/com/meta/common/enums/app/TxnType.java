package com.meta.common.enums.app;

import com.meta.common.enums.VccTxnType;

import java.util.stream.Stream;

public enum TxnType {
    WALLET_RECHARGE_D("兑换USDT资产", "d1010",false),
    WALLET_RECHARGE_C("兑换USDT资产", "c1020",true),
    WALLET_WITHDRAW_D("兑换USD美元", "d1030",false),
    WALLET_WITHDRAW_C("兑换USD美元", "c1040",true),
    WALLET_RECHARGE_FEE_D("兑换USDT资产手续费", "d1011",false),
    WALLET_RECHARGE_FEE_C("兑换USDT资产手续费", "c1021",true),
    WALLET_WITHDRAW_FEE_D("兑换USD美元手续费", "d1012",false),
    WALLET_WITHDRAW_FEE_C("兑换USD美元手续费", "c1022",true),
    CARD_WITHDRAW_D("卡提现收入", "d2010",false),
    CARD_WITHDRAW_C("卡提现支出", "c2020",true),
    CARD_RECHARGE_D("卡充值收入", "d2030",false),
    CARD_RECHARGE_C("卡充值支出", "c2040",true),
    CARD_RECHARGE_FEE_D("卡充值手续费收入", "d2011",false),
    CARD_RECHARGE_FEE_C("卡充值手续费支出", "c2021",true),
    CARD_WITHDRAW_FEE_D("卡提现手续费收入", "d2012",false),
    CARD_WITHDRAW_FEE_C("卡提现手续费支出", "c2022",true),
    INTERNAL_TRANSFER_IN_D("USD美元转入", "d3010",false),
    INTERNAL_TRANSFER_OUT_C("USD美元转出", "c3020",true),
    CARD_ACTIVATION_FEE_D("开卡费收入", "d4010",false),
    CARD_ACTIVATION_FEE_C("开卡费支出", "c4020",true),
    NODE_PREPAID_D("节点预存收入", "d4030",false),
    NODE_PREPAID_C("节点预存支出", "c4040",true),
    CARD_LOGISTICS_FEE_D("物流费收入", "d5030",false),
    CARD_LOGISTICS_FEE_C("物流费支出", "c5020",true),
    CARD_ACTIVATION_COMMISSION_D("开卡返佣收入", "d9010",false),
    CARD_ACTIVATION_COMMISSION_C("开卡返佣支出", "c9020",true),
    CARD_RECHARGE_COMMISSION_D("卡充值返佣收入", "d9030",false),
    CARD_RECHARGE_COMMISSION_C("卡充值返佣支出", "c9040",true),
    NODE_REFERRAL_SPLIT_D("节点推荐分成收入", "d9050",false),
    NODE_REFERRAL_SPLIT_C("节点推荐分成支出", "c9060",true),

    // 激活码的
    CARD_UPDATE("升级配送","00",false),
    APPLY_RECHARGE("申请入账","10",false),
    CARD_ACTIVATION("开卡使用","20",false),
    TRANSFER_RECHARGE("转让入账","30",false),
    TRANSFER_EXPENDITURE("转让支出","31",true),
    SYSTEM_SEND("系统赠送","50",false),
    SYSTEM_REVOKED("系统撤销","51",true),
    EXCHANGE_RECHARGE("兑换入账","40",false),
    EXCHANGE_EXPENDITURE("兑换支出","41",true),

    // 卡片的vcc
    CARD_PAY("卡交易支出","c2160",true),
    CARD_PAY_REFUND("卡交易支出退款收入","d2150",false),
    CARD_REMOVE_REFUND("卡交易删卡退款收入","d2170",false),
    CARD_PAY_REVOKE("卡交易交易撤销费用支出","c2161",true),
    CARD_CLOSE_PAY("卡交易关闭转出支出","c2181",true),
    CARD_PAY_AUTH("卡交易交易授权费支出","c2162",true),
    CARD_BALANCE_PAY("卡交易余额转出支出","c2180",true),
    CARD_PAY_REVERSAL("卡交易支出冲正收入","d2160",true),
    CARD_REFUND_REVERSAL("卡交易支出退款冲正支出","c2150",true),
    ES_CARD_RECHARGE("对外充值接口","d2150",true),
    ES_CARD_DERATE("对外降额接口","c2160",true),
    CARD_QUERY_AUTH("卡交易授权查询","z2100",true),

    //卡片moonbank
    AUTH("支出","c2160",true),
    AUTH_QUERY("授权查询","z2100",true),
    REVERSAL("支出冲正","d2160",true),
    REFUND("支出退款","d2150",true),
    FEE("交易授权费","c2162",true),
    TRADE_PROCESS_FEE("交易授权费","c2162",true),
    TRADE_CROSS_BOARD_FEE("交易授权费","c2162",true),
    TRADE_REFUND_FEE("交易撤销费用","c2161",true),
    FEE_REVERSAL("交易撤销费用","c2161",true),
    ORIGINAL_CREDIT("支出退款","d2150",true),
    ORIGINAL_CREDIT_REVERSAL("支出退款冲正支出","c2150",true),
    SETTLEMENT_DEBIT("差异结算","c2160",true),
    CLEARING("清算","c2160",true),//kun
    AUTHORIZATION("授权","c2162",true),//kun
    AUTH_REVERSAL("授权交易撤销","c2161",true),//kun
    AUTH_FAILURE("授权失败","c2150",true),


    OTHER("other","c8001",true),
    CHARGE("charge","s8001",false),
    TOPUP("topup","s8002",false),
    WITHDRAW("withdraw","s8003",true),
    TRANSFER("transfer","s8004",true),
    CASHBACK("cashback","s8005",false),
    INTEREST("interest","s8006",false),

    //币种资产
    COIN_DEPOSIT("充币", "d1010", false),
    COIN_WITHDRAW("提币", "c1010", true),
    COIN_TRANSFER_IN("转入", "t1010", false),
    COIN_TRANSFER_OUT("转出", "t1020", true),
    COIN_EXCHANGE_IN("兑入", "e1010", false),
    COIN_EXCHANGE_OUT("兑出", "e1020", true),
    COIN_OPEN_CARD_OUT("开卡费", "c4020", true),
    COIN_LOGISTICS_OUT("物流费", "c5020", true),
    COIN_NODE_PREPAID_C("节点预存支出", "c4040",true),
    COIN_CARD_RECHARGE_OUT("卡充值", "c2040", true),
    COIN_CARD_RECHARGE_FEE_OUT("卡充值手续费", "c2021", true),
    COIN_CURRENCY_PURCHASE("买币", "b1010", false),
    COIN_QR_PAY("扫码", "s1010", true);









    public static String getValueByCode(String code){
        String res = null;
        switch (code){
            case "pay":
                res = CARD_PAY.getValue();
                break;
            case "pay_refund":
                res = CARD_PAY_REFUND.getValue();
                break;
            case "remove_refund":
                res = CARD_REMOVE_REFUND.getValue();
                break;
            case "pay_revoke":
                res = CARD_PAY_REVOKE.getValue();
                break;
            case "close_pay":
                res = CARD_CLOSE_PAY.getValue();
                break;
            case "pay_auth":
                res = CARD_PAY_AUTH.getValue();
                break;
            case "balance_pay":
                res = CARD_BALANCE_PAY.getValue();
                break;
            case "pay_reversal":
                res = CARD_PAY_REVERSAL.getValue();
                break;
            case "pay_refund_reversal":
                res = CARD_REFUND_REVERSAL.getValue();
                break;
            case "query_auth":
                res = CARD_QUERY_AUTH.getValue();
                break;

            case "AUTH":
                res = AUTH.getValue();
                break;
            case "AUTH_QUERY":
                res = AUTH_QUERY.getValue();
                break;
            case "REVERSAL":
                res = REVERSAL.getValue();
                break;
            case "REFUND":
                res = REFUND.getValue();
                break;
            case "FEE":
                res = FEE.getValue();
                break;
            case "TRADE_PROCESS_FEE":
                res = TRADE_PROCESS_FEE.getValue();
                break;
            case "TRADE_CROSS_BOARD_FEE":
                res = TRADE_CROSS_BOARD_FEE.getValue();
                break;
            case "TRADE_REFUND_FEE":
                res = TRADE_REFUND_FEE.getValue();
                break;
            case "FEE_REVERSAL":
                res = FEE_REVERSAL.getValue();
                break;
            case "ORIGINAL_CREDIT":
                res = ORIGINAL_CREDIT.getValue();
                break;
            case "ORIGINAL_CREDIT_REVERSAL":
                res = ORIGINAL_CREDIT_REVERSAL.getValue();
            case "other":
                res = OTHER.getValue();
                break;
            case "charge":
                res = CHARGE.getValue();
                break;

             case "topup":
                res = TOPUP.getValue();
                break;
            case "withdraw":
                res = WITHDRAW.getValue();
                break;
            case "transfer":
                res = TRANSFER.getValue();
                break;
            case "cashback":
                res = CASHBACK.getValue();
                break;
            case "interest":
                res = INTEREST.getValue();
                break;
            case "CLEARING":
                res = CLEARING.getValue();
                break;
            case "AUTHORIZATION":
                res = AUTHORIZATION.getValue();
                break;
            case "AUTH_REVERSAL":
                res = AUTH_REVERSAL.getValue();
                break;
            case "AUTH_FAILURE":
                res = AUTH_FAILURE.getValue();
                break;

            default:
                break;


        }
        return res;
    }
    private final String name;
    private final String value;
    private boolean sub;// 是否为支出

    TxnType(String name, String value,boolean sub) {
        this.name = name;
        this.value = value;
        this.sub = sub;
    }

    public boolean isSub() {
        return sub;
    }

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }

    public static TxnType toType(String value) {
        return Stream.of(TxnType.values())
                .filter(p -> p.value.equals(value))
                .findAny()
                .orElse(null);
    }
}
