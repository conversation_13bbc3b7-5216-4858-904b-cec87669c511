package com.meta.common.core.domain.entity;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.meta.common.annotation.Excel;
import com.meta.common.annotation.Excel.ColumnType;
import com.meta.common.core.domain.BaseEntity;

import java.util.Date;


/**
 * 字典数据表 sys_dict_data
 *
 * <AUTHOR>
 * @since 1.0  2020-12-11
 */
@Entity
@Table(name = "sys_dict_data")
@Data
public class SysDictData extends BaseEntity  {
    private static final long serialVersionUID = 1L;

    /**
     * 字典编码
     */
    @Excel(name = "字典编码", cellType = ColumnType.NUMERIC)
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dict_code")
    private Long dictCode;

    /**
     * 字典排序
     */
    @Excel(name = "字典排序", cellType = ColumnType.NUMERIC)
    @Column(name = "dict_sort")
    private Long dictSort;


    /**
     * 字典标签
     */
    @NotBlank(message = "字典标签不能为空")
    @Size(min = 0, max = 100, message = "字典标签长度不能超过100个字符")
    @Excel(name = "字典标签")
    @Column(name = "dict_label")
    private String dictLabel;

    /**
     * 字典标签
     */
    @Size(min = 0, max = 100, message = "字典标签长度不能超过100个字符")
    @Excel(name = "字典标签")
    @Column(name = "dict_label_en")
    private String dictLabelEn;
    /**
     * 字典键值
     */
    @Excel(name = "字典键值")
    @NotBlank(message = "字典键值不能为空")
    @Size(min = 0, max = 300, message = "字典键值长度不能超过100个字符")
    @Column(name = "dict_value")
    private String dictValue;

    /**
     * 字典类型
     */
    @Excel(name = "字典类型")
    @NotBlank(message = "字典类型不能为空")
    @Size(min = 0, max = 100, message = "字典类型长度不能超过100个字符")
    @Column(name = "dict_type")
    private String dictType;

    /**
     * 样式属性（其他样式扩展）
     */
    @Size(min = 0, max = 100, message = "样式属性长度不能超过100个字符")
    @Column(name = "css_class")
    private String cssClass;

    /**
     * 表格字典样式
     */
    @Column(name = "list_class")
    private String listClass;

    /**
     * 是否默认（Y是 N否）
     */
    @Excel(name = "是否默认", readConverterExp = "Y=是,N=否")
    @Column(name = "is_default")
    private String isDefault;

    /**
     * 状态（0正常 1停用）
     */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    @Column(name = "status")
    private String status;

    /**
     * 创建者
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     *
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "update_time")
    private Date updateTime;


    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

}
