package com.meta.common.utils;

import cn.hutool.extra.qrcode.QrCodeUtil;
import com.meta.common.utils.sign.Base64;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

public class QRCodeUtils {

    /**
     * 生成二维码
     */
    public static String generate(String code){
        //⽣成⼆维码 300 表示⼆维码的⼤⼩
        BufferedImage bufferedImage = QrCodeUtil.generate(code, 300, 300);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            ImageIO.write(bufferedImage, "jpg", baos);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        byte[] imageBytes = baos.toByteArray();
        return Base64.encode(imageBytes);
    }
}
