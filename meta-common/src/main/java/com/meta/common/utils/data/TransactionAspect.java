package com.meta.common.utils.data;

import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Aspect
@Component
public class TransactionAspect {

    private static final Logger log = LoggerFactory.getLogger(TransactionAspect.class);

    @Resource
    private TransactionService transactionService;

    @Before("@annotation(TransactionalAspect)")
    public void beginTransaction() {
        transactionService.begin();
        log.info("事务开始");
    }

    @AfterReturning("@annotation(TransactionalAspect)")
    public void commitTransaction() {
        transactionService.commit();
        log.info("事务结束");
    }


    @AfterThrowing(pointcut = "@annotation(TransactionalAspect)", throwing = "ex")
    public void rollbackTransaction(Exception ex) {
        transactionService.rollback();
        log.error("事务回滚", ex.getMessage());
    }
}
