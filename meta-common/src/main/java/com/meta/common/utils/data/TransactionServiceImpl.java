package com.meta.common.utils.data;

import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableSupport;
import org.hibernate.query.internal.NativeQueryImpl;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
@Repository
public class TransactionServiceImpl implements TransactionService {

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private EntityManager entityManager;

    private ThreadLocal<TransactionStatus> threadLocal = new ThreadLocal<>();

    @Override
    public void begin() {
        TransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        threadLocal.set(transactionManager.getTransaction(transactionDefinition));
    }

    @Override
    public void commit() {
        transactionManager.commit(threadLocal.get());
        threadLocal.remove();
    }

    @Override
    public void rollback() {
        transactionManager.rollback(threadLocal.get());
        threadLocal.remove();
    }

    @Override
    public <V> V execute(Callable<V> callable) {
        try {
            begin();
            V result = callable.call();
            commit();
            return result;
        } catch (Exception e) {
            rollback();
            throw new RuntimeException(e);
        }
    }

    @Override
    public <T> Page<T> page(String sql,List<?> params,Class<T> entity){
        PageDomain pageReq = TableSupport.buildPageRequest();
        Pageable pageable = PageRequest.of(pageReq.getPageNo(), pageReq.getPageSize());
        Query totalQuery = entityManager.createNativeQuery("select count(*) from ("+sql+") t");
        for (int i = 0; i < params.size(); i++) {
            totalQuery.setParameter(i + 1, params.get(i));
        }

        List<?> resultList = totalQuery.getResultList();
        if (resultList.isEmpty()) {
            return Page.empty();
        }
        BigInteger total = (BigInteger) resultList.get(0);
        Query contentQuery = entityManager.createNativeQuery(sql);
        for (int i = 0; i < params.size(); i++) {
            contentQuery.setParameter(i + 1, params.get(i));
        }

        contentQuery.unwrap(NativeQueryImpl.class).setResultTransformer(Transformers.aliasToBean(entity));
        contentQuery.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());
        contentQuery.setMaxResults(pageable.getPageSize());
        List<T> list = contentQuery.getResultList();
        if (list.isEmpty()){
            return Page.empty();
        }
        return new PageImpl<>(list, pageable, total.intValue());
    }

    @Override
    public <T> T single(String sql, List<?> params, Class<T> entity) {
        if(params == null)
            params = new ArrayList<>();
        int size = params.size();
        Query contentQuery = entityManager.createNativeQuery(sql);
        for (int i = 0; i < size; i++) {
            contentQuery.setParameter(i + 1, params.get(i));
        }
        contentQuery.unwrap(NativeQueryImpl.class).setResultTransformer(Transformers.aliasToBean(entity));
        List<T> list = contentQuery.getResultList();
        if (list.isEmpty()){
            return null;
        }
        return list.get(0);
    }


    @Override
    public <T> List<T> list(String sql,List<?> params,Class<T> entity){
        int size = 0;
        if (params != null){
            size = params.size();
        }
        Query contentQuery = entityManager.createNativeQuery(sql);
        for (int i = 0; i < size; i++) {
            contentQuery.setParameter(i + 1, params.get(i));
        }
        contentQuery.unwrap(NativeQueryImpl.class).setResultTransformer(Transformers.aliasToBean(entity));
        List<T> list = contentQuery.getResultList();
        if (list.isEmpty()){
            return new ArrayList();
        }
        return list;
    }
}
