package com.meta.common.utils.vcc;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.security.NoSuchAlgorithmException;


public class DESUtils {

    private static final String type="DES/CBC/PKCS5Padding";
    private static final String DES = "DES";

    /**
     * 加密
     * @param message
     * @param key
     * @return
     * @throws Exception
     */
    public static String encrypt(String message, String key) throws Exception {
        Cipher cipher = Cipher.getInstance(type);
        byte[] keyByte=Base64.decodeBase64(key);
        DESKeySpec desKeySpec = new DESKeySpec(keyByte);

        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        Secret<PERSON>ey secretKey = keyFactory.generateSecret(desKeySpec);
        IvParameterSpec iv = new IvParameterSpec(keyByte);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);

        return toHexString(cipher.doFinal(message.getBytes("UTF-8")));
    }

    /**
     * 解密
     * @param message
     * @param key
     * @return
     * @throws Exception
     */
    public static String decrypt(String message, String key) throws Exception {
        byte[] keyByte=Base64.decodeBase64(key);
        byte[] bytesrc = convertHexString(message);
        Cipher cipher = Cipher.getInstance(type);
        DESKeySpec desKeySpec = new DESKeySpec(keyByte);
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        SecretKey secretKey = keyFactory.generateSecret(desKeySpec);
        IvParameterSpec iv = new IvParameterSpec(keyByte);

        cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);

        byte[] retByte = cipher.doFinal(bytesrc);
        return new String(retByte);
    }

    /**
     * 生成符合DES要求的密钥, 长度为64位(8字节).
     */
    public static String generateDesKey() throws NoSuchAlgorithmException {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(DES);
            SecretKey secretKey = keyGenerator.generateKey();
            return Base64.encodeBase64String(secretKey.getEncoded());
    }

    public static byte[] convertHexString(String ss) {
        byte digest[] = new byte[ss.length() / 2];
        for (int i = 0; i < digest.length; i++) {
            String byteString = ss.substring(2 * i, 2 * i + 2);
            int byteValue = Integer.parseInt(byteString, 16);
            digest[i] = (byte) byteValue;
        }

        return digest;
    }
    public static String toHexString(byte b[]) {
        StringBuffer hexString = new StringBuffer();
        for (int i = 0; i < b.length; i++) {
            String plainText = Integer.toHexString(0xff & b[i]);
            if (plainText.length() < 2)
                plainText = "0" + plainText;
            hexString.append(plainText);
        }

        return hexString.toString();
    }
}
