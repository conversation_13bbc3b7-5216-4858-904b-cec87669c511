package com.meta.common.utils.enums;

import com.meta.common.enums.BaseEnum;
import com.meta.common.utils.StringUtils;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.converter.ConverterFactory;
import org.springframework.stereotype.Component;

@Component
public class EnumConvert implements ConverterFactory<String, BaseEnum<?>> {
    @Override
    public <T extends BaseEnum<?>> Converter<String, T> getConverter(Class<T> aClass) {
        return new StringToEnum(aClass);
    }


    public static class StringToEnum<T extends BaseEnum<?>> implements Converter<String, T> {

        private final Class<T> targetType;

        public StringToEnum(Class<T> targetType) {
            this.targetType = targetType;
        }

        @Override
        public T convert(String source) {
            if (StringUtils.isEmpty(source)) {
                return null;
            }
            return (T) EnumConvert.getEnum(this.targetType, source);
        }
    }

    public static <T extends BaseEnum<?>> T getEnum(Class<T> targetType, String source) {
        for (T constant : targetType.getEnumConstants()) {
            if (source.equals(String.valueOf(constant.getOrdinal()))){
                return constant;
            }
        }
        return null;
    }


}