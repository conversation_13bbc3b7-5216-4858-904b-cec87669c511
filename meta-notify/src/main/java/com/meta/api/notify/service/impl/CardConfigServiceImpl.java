package com.meta.api.notify.service.impl;

import com.meta.api.notify.service.CardConfigService;
import com.meta.system.dao.app.CardConfigDao;
import com.meta.system.domain.app.CardConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CardConfigServiceImpl implements CardConfigService {
    @Autowired
    private CardConfigDao cardConfigDao;

    @Override
    public List<CardConfig> findCardConfigAll() {
        return cardConfigDao.findAll();
    }

    @Override
    public CardConfig findCardConfigByCardCfgId(Long cardCfgId) {
        return cardConfigDao.findById(cardCfgId).orElse(null);
    }

//    @Override
//    public CardConfig findCardConfigByCardTypeAndCardLevelAndChannel(String cardType, String cardLevel,String source) {
//        return cardConfigDao.findCardConfigByCardTypeAndCardLevelAndChannel(cardType,cardLevel,source);
//    }
}
