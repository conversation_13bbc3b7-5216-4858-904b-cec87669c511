package com.meta.api.notify.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meta.api.notify.entity.Body;
import com.meta.api.notify.entity.RespHead;
import com.meta.api.notify.entity.Response;
import com.meta.api.notify.enums.NotifyRespCode;
import com.meta.common.enums.vcc.ApiCode;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.common.utils.vcc.DESUtils;
import com.meta.common.utils.vcc.RSAUtils;
import com.meta.common.utils.vcc.StringUtil;
import com.meta.system.config.VccConfig;
import com.meta.system.domain.CreditCard;
import com.meta.system.domain.es.notify.HeadNofity;
import com.meta.system.domain.es.notify.RequestNotify;
import com.meta.system.service.CreditCardService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.security.PrivateKey;
import java.security.PublicKey;

@Component
public class CardNotifyUtils {
    protected final Logger logger = LoggerFactory.getLogger(CardNotifyUtils.class);
    @Autowired
    private CreditCardService creditCardService;


    /**
     * 构建返回体
     * @param respCode
     * @param head
     * @param sign
     * @param keyEnc
     * @param encrypt
     * @return
     */
    private Response getResponse(NotifyRespCode respCode, HeadNofity head, String sign, String keyEnc, String encrypt){
        Response resp = new Response();
        RespHead respHead = new RespHead();
        respHead.setCode(respCode.getValue())
                .setDetail(respCode.getName())
                .setPartnerId(head.getPartnerId())
                .setApiCode(head.getApiCode())
                .setRequestNo(head.getRequestNo())
                .setVersion(head.getVersion())
                .setSign(sign)
                .setKeyEnc(keyEnc);
        resp.setHead(respHead);
        Body body = new Body();
        body.setEncrypt(encrypt);
        resp.setBody(body);
        return resp;
    }


    /**
     * 加密
     * @param
     * @return
     */
    public  Response  encrypt(HeadNofity head, JSONObject jsonObject, NotifyRespCode respCode)  {
        String hexJsonEnc = null;
        String hexKeyEnc = null;
        String hexSign = null;
        if(respCode == null)
            respCode = NotifyRespCode.SUCCESS;
        try {
            String key  = AESUtils.generateAesKey();
            String data = JSON.toJSONString(jsonObject);
            logger.debug("明文:" + data);
            logger.debug("密钥:key:" + key);
            hexJsonEnc = AESUtils.aesEncrypt(key, data);
            logger.debug("明文加密结果encrypt:" + hexJsonEnc);
            RSAUtils rsaUtils   = new RSAUtils();
            //##################################################################
            PrivateKey privatekey = rsaUtils.getPrivateKey(VccConfig.privateStr);
            PublicKey  publicKey  = rsaUtils.getPublicKey(VccConfig.publicKey);
//            PrivateKey privatekey = rsaUtils.getPrivateKey("MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC9E7fupXdmwZx5fGQZgj+FczgCEI9u6LyFxY/RFrkYXHojY7qPgyjaznqCrN3xfGolWO7iq9EbpoRetR5/ovLG8eWlMfWVkYfKZeQ6pfv1eVl+9p4O0rlBpVGVNzbDtLTuwX7Xw0RxFFJyZVR2EQzo0cb8XyZG3Z7XxDPMnOzqGIPVcHp0nL8vE8IPFyrg1z1AYoWUy90B7Kx05h/nNX7jtjNrVnpxHNpmK8kx5DQXrEjZZPVrunXzHRXg2vGkpb6DkRPHUCfuMpFTTL5Oge8O9sjM71tJeGPSmb++k8KL/6j97S4/a69PUH3ZldIMvgsKuzMYCY0O7Pwwrf5B6iTrAgMBAAECggEALlM7eHwQAhwjs1w3xkw0NgUhztex3NGnBvt9nhP8K6zUvAD+P5U6GEoImCW0hysdcqMUfHLuW+Dzg6TKoSkSZI313wCblBbA92T5gykRz3X46HOSDD2y6BOSJoYNo+uNfQXphwGvrij1flO3WuoYiJ6FK2ZAoZJBDcpjiplULpKBOih/MhvQ6CyN3fIyjvaKWJnFNnb5k+lG9i8ff95tuJVBey1OPFNuJ/TvDysAJkzUV9pl8MItlVdYjLLbWrBnVLojpdOI2/OcKjp/TJk+Y+3pw4R1kAH29LvH5nSO8kaaPu186BbSQOLl9/Dw43FwtPudWoePbucZCyQI/TTmqQKBgQDhyQ+e6gnCIvRSwgqBFUduZ5Pk/PWRA7NTKxTF8gRA9vsPju5vll3H3NXJdEwHA7MNlLXgWDsur/dwa9LUkdWPkrzPZiPZK8p5LcudVmpLgvoDzg9paSflA9tKVFyS2BRWoT91BZ+0k8lYkiuZ/iOfC4IF7UZocIaEpEBvQKOl/QKBgQDWYRrcfP2JYSwkDplSdKty1iB3fN/ISb/Q1s1azYrx5DOnWoVCfHauveY0eqXh1lZksZyr1SQ0kY6N6IihgUWGZy8bAezsKKy+b8oHVLa0UPoMUZGRF/KjZu0RBEyfoHGec1J7tSttCLTff4U+06fsYX37X8ZZJOuoSHoOVuh3BwKBgQDH9B6QVqWTtw72p39T97tNzA7O0TLMXSGXeuSntIAN5GxMyADi86BT2n++K+8UmzMbyOIVLy4iV5XjiqmotQoTXxk09ziyIDTsgiD7UsdJ3lF5wygk6wp4p6Sxu+pL5W6FlcGz6eoYqnS6qqBQfR+gvzlD9HDRFy4aE7g4jl8fJQKBgHxaAVnKSsroidE0gq04rcbD/DszPR+R3+kE4EN+nM0pIOk/cbMaPUGpN6JTTmuMh7qK6CUoUoMHt5gLPU3pjWmj6sgQutxVz7X7ZEYSVpSLsC97FSQDryQnbPYE92lUiPNvU3Ycpd7uQPSvpJvH7E0KVCh+6rAE4YlQ2TP+J4P7AoGBANd0ZmRORBn7tXJZpRwJPrliKwyFj4dGIyWCgaUiVYwWSYg3TkHm4LU+zMLcUfF1Cu7qBPhFM/zdhFEnaffqAv4WH9P/BKVba9BhL6z6nhWEI7be2waFUSDNhLkJhxmot0NUpdlsSau+x60HGuaOY1weEZf6bLarn4TI1Le+vxtI");
//            PublicKey  publicKey  = rsaUtils.getPublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt7Bct5InySYB0lyYeMkwJtCnF/q0eHNQ2S7UiquPb4VA8SHvu52RQO37RFViPpqmWp7qtke3QSN3VizZlT/oBKifWW4p1mDTK7uRVSNWOnBkEdWijpGXcD1m6dvCAKoiyPSM+C696viETdTrfx7RanLcfWoKbe/TQliIMzUkdVG2o/gXboI/fw83cG9vAhh7bjB3ONuAd/Dr1LYoJNkhroBWj40yDC49Sw8xVlZGBO8dtDrnm+1EEyvju1iIIQr1me8ucZs5uPFQheeGhlgjeV12s1eDIgp3kdAQN/mu9sGADeYkiDgTRMqCnAl2GFUOdaJsacjWwflxCSzOvwPeSQIDAQAB");
            //##################################################################
            rsaUtils.initKey(privatekey, publicKey);
            // 密钥加密
            byte[] bkey = rsaUtils.encryptRSA(key.getBytes(), false, "utf-8");
             hexKeyEnc = DESUtils.toHexString(bkey);
            logger.debug("密钥加密结果:keyEnc:" + hexKeyEnc);
            // 签名
            String signStr = head.getPartnerId() + "|" + head.getApiCode() + "|" + head.getVersion()+ "|" + head.getRequestNo() + "|" +respCode.getValue() +"|"+respCode.getName()+ "|"+ hexJsonEnc;
            byte[] sign    = rsaUtils.signRSA(signStr.getBytes("utf-8"), false, "utf-8");
            hexSign = DESUtils.toHexString(sign);
            logger.debug("签名结果:sign:" + hexSign);
        } catch (Exception e) {
            e.printStackTrace();
            respCode = NotifyRespCode.INTERNAL_ERROR;
        }finally {
            Response response = getResponse(respCode,head,hexSign,hexKeyEnc,hexJsonEnc);
            return response;
        }

    }

    /**
     * 解密验签
     * @param req 请求体
     * @return
     */
    public  String  decrypt(RequestNotify req)  {
        //1.验证请求参数
        NotifyRespCode respCode =  checkResquest(req);
        if(respCode != NotifyRespCode.SUCCESS){
            return respCode.getValue();
        }
        //2.初始化公私钥
        RSAUtils rsaUtils = initRsa();
        if(rsaUtils == null){
            return NotifyRespCode.INTERNAL_ERROR.getValue();
        }
        String json = null;
        try {
            //3.解析
           String key =  decryptRSAKey(rsaUtils,req.getHead().getKeyEnc());
           json = aesDecrypt(req.getBody().getEncrypt(),key);
           boolean checkJson = checkParam(json,req.getHead().getApiCode());
           if(!checkJson)
               return NotifyRespCode.PARAM_FORMAT_ERROR.getValue();
           boolean ret = verify(rsaUtils,req);
           if(!ret){
                return NotifyRespCode.UNAUTHENTICATED_ERROR.getValue();
           }
        }catch(Exception e){
            return NotifyRespCode.UNAUTHENTICATED_ERROR.getValue();
        }
        //5.校验卡号是否存在
        JSONObject jsonObject = JSONObject.parseObject(json);
        String cardId = jsonObject.getString("cardId");
        CreditCard c = creditCardService.findByCardId(cardId);
        if(c == null){
            return NotifyRespCode.CARD_NO_FOUND.getValue();
        }
        jsonObject.put("creditCard",c);
        return jsonObject.toJSONString();
    }

    private RSAUtils initRsa(){
        RSAUtils   rsaUtils   = new RSAUtils();
        try{
            //我本地的私钥
            PrivateKey privatekey = rsaUtils.getPrivateKey(VccConfig.privateStr);
            PublicKey publicKey  = rsaUtils.getPublicKey(VccConfig.publicKey);
            rsaUtils.initKey(privatekey, publicKey);
        }catch (Exception e){
            return null;
        }
        return rsaUtils;
    }

    /**
     *  解析密钥
     * @param rsaUtils
     * @param hexKeyEnc
     * @return
     * @throws Exception
     */
    private String decryptRSAKey(RSAUtils  rsaUtils,String hexKeyEnc) throws Exception {
        byte[] bkey = rsaUtils.decryptRSA(DESUtils.convertHexString(hexKeyEnc), false, "utf-8");
        String key  = new String(bkey);
        logger.info("密钥解密结果:" + key);
        return key;
    }

    /**
     *  明文解密
     * @param hexJsonEnc
     * @param key
     * @return
     */
    private String aesDecrypt(String hexJsonEnc,String key){
        String json =  AESUtils.aesDecrypt(key, hexJsonEnc);
        return json;
    }

    /**
     * 验签
     * @param rsaUtils
     * @param req
     * @return
     * @throws Exception
     */
    private boolean verify(RSAUtils  rsaUtils,RequestNotify req) throws Exception {
        String signStr = req.getHead().getPartnerId() + "|" + req.getHead().getApiCode() + "|" + req.getHead().getVersion() + "|" + req.getHead().getRequestNo() +"|" +req.getBody().getEncrypt();
        logger.debug("验签：signStr："+signStr);
        byte[]  bsign = DESUtils.convertHexString(req.getHead().getSign());
        boolean ret   = rsaUtils.verifyRSA(signStr.getBytes("utf-8"), bsign, false, "utf-8");
        logger.info("验签结果:" + ret);
        return ret;
    }

    /**
     * 验证请求参数
     * @param req
     * @return
     */
    private NotifyRespCode checkResquest(RequestNotify req){
        //1.验证参数
        logger.info(JSONObject.toJSONString(req));
        if(req == null)
            return NotifyRespCode.PARAMETER_ERROR;
        if(req.getHead() == null || req.getBody() == null)
            return NotifyRespCode.PARAMETER_ERROR;
        if(StringUtil.isEmpty(req.getHead().getApiCode())
            || StringUtil.isEmpty(req.getHead().getKeyEnc())
            || StringUtil.isEmpty(req.getHead().getRequestNo())
            || StringUtil.isEmpty(req.getHead().getPartnerId())
                || StringUtil.isEmpty(req.getHead().getVersion())
                || StringUtil.isEmpty(req.getHead().getSign())
                || StringUtil.isEmpty(req.getBody().getEncrypt()))
            return NotifyRespCode.PARAMETER_ERROR;

        boolean res = ApiCode.checkNotify(req.getHead().getApiCode());
        if(!res)
            return NotifyRespCode.INTERFACE_UNAUTHORIZED;
        //幂等错误 + 请求号重复

        return NotifyRespCode.SUCCESS;
    }

    /**
     * 验证参数格式
     * @param json
     * @param apiCode
     * @return
     */
    public boolean checkParam(String json,String apiCode){
        JSONObject j = JSONObject.parseObject(json);
        boolean res = true;
        ApiCode esApiCode = ApiCode.toType(apiCode);
        switch (esApiCode){
            case createCardNotify:
            case closeCardNotify:
            case suspendCardNotify:
            case unsuspendCardNotify:
                if(!j.containsKey("cardId")){
                    res = false;
                }else if(!j.containsKey("cardStatus")){
                    res = false;
                }
                break;
            case  rechargeCardNotify:
            case  withdrawCardNotify:
            case  transactionNotify:
                if(!j.containsKey("cardId")){
                    res = false;
                }else if(!j.containsKey("transactionId")){
                    res = false;
                }
                break;
            default:
                res = false;
                break;
        }
        return res;
    }

}
