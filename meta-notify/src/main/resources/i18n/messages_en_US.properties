## Error message
not.null=* This field is required.
user.jcaptcha.error=Invalid verification code.
user.jcaptcha.expire=Verification code has expired.
user.not.exists=User does not exist or password is incorrect.
user.password.not.match=User does not exist or password is incorrect.
user.password.retry.limit.count=Incorrect password entered {0} times.
user.password.retry.limit.exceed=Incorrect password entered {0} times. Account locked for 10 minutes.
user.password.delete=Sorry, your account has been deleted.
user.blocked=User is blocked. Please contact the administrator.
role.blocked=Role is blocked. Please contact the administrator.
user.logout.success=Logout successful.

length.not.valid=Length must be between {min} and {max} characters.

user.username.not.valid=* Must consist of 2 to 20 Chinese characters, letters, numbers, or underscores, and must not start with a number.
user.password.not.valid=* Must be 5-50 characters.

user.email.not.valid=Invalid email format.
user.mobile.phone.number.not.valid=Invalid phone number format.
user.login.success=Login successful.
user.notfound=Please login again.
user.forcelogout=Administrator has forced logout. Please login again.
user.unknown.error=Unknown error. Please login again.

# Google Authenticator is not enabled for authentication
google.authenticator.disabled=Google Authenticator is not enabled for authentication


## File upload message
upload.exceed.maxSize=Uploaded file size exceeds the limit! <br/> Maximum allowed file size is: {0}MB!
upload.filename.exceed.length=Uploaded file name exceeds {0} characters.

## Permissions
no.permission=You do not have permission to access the data. Please contact the administrator to add the permission [{0}].
no.create.permission=You do not have permission to create data. Please contact the administrator to add the permission [{0}].
no.update.permission=You do not have permission to modify data. Please contact the administrator to add the permission [{0}].
no.delete.permission=You do not have permission to delete data. Please contact the administrator to add the permission [{0}].
no.export.permission=You do not have permission to export data. Please contact the administrator to add the permission [{0}].
no.view.permission=You do not have permission to view data. Please contact the administrator to add the permission [{0}].

ip.address.blocked=IP address is blocked

## node
node.application.success=Node Application Successful
node.application.duplicate=Please do not submit duplicate applications
node.application.insufficient.balance=Insufficient balance
node.transfer.success=Transfer Successful
node.transfer.failed=Transfer Failed


## wallet
wallet.account.insufficient.balance=Account Insufficient balance
wallet.activitiCode.insufficient.num=Insufficient activation code
wallet.activitiCode.silver.insufficient.num=Insufficient Blue Card activation code
wallet.activitiCode.golden.insufficient.num=Insufficient Gold Card activation code
wallet.activitiCode.goldenBlack.insufficient.num=Insufficient Black Gold Card activation code
wallet.deduct.funds.failed=Failed to deduct funds, insufficient balance in account.

## 用户信息
registration.success=Registration Successful
kyc.already=You have already completed the KYC verification
kyc.idcard.invalid=Invalid ID card number


## 执行结果
operation.success=Operation successful
operation.failed=Operation failed

#####



## 文件
read.success=Read successful
read.failed=Read failed

## download
download.success=Download successful
download.failed=Download failed

## 卡
card.nofound=The card number does not exist.
card.status.execption=Card status is abnormal and cannot be recharged
card.userId.wrong=Not the cardholder's card
card.balance.too.low=The balance in the card must be kept at least 5 dollars
card.deposit.amount.too.low=The recharge amount cannot be less than 
card.withdraw.amount.too.low=The withdraw amount cannot be less than 


####链上交易结果######
trade.transactionid.error=The transaction number information is incorrect. Please verify and try again!
sys.recharge.busy=The recharge system is busy, please try again later...