package com.meta.send.listenter;

import com.meta.send.config.MetaApiConfig;
import com.meta.system.email.Mail;
import com.meta.system.email.SendEmail;
import com.meta.system.service.ISysConfigService;
import com.meta.system.uitls.NotifyDataUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/08/28/15:01
 */
@Slf4j
@Component
public class SpringRabbitListener {

    @Autowired
    private NotifyDataUtils notifyDataUtils;

    @Autowired
    private SendEmail sendEmail;

    @Autowired
    private ISysConfigService sysConfigService;


    @RabbitListener(queues = "queue.transaction.code")
    public void listenTransactionCodeQueue(Long id) throws Exception {

        try {
            log.debug("消费者接收到queue.transaction.code的消息：【" + id + "】");
            boolean deal = notifyDataUtils.deal(id, MetaApiConfig.privateStr, MetaApiConfig.version);
            if (deal) {
                log.info("消费者处理消息成功！" + id);
            } else {
                //可以走spring配置的重试机制
                log.error("消息发送成功，B端接收返回失败" + id);
                throw new Exception("消息发送成功，B端接收返回失败");
            }
        } catch (Exception e) {
            log.error("消息发送失败：【" + id + "】");
            throw new Exception(e);
        }

    }

    @RabbitListener(queues = "queue.transaction.code")
    public void listenTransactionCodeQueue2(Long id) throws Exception {

        try {
            log.debug("消费者2接收到queue.transaction.code的消息：【" + id + "】");

            boolean deal = notifyDataUtils.deal(id, MetaApiConfig.privateStr, MetaApiConfig.version);
            if (deal) {

                log.info("消费者处理消息成功！" + id);
            } else {
                //可以走spring配置的重试机制
                log.error("消息发送成功，B端接收返回失败" + id);
                throw new Exception("消息发送成功，B端接收返回失败");
            }
        } catch (Exception e) {
            log.error("消息发送失败：【" + id + "】");
            throw new Exception(e);
        }

    }

    @RabbitListener(queues = "queue.card.recharge")
    public void listenCardRechargeQueue(Long id) throws Exception {
        try {
            log.debug("消费者接收到queue.card.recharge的消息：【" + id + "】");
            boolean deal = notifyDataUtils.deal(id, MetaApiConfig.privateStr, MetaApiConfig.version);
            if (deal) {
                log.info("消费者处理消息成功！" + id);
            } else {
                //可以走spring配置的重试机制
                log.error("消息发送成功，B端接收返回失败" + id);
                throw new Exception("消息发送成功，B端接收返回失败");
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

//    @RabbitListener(queues = "queue.card.recharge")
//    public void listenCardRechargeQueue2(Long id) throws Exception {
//        try {
//            log.debug("消费者2接收到queue.card.recharge的消息：【" + id + "】");
//            boolean deal = notifyDataUtils.deal(id, MetaApiConfig.privateStr, MetaApiConfig.version);
//            if (deal) {
//                log.info("消费者处理消息成功！" + id);
//            } else {
//                //可以走spring配置的重试机制
//                log.error("消息发送成功，B端接收返回失败" + id);
//                throw new Exception("消息发送成功，B端接收返回失败");
//            }
//        } catch (Exception e) {
//            throw new Exception(e);
//        }
//    }


    @RabbitListener(queues = "queue.card.3ds")
    public void listenCard3dsQueue(Long id) throws Exception {
        try {
            log.debug("消费者接收到queue.card.3ds的消息：【" + id + "】");
            boolean deal = notifyDataUtils.deal(id, MetaApiConfig.privateStr, MetaApiConfig.version);
            if (deal) {

                log.info("消费者处理消息成功！" + id);
            } else {
                //可以走spring配置的重试机制
                log.error("消息发送成功，B端接收返回失败" + id);
                throw new Exception("消息发送成功，B端接收返回失败");
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    @RabbitListener(queues = "queue.card.3ds")
    public void listenCard3dsQueue2(Long id) throws Exception {
        try {
            log.debug("消费者2接收到queue.card.3ds的消息：【" + id + "】");
            boolean deal = notifyDataUtils.deal(id, MetaApiConfig.privateStr, MetaApiConfig.version);
            if (deal) {
                log.info("消费者处理消息成功！" + id);
            } else {
                //可以走spring配置的重试机制
                log.error("消息发送成功，B端接收返回失败" + id);
                throw new Exception("消息发送成功，B端接收返回失败");
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    @RabbitListener(queues = "queue.user.kyc")
    public void listenUserKycQueue(Long id) throws Exception {
        try {
            log.debug("消费者接收到queue.user.kyc的消息：【" + id + "】");
            boolean deal = notifyDataUtils.deal(id, MetaApiConfig.privateStr, MetaApiConfig.version);
            if (deal) {

                log.info("消费者处理消息成功！" + id);
            } else {
                //可以走spring配置的重试机制
                log.error("消息发送成功，B端接收返回失败" + id);
                throw new Exception("消息发送成功，B端接收返回失败");
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    @RabbitListener(queues = "queue.user.kyc")
    public void listenUserKycQueue2(Long id) throws Exception {
        try {
            log.debug("消费者2接收到queue.user.kyc的消息：【" + id + "】");
            boolean deal = notifyDataUtils.deal(id, MetaApiConfig.privateStr, MetaApiConfig.version);
            if (deal) {

                log.info("消费者处理消息成功！" + id);
            } else {
                //可以走spring配置的重试机制
                log.error("消息发送成功，B端接收返回失败" + id);
                throw new Exception("消息发送成功，B端接收返回失败");
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    @RabbitListener(queues = "queue.card.status")
    public void listenCardStatusQueue(Long id) throws Exception {

        try {
            log.debug("消费者接收到queue.card.status的消息：【" + id + "】");
            boolean deal = notifyDataUtils.deal(id, MetaApiConfig.privateStr, MetaApiConfig.version);
            if (deal) {

                log.info("消费者处理消息成功！" + id);
            } else {
                //可以走spring配置的重试机制
                log.error("消息发送成功，B端接收返回失败" + id);
                throw new Exception("消息发送成功，B端接收返回失败");
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    @RabbitListener(queues = "queue.card.status")
    public void listenCardStatusQueue2(Long id) throws Exception {

        try {
            log.debug("消费者2接收到queue.card.status的消息：【" + id + "】");
            boolean deal = notifyDataUtils.deal(id, MetaApiConfig.privateStr, MetaApiConfig.version);
            if (deal) {
                log.info("消费者处理消息成功！" + id);
            } else {
                //可以走spring配置的重试机制
                log.error("消息发送成功，B端接收返回失败" + id);
                throw new Exception("消息发送成功，B端接收返回失败");
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    @RabbitListener(queues = "queue.transaction.created")
    public void listenTransactionCreatedQueue(Long id) throws Exception {

        try {
            log.debug("消费者接收到queue.transaction.created的消息：【" + id + "】");
            boolean deal = notifyDataUtils.deal(id, MetaApiConfig.privateStr, MetaApiConfig.version);
            if (deal) {

                log.info("消费者处理消息成功！" + id);
            } else {
                //可以走spring配置的重试机制
                log.error("消息发送成功，B端接收返回失败" + id);
                throw new Exception("消息发送成功，B端接收返回失败");
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    @RabbitListener(queues = "queue.transaction.created")
    public void listenTransactionCreatedQueue2(Long id) throws Exception {

        try {
            log.debug("消费者2接收到queue.transaction.created的消息：【" + id + "】");
            boolean deal = notifyDataUtils.deal(id, MetaApiConfig.privateStr, MetaApiConfig.version);
            if (deal) {
                log.info("消费者处理消息成功！" + id);
            } else {
                //可以走spring配置的重试机制
                log.error("消息发送成功，B端接收返回失败" + id);
                throw new Exception("消息发送成功，B端接收返回失败");
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    /**
     * 实体卡激活码
     * @param id
     * @throws Exception
     */
    @RabbitListener(queues = "queue.card.activation.code")
    public void listenCardActivationCodeQueue(Long id) throws Exception {

        try {
            log.debug("消费者接收到queue.card.activation.code的消息：【" + id + "】");
            boolean deal = notifyDataUtils.deal(id, MetaApiConfig.privateStr, MetaApiConfig.version);
            if (deal) {

                log.info("消费者处理消息成功！" + id);
            } else {
                //可以走spring配置的重试机制
                log.error("消息发送成功，B端接收返回失败" + id);
                throw new Exception("消息发送成功，B端接收返回失败");
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
    }


    /**
     * h5钱包数据
     * @param id
     * @throws Exception
     */
    @RabbitListener(queues = "queue.h5.listening")
    public void listenH5ListeningQueue(Long id) throws Exception {

        try {
            log.debug("消费者接收到queue.h5.listening的消息：【" + id + "】");
            boolean deal = notifyDataUtils.deal(id, MetaApiConfig.privateStr, MetaApiConfig.version);
            if (deal) {

                log.info("消费者处理消息成功！" + id);
            } else {
                //可以走spring配置的重试机制
                log.error("消息发送成功，B端接收返回失败" + id);
                throw new Exception("消息发送成功，B端接收返回失败");
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    /**
     * 销卡通知
     * @param id
     * @throws Exception
     */
    @RabbitListener(queues = "queue.card.close")
    public void listenCloseCardQueue(Long id) throws Exception {

        try {
            log.debug("消费者接收到queue.card.close的消息：【" + id + "】");
            boolean deal = notifyDataUtils.deal(id, MetaApiConfig.privateStr, MetaApiConfig.version);
            if (deal) {

                log.info("消费者处理消息成功！" + id);
            } else {
                //可以走spring配置的重试机制
                log.error("消息发送成功，B端接收返回失败" + id);
                throw new Exception("消息发送成功，B端接收返回失败");
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    @RabbitListener(queues = "error.queue")
    public void listenErrorQueue(Channel channel, Message message) throws Exception {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();

        try {

            String email = sysConfigService.selectConfigByKey("admin-email");
            if (email.contains(",")) {
                String[] split = email.split(",");
                for (int i = 0; i < split.length; i++) {
                    Mail mail = new Mail();
                    mail.setEmail(split[i]);
                    mail.setContent(message.getMessageProperties().toString());
                    mail.setSubject("发送B端数据错误");
                    //发送通知邮件
                    sendEmail.sendCheckEmail(mail);
                }
            } else {
                Mail mail = new Mail();
                mail.setEmail(email);
                mail.setContent(message.getMessageProperties().toString());
                mail.setSubject("发送B端数据错误");
                //发送通知邮件
                sendEmail.sendCheckEmail(mail);
            }
            //手动ack  第二个参数为false是表示仅仅确认当前消息 true表示确认之前所有的消息
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            //手动nack 告诉rabbitmq该消息消费失败  第三个参数：如果被拒绝的消息应该被重新请求，而不是被丢弃或变成死信，则为true
            channel.basicNack(deliveryTag, false, false);
        }
    }


}
