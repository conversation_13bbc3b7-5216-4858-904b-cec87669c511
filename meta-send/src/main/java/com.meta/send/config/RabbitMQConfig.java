package com.meta.send.config;

import com.meta.common.constant.Constants;
import org.springframework.amqp.core.*;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/08/24/17:33
 */
@Component
public class RabbitMQConfig {

    @Bean
    public MessageConverter messageConverter(){
        return new Jackson2JsonMessageConverter();
    }


    @Bean
    public Queue WorkQueue1(){

        return QueueBuilder.durable(Constants.queue_transaction_code).build();

    }

    @Bean
    public Queue WorkQueue2(){
        return QueueBuilder.durable(Constants.queue_card_recharge).build();

    }


    @Bean
    public Queue WorkQueue3(){

        return QueueBuilder.durable(Constants.queue_card_3ds).build();

    }

    @Bean
    public Queue WorkQueue4(){

        return QueueBuilder.durable(Constants.queue_card_kyc).build();

    }
    @Bean
    public Queue WorkQueue5(){

        return QueueBuilder.durable(Constants.queue_card_status).build();

    }
    @Bean
    public Queue WorkQueue6(){

        return QueueBuilder.durable(Constants.queue_transaction_created).build();

    }
    @Bean
    public Queue WorkQueue7(){

        return QueueBuilder.durable(Constants.queue_card_activation_code).build();

    }
    @Bean
    public Queue WorkQueue8(){

        return QueueBuilder.durable(Constants.queue_h5_listening).build();

    }
    @Bean
    public Queue WorkQueue9(){

        return QueueBuilder.durable(Constants.queue_card_close).build();

    }


    @Bean
    public DirectExchange directExchange(){

        return new DirectExchange(Constants.exchange_name_notify, true, false);

    }

    @Bean
    public Binding bindingWorkQueue1(Queue WorkQueue1, DirectExchange directExchange) {
        return BindingBuilder.bind(WorkQueue1).to(directExchange).with(Constants.routing_key_transaction_code);
    }

    @Bean
    public Binding bindingWorkQueue2(Queue WorkQueue2, DirectExchange directExchange) {
        return BindingBuilder.bind(WorkQueue2).to(directExchange).with(Constants.routing_key_card_recharge);
    }

    @Bean
    public Binding bindingWorkQueue3(Queue WorkQueue3, DirectExchange directExchange) {
        return BindingBuilder.bind(WorkQueue3).to(directExchange).with(Constants.routing_key_card_3ds);
    }

    @Bean
    public Binding bindingWorkQueue4(Queue WorkQueue4, DirectExchange directExchange) {
        return BindingBuilder.bind(WorkQueue4).to(directExchange).with(Constants.routing_key_card_kyc);
    }

    @Bean
    public Binding bindingWorkQueue5(Queue WorkQueue5, DirectExchange directExchange) {
        return BindingBuilder.bind(WorkQueue5).to(directExchange).with(Constants.routing_key_card_status);
    }

    @Bean
    public Binding bindingWorkQueue6(Queue WorkQueue6, DirectExchange directExchange) {
        return BindingBuilder.bind(WorkQueue6).to(directExchange).with(Constants.routing_transaction_created);
    }
    @Bean
    public Binding bindingWorkQueue7(Queue WorkQueue7, DirectExchange directExchange) {
        return BindingBuilder.bind(WorkQueue7).to(directExchange).with(Constants.routing_card_activation_code);
    }
    @Bean
    public Binding bindingWorkQueue8(Queue WorkQueue8, DirectExchange directExchange) {
        return BindingBuilder.bind(WorkQueue8).to(directExchange).with(Constants.routing_key_h5_listening);
    }
    @Bean
    public Binding bindingWorkQueue9(Queue WorkQueue9, DirectExchange directExchange) {
        return BindingBuilder.bind(WorkQueue9).to(directExchange).with(Constants.routing_key_card_close);
    }

}

