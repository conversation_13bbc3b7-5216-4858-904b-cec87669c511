# 项目相关配置
meta:
  # 名称
  name: metabank
  # 版本
  version: 3.2.1
  # 版权年份
  copyrightYear: 2022
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/meta/uploadPath，Linux配置 /home/<USER>/uploadPath）
#  profile: ./Users/<USER>/meta/file
  profile: D:/home/<USER>/file
  # 获取ip地址开关
  addressEnabled: false
  # 登录风控规则
  loginRuleEnabled: false
  # 交易风控规则
  tradeRuleEnabled: true
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
#  resetRedisIp: **************

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8894
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

# 日志配置
logging:
  level:
    com.meta: debug
    org.springframework: warn
  file:
    path: ./Users/<USER>/meta/logs
#    path: G:/home/<USER>/logs

# Spring配置
spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  jpa:
    database: mysql
    generate-ddl: false
    hibernate:
      ddlAuto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
        jdbc:
          #为spring data jpa saveAll方法提供批量插入操作 此处可以随时更改大小 建议500
          batch_size: 500
          batch_versioned_data: true
        order_inserts: true
        order_updates: true
    show-sql: true

  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: *********************************************************************************************************************************************
        username: root
        password: root
#        url: ***************************************************************************************************************************
#        username: metabank
#        password: 555666
#        url: jdbc:mysql://************:3306/meta?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
#        username: metabank
#        password: Z;J1!q2}AGU:5
#        url: **************************************************************************************************************************************************
#        username: metabank
#        password: 555666
#        url: *********************************************************************************************************************************************
#        username: root
#        password: root
#        url: *****************************************************************************************************************************************************************************************
#        username: metabank
#        password: TC]n}(E$43Tr2
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size:  10MB
      # 设置总上传的文件大小
      max-request-size:  20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址 **************
    host: localhost
    # 端口
    port: 6379
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
    database: 10
#rabbitmq配置
  rabbitmq:
    host: b-3a6e11d3-116c-43cf-b645-37b01f200194.mq.ap-southeast-1.amazonaws.com # rabbitMQ的ip地址
    port: 5671 # 端口
    username: mqpaytest
    password: 3nmK7gbiKMqr
    virtual-host: /
    listener:
      simple:
        prefetch: 1
        acknowledge-mode: auto
        retry:
          enabled: true
          initial-interval: 10000
          multiplier: 1
          max-attempts: 5
          max-interval: 1200000ms
    ssl:
      enabled: true


  boot:
    filters:
      # 这里假设你的Filter类的包名为com.meta.system.filter
      # Filter的名称可以根据你的需要进行修改
      filter-name:
        order: -1
        class: com.meta.system.filter.IPBlacklistFilter


#vcc 配置
#vcc:
#  url: https://test-open.whalet.com/api
#  version: 1.0
#  partnerId: 202305061716298892600001
#  #test服务器私钥
##  privateStr: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC9E7fupXdmwZx5fGQZgj+FczgCEI9u6LyFxY/RFrkYXHojY7qPgyjaznqCrN3xfGolWO7iq9EbpoRetR5/ovLG8eWlMfWVkYfKZeQ6pfv1eVl+9p4O0rlBpVGVNzbDtLTuwX7Xw0RxFFJyZVR2EQzo0cb8XyZG3Z7XxDPMnOzqGIPVcHp0nL8vE8IPFyrg1z1AYoWUy90B7Kx05h/nNX7jtjNrVnpxHNpmK8kx5DQXrEjZZPVrunXzHRXg2vGkpb6DkRPHUCfuMpFTTL5Oge8O9sjM71tJeGPSmb++k8KL/6j97S4/a69PUH3ZldIMvgsKuzMYCY0O7Pwwrf5B6iTrAgMBAAECggEALlM7eHwQAhwjs1w3xkw0NgUhztex3NGnBvt9nhP8K6zUvAD+P5U6GEoImCW0hysdcqMUfHLuW+Dzg6TKoSkSZI313wCblBbA92T5gykRz3X46HOSDD2y6BOSJoYNo+uNfQXphwGvrij1flO3WuoYiJ6FK2ZAoZJBDcpjiplULpKBOih/MhvQ6CyN3fIyjvaKWJnFNnb5k+lG9i8ff95tuJVBey1OPFNuJ/TvDysAJkzUV9pl8MItlVdYjLLbWrBnVLojpdOI2/OcKjp/TJk+Y+3pw4R1kAH29LvH5nSO8kaaPu186BbSQOLl9/Dw43FwtPudWoePbucZCyQI/TTmqQKBgQDhyQ+e6gnCIvRSwgqBFUduZ5Pk/PWRA7NTKxTF8gRA9vsPju5vll3H3NXJdEwHA7MNlLXgWDsur/dwa9LUkdWPkrzPZiPZK8p5LcudVmpLgvoDzg9paSflA9tKVFyS2BRWoT91BZ+0k8lYkiuZ/iOfC4IF7UZocIaEpEBvQKOl/QKBgQDWYRrcfP2JYSwkDplSdKty1iB3fN/ISb/Q1s1azYrx5DOnWoVCfHauveY0eqXh1lZksZyr1SQ0kY6N6IihgUWGZy8bAezsKKy+b8oHVLa0UPoMUZGRF/KjZu0RBEyfoHGec1J7tSttCLTff4U+06fsYX37X8ZZJOuoSHoOVuh3BwKBgQDH9B6QVqWTtw72p39T97tNzA7O0TLMXSGXeuSntIAN5GxMyADi86BT2n++K+8UmzMbyOIVLy4iV5XjiqmotQoTXxk09ziyIDTsgiD7UsdJ3lF5wygk6wp4p6Sxu+pL5W6FlcGz6eoYqnS6qqBQfR+gvzlD9HDRFy4aE7g4jl8fJQKBgHxaAVnKSsroidE0gq04rcbD/DszPR+R3+kE4EN+nM0pIOk/cbMaPUGpN6JTTmuMh7qK6CUoUoMHt5gLPU3pjWmj6sgQutxVz7X7ZEYSVpSLsC97FSQDryQnbPYE92lUiPNvU3Ycpd7uQPSvpJvH7E0KVCh+6rAE4YlQ2TP+J4P7AoGBANd0ZmRORBn7tXJZpRwJPrliKwyFj4dGIyWCgaUiVYwWSYg3TkHm4LU+zMLcUfF1Cu7qBPhFM/zdhFEnaffqAv4WH9P/BKVba9BhL6z6nhWEI7be2waFUSDNhLkJhxmot0NUpdlsSau+x60HGuaOY1weEZf6bLarn4TI1Le+vxtI
#  #我本地公钥
##  publicStr: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt7Bct5InySYB0lyYeMkwJtCnF/q0eHNQ2S7UiquPb4VA8SHvu52RQO37RFViPpqmWp7qtke3QSN3VizZlT/oBKifWW4p1mDTK7uRVSNWOnBkEdWijpGXcD1m6dvCAKoiyPSM+C696viETdTrfx7RanLcfWoKbe/TQliIMzUkdVG2o/gXboI/fw83cG9vAhh7bjB3ONuAd/Dr1LYoJNkhroBWj40yDC49Sw8xVlZGBO8dtDrnm+1EEyvju1iIIQr1me8ucZs5uPFQheeGhlgjeV12s1eDIgp3kdAQN/mu9sGADeYkiDgTRMqCnAl2GFUOdaJsacjWwflxCSzOvwPeSQIDAQAB
#  privateStr: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC3sFy3kifJJgHSXJh4yTAm0KcX+rR4c1DZLtSKq49vhUDxIe+7nZFA7ftEVWI+mqZanuq2R7dBI3dWLNmVP+gEqJ9ZbinWYNMru5FVI1Y6cGQR1aKOkZdwPWbp28IAqiLI9Iz4Lr3q+IRN1Ot/HtFqctx9agpt79NCWIgzNSR1Ubaj+Bdugj9/Dzdwb28CGHtuMHc424B38OvUtigk2SGugFaPjTIMLj1LDzFWVkYE7x20Oueb7UQTK+O7WIghCvWZ7y5xmzm48VCF54aGWCN5XXazV4MiCneR0BA3+a72wYAN5iSIOBNEyoKcCXYYVQ51omxpyNbB+XEJLM6/A95JAgMBAAECggEAB9ehvbyhsjbLMR3+7Hk6cw3JdH6hodragss7C8iTUSGRSjo2r/3kK699YMd8cEvN+mR6hNm++yr2d1lm2LZnw3sngnvvkVWE7oMNi2L81XXi79f9HrUckBAvCqD1QW4CGi5GrnngZd3CWEhU3ZKFQlQxEbGrTFJG5Lg+6GDGc7O83Ybt3DpRdBu3xyKqstio3Bnyi726xgoFvyapVxMA80NqHygp3I0xQM7+xcqcpToA76BnzEwxrcJA46fVz6ARpeoZJZm2LEpLjwtUUPP4nPGWaFbpvJWBVrYtrIUsbdvU/k872MJrGU2HKM2Y3pKE9ftf3r9BsUmjhg4Qqz3EAQKBgQDivK++jRWQg76C5tjomhTtdHP3UyY5NNepfxWU/fXGI2pXGxI8JFvMXTmA2mcOw3smPsXJTzRVh5rsLFZcAI1cNkgr7St0Cq83sbes2qgWLUU/9hwD/44zcK2AbGCUA/l1XjshHdzBUjYN/Te90HSE6POhdrRus9Mk7H6L2wyWiQKBgQDPZWFKIrwhIImf9vlaiKT6DT3+61sErPnOB6MXee7iMqtfSdFrry6vU+tR/YvQP5BQm5RHaFqx+wMXlv5g4HSzAlN4Y5pt2KslVRyx77uesFqDyKPK8AzVZuXGQwL35zqyeR3qWMQAiyJpbYa5i9JOlOL1NWa19VMOG2/6rHsZwQKBgAUSZ3QBv+u9prNS6qM5XyJ4qmdr6rz426Ik/5yXmbnW7PgJ2PyTa6JEq5agTBHeeZC/crkwFmGbaPHDhCMGuCLJ7A9ffMtZudWrGgq50Wy9koD9xl6ohsvLx3XJ9tcYx6nCc9wnyNpiNmdVtLuAQDsA4wJHn32idCStTZ9fRQbRAoGBAImA4scfTnH8O6LmQR6oOBFdVDw+WMM52AmkgJohCqPICl0L+SAXLrGpxvw+SUNR0WHQNLg/VNpp3Pv37UHXrye8JBFOoGWx4I7I5lSG6HLm46w6C3aSP0ABj9gqN76a/Qy1RoNIRPNDTBZwOoGsVUwqZGtE84syUtWolU6yqo9BAoGAAedNwM57PufFJHSA11rFM1nIOyTeG83bQInAjacLsdQDe12kFZeC0jTZKC/8R6S+BpsDGO2NLUhOJ5QstID7qX+ba+bqkcUzVTzrJJM2/U/wmeSIRKBvb//XGcMKy+A+pnlwV6ODQHzEE5h0BSRW1hgwWICYPGBBGIKGEsScz7c=
#  publicStr: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvw2IzvWHwCAWbVYbh/lOv1fUGR5AtHvwLQLZuyrY0GiNSovSuGbhsWQ/ImTqqn5UtSPHt+2ANGg6wibHOIYG87uqp2q/HLLgs5IYh39tHcyX+saRBAADpFShgeM+VZEfOKa97HvNfJskqIj0JsCjZsNRardxbvJtQpn6hMuo0l7w0cY9qvzjKn0kcslKFYafz9h24wQTyjJrd+G/VjPt/EDn3zbY8OzGv6nI8j0uQSRN5SkIzV09Yk2Ymzivb/gHNOr6qU9r2pGsjn+ObmL1avVXA4niL6yoqe5YQ0QOMJ5hIey3WBF9PRgBljz64hoyPB8r1mvz6uTmnCQlcOntOwIDAQAB

wallet:
  userAddress:
    url: http://************:8290/user/address
    key: ja88@uP660088#Meta

##meta 配置
#es:
#  version: 1.0
#  #我的私钥
#  privateStr: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDnFCS4VtrKk9fKMhivx044jBm/F9YCzcj0RTxJ0SN+RfxQhtfQVO0+Tg/9ZmuZZBpRCN827YFeSj355/AtgFdmeX/nX4+xjplK6e6/678n1wmlQGIKFRSgs+qDA++Atqtv7FEjpCphpj3+tWt3FsLcBhIrTL7L4pTu8OQyS+InqFj6burPzBdivRQ1DbmCXwGK9WmfiHr403Rd8bfOxVs/IHmLG20QjlP3xfE+IDWp7Wx90TqNBoTMf93wos+SmNRL+HUK+6QaMQuuOuDFfgePsiURT+FEyQE+AnMrB3Npv2UY9UysYeEgS7z1gI8ddxx9orXU3D8hsS/l6vpo8kgTAgMBAAECggEBAKhcB8j7FxvDjdR2nX/soXQXt2aAMmAGmORIUX2iz85fpU7yf+j9B4F+lK3WYoz23ymtYhx9/OS7CN68e0RbntI9sc/c0y/VqqBnQpk4ZSTcyt1IxGV+KHJRw4pDYsuPy+aW8iMap/pPihm7CppQzhZKohBEMTsRUdupTUnL+fFTl1mgu4+b+OxnAiVEJB9/yZTp2NMrJEYYHcnHWr48boemmoUOVvzZ998GxdwFLObO3U+lU9IWdHXwXY/+kCPsMA+l2w2u5Skxl77zz0VpvDGJTPV9Y/HERikGxCDPztF50nFY8VVB9bxFJhyIZPBZ0/Pd+0Dk1PxsSFrjN+M8hOkCgYEA9ZdORJEvG4I0C3N2AKZYUA7LYrquSumAoLvGBVzVbyHIfBkYq/6kiYIUBn7i+cUdWVP8tMlSZXADmcpSuLcfQbbwJTCLCQX+Le8wz0XpOiBAXyGz5gJlrdDyBE0u9GGWKD363DWaRizFHzZMHXeL4/RPzRcMLVfsYx7ZsonHyM8CgYEA8N9gdvoXkZi/NpguDuPSckKPL8eYcbrhQIi/DozbDqAUeTAEt1lKFSCnpSx0mIswI6Q6DxlcV940ZPWVK3gcYwA6ZkFr2z+nZrcv81mmSFv9oJAgdsSx5EtBe8CRTflNIeygNuy5D1cJ8N2rufwt3GykEaN+TGyQtcVkRUTf1X0CgYApe7tOva9VWDLuVDx0c6SrMTSNorbaFIMTo2QZd8rdkLlKrPqL4uFsELrNNhWk1vTUJ+mhp/fxBnC1Q4Whid0PvpTl00NI7Og4XhTCji6NEPGOoCTBD1qZd5fJvjfWM72nYoDDPZKnk9xgH03QFGdew9/O5ru51QYq4AUpsTg75QKBgD5eCDMmND4Np8zyAFL4qD/PfhWn/4/LDJHSFm4lCH8z8bkjNsVmiCkKSH4bEaGBwJgp6KKKNX/G2BggAly7/9WedICPhebCB40v92lyF/z6XKRbTRyTd2AeIDlztevTliYnh6BREOWo6rkEHHfOlO1S7/RlOcl9/sYh8wGAgNhdAoGACiWWO/NWwyKB6JgXq1E4TRwMbIzeTmzvfLVYikjHYyw/8gtFSiJRYdpeJ1cEwKvlMgAK6CpWGdnuVq6GuaDv0udESTLP5xycJJU7882seD2x7BebyttMsxzBtpEhicOWjEW8NTqGrlFS1xIBBLTMY2R4BMsCnMJMym6wMMpfMHo=
#  #我的公钥
#  publicStr: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5xQkuFbaypPXyjIYr8dOOIwZvxfWAs3I9EU8SdEjfkX8UIbX0FTtPk4P/WZrmWQaUQjfNu2BXko9+efwLYBXZnl/51+PsY6ZSunuv+u/J9cJpUBiChUUoLPqgwPvgLarb+xRI6QqYaY9/rVrdxbC3AYSK0y+y+KU7vDkMkviJ6hY+m7qz8wXYr0UNQ25gl8BivVpn4h6+NN0XfG3zsVbPyB5ixttEI5T98XxPiA1qe1sfdE6jQaEzH/d8KLPkpjUS/h1CvukGjELrjrgxX4Hj7IlEU/hRMkBPgJzKwdzab9lGPVMrGHhIEu89YCPHXccfaK11Nw/IbEv5er6aPJIEwIDAQAB

#vcc 配置
vcc:
  url: https://test-open.whalet.com/api
  version: 1.0
  partnerId: 202305061716298892600001
  privateStr: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC3sFy3kifJJgHSXJh4yTAm0KcX+rR4c1DZLtSKq49vhUDxIe+7nZFA7ftEVWI+mqZanuq2R7dBI3dWLNmVP+gEqJ9ZbinWYNMru5FVI1Y6cGQR1aKOkZdwPWbp28IAqiLI9Iz4Lr3q+IRN1Ot/HtFqctx9agpt79NCWIgzNSR1Ubaj+Bdugj9/Dzdwb28CGHtuMHc424B38OvUtigk2SGugFaPjTIMLj1LDzFWVkYE7x20Oueb7UQTK+O7WIghCvWZ7y5xmzm48VCF54aGWCN5XXazV4MiCneR0BA3+a72wYAN5iSIOBNEyoKcCXYYVQ51omxpyNbB+XEJLM6/A95JAgMBAAECggEAB9ehvbyhsjbLMR3+7Hk6cw3JdH6hodragss7C8iTUSGRSjo2r/3kK699YMd8cEvN+mR6hNm++yr2d1lm2LZnw3sngnvvkVWE7oMNi2L81XXi79f9HrUckBAvCqD1QW4CGi5GrnngZd3CWEhU3ZKFQlQxEbGrTFJG5Lg+6GDGc7O83Ybt3DpRdBu3xyKqstio3Bnyi726xgoFvyapVxMA80NqHygp3I0xQM7+xcqcpToA76BnzEwxrcJA46fVz6ARpeoZJZm2LEpLjwtUUPP4nPGWaFbpvJWBVrYtrIUsbdvU/k872MJrGU2HKM2Y3pKE9ftf3r9BsUmjhg4Qqz3EAQKBgQDivK++jRWQg76C5tjomhTtdHP3UyY5NNepfxWU/fXGI2pXGxI8JFvMXTmA2mcOw3smPsXJTzRVh5rsLFZcAI1cNkgr7St0Cq83sbes2qgWLUU/9hwD/44zcK2AbGCUA/l1XjshHdzBUjYN/Te90HSE6POhdrRus9Mk7H6L2wyWiQKBgQDPZWFKIrwhIImf9vlaiKT6DT3+61sErPnOB6MXee7iMqtfSdFrry6vU+tR/YvQP5BQm5RHaFqx+wMXlv5g4HSzAlN4Y5pt2KslVRyx77uesFqDyKPK8AzVZuXGQwL35zqyeR3qWMQAiyJpbYa5i9JOlOL1NWa19VMOG2/6rHsZwQKBgAUSZ3QBv+u9prNS6qM5XyJ4qmdr6rz426Ik/5yXmbnW7PgJ2PyTa6JEq5agTBHeeZC/crkwFmGbaPHDhCMGuCLJ7A9ffMtZudWrGgq50Wy9koD9xl6ohsvLx3XJ9tcYx6nCc9wnyNpiNmdVtLuAQDsA4wJHn32idCStTZ9fRQbRAoGBAImA4scfTnH8O6LmQR6oOBFdVDw+WMM52AmkgJohCqPICl0L+SAXLrGpxvw+SUNR0WHQNLg/VNpp3Pv37UHXrye8JBFOoGWx4I7I5lSG6HLm46w6C3aSP0ABj9gqN76a/Qy1RoNIRPNDTBZwOoGsVUwqZGtE84syUtWolU6yqo9BAoGAAedNwM57PufFJHSA11rFM1nIOyTeG83bQInAjacLsdQDe12kFZeC0jTZKC/8R6S+BpsDGO2NLUhOJ5QstID7qX+ba+bqkcUzVTzrJJM2/U/wmeSIRKBvb//XGcMKy+A+pnlwV6ODQHzEE5h0BSRW1hgwWICYPGBBGIKGEsScz7c=
  publicStr: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvw2IzvWHwCAWbVYbh/lOv1fUGR5AtHvwLQLZuyrY0GiNSovSuGbhsWQ/ImTqqn5UtSPHt+2ANGg6wibHOIYG87uqp2q/HLLgs5IYh39tHcyX+saRBAADpFShgeM+VZEfOKa97HvNfJskqIj0JsCjZsNRardxbvJtQpn6hMuo0l7w0cY9qvzjKn0kcslKFYafz9h24wQTyjJrd+G/VjPt/EDn3zbY8OzGv6nI8j0uQSRN5SkIzV09Yk2Ymzivb/gHNOr6qU9r2pGsjn+ObmL1avVXA4niL6yoqe5YQ0QOMJ5hIey3WBF9PRgBljz64hoyPB8r1mvz6uTmnCQlcOntOwIDAQAB

es:
  version: 1.0
  #我的私钥
  privateStr: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDnFCS4VtrKk9fKMhivx044jBm/F9YCzcj0RTxJ0SN+RfxQhtfQVO0+Tg/9ZmuZZBpRCN827YFeSj355/AtgFdmeX/nX4+xjplK6e6/678n1wmlQGIKFRSgs+qDA++Atqtv7FEjpCphpj3+tWt3FsLcBhIrTL7L4pTu8OQyS+InqFj6burPzBdivRQ1DbmCXwGK9WmfiHr403Rd8bfOxVs/IHmLG20QjlP3xfE+IDWp7Wx90TqNBoTMf93wos+SmNRL+HUK+6QaMQuuOuDFfgePsiURT+FEyQE+AnMrB3Npv2UY9UysYeEgS7z1gI8ddxx9orXU3D8hsS/l6vpo8kgTAgMBAAECggEBAKhcB8j7FxvDjdR2nX/soXQXt2aAMmAGmORIUX2iz85fpU7yf+j9B4F+lK3WYoz23ymtYhx9/OS7CN68e0RbntI9sc/c0y/VqqBnQpk4ZSTcyt1IxGV+KHJRw4pDYsuPy+aW8iMap/pPihm7CppQzhZKohBEMTsRUdupTUnL+fFTl1mgu4+b+OxnAiVEJB9/yZTp2NMrJEYYHcnHWr48boemmoUOVvzZ998GxdwFLObO3U+lU9IWdHXwXY/+kCPsMA+l2w2u5Skxl77zz0VpvDGJTPV9Y/HERikGxCDPztF50nFY8VVB9bxFJhyIZPBZ0/Pd+0Dk1PxsSFrjN+M8hOkCgYEA9ZdORJEvG4I0C3N2AKZYUA7LYrquSumAoLvGBVzVbyHIfBkYq/6kiYIUBn7i+cUdWVP8tMlSZXADmcpSuLcfQbbwJTCLCQX+Le8wz0XpOiBAXyGz5gJlrdDyBE0u9GGWKD363DWaRizFHzZMHXeL4/RPzRcMLVfsYx7ZsonHyM8CgYEA8N9gdvoXkZi/NpguDuPSckKPL8eYcbrhQIi/DozbDqAUeTAEt1lKFSCnpSx0mIswI6Q6DxlcV940ZPWVK3gcYwA6ZkFr2z+nZrcv81mmSFv9oJAgdsSx5EtBe8CRTflNIeygNuy5D1cJ8N2rufwt3GykEaN+TGyQtcVkRUTf1X0CgYApe7tOva9VWDLuVDx0c6SrMTSNorbaFIMTo2QZd8rdkLlKrPqL4uFsELrNNhWk1vTUJ+mhp/fxBnC1Q4Whid0PvpTl00NI7Og4XhTCji6NEPGOoCTBD1qZd5fJvjfWM72nYoDDPZKnk9xgH03QFGdew9/O5ru51QYq4AUpsTg75QKBgD5eCDMmND4Np8zyAFL4qD/PfhWn/4/LDJHSFm4lCH8z8bkjNsVmiCkKSH4bEaGBwJgp6KKKNX/G2BggAly7/9WedICPhebCB40v92lyF/z6XKRbTRyTd2AeIDlztevTliYnh6BREOWo6rkEHHfOlO1S7/RlOcl9/sYh8wGAgNhdAoGACiWWO/NWwyKB6JgXq1E4TRwMbIzeTmzvfLVYikjHYyw/8gtFSiJRYdpeJ1cEwKvlMgAK6CpWGdnuVq6GuaDv0udESTLP5xycJJU7882seD2x7BebyttMsxzBtpEhicOWjEW8NTqGrlFS1xIBBLTMY2R4BMsCnMJMym6wMMpfMHo=
  #我的公钥
  publicStr: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5xQkuFbaypPXyjIYr8dOOIwZvxfWAs3I9EU8SdEjfkX8UIbX0FTtPk4P/WZrmWQaUQjfNu2BXko9+efwLYBXZnl/51+PsY6ZSunuv+u/J9cJpUBiChUUoLPqgwPvgLarb+xRI6QqYaY9/rVrdxbC3AYSK0y+y+KU7vDkMkviJ6hY+m7qz8wXYr0UNQ25gl8BivVpn4h6+NN0XfG3zsVbPyB5ixttEI5T98XxPiA1qe1sfdE6jQaEzH/d8KLPkpjUS/h1CvukGjELrjrgxX4Hj7IlEU/hRMkBPgJzKwdzab9lGPVMrGHhIEu89YCPHXccfaK11Nw/IbEv5er6aPJIEwIDAQAB



# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 300000
  # 是否允许账户多终端登录
  soloLogin: false


# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

arcFace:
  # 应用ID
  appId: BuQo8C4eBakpMMhTEtDJp1xEKcDBrDGca2PtDW1oK9Tw
  # 应用密钥
  sdkKeyForWindows: A8CdeNmhc1LjfYm6T1X8aJTMA5N4i9T2rBNzGq5xpLoj
  sdkKeyForLinux: A8CdeNmhc1LjfYm6T1X8aJTM1xnwytPEH8A6fLhXP2J5
  isAbsolutePath: false
  ## SDK路径
  sdkPath: /sdk/


mail:
  username: <EMAIL>
  password: TGGLTiJnN2KrrcZ
  authorizationCode: FFTPBUWAVPNIMZDC
  serverHost: smtp.163.com
  serverPort: 465
  reconnectSecond: 600


#moonbank 配置
moonbank:
  gateway: https://test.asinx.io/api-web
  appId: app_35940
  appSecret: b635dd5c87f7bf73387929203321b1e1
  notifyTimeout: 20000
  notifyConnectTimeout: 20000
  useProxy: false
  proxyAddress:
  proxyPort:

#kun 配置 + 配置文件
kun:
  environment: test
  ip: *************
