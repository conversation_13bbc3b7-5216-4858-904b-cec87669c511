## Error message
not.null=* This field is required.
user.jcaptcha.error=Invalid verification code.
user.jcaptcha.email.error=Email verification code error
user.jcaptcha.email.expire=Email verification code has expired
user.jcaptcha.expire=Verification code has expired.
google.jcaptcha.error1=Google verification code format error
google.jcaptcha.error2=Google verification code verification error
google.jcaptcha.error3=For the security of your funds, please set up Google Authenticator first.
user.not.exists=User does not exist or password is incorrect.
user.type.not.match=User type does not match
user.invitation.invalid=Invalid invitation code.
user.password.not.match=User does not exist or password is incorrect.
user.password.retry.limit.count=Incorrect password entered {0} times.
user.password.retry.limit.exceed=Incorrect password entered {0} times. Account locked for 10 minutes.
user.password.delete=Sorry, your account has been deleted.
user.blocked=User is blocked. Please contact the administrator.
role.blocked=Role is blocked. Please contact the administrator.
user.logout.success=Logout successful.
auth.error=Timed out, please log in again.
length.not.valid=Length must be between {min} and {max} characters.
user.username.not.valid=* Must consist of 2 to 20 Chinese characters, letters, numbers, or underscores, and must not start with a number.
user.password.not.valid=* Must be 5-50 characters.
user.email.not.valid=Invalid email format.
user.email.not.valid2=The email length should not exceed 50 characters.
user.mobile.phone.number.not.valid=Invalid phone number format.
user.login.success=Login successful.
user.notfound=Please login again.
user.login.in.other.device=Your account has been logged in from another location!
user.forcelogout=Administrator has forced logout. Please login again.
user.unknown.error=Unknown error. Please login again.
user.set.password.failed=Failed to set password
user.set.password.error=Password must be 6-24 characters long, with at least one uppercase letter, one lowercase letter, and one number.
user.password.error=Login Password Error
user.password.not.same=New and old passwords cannot be the same.
user.phone.error=Please set your phone number first
# Google Authenticator is not enabled for authentication
google.authenticator.disabled=Google Authenticator is not enabled for authentication

## File upload message
upload.exceed.maxSize=Uploaded file size exceeds the limit! <br/> Maximum allowed file size is: {0}MB!
upload.filename.exceed.length=Uploaded file name exceeds {0} characters.

## Permissions
no.permission=You do not have permission to access the data. Please contact the administrator to add the permission [{0}].
no.create.permission=You do not have permission to create data. Please contact the administrator to add the permission [{0}].
no.update.permission=You do not have permission to modify data. Please contact the administrator to add the permission [{0}].
no.delete.permission=You do not have permission to delete data. Please contact the administrator to add the permission [{0}].
no.export.permission=You do not have permission to export data. Please contact the administrator to add the permission [{0}].
no.view.permission=You do not have permission to view data. Please contact the administrator to add the permission [{0}].
ip.address.blocked=IP address is blocked

## node
node.application.success=Node Application Successful
node.application.duplicate=Please do not submit duplicate applications
node.application.support=Only supports USD and USDT
node.application.insufficient.balance=Insufficient balance
node.application.level.invalid=Invalid node level
node.application.level.mismatch=The application level cannot be less than {0}
node.transfer.success=Transfer Successful
node.transfer.failed=Transfer Failed
node.auto.upgrade.success=Node upgrade successful
node.auto.upgrade.failed=Node upgrade failed
node.user.not.exist=User does not exist
node.transfer.insufficient.num=Transfer amount exceeds the transfer limit of the receiving user!
node.transfer.level=Transfers are only allowed to users with a lower level than oneself!
node.user.level.not.match= User node level is not Lv04
node.user.info.exists=User node configuration already exists.
node.user.info.not.exists=User node configuration not exists.
node.user.info.card.open.rebate=The range of commission for card opening fee needs to be within {0}~{1}.
node.user.info.card.recharge=The range of commission for recharge needs to be within {0}~{1}.

## wallet
wallet.account.insufficient.balance=USD Insufficient balance
wallet.account.insufficient.coinbalance={0} balance is insufficient
wallet.account.insufficient.balance2=USDT Insufficient balance
wallet.activitiCode.insufficient.num=Insufficient Activation Coins
wallet.activitiCode.silver.insufficient.num=Insufficient Silver Coins
wallet.activitiCode.golden.insufficient.num=Insufficient Gold Coins
wallet.activitiCode.goldenBlack.insufficient.num=Insufficient Black Gold Card activation code
wallet.activitiCode.white.insufficient.num=Insufficient White Coins
wallet.deduct.funds.failed=Failed to deduct funds, insufficient balance in USD.
wallet.trade.pwd.error=The KazePay PIN is entered incorrectly, please re-enter
wallet.trade.pwd.error2=The KazePay PIN is incorrect. The number of remaining entries is: {0}
wallet.trade.pwd.error3=The source address of funds has been checked to be high risk. The account has been frozen. Please contact the administrator.
wallet.trade.pwd.error.tip=If the transaction password is entered incorrectly five times, fund transactions will be frozen for 24 hours. Contact customer service to unfreeze during this period.
wallet.trade.pwd.noset=Transaction password not set
wallet.trade.pwd.failed=Failed to reset trading password
wallet.coin.insufficient.balance=Insufficient balance in {0} wallet!
wallet.coin.not.exist=You do not have a {0} asset account yet, please recharge now!
wallet.coin.exchange.type.not.exist=Exchange type does not exist
wallet.error.trade.not.auth=Please enter transaction password
wallet.error.face.not.auth=Please use facial recognition
wallet.error.not.auth=Please enter transaction password or use facial recognition
wallet.email.can.not.yourself=The recipient of the transfer cannot be yourself.
wallet.transfer.success=Transfer successful.
wallet.transfer.failed=Transfer failed.
wallet.trade.pwd.not.same=New and old passwords cannot be the same.
wallet.exchange.number=The exchange value cannot be less than 10.
wallet.withdrawal.number=The withdrawal value cannot be less than 10.
wallet.exchange.activecard.number=1)The value of the first recharge card cannot be less than {0}.

## wello平台
exchange.wello.order.limit.error=User order limit reached.

## alixpay平台
exchange.alixpay.order.content=QR Pay
exchange.alixpay.bank.nonsupport=Sorry, {0} is not currently supported.
exchange.alixpay.bank.status.abnormal=An issue was detected with the merchant's bank account. Please try again later.
exchange.alixpay.minimum.payment=Sorry, the minimum payment limit is {0} {1}.
exchange.alixpay.maximum.payment=Sorry, the maximum payment limit is {0} {1}.
exchange.alixpay.payment.unavailable=Payment is temporarily unavailable. Please try again later or contact customer support.
exchange.alixpay.not.sufficient.funds=Your {0} balance is insufficient.
exchange.alixpay.payment_failure=Payment failed, please try again later.
exchange.alixpay.currency.nonsupport=Sorry, {0} is not currently supported.
exchange.alixpay.single.limit.error=Sorry, the single payment amount cannot exceed {0} {1}.
exchange.alixpay.day.limit.error=Sorry, the daily limit is {0} {1}, your remaining available limit is {1} {2}.

## 通用
common.system.error=The system is busy. Please try again later.

## 用户信息
registration.success=Registration Successful
bindemail.success=Email binding successful
initial.login.password=The initial login password is
kyc.already=You have already completed the KYC verification
kyc.idcard.invalid=Invalid ID card number
kyc.idcard.issueDate.not.empty=Issue date of the ID card cannot be empty
kyc.idcard.expireDate.not.empty=Expiration date of the ID card cannot be empty
faceFeature.already=Facial information has been collected, no need to repeat input.
faceFeature.not.exists=Face feature not collected

## 执行结果
operation.success=Operation successful
operation.failed=Operation failed

## 文件
read.success=Read successful
read.failed=Read failed
read.isEmpty=Empty file

## download
download.success=Download successful
download.failed=Download failed

## 卡
card.nofound=The card number does not exist.
card.status.execption=Card status is abnormal and cannot be recharged
card.userId.wrong=Not the cardholder's card
card.balance.too.low=The balance in the card must be kept at least 
card.deposit.amount.too.low=The recharge amount cannot be less than 
card.deposit.amount.too.more=The recharge amount cannot be greater than 
card.withdraw.amount.too.low=The withdraw amount cannot be less than 
card.balance.too.shao=Please recharge at least {0}USD to activate the card .
card.notUnfreeze.cards=Please contact the administrator to unfreeze the card
card.not.apply=The system is being fully upgraded, and card issuance is suspended. The specific recovery time will be announced through official announcements.
card.lock.cards=The system is being fully upgraded and card locking is suspended.
card.not.recharge=The system is being fully upgraded and recharges have been suspended. The specific recovery time will be announced through official announcements.

####链上交易结果######
trade.transactionid.error=The transaction number information is incorrect. Please verify and try again!
trade.transaction.error=This transaction was unsuccessful.
sys.busy=The  system is busy, please try again later...
sys.recharge.busy=The recharge system is busy, please try again later...

### mail
mail.send.content.template=【KazePay】Verification code: {0}. Please complete the operation within {1} minutes. If it wasn't you, please ignore this message. 
mail.send.subject.resetPasswd=Reset login Password
mail.send.subject.resetTradePasswd=Reset Trading Password 
mail.send.subject.login=Login Verification Code 
mail.send.subject.register=Account Registration 
mail.send.reset.googleKey=Reset Google Authenticator
mail.send.subject.bind=Bind Email
mail.not.exists=Email does not exist 
mail.already.exists=Email already exists 
mail.send.success=Sent successfully 
mail.send.failed=Sending failed
mail.bind.error=This email is not a registered binding email for this user!

### VCC
vcc.sys.error=Current network request is poor, please try again later!

### 数据字典
sys_user_sex_0= Man
sys_user_sex_1=Woman
sys_user_sex_2=Unknown
sys_show_hide_0=Show
sys_show_hide_1=Hide
sys_normal_disable_0=Normal
sys_normal_disable_1=Disabled
sys_job_status_0=Normal
sys_job_status_1=Paused
sys_job_group_DEFAULT=Default
sys_job_group_SYSTEM=System
sys_yes_no_Y=Yes
sys_yes_no_N=No
sys_yes_no_2_1=Yes
sys_yes_no_2_0=No
sys_notice_type_1=Notification
sys_notice_type_2=Announcement
sys_notice_status_0=Normal
sys_notice_status_1=Closed
sys_oper_type_1=Add
sys_oper_type_2=Modify
sys_oper_type_3=Delete
sys_oper_type_4=Authorize
sys_oper_type_5=Export
sys_oper_type_6=Import
sys_oper_type_7=Force Logout
sys_oper_type_8=Generate Code
sys_oper_type_9=Clear Data
sys_common_status_0=Success
sys_common_status_1=Failure
credit_card_type_01=5319-USA Master Consumer Card
credit_card_type_02=4040-USA Visa Consumer Card
credit_card_type_03=Virtual UnionPay Card
credit_card_type_88=Swiss NFT virtual VISA card
credit_card_type_80=2229-HK Master Apple Pay
credit_card_type_81=5532-USA Master Subscription Card
credit_card_type_82=4896-USA Visa Subscription Card
credit_card_type_83=52XX-HK Master Apple Pay
credit_card_type_11=French MasterCard
credit_card_type_12=Physical VISA Card
credit_card_type_13=Physical UnionPay Card
credit_card_level_01=Silver Coin
credit_card_level_02=Gold Coin
credit_card_level_03=Black Gold Card
credit_card_level_04=White Coin
vcc_card_bin_493193=Visa
vcc_card_bin_222929=MasterCard
vcc_card_status_processing=In Progress
vcc_card_status_open_card_fail=Rejected
vcc_card_status_used=In Use
vcc_card_status_remove_processing=Under Cancellation
vcc_card_status_cancel=Cancelled
vcc_card_status_suspending=Locked
vcc_card_status_un_suspending=Unlocking
vcc_card_status_suspend=Locked
vcc_card_status_TBA=Pending activation
vcc_card_status_ACTIVE=Normal
vcc_card_status_CLOSE=CLOSE
vcc_card_status_INACTIVE=Freeze
vcc_card_status_AUDITING=Card opening
vcc_card_status_AUDIT_NOT_PASS=Card opening failed
vcc_card_status_EXPIRED=Expired

vcc_trade_type_card_deposit=Card recharge
vcc_trade_type_card_withdraw=Card withdrawal
vcc_trade_type_pay=Expenditure
vcc_trade_type_pay_refund=Refund of expenditure
vcc_trade_type_remove_refund=Card deletion refund
vcc_trade_type_pay_revoke=Transaction cancellation fee
vcc_trade_type_close_pay=Close transfer
vcc_trade_type_pay_auth=Transaction authorization fee
vcc_trade_type_balance_pay=Balance transfer
vcc_trade_type_pay_reversal=Expenditure reversal
vcc_trade_type_pay_refund_reversal=Refund reversal of expenditure
vcc_trade_type_query_auth=Authorization inquiry
vcc_trade_type_AUTH=Authorized consumption
vcc_trade_type_AUTH_QUERY=Authorized Query
vcc_trade_type_REVERSAL=reversal
vcc_trade_type_REFUND=refund
vcc_trade_type_FEE=fee
vcc_trade_type_TRADE_PROCESS_FEE=trade process fee
vcc_trade_type_TRADE_CROSS_BOARD_FEE=trade cross board fee
vcc_trade_type_TRADE_REFUND_FEE=trade refund fee
vcc_trade_type_FEE_REVERSAL=fee reversal
vcc_trade_type_ORIGINAL_CREDIT=otc refund
vcc_trade_type_ORIGINAL_CREDIT_REVERSAL=otc refund reversal
vcc_trade_type_SETTLEMENT_DEBIT=Differential settlement	
vcc_trade_type_charge=charge
vcc_trade_type_refund=refund
vcc_trade_type_topup=topup
vcc_trade_type_withdraw=withdraw
vcc_trade_type_transfer=transfer
vcc_trade_type_cashback=cashback
vcc_trade_type_interest=interest
vcc_trade_type_fee=fee
vcc_trade_type_other=other types
vcc_trade_type_CLEARING=CLEARING
vcc_trade_type_AUTHORIZATION=AUTHORIZATION
vcc_trade_type_AUTH_REVERSAL=AUTH REVERSAL
vcc_trade_type_AUTH_FAILURE=AUTH FAILURE
vcc_trade_type_SETTLED=Settlement
vcc_trade_type_VALIDATION=Authorized
vcc_trade_type_COMPULSORY_LIQUIDATION=Compulsory payment request
vcc_trade_type_PAYMENT_REQUEST_AFTER_REVERSAL=Payment request after reversal
vcc_trade_type_SETTLED_SETTLEMENT_DEBIT=Decimal settlement

coin_txn_type_d1010=Deposit coins
coin_txn_type_c1010=Withdraw coins
coin_txn_type_t1010=Transfer in
coin_txn_type_t1020=Transfer out
coin_txn_type_e1010=Cash in
coin_txn_type_e1020=Cash out
coin_txn_type_c4020=Card opening fee
coin_txn_type_c2040=Card recharge
coin_txn_type_c2021=Card recharge fee
coin_txn_type_c5020=Logistics fee
coin_txn_type_c4040=Node deposit expenditure
coin_txn_type_b1010=Buy coins
coin_txn_type_s1010=QR Pay

usd_txn_type_d1010=Exchange out
usd_txn_type_c1020=Exchange out
usd_txn_type_d1030=Exchange in
usd_txn_type_c1040=Exchange in
usd_txn_type_d1011=Exchange fee
usd_txn_type_c1021=Exchange fee
usd_txn_type_d1012=Exchange fee
usd_txn_type_c1022=Exchange fee
usd_txn_type_d2010=Card withdrawal
usd_txn_type_c2020=Card withdrawal
usd_txn_type_d2030=Card recharge
usd_txn_type_c2040=Card recharge
usd_txn_type_d2011=Card recharge fee
usd_txn_type_c2021=Card recharge fee
usd_txn_type_d2012=Card withdrawal fee
usd_txn_type_d2022=Card withdrawal fee
usd_txn_type_d2013=Card transaction authorization fee 
usd_txn_type_c2023=Card transaction authorization fee 
usd_txn_type_d2014=Card consumption less than 30U handling fee 
usd_txn_type_c2024=Card consumption less than 30U handling fee 
usd_txn_type_d3010=USD dollar transfer
usd_txn_type_c3020=USD transfer out
usd_txn_type_d4010=Card activation fee
usd_txn_type_c4020=Card activation fee
usd_txn_type_d4030=Node pre-deposit
usd_txn_type_c4040=Node pre-deposit
usd_txn_type_d9010=Card activation commission
usd_txn_type_c9020=Card activation commission
usd_txn_type_d9030=Card recharge commission
usd_txn_type_c9040=Card recharge commission
usd_txn_type_d9050=Node referral commission
usd_txn_type_c9060=Node referral commission
usd_txn_type_d5030 = logistics fee income
usd_txn_type_c5020 = logistics fee expenditure
usd_txn_type_c8001=other
usd_txn_type_s8001=charge
usd_txn_type_s8002=topup
usd_txn_type_s8003=withdraw
usd_txn_type_s8004=transfer
usd_txn_type_s8005=cashback
usd_txn_type_s8006=interest

code_txn_type_10=Apply for deposit
code_txn_type_20=Card Activation Coin Usage
code_txn_type_00=Upgrade delivery
code_txn_type_30=Card Activation Coin Transfer In
code_txn_type_31=Card Activation Coin Transfer Out
code_txn_type_40=Card Activation Coin Exchange In
code_txn_type_41=Card Activation Coin Exchange Out
code_txn_type_50=System gift
code_txn_type_51=System revoked
node_type_00=V0
node_type_01=V1
node_type_02=V2
node_type_03=V3
node_type_04=V4
node_type_10=Global Partners
kyc_auth_type_0=Personal account
kyc_auth_type_1=Business account
kyc_cert_type_0=ID card
kyc_cert_type_1=Passport
userType_00=System user
userType_01=APP user
approval_status_0=In review
approval_status_1=Approved
approval_status_2=Not approved
file_type_01=ID card front
file_type_02=ID card back
file_type_03=Passport
exchange_type_1=USD to coins
exchange_type_0=Coins to USD
file_type_04=Business license
kyc_cert_type_3=Not verified
vcc_trade_status_processing=Processing
vcc_trade_status_success=Success
vcc_trade_status_fail=Failure
txn_status_0=FAIL
txn_status_1=SUCCESS
txn_status_2=PENDING
coin_txn_status_2=PENDING
coin_txn_status_1=SUCCESS
coin_txn_status_0=FAIL
usd_txn_type_c2160=Expenditure
usd_txn_type_d2150=Expenditure refund
usd_txn_type_d2170=Card deletion refund
usd_txn_type_c2161=Transaction cancellation fee
usd_txn_type_c2181=Close transfer out
usd_txn_type_c2162=Transaction authorization fee
usd_txn_type_c2180=Balance transfer out
usd_txn_type_d2160=Expenditure reversal
usd_txn_type_c2150=Expenditure refund reversal
usd_txn_type_z2100=Authorization inquiry
commission_type_1=Card activation commission
commission_type_2=Recharge commission
commission_type_3=Node upgrade
valid_type_Y=Valid
valid_type_N=Invalid
trade_auth_type_0=Trade Password
trade_auth_type_1=Facial Recognition
usd_txn_type_c2022=Card withdrawal fee
anpt_direct_1=Add
anpt_direct_2=Deduction
anpt_type_recharge=Recharge card
anpt_type_share_card=Share card application
anpt_type_share_v1=Share upgrade V1
anpt_type_share_v2=Share upgrade V2
anpt_type_share_v3=Share upgrade V3
anpt_type_share_v4=Share upgrade V4

please.fill.info=Please fill in the user details first
card.isActive= The card has been activated
favorite.address.exists=This address has been favorited
favorite.address.notexists=This address is not in the favorites list
asset.not.null=The type of asset being sent cannot be empty
user.card.has.moder=You cannot hold more than 5 cards of this type.


coupon.error1=Coupon does not exist
coupon.error2=Coupon is owned by another user
coupon.error3=Usage type cannot be empty
coupon.error4=Coupon code cannot be empty
coupon.error5=Card opening type cannot be empty
coupon.error6=Amount cannot be empty
coupon.error7=Currency cannot be empty
coupon.error8=Card cannot be empty
coupon.error9=The coupon has expired
coupon.error10=Discount currency does not match
coupon.error11=The coupon has not yet expired
coupon.error12=Coupons cannot be used when using activation coins
coupon.error15=The actual payment amount must be greater than 0
coupon.error16=This coupon is not applicable to this card level
coupon.error17=This coupon is not applicable to this card
coupon.error18=This coupon is not available for redemption. Please enter the coupon code directly to use it.
coupon.error19=This coupon has exceeded the number of times it can be used.

STATUS_SUCCESS=SUCCESS
STATUS_FAIL=FAIL
STATUS_DECLINED=FAIL
STATUS_PENDING=PENDING
STATUS_OTHER=OTHER
STATUS_CANCEL=CANCEL
STATUS_DELETE=DELETE
STATUS_CONFIRM=CONFIRM
STATUS_APPROVED=SUCCESS

kyc.img.type=The file format can only be one of jpg, jpeg, and png
id.front.image.not.null=The front photo of the ID card cannot be empty
id.holding.image.not.null=The photo holding the ID card cannot be empty
id.back.image.not.null=The photo on the back of the ID cannot be empty
card.status.noAactive=Card status abnormal
card.setpin.error=Card cannot set PIN
card.no.error=Card number error
card.no.exist=Card number does not exist
have.physical.num.error=A user can only have up to 5 physical cards
user.verify.code.error=Please go to the latest version page to perform the operation ({0})
user.verify.code.error2=Graphic verification code error
card.not.recharge.oldkun=This card has stopped recharging.
card.not.support=This card does not support
card.not.exchange=Not open yet
card.not.transfer=No amount to transfer
user.error=Account abnormality, please contact customer service for processing
api.version.upgrade=This API has been upgraded, please update to the latest version
card.hoder.not.set=Please set the cardholder information first
card.use.code.error=This card does not support activation coins to open the card
card.open.recharge.error=Wrong amount of recharge when opening the card
card.fx.user.not=Please set the cardholder information first
