package com.meta.web.app.controller;

import com.meta.common.annotation.ApiEncrypt;
import com.meta.common.annotation.RepeatSubmit;
import com.meta.common.core.controller.BaseController;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.enums.SecurityAuthType;
import com.meta.common.enums.app.AlgorithmType;
import com.meta.common.enums.app.CardLevel;
import com.meta.common.enums.app.NodeLevel;
import com.meta.common.enums.app.TxnType;
import com.meta.common.utils.DateUtils;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.request.IInsert;
import com.meta.common.utils.request.IUpdate;
import com.meta.framework.web.controller.card.BaseCardCommon;
import com.meta.framework.web.service.app.PreSecurityAuth;
import com.meta.system.domain.Wallet;
import com.meta.system.domain.app.NodeConfig;
import com.meta.system.domain.app.UserCommission;
import com.meta.system.domain.app.node.NodeApplication;
import com.meta.system.domain.app.node.UserNodeInfo;
import com.meta.system.domain.log.TxnDtlCode;
import com.meta.system.dto.TxnDto;
import com.meta.system.dto.VccReqDto;
import com.meta.system.dto.node.CodeExchangeDto;
import com.meta.system.dto.node.NodeApplicationDto;
import com.meta.system.dto.node.NodeTransferDto;
import com.meta.system.service.*;
import com.meta.system.vo.*;
import com.meta.web.vo.TransferVo;
import com.meta.common.exception.ServiceException;
import com.meta.web.dto.MultiParamWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.List;

/**
 * 节点
 */
@RestController
@RequestMapping("/app/node")
public class NodeController extends BaseController {

    @Resource
    private NodeService nodeService;

    @Resource
    private ISysUserService userService;


    @Resource
    private WalletService walletService;

    @Resource
    private UserCommissionService userCommissionService;

    @Autowired
    private TxnService txnService;

    @Autowired
    private UserNodeInfoService nodeInfoService;

    @Autowired
    private BaseCardCommon baseCardCommon;

    @Autowired
    private ExchangeRateService exchangeRateService;

    /**
     * 节点申请分页查询
     */
    @GetMapping("/list")
    public TableDataInfo list(NodeApplicationDto apply) {
        return getDataTable(nodeService.list(apply));
    }

    /**
     * 节点申请
     *
     * @param apply 节点申请请求对象
     * @return AjaxResult
     */
    @PostMapping("/apply")
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    @PreSecurityAuth(type = SecurityAuthType.ALL)
    @ApiEncrypt
    public AjaxResult applyNode(@RequestBody @Validated(IInsert.class) NodeApplication apply) throws Exception {
        AjaxResult ajaxResult = nodeService.apply(apply);
        if (ajaxResult != null) return ajaxResult;
        Wallet wallet = walletService.findByUserId(SecurityUtils.getLoginUser().getUserId());
        if (wallet.getReferrerId() != null) {
            //积分处理
            if (NodeLevel.FIRST.equals(NodeLevel.getByName(apply.getNodeLevel()))) {
                baseCardCommon.dealApnt(wallet.getReferrerId(), "share_v1", apply.getId().toString(), null, null);
            } else if (NodeLevel.SECOND.equals(NodeLevel.getByName(apply.getNodeLevel()))) {
                baseCardCommon.dealApnt(wallet.getReferrerId(), "share_v2", apply.getId().toString(), null, null);
            } else if (NodeLevel.THIRD.equals(NodeLevel.getByName(apply.getNodeLevel()))) {
                baseCardCommon.dealApnt(wallet.getReferrerId(), "share_v3", apply.getId().toString(), null, null);
            } else if (NodeLevel.FOURTH.equals(NodeLevel.getByName(apply.getNodeLevel()))) {
                baseCardCommon.dealApnt(wallet.getReferrerId(), "share_v4", apply.getId().toString(), null, null);
            }
        }

        return AjaxResult.success(MessageUtils.message("node.application.success"));// 暂时不需要审批 返回申请成功
    }

    /**
     * 节点划转
     *
     * @param transfer 节点划转请求对象
     * @return AjaxResult
     */
    @PostMapping("/transfer")
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    @PreSecurityAuth(type = SecurityAuthType.ALL)
    @ApiEncrypt
    public AjaxResult transferNode(@Validated @RequestBody NodeTransferDto transfer) {
        SysUser user = userService.selectUserByUserName(transfer.getUsername());
        if (user == null) {
            return AjaxResult.error(MessageUtils.message("node.user.not.exist"));
        }
        Wallet wallet = walletService.findByUserId(user.getUserId());

        // 查询可划转数量
//        NodeConfig nodeConfig = nodeService.findNodeConfigByNodeLevel(wallet.getNodeLevel());
//        if (nodeConfig == null){
//            return AjaxResult.success(MessageUtils.message("node.transfer.failed"));
//        }

        //  划转方
        Wallet currentUser = walletService.findByUserId(SecurityUtils.getLoginUser().getUserId());
        String currentNodeLevel = currentUser.getNodeLevel();

        //判断数量是否为负数
        if (transfer.getActiveCodeNum() <= 0) {
            return AjaxResult.success(MessageUtils.message("node.transfer.failed"));
        }


        //可划转最大数量
        Integer totalRightScardNum = 0;
        if (currentNodeLevel.equals("00")) {
            NodeConfig currentNodeConfig = nodeService.findNodeConfigByNodeLevel(currentNodeLevel);
            totalRightScardNum = currentNodeConfig.getTotalRightScardNum();
        } else {
            NodeConfig previousNode = nodeService.findPreviousNode(currentUser.getNodeLevel());
            totalRightScardNum = previousNode.getTotalRightScardNum();
        }
        Integer userNum = wallet.getTotalExchangeSilverActiveCodeNum() + wallet.getSilverActiveCodeNum();
        logger.info("划转接收方持有银卡数量：" + userNum);
        if (userNum + transfer.getTotalExchangeSilverActiveCodeNum() > totalRightScardNum) {
            // 收款账户可划转数量不足
            return AjaxResult.error(MessageUtils.message("node.transfer.insufficient.num"));
        }
        //判断是否是个人用户
        if (wallet.getNodeLevel().equals("00") && currentUser.getNodeLevel().equals("00")) {
            logger.info("个人用户向个人用户划转");
        } else {
            // 判断转入节点等级是否小于当前用户的节点等级
            if (!NodeLevel.isHigher(currentUser.getNodeLevel(), wallet.getNodeLevel())) {
                return AjaxResult.error(MessageUtils.message("node.transfer.level"));
            }
        }


        // 判断剩余的激活码是否足够
        if (!walletService.checkCardBalance(transfer)) {
            return AjaxResult.error(MessageUtils.message("wallet.activitiCode.insufficient.num"));
        }
        //扣款
        VccReqDto source = new VccReqDto();
        source
                .setCodeFromUserId(SecurityUtils.getLoginUser().getUserId()) // 扣款id
                .setCodeToUserId(user.getUserId()) // 收款id
                .setCardLevel(transfer.getCardLevel()) // 激活码的类型
                .setTxnTypeCode(TxnType.TRANSFER_EXPENDITURE) // 转让支出
                .setActiveCodeNum(transfer.getActiveCodeNum()) //需要处理的激活码数量
                .setActiveCodeAlgorithmType(AlgorithmType.SUB)
                .setUserId(SecurityUtils.getLoginUser().getUserId()) // 用户id
                .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                .setWalletAmount(BigDecimal.ZERO); // 预存金额

        // 收款
        VccReqDto target = new VccReqDto();
        target
                .setCodeFromUserId(SecurityUtils.getLoginUser().getUserId()) // 扣款id
                .setCodeToUserId(user.getUserId()) // 收款id
                .setCardLevel(transfer.getCardLevel()) // 激活码的类型
                .setTxnTypeCode(TxnType.TRANSFER_RECHARGE) // 转让入账
                .setActiveCodeNum(transfer.getActiveCodeNum()) //需要处理的激活码数量
                .setActiveCodeAlgorithmType(AlgorithmType.ADD)
                .setUserId(user.getUserId()) // 用户id
                .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                .setWalletAmount(BigDecimal.ZERO); // 预存金额
        AjaxResult ajaxResult = walletService.transferCode(source, target);
        TxnDtlCode dtlCode = (TxnDtlCode) ajaxResult.get("codeDetail");
        if (ajaxResult.isSuccess()) {
            return AjaxResult.success(dtlCode);
        }
        return AjaxResult.success(MessageUtils.message("node.transfer.failed"));
    }

    /**
     * 划转记录查询
     */
    @GetMapping("/transferRecord")
    public TableDataInfo transferRecord(String startDate, String endDate) {
        TxnDto txnDto = new TxnDto();
        txnDto.setStartDate(startDate);
        txnDto.setEndDate(endDate);
        txnDto.setUserId(SecurityUtils.getLoginUser().getUserId());
        txnDto.setTxnTypes(Arrays.asList(TxnType.CARD_UPDATE, TxnType.TRANSFER_RECHARGE, TxnType.TRANSFER_EXPENDITURE, TxnType.SYSTEM_SEND, TxnType.SYSTEM_REVOKED));
        return getDataTable(txnService.pageForCode(txnDto));
    }

    /**
     * 划转记录详情
     */
    @GetMapping("/transferDetail")
    public AjaxResult transferDetail(@RequestParam("id") Long id) {
        return AjaxResult.success(txnService.detailByCode(id, SecurityUtils.getLoginUser().getUserId()));
    }


    /**
     * 激活码兑换
     *
     * @param exchange 兑换请求对象
     * @return AjaxResult
     */
    @PostMapping("/exchange")
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    @PreSecurityAuth(type = SecurityAuthType.ALL)
    @ApiEncrypt
    public AjaxResult exchangeCards(@Validated @RequestBody CodeExchangeDto exchange) {
        exchange.setUserId(SecurityUtils.getLoginUser().getUserId());
        AjaxResult result = walletService.exchangeCards(exchange);// 兑换
        if (!result.get("code").equals(200)) {
            return result;
        }
        return AjaxResult.success("兑换成功");
    }

    /**
     * 兑换记录查询
     */
    @GetMapping("/exchangeRecord")
    public TableDataInfo exchangeRecord(String startDate, String endDate) {
        TxnDto txnDto = new TxnDto();
        txnDto.setStartDate(startDate);
        txnDto.setEndDate(endDate);
        txnDto.setUserId(SecurityUtils.getLoginUser().getUserId());
        txnDto.setTxnTypes(Arrays.asList(TxnType.EXCHANGE_RECHARGE, TxnType.EXCHANGE_EXPENDITURE));
        return getDataTable(txnService.pageForCode(txnDto));
    }

    /**
     * 兑换记录详情
     */
    @GetMapping("/exchangeDetail")
    public AjaxResult exchangeDetail(@RequestParam("id") Long id) {
        return AjaxResult.success(txnService.detailByCode(id, SecurityUtils.getLoginUser().getUserId()));
    }

    /**
     * 节点中心(节点详情)
     */
    @GetMapping("/center")
    public AjaxResult center() {
        NodeCenter nodeCenter = nodeService.findNodeCenter(SecurityUtils.getLoginUser().getUserId());
        return AjaxResult.success(nodeCenter);
    }

    /**
     * 获取卡参数配置表(全部)
     */
    @GetMapping("/getNodeConfig")
    public AjaxResult getNodeConfig() {
        List<NodeConfig> configAll = nodeService.findNodeConfigAll();
        return AjaxResult.success(configAll);
    }

    /**
     * 根据参数id获取详情
     */
    @GetMapping("/getNodeConfigByNodeLevel")
    public AjaxResult getNodeConfigByNodeLevel(@RequestParam("nodeLevel") String nodeLevel) {
        NodeConfig config = nodeService.findNodeConfigByNodeLevel(nodeLevel);
        return AjaxResult.success(config);
    }


    /**
     * 查询是否可升级为下一节点
     */
    @GetMapping("/checkUpgrade")
    public AjaxResult checkUpgrade() {
        NodeLevel nodeLevel = nodeService.checkUpgrade(SecurityUtils.getLoginUser().getUserId());
        return AjaxResult.success(nodeLevel.isUpgrade());
    }

    /**
     * 自动升级节点
     */
    @GetMapping("/autoUpgrade")
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    public AjaxResult autoUpgrade() {
        NodeLevel nodeLevel = nodeService.checkUpgrade(SecurityUtils.getLoginUser().getUserId());
        if (nodeLevel.isUpgrade()) {
            nodeService.autoUpgrade(SecurityUtils.getLoginUser().getUserId(), nodeLevel);
            return AjaxResult.success(MessageUtils.message("node.auto.upgrade.success"));
        }
        return AjaxResult.error(MessageUtils.message("node.auto.upgrade.failed"));
    }


    /**
     * 根据 username 获取划转对象信息 - 旧版API，提示用户升级
     * 
     * @param username
     * @param cardLevel
     * @return
     */
    @GetMapping("/getTransferByUsername")
    public AjaxResult getTransferByUsername(@RequestParam("username") String username, @RequestParam("cardLevel") String cardLevel) {
        throw new ServiceException(MessageUtils.message("api.version.upgrade"));
    }

    /**
     * 根据 username 获取划转对象信息 - 新版API
     * 
     * @param wrapper 包含参数的包装对象
     * @return
     */
    @ApiEncrypt
    @PostMapping("/getTransferByUsername")
    public AjaxResult getTransferByUsernamePost(@RequestBody MultiParamWrapper wrapper) {
        String username = wrapper.getString("username");
        String cardLevel = wrapper.getString("cardLevel");
        
        SysUser user = userService.selectUserByUserName(username);
        if (user == null) {
            return AjaxResult.error(MessageUtils.message("node.user.not.exist"));
        }
        Wallet wallet = walletService.findByUserId(user.getUserId());
        // 查询可划转数量
        NodeConfig nodeConfig = null;
        //获取当前登录用户的节点等级
        Wallet currentUser = walletService.findByUserId(SecurityUtils.getLoginUser().getUserId());
        String currentNodeLevel = currentUser.getNodeLevel();
        if (currentNodeLevel.equals("00")) {
            nodeConfig = nodeService.findNodeConfigByNodeLevel(currentNodeLevel);
        } else {
            nodeConfig = nodeService.findPreviousNode(currentUser.getNodeLevel());
        }
        TransferVo transferVo = new TransferVo();
        transferVo.setNickname(user.getNickName());
        transferVo.setNodeLevel(wallet.getNodeLevel());
        // 可划转银卡数量
        Integer count = nodeConfig.getTotalRightScardNum() - wallet.getTotalExchangeSilverActiveCodeNum() - wallet.getSilverActiveCodeNum();

        // 转换成需要的卡等级
        Integer integer = CardLevel.exchange(CardLevel.SILVER, count, CardLevel.getByValue(cardLevel));
        if (integer < 0) {
            integer = 0;
        }
        transferVo.setTransferNum(integer);
        return AjaxResult.success(transferVo);
    }

    /**
     * 获取佣金明细(分页)
     */
    @GetMapping("/commissionList")
    public TableDataInfo commissionList(UserCommission userCommission) {
        userCommission.setUserId(SecurityUtils.getLoginUser().getUserId());
        return getDataTable(userCommissionService.getCommissionList(userCommission));
    }

    /**
     * 获取佣金明细(分页) 3.0
     */
    @PostMapping("/commissionList3")
    @ApiEncrypt
    public TableDataInfo commissionList3(@RequestBody UserCommissionVo userCommissionVo) {

        BigInteger userId = BigInteger.valueOf(SecurityUtils.getLoginUser().getUserId());
        userCommissionVo.setUserId(userId);
        return getDataTable(userCommissionService.getCommissionList3(userCommissionVo));
    }


    /**
     * 获取佣金明细(分页)
     */
    @GetMapping("/commissionDetail")
    public AjaxResult commissionDetail(@RequestParam("id") Long id) {
        return AjaxResult.success(userCommissionService.getCommissionDetail(SecurityUtils.getLoginUser().getUserId(), id));
    }

    /**
     * 获取佣金统计
     */
    @GetMapping("/commissionStat")
    public AjaxResult commissionStat() {
        return AjaxResult.success(userCommissionService.commissionStat());
    }


    /**
     * 新增用户节点返佣配置
     */
    @PostMapping("/addUserNodeInfo")
    @RepeatSubmit
    public AjaxResult addUserNodeInfo(@Validated(IInsert.class) @RequestBody UserNodeInfo userNodeInfo) {
        // 检查当前用户的节点等级是否为LV4且不存在自定义节点配置
        AjaxResult result = nodeInfoService.checkNodeLevel(userNodeInfo);
        if (result.isSuccess()) {
            userNodeInfo.setRecommendRebate(new BigDecimal(0.1000)); // 默认0.1000
            userNodeInfo.setNodeLevel(NodeLevel.FOURTH.getOrdinal());
            userNodeInfo.setIsValid(StringUtils.isEmpty(userNodeInfo.getIsValid()) ? "Y" : userNodeInfo.getIsValid());
            userNodeInfo.setCreateBy(SecurityUtils.getUsername());
            userNodeInfo.setCreateTime(DateUtils.getNowDate());
            userNodeInfo.setUpdateTime(DateUtils.getNowDate());
            userNodeInfo.setUpdateBy(SecurityUtils.getUsername());
            nodeInfoService.save(userNodeInfo);
        }
        return result;
    }


    /**
     * 修改用户节点返佣配置
     */
    @PutMapping("/editUserNodeInfo")
    @RepeatSubmit
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult editUserNodeInfo(@Validated(IUpdate.class) @RequestBody UserNodeInfo userNodeInfo) {
        UserNodeInfo nodeInfo = nodeInfoService.findByUserId(userNodeInfo.getUserId());
        if (nodeInfo != null) {
            AjaxResult result = nodeInfoService.rateLimit(userNodeInfo);
            if (result.isSuccess()) {
                nodeInfo.setCardOpenRebate(userNodeInfo.getCardOpenRebate());
                nodeInfo.setIsValid(userNodeInfo.getIsValid());
                nodeInfo.setCardRechargeRebate(userNodeInfo.getCardRechargeRebate());
                nodeInfo.setRemark(userNodeInfo.getRemark());
                nodeInfo.setUpdateBy(SecurityUtils.getUsername());
                nodeInfo.setUpdateTime(DateUtils.getNowDate());
                nodeInfoService.save(nodeInfo);
                return AjaxResult.success();
            }
            return result;
        }
        return AjaxResult.error(MessageUtils.message("node.user.info.not.exists"));
    }

    /**
     * 查询用户的子节点(Lv4,且已有自定义返佣配置)的返佣配置(分页)
     */
    @GetMapping("/getChildNodeInfos")
    public TableDataInfo getChildNodeInfos(String email) {
        return getDataTable(nodeInfoService.getChildNodeInfos(SecurityUtils.getLoginUser().getUserId(), email));
    }

    /**
     * 查询用户的子节点(Lv4,且已有自定义返佣配置)的返佣配置(分页)
     */
    @GetMapping("/getAllChildNodeInfos")
    public AjaxResult getAllChildNodeInfos(String email) {
        return AjaxResult.success(nodeInfoService.getAllChildNodeInfos(SecurityUtils.getLoginUser().getUserId(), email));
    }

    /**
     * 查询未自定义返佣的用户数据
     */
    @GetMapping("/getNotCustomChildUsers")
    public TableDataInfo getNotCustomChildUsers(String email) {
        return getDataTable(nodeInfoService.getNotCustomChildUsers(SecurityUtils.getLoginUser().getUserId(), email));
    }

    /**
     * 获取所有的LV4的username
     *
     * @return
     */
    @GetMapping("/getNotCustomChildUsername")
    public AjaxResult getNotCustomChildUsername() {
        return AjaxResult.success(nodeInfoService.getNotCustomChildUsername(SecurityUtils.getLoginUser().getUserId()));
    }

    /**
     * 自定义返佣配置 范围限制
     */
    @GetMapping("/rebateRange")
    public AjaxResult rebateRange() {
        NodeConfig lv4 = nodeService.findNodeConfigByNodeLevel(NodeLevel.FOURTH.getOrdinal());
        NodeConfigVo nodeConfigVo = new NodeConfigVo();
        nodeConfigVo.setCardOpenRebate(lv4.getCardOpenRebate());
        nodeConfigVo.setMaxCardOpenRebate(lv4.getMaxCardOpenRebate());
        nodeConfigVo.setCardRechargeRebate(lv4.getCardRechargeRebate());
        nodeConfigVo.setMaxCardRechargeRebate(lv4.getMaxCardRechargeRebate());
        return AjaxResult.success(nodeConfigVo);
    }

    /**
     * 合作方获取管理下的用户信息
     *
     * @param uuid
     * @return
     */
    @GetMapping("/getChildInfos")
    public TableDataInfo getChildInfos(String uuid) {
        return getDataTable(nodeInfoService.getChildInfos(SecurityUtils.getLoginUser().getUserId(), uuid));
    }

    /**
     * 统计人数
     *
     * @return
     */
    @GetMapping("/getCommissionCount")
    public AjaxResult getCommissionCount() {
        AjaxResult ajaxResult = AjaxResult.success();
        Long userId = SecurityUtils.getLoginUser().getUserId();
        ajaxResult.put("count", userCommissionService.getCommissionCount(userId));
        return ajaxResult;
    }


    /**
     * 统计人数
     *
     * @return
     */
    @GetMapping("/getCommissionTotal")
    public AjaxResult getCommissionTotal() {

        Long userId = SecurityUtils.getLoginUser().getUserId();

        return AjaxResult.success(userCommissionService.getCommissionTotal(userId));
    }

    /**
     * 获取佣金信息
     *
     * @param
     * @return
     */
    @ApiEncrypt
    @PostMapping("/teamCommissionDetail")
    public TableDataInfo teamCommissionDetail(@RequestBody TeamCommissionVo teamCommissionVo) {
        Page<UserTeamCommissionVo> page = userCommissionService.teamCommissionDetail(SecurityUtils.getLoginUser().getUserId(), teamCommissionVo);
        List<UserTeamCommissionVo> list = page.getContent();
        for (UserTeamCommissionVo vo : list) {
            String email = vo.getChildName();
            if (email.contains("@")) {
                String[] str = email.split("@");
                String l1 = str[0];
                String l2 = str[1];
                if (l1.length() > 4) {

                    // 计算保留的字符数量
                    int keepStart = 2; // 开头保留的字符数
                    int keepEnd = 2;   // 结尾保留的字符数
                    String maskedUsername = l1.substring(0, keepStart) + "**" +
                            l1.substring(l1.length() - keepEnd);
                    String end = maskedUsername + "@" + l2;
                    vo.setChildName(end);
                } else {
                    int keepStart = 1; // 开头保留的字符数
                    int keepEnd = 1;   // 结尾保留的字符数
                    String maskedUsername = l1.substring(0, keepStart) + "**" +
                            l1.substring(l1.length() - keepEnd);
                    String end = maskedUsername + "@" + l2;
                    vo.setChildName(end);
                }

            }
        }

        return getDataTable(page);
    }


}
