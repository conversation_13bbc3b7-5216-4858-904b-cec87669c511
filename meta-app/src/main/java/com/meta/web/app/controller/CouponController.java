package com.meta.web.app.controller;

import com.meta.common.annotation.ApiEncrypt;
import com.meta.common.constant.HttpStatus;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.core.page.TableSupport;
import com.meta.common.exception.ServiceException;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.code.BusinessBizCode;
import com.meta.system.domain.app.CardConfig;
import com.meta.system.dto.CouponDto;
import com.meta.system.service.CreditCardService;
import com.meta.system.service.ExchangeRateService;
import com.meta.system.service.MetaCouponGenCfgService;
import com.meta.system.service.MetaCouponInfoService;
import com.meta.web.service.CardConfigService;
import com.meta.web.dto.MultiParamWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/10/09/14:03
 */
@RestController
@RequestMapping("/coupon")
public class CouponController {

    @Autowired
    private MetaCouponInfoService metaCouponInfoService;

    @Autowired
    private MetaCouponGenCfgService metaCouponGenCfgService;

    @Autowired
    private CardConfigService cardConfigService;

    @Autowired
    private CreditCardService creditCardService;

    @Autowired
    private ExchangeRateService exchangeRateService;

    /**
     * 通过券码领取优惠券
     */
    @ApiEncrypt
    @PostMapping("/getCouponInfo")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult getCouponInfo(@RequestBody CouponDto couponDto) {
        Long userId = SecurityUtils.getLoginUser().getUserId();
        return metaCouponInfoService.getCouponInfo(couponDto.getCouponNm(), userId);
    }

    /**
     * 获取自己发放的券以及自己拥有的券
     */
    @ApiEncrypt
    @PostMapping("/couponInfoPageByUser")
    @Transactional
    public TableDataInfo couponInfoPage(@RequestBody CouponDto couponDto) {
        Long userId = SecurityUtils.getLoginUser().getUserId();
        couponDto.setUserId(userId);
        return getDataTable(metaCouponInfoService.couponInfoPageByUser(couponDto));
    }

    /**
     * 可折扣金额 - 旧版API，提示用户升级
     */
    @GetMapping("/discountAmount")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult discountAmount(CouponDto couponDto) {
        throw new ServiceException(MessageUtils.message("api.version.upgrade"));
    }

    /**
     * 可折扣金额 - 新版API
     *
     * @return
     */
    @ApiEncrypt
    @PostMapping("/discountAmount")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult discountAmountPost(@RequestBody CouponDto couponDto) {
        CardConfig config = null;
        if ("NEW".equals(couponDto.getCouponScenarios())) {
            if (couponDto.getCardCfgId() == null) {
                return AjaxResult.error();
            }
            config = cardConfigService.findCardConfigByCardCfgId(couponDto.getCardCfgId());
        }

        return metaCouponInfoService.getAmount(couponDto, config);
    }

    /**
     * 响应请求分页数据
     */
    protected TableDataInfo getDataTable(Page<?> page) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(page.getContent());
        rspData.setTotal(page.getTotalElements());
        rspData.setSize(pageReq.getPageSize());
        rspData.setPages(page.getTotalPages());
        rspData.setCurrent(pageReq.getPageNo() + 1);
        return rspData;
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows) {
        return rows == BusinessBizCode.OPTION_SUCCESS.getCode() ? AjaxResult.success() : AjaxResult.error();
    }

}
