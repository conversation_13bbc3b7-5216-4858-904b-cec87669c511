package com.meta.web.enums;

import com.meta.common.enums.BaseEnum;

import java.util.stream.Stream;

/**
 *  卡 返回信息参数
 */
public enum RespCode implements BaseEnum<RespCode> {

    SUCCESS("SUCCESS","成功"),
    PROCESSING("PROCESSING","处理中"),
    FAILURE("FAILURE","失败"),
    INTERNAL_ERROR("INTERNAL_ERROR","内部错误"),
    PARAM_FORMAT_ERROR("PARAM_FORMAT_ERROR","参数格式错误"),
    PARAMETER_ERROR("PARAMETER_ERROR","参数错误"),
    IDEMPOTENT_ERROR("IDEMPOTENT_ERROR","幂等错误"),
    REQUEST_NO_NOT_UNIQUE("REQUEST_NO_NOT_UNIQUE","请求号重复"),
    UNAUTHORIZED("UNAUTHORIZED","未授权"),
    UNAUTHENTICATED_ERROR("UNAUTHENTICATED_ERROR","认证(签名)错误"),
    INTERFACE_UNAUTHORIZED("INTERFACE_UNAUTHORIZED","接⼝未授权"),
    CARD_NO_FOUND("CARD_NO_FOUND","卡号不存在"),
    CARD_NO_USED("CARD_NO_USED","卡状态非使用中"),
    CARD_RECHARGE_TOO_LOW("CARD_RECHARGE_TOO_LOW","卡充值金额过低"),
    CARD_WITHDRAW_TOO_LOW("CARD_WITHDRAW_TOO_LOW","卡降额金额过低"),
    CARD_BALANCE_NOT_ENOUGH("CARD_BALANCE_NOT_ENOUGH","卡内余额不足"),
    VCC_ERROR("VCC_ERROR","内部vcc错误"),;

    String value;
    String name;

    RespCode(String value, String name) {
        this.value = value;this.name = name;
    }

    public String getName(){
        return this.name;
    }
    public String getValue(){
        return this.value;
    }
    @Override
    public String getOrdinal() {
        return String.valueOf(this.ordinal());
    }


    public static RespCode toType(String value) {
        return Stream.of(RespCode.values())
                .filter(p -> p.value == value)
                .findAny()
                .orElse(null);
    }
}
