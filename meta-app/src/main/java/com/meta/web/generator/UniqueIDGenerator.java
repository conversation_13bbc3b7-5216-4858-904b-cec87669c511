package com.meta.web.generator;

import com.meta.common.utils.DateUtils;

import java.util.Random;

public class UniqueIDGenerator {
    private static int counter = 1;
    private static String lastDate = DateUtils.dateTime();
    private static Random random = new Random();

    public static synchronized String generateID() {

        String currentDate = DateUtils.dateTime();
        if (!currentDate.equals(lastDate)) {
            counter = 1;
            lastDate = currentDate;
        }
        String timeStamp = DateUtils.dateTimeNow();
        String randomNum = String.format("%06d", random.nextInt(1000000));
        String id = timeStamp + randomNum + String.format("%04d", counter);
        counter = (counter + 1) % 10000;
        return id;
    }

    public static void main(String[] args) {
        System.out.println(generateID());
    }
}
