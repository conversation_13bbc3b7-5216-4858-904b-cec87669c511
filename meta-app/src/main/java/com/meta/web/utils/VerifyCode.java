package com.meta.web.utils;

import com.meta.common.utils.StringUtils;
import com.meta.web.contants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/03/17/1:51
 */
@Slf4j
@Component
public class VerifyCode {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @PostConstruct
    public void cacheVerifyCode() {
        redisTemplate.delete(Constants.CHAINKEY);
        String chainData = "₿,Ξ,Ł,Ɖ,Ⓝ,ɱ,⚡,Ꞓ,Ӿ,Ƀ,◎,🔗,⛓,📦,📄,🌐,🖥️,🔒,🌍,💰,👛,💸,📍,🔐,📜,🤖,🌱,🔮,🛠️,⛏,⚖,🗳️,🛡️,🌉,🛤️,🎯";
        // 使用 split() 方法将字符串按逗号分隔为数组
        String[] symbolsArray = chainData.split(",");

        // 将数组转换为 List
        List<String> symbolsList = Arrays.asList(symbolsArray);
        redisTemplate.opsForList().leftPushAll(Constants.CHAINKEY, symbolsArray);

    }

    /**
     * 生成验证码
     *
     * @param email
     * @return
     */
    public Map create(String email) {
        List<String> symbolsList = redisTemplate.opsForList().range(Constants.CHAINKEY, 0, -1);

        // 打乱列表顺序
        Collections.shuffle(symbolsList);
        // 取出前四个元素
        List<String> randomFour = symbolsList.subList(0, 4);
        // 浅拷贝
        List<String> list = new ArrayList<>();
        list.addAll(randomFour);
        log.info("随机获取的四个不重复的字符串: " + list);

        // 从这四个中再随机获取三个不重复的字符串
        Collections.shuffle(randomFour);
        List<String> randomThree = randomFour.subList(0, 3);
        // 输出结果
        log.info("从这四个中随机获取的三个不重复的字符串: " + randomThree);
        //K-第一个图标
        //Z-第二个图标
        //P-第三个图标
        //Y-不存在
        // 遍历 B 中的每个字符，检查在 A 中的位置并映射结果
        StringBuilder result = new StringBuilder();
        for (String symbol : list) {
            if (randomThree.contains(symbol)) {
                // 根据 randomThree 中的顺序映射为 K, Z, P

                int index = randomThree.indexOf(symbol);

                switch (index) {
                    case 0:
                        result.append("K,");
                        break;
                    case 1:
                        result.append("Z,");
                        break;
                    case 2:
                        result.append("P,");
                        break;
                }
            } else {

                result.append("Y,");
            }
        }
        String s = result.toString();
        s = s.substring(0, s.length() - 1);
        log.info("字符结果：" + s);


        Map<String, String> map = new HashMap<>();
        map.put("symbol", String.join(",", list));
        map.put("verify", s);

        String str = String.join(",", randomThree);
        log.info("str:" + str);
        redisTemplate.opsForValue().set(Constants.CHAINKEY_VERIFY + email, str, 5, TimeUnit.MINUTES);
        return map;

    }

    /**
     * 验证验证码
     *
     * @param email
     * @param code
     * @return
     */
    public boolean verify(String email, String code) {
        String str = redisTemplate.opsForValue().get(Constants.CHAINKEY_VERIFY + email);
        log.info("验证str:" + str);
        if (StringUtils.isEmpty(code) || !str.equals(code)) {
            redisTemplate.delete(Constants.CHAINKEY_VERIFY + email);
            log.info("验证结果：false");
            return false;
        }
        log.info("验证结果：true");
        return true;
    }

}
