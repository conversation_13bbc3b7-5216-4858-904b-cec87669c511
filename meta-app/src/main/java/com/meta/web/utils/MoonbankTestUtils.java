package com.meta.web.utils;

/**
 * <AUTHOR>
 * @date 2023/12/20/10:31
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meta.common.utils.uuid.UniqueIDGenerator;
import com.meta.system.moonbank.util.AesAPIUtils;
import com.meta.system.vcc.HttpUtils;
import org.apache.http.HttpResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Component
public class MoonbankTestUtils {
    private static final Logger log = LoggerFactory.getLogger(MoonbankTestUtils.class);
    private static String appId = "app_35940";
    private static String appSecret = "b635dd5c87f7bf73387929203321b1e1";


    //    private static String url = "http://testapi.metabanks.cc/meta/api/mcard/notify";
    private static String url = "http://127.0.0.1:8893/meta/api/mcard/notify";

    public static void main(String[] args) throws Exception {


        String type = "CARD_CLOSE_RESULT";
        int userBankcardId = 655;
        //发送请求
        sendPostRequest(type, userBankcardId);
    }


    private String getResult(String apiCode, int userBankcardId) {
        JSONObject req = new JSONObject();
        switch (apiCode) {

            case "ACTIVATION_CODE":
                req.put("cardNo", "****************");
                req.put("code", "********");
                req.put("createAt", 1737019487337L);
                req.put("currency", "USD");
                break;

            case "TRANSACTION_VERIFICATION_CODE":
                req.put("cardNo", "****************");
                req.put("code", "1547");
                req.put("createAt", 1691249025085L);
                req.put("userBankcardId", "654");
                break;
            case "CARD_STATUS_CHANGE":
                req.put("createAt", 1691249025085L);
                req.put("reason", "待激活");
                req.put("status", "TBA");
                req.put("userBankcardId", userBankcardId);
                req.put("cardNo", "****************");
                break;


            case "TRANSACTION_CREATED":
                req.put("cardNo", "****************");
                req.put("createAt", 1723695981357L);
                req.put("currency", "USD");
                req.put("userBankcardId", "654");
                JSONObject j2 = new JSONObject();
                j2.put("authType", "PURCHASE");
                j2.put("failure_reason", "");
                j2.put("feeAmount", "-0.25");
                j2.put("feeCurrency", "USD");
                j2.put("id", 8799);

                j2.put("localCurrency", "USD");
                j2.put("localCurrencyAmt", "-8.42");
                j2.put("merchantName", "Gate Retail Wizz EUR");
                j2.put("respCode", "100001");
                j2.put("respCodeDesc", "Authorization Approval");
                j2.put("occurTime", 1723695980000L);
                j2.put("receiptNo", "ipi_5Antbur8WG4fHSAyHmjsBVlR");
                j2.put("recordNo", "ipi_ogrdY2BaOLhhEEN021TJeqzO");
                j2.put("transCurrency", "EUR");
                j2.put("transCurrencyAmt", "-7.40");
                j2.put("transStatus", "APPROVED");
                j2.put("transType", "AUTH");
//                j2.put("receiptNo","0601003");
                req.put("transaction", j2);

                break;
            case "CARD_RECHARGE_RESULT":

                req.put("amount", 49.1);
                req.put("cardNo", "****************");
                req.put("createAt", 1716797461205L);
                req.put("currency", "USD");
                req.put("receiveAmount", 49.1);
                req.put("requestOrderId", "APPLY241026195958701032746");
                req.put("status", "SUCCESS");
                req.put("userBankcardId", userBankcardId);
                break;
            case "COIN_RECHARGE_RESULT":
                req.put("amount", 100);
                req.put("cardNo", "USDT");
                req.put("createAt", 1692711247364L);
                req.put("currency", "USD");
                req.put("requestOrderId", "MB230822103810254026143");
                req.put("receiveAmount", 99.500000000000000000);
                req.put("status", "SUCCESS");
                req.put("userBankcardId", "140");
                break;
            case "CARD_3DS_AUTH_RESULT":
                req.put("cardNo", "****************");
                req.put("createAt", 1692711247364L);
                req.put("authId", "MB230822103810254026143");
                req.put("authResult", "UNAUTHORIZED");
                req.put("userBankcardId", 2097);
                break;
            case "USER_KYC_STATUS_CHANGE":
                req.put("uid", "36427");
                req.put("createAt", 1692711247364L);
                req.put("kycStatus", "PASSED");
                req.put("reason", "reason");
                break;
            case "CARD_CLOSE_RESULT":
                req.put("amount", 49);
                req.put("cardNo", "****************");
                req.put("cardNo", 1741595250202L);
                req.put("currency", "USD");
                req.put("receiveAmount", 48.6);
                req.put("requestOrderId", "mc-202503101627137616230002");
                req.put("status", "FAILED");
                req.put("userBankcardId", "4024");

                break;



            default:
                break;
        }
        //加密
        System.out.println("req=" + req);
        String jsonDataString = JSON.toJSONString(req);
        System.out.println("jsonDataString=" + jsonDataString);
        String signature = AesAPIUtils.encode(jsonDataString, appSecret);
        //MoonbankEncryptUtil.encode(appSecret, jsonDataString);
        System.out.println("signature=" + signature);

        //解密
        String rest = AesAPIUtils.decode(signature, appSecret);
        //MoonbankEncryptUtil.decode(appSecret, signature);
        System.out.println("======rest====" + rest);
        return signature;

    }


    public static void sendPostRequest(String type, int userBankcardId) {
        // 创建一个RestTemplate对象
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // 设置请求体参数
        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();


        requestBody.add("appId", appId);
        requestBody.add("type", type);
        requestBody.add("uniqueNo", UniqueIDGenerator.generateID());
        MoonbankTestUtils testUtils = new MoonbankTestUtils();

        String result = testUtils.getResult(type, userBankcardId);
        requestBody.add("result", result);

        JSONObject req = new JSONObject();


        // 创建请求实体对象
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);

        // 发送POST请求
        ResponseEntity<String> responseEntity = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
        );

        // 获取响应结果
        String response = responseEntity.getBody();
        System.out.println("结果：" + response);
    }


//    private static void sendPostRequest2() throws Exception {
//        JSONObject req = new JSONObject();
//        String type = "CARD_RECHARGE_RESULT";
//        int userBankcardId = 329;
//        req.put("appId", appId);
//        req.put("type", type);
//        req.put("uniqueNo", UniqueIDGenerator.generateID());
//        MoonbankTestUtils testUtils = new MoonbankTestUtils();
//        String result = testUtils.getResult(type, userBankcardId);
//        req.put("result", result);
//
//        Map headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/x-www-form-urlencoded");
//        HttpResponse resultRep = HttpUtils.doPost(url, "", "POST", headers, null, req.toJSONString());
//        String response = resultRep.toString();
//        System.out.println("结果：" + response);
//    }
}
