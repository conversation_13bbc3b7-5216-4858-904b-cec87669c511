package com.meta.web.utils;

import com.alibaba.fastjson.JSONObject;
import com.meta.common.annotation.RepeatSubmit;
import com.meta.common.enums.BizError;
import com.meta.common.enums.app.TxnStatus;
import com.meta.common.enums.app.TxnType;
import com.meta.common.exception.CustomException;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.sign.Md5Utils;
import com.meta.system.domain.Wallet;
import com.meta.system.domain.app.CoinBalance;
import com.meta.system.domain.app.CoinBalancePK;
import com.meta.system.domain.app.CoinTxnDtl;
import com.meta.system.domain.app.MetaGatePrePay;
import com.meta.system.domain.log.TxnDtlCode;
import com.meta.system.enums.CoinType;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.MetaGatePrePayService;
import com.meta.system.service.TxnService;
import com.meta.system.service.WalletService;
import com.meta.system.vcc.HttpUtils;
import com.meta.web.contants.Constants;
import com.meta.web.core.config.WalletConfig;
import com.meta.web.redis.BizReidsLock;
import com.meta.web.service.CoinBalanceService;
import com.meta.system.service.CoinTxnDtlService;
import com.meta.web.vo.GateAccessVo;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.math.BigDecimal;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.time.Instant;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/03/26/17:35
 */
@Component
public class GateUtil {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private BizReidsLock bizRedisLock;

    @Autowired
    private CoinBalanceService coinBalanceService;

    @Autowired
    private MetaGatePrePayService metaGatePrePayService;

    @Autowired
    private CoinTxnDtlService coinTxnDtlService;

    @Autowired
    private WalletService walletService;

    @Autowired
    private TxnService txnService;

    private static final String HMAC_SHA512 = "HmacSHA512";

    private static String toHexString(byte[] bytes) {
        Formatter formatter = new Formatter();
        for (byte b : bytes) {
            formatter.format("%02x", b);
        }
        return formatter.toString();
    }

    /**
     * 签名算法
     *
     * @param data
     * @param key
     * @return
     * @throws InvalidKeyException
     * @throws NoSuchAlgorithmException
     */
    public String calculateHMAC(String data, String key) throws InvalidKeyException, NoSuchAlgorithmException {
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(), HMAC_SHA512);
        Mac mac = Mac.getInstance(HMAC_SHA512);
        mac.init(secretKeySpec);
        return toHexString(mac.doFinal(data.getBytes()));
    }

    /**
     * 生成指定长度的随机字符串
     *
     * @param length
     * @return
     */
    public String generateRandomString(int length) {
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < length; i++) {
            int randomIndex = random.nextInt(characters.length());
            sb.append(characters.charAt(randomIndex));
        }
        return sb.toString();
    }

    /**
     * 请求头
     *
     * @return
     */
    public HttpHeaders getHttpHeaders(String body, String nonce, String type) {
        HttpHeaders headers = new HttpHeaders();
        try {

            headers.setContentType(MediaType.APPLICATION_JSON);
            String clientId = sysConfigService.selectConfigByKey("gate_clientId");
            //签名
            String key = "";
            if (type.equals("pay")) {
                key = sysConfigService.selectConfigByKey("gate_pay_key");
            } else if (type.equals("authorize")) {
                key = sysConfigService.selectConfigByKey("gate_authorize_key");
            }

            long utcTimestampMillis = Instant.now().toEpochMilli();
            String timeStamp = String.valueOf(utcTimestampMillis);
            String data = String.format("%s\n%s\n%s\n", timeStamp, nonce, body);
            String hmac = calculateHMAC(data, key);
            headers.set("X-GatePay-Certificate-ClientId", clientId);
            headers.set("X-GatePay-Signature", hmac);
            headers.set("X-GatePay-Timestamp", timeStamp);
            headers.set("X-GatePay-Nonce", nonce);

        } catch (InvalidKeyException e) {
            System.err.println("InvalidKeyException: " + e.getMessage());
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            System.err.println("NoSuchAlgorithmException: " + e.getMessage());
            e.printStackTrace();
        }

        return headers;
    }

    /**
     * 创建预付单
     *
     * @param merchantTradeNo 商户的订单号
     * @param terminalType    交易来源
     * @param currency        支付币种
     * @param orderAmount     交易金额
     * @param goodsType       商品
     * @param goodsName       商品名称
     * @param goodsDetail     商品描述
     * @param returnUrl       订单支付成功后返回跳转地址
     * @return 预付单prepayId
     */
    public String CreatePayOrder(String merchantTradeNo, String terminalType, String currency, BigDecimal orderAmount, String goodsType, String goodsName, String goodsDetail, String returnUrl) {
        String gate = sysConfigService.selectConfigByKey("gate_api_url");
        String url = gate + "/v1/pay/order";

        String data = "{ \"merchantTradeNo\":\"" + merchantTradeNo + "\"," +
                "    \"env\":{" +
                "        \"terminalType\":\"" + terminalType + "\"" +
                "    }," +
                "    \"currency\":\"" + currency + "\"," +
                "    \"orderAmount\":\"" + orderAmount + "\"," +
                "    \"goods\":{" +
                "        \"goodsType\":\"" + goodsType + "\"," +
                "        \"goodsName\":\"" + goodsName + "\"," +
                "        \"goodsDetail\":\"" + goodsDetail + "\"" +
                "    },\n" +
                "    \"returnUrl\":\"" + returnUrl + "\"" +
                "}";

        // 生成10位的随机字符串
        String nonce = generateRandomString(10);
        HttpHeaders headers = getHttpHeaders(data, nonce, "pay");
        HttpEntity<String> request = new HttpEntity<>(data, headers);
        RestTemplate restTemplate = new RestTemplate();
        logger.info("===url===" + url);
        logger.info("===headers===" + headers);
        logger.info("===data===" + data);
        logger.info("===request===" + request);
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
        if (response.getStatusCode().is2xxSuccessful()) {
            String body = response.getBody();
            logger.info("create pay order: " + body);
            JSONObject json = JSONObject.parseObject(body);
            String code = json.getString("code");
            if ("000000".equals(code)) {
                JSONObject dataObject = json.getJSONObject("data");
                String prepayID = dataObject.getString("prepayId");
                return prepayID;
            }
        }
        return null;
    }

    /**
     * 查询订单状态
     * @param merchantTradeNo
     * @param prepayId
     * @return
     */
    public boolean getOrderStatue(String merchantTradeNo, String prepayId) {
        String gate = sysConfigService.selectConfigByKey("gate_api_url");
        String url = gate + "/v1/pay/order/query";

        String data = "{ \"prepayId\":\"" + prepayId + "\"," +

                "    \"merchantTradeNo\":\"" + merchantTradeNo + "\"" +
                "}";

        // 生成10位的随机字符串
        String nonce = generateRandomString(10);
        HttpHeaders headers = getHttpHeaders(data, nonce, "pay");
        HttpEntity<String> request = new HttpEntity<>(data, headers);
        RestTemplate restTemplate = new RestTemplate();
        logger.info("===url===" + url);
        logger.info("===headers===" + headers);
        logger.info("===data===" + data);
        logger.info("===request===" + request);
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
        if (response.getStatusCode().is2xxSuccessful()) {
            String body = response.getBody();
            logger.info("selectConfigByKey pay order: " + body);
            JSONObject json = JSONObject.parseObject(body);
            String code = json.getString("code");
            if ("000000".equals(code)) {
                JSONObject dataObject = json.getJSONObject("data");
                String status = dataObject.getString("status");
                if ("PAID".equals(status)) {
                    return true;
                }

            }
        }
        return false;
    }


    /**
     * 获取授权⻚⾯
     *
     * @param agent 安卓端/IOS端
     */
    public String gateOauth(String agent) {
        // 创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate();

        String gateUrl = sysConfigService.selectConfigByKey("gate_api_url");
        String clientId = sysConfigService.selectConfigByKey("gate_clientId");
        String state = sysConfigService.selectConfigByKey("gate_state");
        String app_prod_api = sysConfigService.selectConfigByKey("app_prod_api");
        String returnUrl = app_prod_api + "gate/authBack";
        // 设置请求的URL
        String url = gateUrl + "/oauth/authorize?" +
                "response_type=code" +
                "&client_id=" + clientId +
                "&redirect_uri=" + returnUrl +
                "&scope=read_profile,read_email,read_wallet" +
                "&state=" + state;

        // 创建HttpHeaders并设置'Content-Type'和'User-Agent'
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "code");
        if ("android".equals(agent)) {
            headers.set("User-Agent", "gateio/android");
        } else if ("ios".equals(agent)) {
            headers.set("User-Agent", "gateio/ios");
        }
        // 创建HttpEntity，将headers作为参数传入
        HttpEntity<String> entity = new HttpEntity<>(headers);
        // 发送GET请求
        logger.info("authorize_url=====：" + url);
        logger.info("headers===: " + headers.toString());
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
        // 输出响应体
        logger.info("response=====：" + response.getBody());

        return url;


    }

    /**
     * 换取 access_token
     *
     * @param grant_type 授权类型
     * @param code       授权令牌
     * @return
     */
    public GateAccessVo getToken(String grant_type, String code) {


        // 创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate();
        String gateUrl = sysConfigService.selectConfigByKey("gate_api_url");
        // 设置请求的URL
        String url = gateUrl + "/oauth/token";

        String app_prod_api = sysConfigService.selectConfigByKey("app_prod_api");
        String returnUrl = app_prod_api + "gate/authBack";

        // 构造请求体JSON字符串
        String data = "{" +
                "    \"grant_type\":\"" + grant_type + "\"," +
                "    \"code\":\"" + code + "\"," +
                "    \"redirect_uri\":\"" + returnUrl + "\"" +
                "}";
        //时间戳

        // 生成10位的随机字符串
        String nonce = generateRandomString(10);
        HttpHeaders headers = getHttpHeaders(data, nonce, "authorize");

        // 创建HttpEntity，将headers和请求体作为参数传入
        HttpEntity<String> entity = new HttpEntity<>(data, headers);

        // 发送POST请求并接收响应
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);

        // 输出响应体
        System.out.println(response.getBody());
        if (response.getStatusCode().is2xxSuccessful()) {
            String body = response.getBody();
            logger.info("/oauth/token: " + body);
            JSONObject json = JSONObject.parseObject(body);
            String accessToken = json.getString("access_code");
            String refreshToken = json.getString("refresh_token");
            String scope = json.getString("scope");
            String tokenType = json.getString("token_type");
            String expiredIn = json.getString("expired_in");
            GateAccessVo gateAccessVo = new GateAccessVo();
            gateAccessVo.setAccessToken(accessToken);
            gateAccessVo.setRefreshToken(refreshToken);
            gateAccessVo.setScope(scope);
            gateAccessVo.setTokenType(tokenType);
            gateAccessVo.setExpiredIn(expiredIn);
            return gateAccessVo;
        }
        return null;
    }

    /**
     * 获取⽤户信息(包括Email)
     *
     * @param token
     * @return
     */
    public JSONObject getGateUserEmail(String token, String tokenType) {

        // 创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate();
        // 设置请求的URL
        String gateUrl = sysConfigService.selectConfigByKey("gate_api_url");
        String url = gateUrl + "/api/user_email";


        // 创建HttpHeaders并设置'Authorization'头
        HttpHeaders headers = new HttpHeaders();

        headers.set("Authorization", tokenType + " " + token);
        logger.info("==token===", token);
        logger.info("==tokenType===", tokenType);
        logger.info("==Authorization===", tokenType + " " + token);

        // 创建HttpEntity，只需传入headers因为是GET请求，没有请求体
        HttpEntity<String> entity = new HttpEntity<>(headers);

        // 发送GET请求并接收响应
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

        // 输出响应体
        if (response.getStatusCode().is2xxSuccessful()) {
            String body = response.getBody();
            logger.info("/user_email: " + body);
            JSONObject json = JSONObject.parseObject(body);
            String status = json.getString("status");
            if ("SUCCESS".equals(status)) {
                JSONObject dataObject = json.getJSONObject("data");
                String uid = dataObject.getString("uid");
                String email = dataObject.getString("email");
                JSONObject resulet = new JSONObject();
                resulet.put("uid", uid);
                resulet.put("email", email);
                return resulet;
            }
        }
        return null;
    }

    /**
     * 处理gate订单状态通知
     *
     * @param timestamp
     * @param nonce
     * @param signature
     * @param requestBody
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    public ResponseEntity<String> dealNotice(String timestamp, String nonce, String signature, String requestBody) {
        JSONObject jsonObject = JSONObject.parseObject(requestBody);
        String bizType = jsonObject.getString("bizType");
        String bizId = jsonObject.getString("bizId");
        String bizStatus = jsonObject.getString("bizStatus");
        String dataObject = jsonObject.getString("data");
        String error = "{\"status\": \"FAIL\", \"code\": \"400000\", \"data\": {\"result\": \"FAIL\"}, \"errorMessage\": \"FAIL\"}";
        String success = "{\"status\": \"SUCCESS\", \"code\": \"000000\", \"data\": {\"result\": \"SUCCESS\"}, \"errorMessage\": \"\"}";
        try {
            //验证签名
            String data = String.format("%s\n%s\n%s\n", Long.valueOf(timestamp), nonce, requestBody);
            String key = sysConfigService.selectConfigByKey("gate_pay_key");
            String hmac = calculateHMAC(data, key);
            logger.info("====hmac===" + hmac);
            //判断签名是否正确
            if (hmac.equals(signature)) {
                logger.info("签名验证成功");
                //支付成功
                if ("PAY".equals(bizType) && bizStatus.equals("PAY_SUCCESS") && StringUtils.isNotEmpty(bizId)) {
                    //对数据做处理
                    //限制多次请求同一个bizId
                    if (!bizRedisLock.locks(bizId, "gate.io.bizId:", 5000)) {
                        throw new CustomException(MessageUtils.message(BizError.SYS_RECHARGE_BUSY.value()));
                    }
                    JSONObject dataJson = JSONObject.parseObject(dataObject);
                    String merchantTradeNo = dataJson.getString("merchantTradeNo");
                    String orderAmount = dataJson.getString("orderAmount");
                    String transactionId = dataJson.getString("transactionId");
                    String currency = dataJson.getString("currency");
                    String createTime = dataJson.getString("createTime");
                    if (!currency.equals("USDT")) {
                        return ResponseEntity.ok(error);
                    }

                    //更新订单状态
                    MetaGatePrePay metaGatePrePay = metaGatePrePayService.findData(merchantTradeNo);
                    if (metaGatePrePay != null) {
                        //查询订单状态
                        boolean orderStatue = getOrderStatue(merchantTradeNo, metaGatePrePay.getPrepayId());
                        if(!orderStatue){
                            return ResponseEntity.ok(error);
                        }
                        boolean giveFlag = false;//是否赠送卡
                        metaGatePrePayService.updateData(merchantTradeNo, bizStatus, transactionId);
                        CoinBalance cb = coinBalanceService.findByUserIdAndCoinCode(metaGatePrePay.getUserId(), currency);
                        //对于新用户得创建一个钱包地址
                        if (cb == null) {
                            giveFlag = true;
                            CoinBalancePK pk = new CoinBalancePK(metaGatePrePay.getUserId(), "USDT", "TRC20");
                            cb = new CoinBalance();
                            cb.setCoinBalance(BigDecimal.ZERO);
                            cb.setCanRechargeCoin(Boolean.TRUE);
                            cb.setCanWithdrawCoin(Boolean.TRUE);
                            cb.setFreezeBalance(BigDecimal.ZERO);
                            cb.setUserId(metaGatePrePay.getUserId());
                            cb.setUncltCoinBalance(BigDecimal.ZERO);
                            cb.setUncltBep20CoinBalance(BigDecimal.ZERO);
                            cb.setCoinCode(CoinType.USDT.getCode());
                            cb.setUncltErc20ArbCoinBalance(BigDecimal.ZERO);
                            cb.setUncltErc20BaseCoinBalance(BigDecimal.ZERO);
                            coinBalanceService.save(cb);
                            cb.setCoinCode(CoinType.USDC.getCode());
                            coinBalanceService.save(cb);

                        } else {
                            //判断是否赠送过卡
                            if (cb.getGateRecharge() != null && (cb.getGateRecharge().compareTo(new BigDecimal(200)) == 0 || cb.getGateRecharge().compareTo(new BigDecimal(500)) == 0 || cb.getGateRecharge().compareTo(new BigDecimal(2000)) == 0 || cb.getGateRecharge().compareTo(new BigDecimal(5000)) == 0)) {
                                giveFlag = false;
                            } else {
                                giveFlag = true;
                            }
                        }

                        //增加用户USDT
                        cb.setCoinBalance(cb.getCoinBalance().add(new BigDecimal(orderAmount)));

                        //增加流水
                        CoinTxnDtl r = new CoinTxnDtl();
                        r.setUserId(cb.getUserId().longValue());
                        r.setFromAddress("Gate.io");
                        r.setReceiptNo(transactionId);
                        r.setRecordType(Constants.RECORD_TYPE_RECEIPTS);
                        r.setTxnCode(Constants.txnCode.d1010);
                        r.setTxnCoin(Constants.coin.USDT);
                        r.setTxnDesc("充值");
                        r.setTxnAmount(new BigDecimal(orderAmount));
                        r.setUserBalance(cb.getCoinBalance());
                        r.setTxnFee(null);
                        r.setTxnTime(new Date(Long.valueOf(createTime)));
                        r.setCreateTime(new Date());
                        r.setTxnStatus(1);
                        coinTxnDtlService.save(r);

                        //是否增加金/银卡、流水
                        if (giveFlag) {
                            if (new BigDecimal(orderAmount).compareTo(new BigDecimal("500")) == 0) {
                                cb.setGateRecharge(new BigDecimal("500"));
                                Wallet w = walletService.findByUserId(cb.getUserId());
                                w.setSilverActiveCodeNum(w.getSilverActiveCodeNum() + 1);
                                //流水
                                Long accountId = Long.valueOf(sysConfigService.selectConfigByKey("company_account_id"));
                                TxnDtlCode code = new TxnDtlCode();
                                code.setUserId(cb.getUserId());
                                code.setTxnType(TxnType.SYSTEM_SEND.getValue());
                                code.setTxnDesc(TxnType.SYSTEM_SEND.getName());
                                code.setFromUserId(accountId);
                                code.setToUserId(cb.getUserId());
                                code.setTxnNum(1);
                                code.setSilverCodeBalance(w.getSilverActiveCodeNum());
                                code.setGoldenCodeBalance(w.getGoldenActiveCodeNum());
                                code.setGoldenBlackCodeBalance(w.getGoldenblackActiveCodeNum());
                                code.setWhiteCodeBalance(w.getWhiteActiveCodeNum());
                                code.setTxnStatus(TxnStatus.SUCCESS.getValue());
                                code.setCodeType("01");
                                txnService.dealTxnForCode(code);

                                //新增卡
                                walletService.updateCode(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                                        , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                                        , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                                        , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());


                            } else if (new BigDecimal(orderAmount).compareTo(new BigDecimal("2000")) == 0) {
                                cb.setGateRecharge(new BigDecimal("2000"));
                                Wallet w = walletService.findByUserId(cb.getUserId());
                                w.setGoldenActiveCodeNum(w.getGoldenActiveCodeNum() + 1);
                                //流水
                                Long accountId = Long.valueOf(sysConfigService.selectConfigByKey("company_account_id"));
                                TxnDtlCode code = new TxnDtlCode();
                                code.setUserId(cb.getUserId());
                                code.setTxnType(TxnType.SYSTEM_SEND.getValue());
                                code.setTxnDesc(TxnType.SYSTEM_SEND.getName());
                                code.setFromUserId(accountId);
                                code.setToUserId(cb.getUserId());
                                code.setTxnNum(1);
                                code.setSilverCodeBalance(w.getSilverActiveCodeNum());
                                code.setGoldenCodeBalance(w.getGoldenActiveCodeNum());
                                code.setGoldenBlackCodeBalance(w.getGoldenblackActiveCodeNum());
                                code.setWhiteCodeBalance(w.getWhiteActiveCodeNum());
                                code.setTxnStatus(TxnStatus.SUCCESS.getValue());
                                code.setCodeType("02");
                                txnService.dealTxnForCode(code);

                                //新增卡
                                walletService.updateCode(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                                        , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                                        , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                                        , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());
                            }
                        } else if (new BigDecimal(orderAmount).compareTo(new BigDecimal("5000")) == 0) {
                            cb.setGateRecharge(new BigDecimal("5000"));
                            Wallet w = walletService.findByUserId(cb.getUserId());
                            w.setGoldenActiveCodeNum(w.getGoldenActiveCodeNum() + 2);
                            //流水
                            Long accountId = Long.valueOf(sysConfigService.selectConfigByKey("company_account_id"));
                            TxnDtlCode code = new TxnDtlCode();
                            code.setUserId(cb.getUserId());
                            code.setTxnType(TxnType.SYSTEM_SEND.getValue());
                            code.setTxnDesc(TxnType.SYSTEM_SEND.getName());
                            code.setFromUserId(accountId);
                            code.setToUserId(cb.getUserId());
                            code.setTxnNum(2);
                            code.setSilverCodeBalance(w.getSilverActiveCodeNum());
                            code.setGoldenCodeBalance(w.getGoldenActiveCodeNum());
                            code.setGoldenBlackCodeBalance(w.getGoldenblackActiveCodeNum());
                            code.setWhiteCodeBalance(w.getWhiteActiveCodeNum());
                            code.setTxnStatus(TxnStatus.SUCCESS.getValue());
                            code.setCodeType("02");
                            txnService.dealTxnForCode(code);

                            //新增卡
                            walletService.updateCode(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                                    , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                                    , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                                    , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());
                        }

                        coinBalanceService.updateCoinBalance(cb);
                    } else {
                        logger.info("订单已成交");

                    }


                    return ResponseEntity.ok(success);
                }

            } else {
                logger.info("签名验证失败");
                return ResponseEntity.ok(error);
            }


        } catch (InvalidKeyException e) {
            System.err.println("InvalidKeyException: " + e.getMessage());
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            System.err.println("NoSuchAlgorithmException: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            bizRedisLock.unlocks(bizId, Constants.redisLock.TXID);
        }
        return ResponseEntity.ok(success);

    }


//    public static void main(String[] args) throws Exception {
//        GateUtil gateUtil = new GateUtil();
//        String timeStamp = "1673613945439";
//        String nonce = "3133420233";
//        String body = "{\"code\":\"ac8B7Pl7C-XgfH6zxtd3SidYt7XIfWKU\",\"grant_type\":\"authorization_code\",\"redirect_uri\":\"https://gate.bytetopup.com\",\"client_id\":\"2Ugf9YGMCFRk85Yy\"}";
//        String data = String.format("%s\n%s\n%s\n", timeStamp, nonce, body);
//        String key = "mHghi1Dq0rTX5jwCTe-rkDgMdNLZ1jmqPBBSBMqfutQ=";
//        String hmac = gateUtil.calculateHMAC(data, key);
//        System.out.println(hmac);
//
//
//        long utcTimestampMillis = Instant.now().toEpochMilli();
//        // 打印UTC时间戳（毫秒）
//        System.out.println(utcTimestampMillis);
//
//        // 生成30位的随机字符串
//        String randomString = gateUtil.generateRandomString(30);
//        System.out.println(randomString);
//
//
//    }
}
