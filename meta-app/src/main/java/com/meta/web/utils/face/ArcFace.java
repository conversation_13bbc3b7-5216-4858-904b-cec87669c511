package com.meta.web.utils.face;

import com.arcsoft.face.*;
import com.arcsoft.face.enums.DetectMode;
import com.arcsoft.face.enums.DetectOrient;
import com.arcsoft.face.enums.ErrorInfo;
import com.arcsoft.face.toolkit.ImageInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static com.arcsoft.face.toolkit.ImageFactory.getRGBData;

@Component
public class ArcFace {

    //从官网获取
    @Value("${arcFace.appId}")
    private String appId;

    @Value("${arcFace.sdkKeyForWindows}")
    private String sdkKeyForWindows;

    @Value("${arcFace.sdkKeyForLinux}")
    private String sdkKeyForLinux;

    private FaceEngine faceEngine;

    @Value("${arcFace.isAbsolutePath}")
    private boolean isAbsolutePath;

    @Value("${arcFace.sdkPath}")
    private String sdkPath;


//    @PostConstruct
    public void init(){
        if (faceEngine != null){
            return;
        }
        FaceEngine _faceEngine= null;
        if (isAbsolutePath){
            _faceEngine = new FaceEngine(sdkPath);
        }else {
            String path = sdkPath+ (isWindows() ? "windows" : "linux");
            _faceEngine = new FaceEngine(getAbsolutePath(path));
        }
        //激活引擎
        int errorCode =  _faceEngine.activeOnline(appId,isWindows() ? sdkKeyForWindows : sdkKeyForLinux);

        if (errorCode != ErrorInfo.MOK.getValue() && errorCode != ErrorInfo.MERR_ASF_ALREADY_ACTIVATED.getValue()) {
            System.out.println(errorCode);
            System.out.println("引擎激活失败");
        }


        ActiveFileInfo activeFileInfo=new ActiveFileInfo();
        errorCode = _faceEngine.getActiveFileInfo(activeFileInfo);
        if (errorCode != ErrorInfo.MOK.getValue() && errorCode != ErrorInfo.MERR_ASF_ALREADY_ACTIVATED.getValue()) {
            System.out.println(errorCode);
            System.out.println("获取激活文件信息失败");
        }

        //引擎配置
        EngineConfiguration engineConfiguration = new EngineConfiguration();
        engineConfiguration.setDetectMode(DetectMode.ASF_DETECT_MODE_IMAGE);
        engineConfiguration.setDetectFaceOrientPriority(DetectOrient.ASF_OP_ALL_OUT);
        engineConfiguration.setDetectFaceMaxNum(10);
        engineConfiguration.setDetectFaceScaleVal(16);
        //功能配置
        FunctionConfiguration functionConfiguration = new FunctionConfiguration();
        functionConfiguration.setSupportAge(true);
        functionConfiguration.setSupportFace3dAngle(true);
        functionConfiguration.setSupportFaceDetect(true);
        functionConfiguration.setSupportFaceRecognition(true);
        functionConfiguration.setSupportGender(true);
        functionConfiguration.setSupportLiveness(true);
        functionConfiguration.setSupportIRLiveness(true);
        engineConfiguration.setFunctionConfiguration(functionConfiguration);
        _faceEngine.setLivenessParam(0.5f, 0.7f);


        //初始化引擎
        errorCode = _faceEngine.init(engineConfiguration);

        if (errorCode != ErrorInfo.MOK.getValue()) {
            System.out.println(errorCode);
            System.out.println("初始化引擎失败");
        }
        faceEngine = _faceEngine;
    }

    //人脸检测
    public List<FaceInfo> faceDetection(File file) {
        isVirtual();
        //人脸检测
        ImageInfo imageInfo = getRGBData(file);
        List<FaceInfo> faceInfoList = new ArrayList<>();
        faceEngine.detectFaces(imageInfo.getImageData(), imageInfo.getWidth(), imageInfo.getHeight(), imageInfo.getImageFormat(), faceInfoList);
        System.out.println(faceInfoList);
        return faceInfoList;
    }

    //人脸检测
    public List<FaceInfo> faceDetection(byte[] bytes) {
        isVirtual();
        //人脸检测
        ImageInfo imageInfo = getRGBData(bytes);
        if (imageInfo == null){
            throw new RuntimeException("无人脸信息");
        }
        List<FaceInfo> faceInfoList = new ArrayList<>();
        faceEngine.detectFaces(imageInfo.getImageData(), imageInfo.getWidth(), imageInfo.getHeight(), imageInfo.getImageFormat(), faceInfoList);
        return faceInfoList;
    }

    // 特征提取
    public FaceFeature featureExtraction(byte[] bytes,boolean checkLiveness) {
        isVirtual();
        ImageInfo imageInfo = getRGBData(bytes);
        FaceFeature faceFeature = new FaceFeature();
        List<FaceInfo> faceInfoList = faceDetection(bytes);
        if (faceInfoList.size() == 0){
            throw new RuntimeException("人脸检测失败");
        }
        if (checkLiveness){
            getLiveness(getImageInfo(bytes),faceInfoList);
            List<LivenessInfo> livenessInfoList = new ArrayList<>();
            int liveness = faceEngine.getLiveness(livenessInfoList);
            if (liveness == 0 && livenessInfoList.size() > 0 &&  livenessInfoList.get(0).getLiveness() != 1){
                throw new RuntimeException("活体检测失败");
            }
        }
        faceEngine.extractFaceFeature(imageInfo.getImageData(), imageInfo.getWidth(), imageInfo.getHeight(), imageInfo.getImageFormat(), faceInfoList.get(0), faceFeature);
        System.out.println("特征值大小：" + faceFeature.getFeatureData().length);
        return faceFeature;
    }

    /**
     * 特征对比
     * @param target 特征数据1
     * @param source 特征数据2
     * @return  FaceSimilar 相似度
     */
    public FaceSimilar compareFaceFeature(byte[] target, byte[] source) {
        isVirtual();
        FaceFeature targetFaceFeature = new FaceFeature();
        targetFaceFeature.setFeatureData(target);
        FaceFeature sourceFaceFeature = new FaceFeature();
        sourceFaceFeature.setFeatureData(source);
        FaceSimilar faceSimilar = new FaceSimilar();
        faceEngine.compareFaceFeature(targetFaceFeature, sourceFaceFeature, faceSimilar);
        System.out.println("相似度：" + faceSimilar.getScore());
        return faceSimilar;
    }

    /**
     * 设置活体检测
     * @param rgbThreshold RGB 阈值
     * @param irThreshold 红外阈值
     */
    public void setLivenessParam(float rgbThreshold, float irThreshold) {
        isVirtual();
        faceEngine.setLivenessParam(rgbThreshold, irThreshold);
    }

    /**
     * 人脸属性检测
     */
    public void getFaceAttribute(ImageInfo imageInfo, List<FaceInfo> faceInfoList) {
        isVirtual();
        //人脸属性检测
        FunctionConfiguration configuration = new FunctionConfiguration();
        configuration.setSupportAge(true);
        configuration.setSupportFace3dAngle(true);
        configuration.setSupportGender(true);
        configuration.setSupportLiveness(true);
        faceEngine.process(imageInfo.getImageData(), imageInfo.getWidth(), imageInfo.getHeight(), imageInfo.getImageFormat(), faceInfoList, configuration);
    }

    /**
     * 性别检测
     */
    public List<GenderInfo> getGender(List<GenderInfo> genderInfoList) {
        isVirtual();
        faceEngine.getGender(genderInfoList);
        return genderInfoList;
    }

    /**
     * 年龄检测
     */
    public List<AgeInfo> getAge(List<AgeInfo> ageInfoList) {
        isVirtual();
        faceEngine.getAge(ageInfoList);
        return ageInfoList;
    }

    /**
     * 3D信息检测
     */
    public List<Face3DAngle> getFace3DAngle(List<Face3DAngle> face3DAngleList) {
        isVirtual();
        faceEngine.getFace3DAngle(face3DAngleList);
        return face3DAngleList;
    }

    /**
     * 活体检测
     */
    public List<LivenessInfo> getLiveness(ImageInfo imageInfo, List<FaceInfo> faceInfoList) {
        isVirtual();
        getFaceAttribute(imageInfo, faceInfoList);
        List<LivenessInfo> livenessInfoList = new ArrayList<>();
        faceEngine.getLiveness(livenessInfoList);
        return livenessInfoList;
    }


    // 判断操作系统类型
    public static boolean isWindows() {
        return System.getProperty("os.name").toLowerCase().contains("windows");
    }

    // 获取项目的绝对路径 加上 SDK路径
    public String getAbsolutePath(String path) {
        String basePath = new File("").getAbsolutePath();
        return basePath+path;
    }


    public ImageInfo getImageInfo(byte[] bytes) {
        return getRGBData(bytes);
    }

    /**
     * 检测引擎是否虚拟化
     */
    public void isVirtual() {
        if (faceEngine == null) {
           init();
        }
    }

}