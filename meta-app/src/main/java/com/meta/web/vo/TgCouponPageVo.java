package com.meta.web.vo;

import com.meta.common.valid.TgYz;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2025/01/08/16:39
 */
@Data
public class TgCouponPageVo {

    @NotEmpty(groups = {TgYz.class}, message = "id cannot be empty")
    private String id; //tg的id
    private String md5sum; //加密内容
    @NotEmpty(groups = {TgYz.class}, message = "channel cannot be empty")
    private String channel; //渠道

    private Integer pageSize;//每页大小
    private Integer pageNum;//页码

    private String couponType;// 类型(D-折扣券，M-满减券)
    private String couponScenarios;// 用途(NEW-开卡，TOPUP-充值)
    private String couponCardType;//  卡类型
    private String couponCardlevel;//  卡等级（01-银卡，02-金卡）
    private Long cardCfgId;// 卡配置id
    private String coin;// 币种

}
