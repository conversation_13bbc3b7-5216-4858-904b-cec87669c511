package com.meta.web.vo;

import com.meta.common.valid.TgYz;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/12/20/16:06
 */
@Data
public class TgVo {
    @NotEmpty(groups = {TgYz.class}, message = "id cannot be empty")
    private String id; //tg的id
    private String md5sum; //加密内容
    @NotEmpty(groups = {TgYz.class}, message = "channel cannot be empty")
    private String channel; //渠道

    private String cardId;//卡id

    private String code;

    private String cardAlias;//别名
}
