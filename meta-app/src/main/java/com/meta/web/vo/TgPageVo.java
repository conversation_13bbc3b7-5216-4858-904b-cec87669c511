package com.meta.web.vo;

import com.meta.common.valid.TgYz;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/12/20/16:06
 */
@Data
public class TgPageVo {
    @NotEmpty(groups = {TgYz.class}, message = "id cannot be empty")
    private String id; //tg的id
    private String md5sum; //加密内容
    @NotEmpty(groups = {TgYz.class}, message = "channel cannot be empty")
    private String channel; //渠道

    private Integer pageSize;//每页大小
    private Integer pageNum;//页码

    private String coinNet;//网络
    private String userName;//用户名
    private String startDate;//开始时间
    private String endDate;//结束时间
    private String txnId;//交易id
    private String cardId;//卡交易id
    private String type;


}
