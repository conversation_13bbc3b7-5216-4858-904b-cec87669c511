package com.meta.web.vo;

import com.meta.common.valid.TgYz;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/12/20/19:47
 */
@Data
public class TgApplyCard {
    @NotEmpty(groups = {TgYz.class}, message = "id cannot be empty")
    private String id; //id
    private String md5sum; //加密
    @NotEmpty(groups = {TgYz.class}, message = "channel cannot be empty")
    private String channel; //渠道
    @NotEmpty(groups = {TgYz.class}, message = "cardCfgId cannot be empty")
    private String cardCfgId; //卡配置id
    @NotEmpty(groups = {TgYz.class}, message = "ctype cannot be empty")
    private String ctype; //结算方式
    private String email; //邮箱
    @NotEmpty(groups = {TgYz.class}, message = "firstName cannot be empty")
    private String firstName; //名
    @NotEmpty(groups = {TgYz.class}, message = "lastName cannot be empty")
    private String lastName; //姓
    @NotEmpty(groups = {TgYz.class}, message = "mobilePrefix cannot be empty")
    private String mobilePrefix; //手机号前缀
    @NotNull(groups = {TgYz.class}, message = "mobile cannot be empty")
    private String mobile; //手机号

    @NotEmpty(groups = {TgYz.class}, message = "code cannot be empty")
    private String code; //邮箱验证码

    @NotEmpty(groups = {TgYz.class}, message = "cardLevelType cannot be empty")
    private String cardLevelType; //使用激活币类型

    private String couponNm; //券码


}
