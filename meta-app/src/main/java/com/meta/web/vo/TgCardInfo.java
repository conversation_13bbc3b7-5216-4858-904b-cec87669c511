package com.meta.web.vo;

import com.meta.common.valid.TgYz;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/12/20/11:26
 */
@Data
public class TgCardInfo {
    @NotEmpty(groups = {TgYz.class}, message = "id cannot be empty")
    private String id;
    @NotEmpty(groups = {TgYz.class}, message = "channel cannot be empty")
    private String channel; //渠道

    private String md5sum; //加密内容

    private String type; //类型（虚卡/实卡）

    private String cardType;//卡片类型


}
