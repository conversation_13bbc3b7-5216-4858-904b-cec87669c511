package com.meta.web.vo;

import com.meta.common.valid.TgYz;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/12/20/10:28
 */
@Data
public class CoinWalletVo {
    @NotEmpty(groups = {TgYz.class}, message = "id cannot be empty")
    private String id;
    @NotEmpty(groups = {TgYz.class}, message = "channel cannot be empty")
    private String channel; //渠道

    private String md5sum; //加密内容

    @NotEmpty(groups = {TgYz.class}, message = "coin cannot be empty")
    private String coin; //币种

    @NotEmpty(groups = {TgYz.class}, message = "network cannot be empty")
    private String network; //网络
}
