package com.meta.web.service.impl;

import cn.hutool.core.lang.UUID;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meta.common.config.RedisLock;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.domain.entity.SysDictData;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.SnowflakeIdGenerator;
import com.meta.common.utils.StringUtils;
import com.meta.system.constant.Constants;
import com.meta.system.dao.*;
import com.meta.system.domain.Wallet;
import com.meta.system.domain.app.MetaWelloRequestLog;
import com.meta.system.service.ISysUserService;
import com.meta.system.service.WalletService;
import com.meta.system.service.impl.SysConfigServiceImpl;
import com.meta.system.service.impl.SysDictDataServiceImpl;
import com.meta.system.uitls.RedisConstants;
import com.meta.system.vo.MetaExchangeOrderInfoVo;
import com.meta.system.config.WelloConfig;
import com.meta.system.dto.wello.WelloOrderDto;
import com.meta.system.dto.wello.WelloOrderSelectDto;
import com.meta.system.dto.wello.WelloPlaceOrderDto;
import com.meta.system.dto.wello.WelloQuoteDto;
import com.meta.system.domain.app.MetaExchangeOrderInfo;
import com.meta.system.domain.app.MetaWelloLog;
import com.meta.web.enums.TgCode;
import com.meta.web.service.WelloService;
import com.meta.system.uitls.WelloUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/14
 */

@Service
@Slf4j
public class WelloServiceImpl implements WelloService {

    @Autowired
    private SysConfigServiceImpl sysConfigService;
    @Autowired
    private WelloUtils welloUtils;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private MetaExchangeOrderInfoDao metaExchangeOrderInfoDao;
    @Autowired
    private WalletService walletService;
    @Autowired
    private MetaWelloLogDao metaWelloLogDao;
    @Autowired
    private SysDictDataServiceImpl sysDictDataService;
    @Autowired
    private ISysUserService iSysUserService;
    @Autowired
    private MetaWelloRequestLogDao metaWelloRequestLogDao;
    @Autowired
    private RedisLock redisLock;

    /**
     * 获取交易对列表
     * @return
     */
    @Override
    @Transactional
    public AjaxResult getTradingPairList() {
        String tradingPair = redisTemplate.opsForValue().get(Constants.WELLO_TRADING_PAIR_KEY);
        if (StringUtils.isNotEmpty(tradingPair)) {
            return AjaxResult.success(JSONUtil.parseArray(tradingPair));
        }
        log.info("------正在获取wello交易对列表------");
        RestTemplate restTemplate = new RestTemplate();
        String url = sysConfigService.selectConfigByKey("wello_trading_pair_url");

        HttpHeaders httpHeaders = welloUtils.createHttpHeaders(null);

        HttpEntity<String> entity = new HttpEntity<>(httpHeaders);

        try{
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            JSONObject body = JSONUtil.parseObj(response.getBody());
            if (body.get("success").equals(true)) {
                List<Map<String, Object>> result = new ArrayList<>();
                JSONObject data = body.getJSONObject("data");
                System.out.println("data: " + data);
                JSONArray buySelectors = data.getJSONArray("buySelectors");
                for (Object o : buySelectors) {
                    JSONObject object = (JSONObject) o;
                    // 法定货币资产
                    JSONObject fiatAsset = object.getJSONObject("fiatAsset");
                    // 货币代码
                    String assetCode = fiatAsset.getStr("assetCode");
                    Map<String, Object> map = new HashMap<>();
                    map.put("legalTenderCode", assetCode);
                    // 加密货币资产列表
                    List<Map<String, Object>> cryptocurrencyList = new ArrayList<>();

                    // USDT币种
                    Map<String, Object> USDTCurrency = new HashMap<>();
                    // USDT网络列表
                    List<Map<String, Object>> USDTNetworkList = new ArrayList<>();
                    // USDC币种
                    Map<String, Object> USDCCurrency = new HashMap<>();
                    // USDC网络列表
                    List<Map<String, Object>> USDCNetworkList = new ArrayList<>();
//                    // USDC币种
//                    Map<String, Object> USDCCurrency = new HashMap<>();
//                    // USDC网络列表
//                    List<Map<String, Object>> USDCNetworkList = new ArrayList<>();

                    JSONArray cryptoSelectors = object.getJSONArray("cryptoSelectors");
                    for (Object c : cryptoSelectors) {
                        JSONObject crypto = (JSONObject) c;
                        JSONObject asset = crypto.getJSONObject("asset");
                        // 加密货币代码
                        String assetCode1 = asset.getStr("assetCode");
                        if (crypto.get("enabled").equals(true) && assetCode1.equals("USDT")){
                            // 加密货币资产
                            Map<String, Object> network = new HashMap<>();
                            network.put("network", crypto.get("network"));
                            network.put("minLimit", crypto.get("minLimit"));
                            network.put("maxLimit", crypto.get("maxLimit"));
                            USDTNetworkList.add(network);
                        }
                    if (crypto.get("enabled").equals(true) && assetCode1.equals("USDC")){
                        // 加密货币资产
                        Map<String, Object> network = new HashMap<>();
                        network.put("network", crypto.get("network"));
                        network.put("minLimit", crypto.get("minLimit"));
                        network.put("maxLimit", crypto.get("maxLimit"));
                        USDCNetworkList.add(network);
                    }
//                    if (crypto.get("enabled").equals(true) && assetCode1.equals("USDC")){
//                        // 加密货币资产
//                        Map<String, Object> network = new HashMap<>();
//                        network.put("network", crypto.get("network"));
//                        network.put("minLimit", crypto.get("minLimit"));
//                        network.put("maxLimit", crypto.get("maxLimit"));
//                        USDCNetworkList.add(network);
//                    }

                    }
                    USDTCurrency.put("currencyCode", "USDT");
                    USDTCurrency.put("network", USDTNetworkList);
                    USDCCurrency.put("currencyCode", "USDC");
                    USDCCurrency.put("network", USDCNetworkList);
//                    USDCCurrency.put("currencyCode", "USDC");
//                    USDCCurrency.put("network", USDCNetworkList);


                    cryptocurrencyList.add(USDTCurrency);
                    cryptocurrencyList.add(USDCCurrency);
//                    cryptocurrencyList.add(USDCCurrency);
                    map.put("cryptocurrency", cryptocurrencyList);
                    result.add(map);

                }

                // 过滤掉 network 为空的对象
                List<Map<String, Object>> filteredResult = result.stream()
                        .filter(WelloServiceImpl::hasValidNetwork)
                        .collect(Collectors.toList());

                redisTemplate.opsForValue().set(Constants.WELLO_TRADING_PAIR_KEY, JSONUtil.toJsonStr(filteredResult));

                return AjaxResult.success(filteredResult);
            }
        } catch (HttpStatusCodeException e) {
            JSONObject object = JSONUtil.parseObj(e.getResponseBodyAsString());
            saveWelloRequestLog("headers:" + httpHeaders.toString(), object.toString(), url, object.getStr("code"), object.getStr("message"));
            return AjaxResult.error(object.getStr("message"));
        }
        return null;
    }

    /**
     * 判断数据是否包含有效的 network
     */
    public static boolean hasValidNetwork(Map<String, Object> data) {
        List<Map<String, Object>> cryptocurrencies = (List<Map<String, Object>>) data.get("cryptocurrency");
        if (cryptocurrencies != null) {
            for (Map<String, Object> crypto : cryptocurrencies) {
                List<Map<String, Object>> network = (List<Map<String, Object>>) crypto.get("network");
                if (network != null && !network.isEmpty()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取报价
     * @param welloQuoteDto
     * @return
     */
    @Override
    @Transactional
    public AjaxResult getQuote(WelloQuoteDto welloQuoteDto) {
        log.info("wello获取报价参数: {}", welloQuoteDto);
        RestTemplate restTemplate = new RestTemplate();
        String url = sysConfigService.selectConfigByKey("wello_quote_url")
                + "?side=" + welloQuoteDto.getSide()
                + "&merchantCode=" + WelloConfig.merchantCode
                + "&cryptoCurrency=" + welloQuoteDto.getCryptoCurrency()
                + "&network=" + Constants.WELLO_NETWORK
                + "&fiatCurrency=" + welloQuoteDto.getFiatCurrency()
                + "&requestCurrency=" + welloQuoteDto.getRequestCurrency()
                + "&requestAmount=" + welloQuoteDto.getRequestAmount();


        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("side", welloQuoteDto.getSide());
        requestBody.put("merchantCode", WelloConfig.merchantCode);
        requestBody.put("cryptoCurrency", welloQuoteDto.getCryptoCurrency());
        requestBody.put("network", Constants.WELLO_NETWORK);
        requestBody.put("fiatCurrency", welloQuoteDto.getFiatCurrency());
        requestBody.put("requestCurrency", welloQuoteDto.getRequestCurrency());
        requestBody.put("requestAmount", welloQuoteDto.getRequestAmount());

        if (welloQuoteDto.getPaymentMethodType() != null && !welloQuoteDto.getPaymentMethodType().isEmpty()) {
            requestBody.put("paymentMethodType", welloQuoteDto.getPaymentMethodType());
            url += "&paymentMethodType=" + welloQuoteDto.getPaymentMethodType();
        }

        HttpHeaders httpHeaders = welloUtils.createHttpHeaders(requestBody);
        log.info("------获取wello报价请求参数: {}-------", requestBody);

        HttpEntity<String> entity = new HttpEntity<>(httpHeaders);
        try {
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            JSONObject body = JSONUtil.parseObj(response.getBody());
            log.info("获取wello报价返回body: {}", body);
            if (body.get("success").equals(true)) {
                JSONObject data = body.getJSONObject("data");
                JSONObject fees = data.getJSONObject("fees");
                Map<String, Object> result = new HashMap<>();
                if (!welloQuoteDto.getRequestCurrency().equals("USDT") || !welloQuoteDto.getCryptoCurrency().equals("USDC")) {
                    result.put("requestAmount", welloQuoteDto.getRequestAmount());
                    result.put("totalQuoteAmount", welloQuoteDto.getRequestAmount());
                } else {
                    result.put("requestAmount", data.get("quoteAmount"));
                    // 计算总报价  数量 * 单价 + 手续费
                    BigDecimal totalQuoteAmount = new BigDecimal(data.get("quoteAmount").toString())
                            .multiply(new BigDecimal(data.get("quotePrice").toString()))
                            .add(new BigDecimal(fees.get("total").toString()))
                            .setScale(2, RoundingMode.FLOOR);

                    result.put("totalQuoteAmount", totalQuoteAmount.toString());
            }
                result.put("quoteId", data.get("quoteId"));
                result.put("quotePrice", data.get("quotePrice"));
                result.put("validUntil", data.get("validUntil"));
                result.put("fiatFee", fees.get("fiatFee"));
                result.put("tradeFee", fees.get("tradeFee"));
                result.put("merchantFee", fees.get("merchantFee"));
                result.put("networkFee", fees.get("networkFee"));
                result.put("total", fees.get("total"));
                result.put("requestCurrency", welloQuoteDto.getRequestCurrency());
                result.put("cryptoCurrency", welloQuoteDto.getCryptoCurrency());
                result.put("fiatCurrency", welloQuoteDto.getFiatCurrency());
                result.put("side", welloQuoteDto.getSide());

                log.info("------返回给前端的报价数据: {}------", result);
                return AjaxResult.success(result);
            }
        } catch (HttpStatusCodeException e) {
            JSONObject object = JSONUtil.parseObj(e.getResponseBodyAsString());
            saveWelloRequestLog("headers:" + httpHeaders.toString() + " requestBody:" + requestBody, object.toString(), url, object.getStr("code"), object.getStr("message"));
            log.error("获取wello报价失败,返回body: {}", object);
            return AjaxResult.error(object.getStr("message"));
        }
        return null;
    }

    /**
     * 下订单
     * @param welloPlaceOrderDto
     * @return
     */
    @Override
    @Transactional
    public AjaxResult placeAnOrder(WelloPlaceOrderDto welloPlaceOrderDto) {
        log.info("wello下订单参数: {}", welloPlaceOrderDto);
        if (welloPlaceOrderDto.getRequestAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return AjaxResult.error("请求金额必须大于0");
        }

        Long userId = SecurityUtils.getLoginUser().getUserId();
//        Long userId = 10000639L;
        Wallet w = walletService.findByUserId(userId);
        if (w == null) {
            log.info("用户不存在2");
            return AjaxResult.error(TgCode.USERERROR.getCode(), TgCode.USERERROR.getDesc());
        }

        String configWMA = WelloConfig.walletAddress;
        String redisMWA = redisTemplate.opsForValue().get(Constants.WELLO_MERCHANT_WALLET_ADDRESS_KEY);
        if (redisMWA == null || redisMWA.isEmpty()) {
            redisTemplate.opsForValue().set(Constants.WELLO_MERCHANT_WALLET_ADDRESS_KEY, configWMA);
            redisMWA = configWMA;
        }
        String dbMWA = sysConfigService.selectConfigByKey("wello_merchant_wallet_address");

        log.info("商户钱包地址匹配检查 - Redis: {}, DB: {}, Config: {}", redisMWA, dbMWA, configWMA);
        boolean allEqual = Objects.equals(redisMWA, dbMWA) && Objects.equals(dbMWA, configWMA);
        if (!allEqual) {
            log.error("商户钱包地址配置不一致");
            return AjaxResult.error(MessageUtils.message("common.system.error"));
        }

        // 加锁
        Boolean lock = redisLock.lock(Constants.WELLO_ORDER_LOCK_KEY + userId, 15000);
        if (!lock){
            log.info("wello------下订单失败,获取锁失败,用户ID: {}", userId);
            return AjaxResult.error(MessageUtils.message("common.system.error"));
        }

        List<MetaExchangeOrderInfo> metaExchangeOrderInfoList = metaExchangeOrderInfoDao.getPendingOrderSubmissionByUserId(userId);
        int limit = Integer.parseInt(sysConfigService.selectConfigByKey("wello_order_limit"));
        if (metaExchangeOrderInfoList.size() >= limit){
            log.info("用户可下订单已达上限,用户ID: {}", w.getUserId());
            return AjaxResult.error(MessageUtils.message("exchange.wello.order.limit.error"));
        }


        RestTemplate restTemplate = new RestTemplate();
        String url = sysConfigService.selectConfigByKey("wello_place_order_url");
        String redirectURL = welloPlaceOrderDto.getRedirectPrefix() + sysConfigService.selectConfigByKey("wello_redirect_url");
        String token = UUID.randomUUID().toString().replace("-", "") + System.currentTimeMillis();
        redirectURL += "&token=" + token;

        Map<String, Object> requestBody = new HashMap<>();

        List<Map<String, Object>> walletAddresses = new ArrayList<>();
        Map<String, Object> walletAddress = new LinkedHashMap<>();
        walletAddress.put("network", Constants.WELLO_NETWORK);
        walletAddress.put("address", redisMWA);
        walletAddresses.add(walletAddress);

        requestBody.put("idempotenceKey", UUID.randomUUID().toString().replace("-", "") + System.currentTimeMillis());
        requestBody.put("merchantCode", WelloConfig.merchantCode);
        requestBody.put("side", welloPlaceOrderDto.getSide());
//        requestBody.put("merchantUID", "");
        long merchantOrderId = SnowflakeIdGenerator.nextId();
        requestBody.put("merchantOrderId", merchantOrderId);
        requestBody.put("cryptoCurrency", welloPlaceOrderDto.getCryptoCurrency());
        requestBody.put("cryptoNetwork", Constants.WELLO_NETWORK);
        requestBody.put("fiatCurrency", welloPlaceOrderDto.getFiatCurrency());
        requestBody.put("requestCurrency", welloPlaceOrderDto.getRequestCurrency());
        requestBody.put("requestAmount", welloPlaceOrderDto.getRequestAmount());
        requestBody.put("walletAddresses", walletAddresses);
        if (!welloPlaceOrderDto.getRedirectPrefix().equals("app")) requestBody.put("redirectURL", redirectURL);
        log.info("wello------下订单请求参数: {}", requestBody);

        HttpHeaders httpHeaders = welloUtils.createHttpHeaders(requestBody);
        log.info("wello------下订单请求头: {}", httpHeaders);

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, httpHeaders);

        try {
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            JSONObject body = JSONUtil.parseObj(response.getBody());
            log.info("wello------下订单返回body: {}", body);

            saveWelloRequestLog("headers:" + httpHeaders.toString() + " requestBody:" + requestBody, body.toString(), url, body.getStr("code"), body.getStr("message"));

            if (body.get("success").equals(true)) {
                JSONObject data = body.getJSONObject("data");
                // 付款地址
                String checkoutUrl = data.getStr("checkoutUrl");

                String url2 = sysConfigService.selectConfigByKey("wello_order_details_url")
                        + "?merchantOrderId=" + merchantOrderId;
                Map<String, Object> requestBody2 = new HashMap<>();
                requestBody2.put("merchantOrderId", merchantOrderId);
                HttpHeaders httpHeader2 = welloUtils.createHttpHeaders(requestBody2);
                HttpEntity<Map<String, Object>> entity2 = new HttpEntity<>(httpHeader2);
                ResponseEntity<String> response2 = restTemplate.exchange(url2, HttpMethod.GET, entity2, String.class);
                JSONObject body2 = JSONUtil.parseObj(response2.getBody());
                log.info("wello------下订单后的订单详情body: {}", body2);
                if (body2.get("success").equals(true)) {
                    // 保存货币订单和货币订单记录
                    ObjectMapper objectMapper = new ObjectMapper();
                    WelloOrderDto welloOrderDto;
                    try {
                        welloOrderDto = objectMapper.readValue(body2.getStr("data"), WelloOrderDto.class);
                        log.info("wello------赋值到welloOrderDto: {}", welloOrderDto);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    MetaWelloLog metaWelloLog = new MetaWelloLog();
                    copyPropertiesFromWoDtoToWLog(welloOrderDto, metaWelloLog);
                    metaWelloLog.setSysId("99"); // kazepay为99，后续增加其他平台再修改为动态

                    String cryptoAmount;
                    if (welloPlaceOrderDto.getRequestCurrency().equals(welloPlaceOrderDto.getCryptoCurrency())) {
                        cryptoAmount = welloPlaceOrderDto.getRequestAmount().toString();
                    } else {
                        cryptoAmount = String.valueOf(welloPlaceOrderDto.getRequestAmount()
                                .subtract(new BigDecimal(welloPlaceOrderDto.getTotal()))
                                .divide(new BigDecimal(welloPlaceOrderDto.getQuotePrice()), 2, RoundingMode.FLOOR));
                    }

                    MetaExchangeOrderInfo metaExchangeOrderInfo = new MetaExchangeOrderInfo();
                    copyPropertiesFromWlogToEOInfo(metaWelloLog, metaExchangeOrderInfo);
                    metaExchangeOrderInfo.setTotalQuoteAmount(welloPlaceOrderDto.getTotalQuoteAmount());
                    metaExchangeOrderInfo.setCheckoutUrl(checkoutUrl);
                    metaExchangeOrderInfo.setUserId(userId);
                    metaExchangeOrderInfo.setSysId("99"); // kazepay为99，后续增加其他平台再修改为动态
                    metaExchangeOrderInfo.setOrderTime(metaWelloLog.getCreatedAt());
                    metaExchangeOrderInfo.setFiatFee(welloPlaceOrderDto.getFiatFee());
                    metaExchangeOrderInfo.setTradeFee(welloPlaceOrderDto.getTradeFee());
                    metaExchangeOrderInfo.setMerchantFee(welloPlaceOrderDto.getMerchantFee());
                    metaExchangeOrderInfo.setNetworkFee(welloPlaceOrderDto.getNetworkFee());
                    metaExchangeOrderInfo.setTotal(welloPlaceOrderDto.getTotal());
                    metaExchangeOrderInfo.setQuotePrice(welloPlaceOrderDto.getQuotePrice());
                    metaExchangeOrderInfo.setDelFlag("exist");
                    metaExchangeOrderInfo.setNetwork(Constants.WELLO_NETWORK);
                    metaExchangeOrderInfo.setCryptoAmount(cryptoAmount);
                    String orderStatusDetails = sysDictDataService.getLabel("coin_purchase_order_status", metaExchangeOrderInfo.getOrderStatus());
                    metaExchangeOrderInfo.setOrderStatusDetail(orderStatusDetails);

                    log.info("wello------插入metaWelloLog表: {}", metaWelloLog);
                    log.info("wello------插入metaExchangeOrderInfo表: {}", metaExchangeOrderInfo);

                    metaExchangeOrderInfoDao.save(metaExchangeOrderInfo);
                    metaWelloLogDao.save(metaWelloLog);

                    redisTemplate.opsForValue().set(Constants.WELLO_CHECKOUT_TOKEN_KEY + token,
                            userId + ":" + metaExchangeOrderInfo.getMerchantOrderId(), 30, TimeUnit.MINUTES);

                    // 将订单号存入redis，设置过期时间，过期后由 redis监听 处理
                    long timeout = Long.parseLong(sysConfigService.selectConfigByKey("wello_order_timeout"));
                    redisTemplate.opsForValue().set(Constants.WELLO_ORDER_EXPIRATION_KEY + metaExchangeOrderInfo.getMerchantOrderId(),
                            metaExchangeOrderInfo.getMerchantOrderId(), timeout, TimeUnit.HOURS);

                    return AjaxResult.success(metaExchangeOrderInfo);
                }
            } else {
                return AjaxResult.error(body.getStr("message"));
            }
        } catch (HttpStatusCodeException e) {
            JSONObject object = JSONUtil.parseObj(e.getResponseBodyAsString());
            saveWelloRequestLog("headers:" + httpHeaders.toString() + " requestBody:" + requestBody, object.toString(), url, object.getStr("code"), object.getStr("message"));;
            log.error("wello------下订单失败,返回body: {}", object);
            return AjaxResult.error(object.getStr("message"));
        } finally {
            redisLock.unlock(Constants.WELLO_ORDER_LOCK_KEY + userId);
        }
        return null;
    }

    /**
     * 复制属性从 MetaWelloLog 到 MetaExchangeOrderInfo
     * @param metaWelloLog
     * @param metaExchangeOrderInfo
     */
    public void copyPropertiesFromWlogToEOInfo(MetaWelloLog metaWelloLog, MetaExchangeOrderInfo metaExchangeOrderInfo) {
        if (metaWelloLog.getMerchantOrderId() != null) {
            metaExchangeOrderInfo.setMerchantOrderId(metaWelloLog.getMerchantOrderId());
        }
        if (metaWelloLog.getSide() != null) {
            metaExchangeOrderInfo.setSide(metaWelloLog.getSide());
        }
        if (metaWelloLog.getCryptoCurrency() != null) {
            metaExchangeOrderInfo.setCryptoCurrency(metaWelloLog.getCryptoCurrency());
        }
        if (metaWelloLog.getFiatCurrency() != null) {
            metaExchangeOrderInfo.setFiatCurrency(metaWelloLog.getFiatCurrency());
        }
        if (metaWelloLog.getFiatAmount() != null) {
            metaExchangeOrderInfo.setFiatAmount(metaWelloLog.getFiatAmount());
        }
        if (metaWelloLog.getCryptoAmount() != null) {
            metaExchangeOrderInfo.setCryptoAmount(metaWelloLog.getCryptoAmount());
        }
        if (metaWelloLog.getOrderStatus() != null) {
            metaExchangeOrderInfo.setOrderStatus(metaWelloLog.getOrderStatus());
        }
        if (metaWelloLog.getQuotePrice() != null) {
            metaExchangeOrderInfo.setQuotePrice(metaWelloLog.getQuotePrice());
        }
        if (metaWelloLog.getFiatFee() != null) {
            metaExchangeOrderInfo.setFiatFee(metaWelloLog.getFiatFee());
        }
        if (metaWelloLog.getTradeFee() != null) {
            metaExchangeOrderInfo.setTradeFee(metaWelloLog.getTradeFee());
        }
        if (metaWelloLog.getMerchantFee() != null) {
            metaExchangeOrderInfo.setMerchantFee(metaWelloLog.getMerchantFee());
        }
        if (metaWelloLog.getNetworkFee() != null) {
            metaExchangeOrderInfo.setNetworkFee(metaWelloLog.getNetworkFee());
        }
        if (metaWelloLog.getTotal() != null) {
            metaExchangeOrderInfo.setTotal(metaWelloLog.getTotal());
        }
        if (metaWelloLog.getMethod() != null) {
            metaExchangeOrderInfo.setMethod(metaWelloLog.getMethod());
        }
        if (metaWelloLog.getAccountNumber() != null) {
            metaExchangeOrderInfo.setAccountNumber(metaWelloLog.getAccountNumber());
        }
        if (metaWelloLog.getTxId() != null) {
            metaExchangeOrderInfo.setTxId(metaWelloLog.getTxId());
        }
        if (metaWelloLog.getFailCode() != null) {
            metaExchangeOrderInfo.setFailCode(metaWelloLog.getFailCode());
        }
        if (metaWelloLog.getFailReason() != null) {
            metaExchangeOrderInfo.setFailReason(metaWelloLog.getFailReason());
        }
        if (metaWelloLog.getCreatedAt() != null) {
            metaExchangeOrderInfo.setCreatedAt(metaWelloLog.getCreatedAt());
        }
        if (metaWelloLog.getUpdatedAt() != null) {
            metaExchangeOrderInfo.setUpdatedAt(metaWelloLog.getUpdatedAt());
        }
    }

    /**
     * 更新webhookURL
     * @param webhookURL
     * @return
     */
    @Override
    public AjaxResult updateWebhookURL(String webhookURL) {
        RestTemplate restTemplate = new RestTemplate();
        String url = sysConfigService.selectConfigByKey("wello_update_webhook_url");

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("merchantCode", WelloConfig.merchantCode);
        requestBody.put("webhookUrl", webhookURL);

        HttpHeaders httpHeader = welloUtils.createHttpHeaders(requestBody);

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, httpHeader);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.PUT, entity, String.class);
        JSONObject body = JSONUtil.parseObj(response.getBody());
        if (body.get("success").equals(true)) {
            JSONObject data = body.getJSONObject("data");
            // 返回更新后的webhookURL
            String webhookUrl = data.getStr("webhookUrl");
            return AjaxResult.success(null, webhookUrl);
        }
        return null;
    }

    /**
     * 根据条件查询 订单分页列表
     * @param welloOrderSelectDto
     * @return
     */
    @Override
    public Page<MetaExchangeOrderInfoVo> getOrderPage(WelloOrderSelectDto welloOrderSelectDto) {
        // 将日期转换为时间戳进行查询
        if (StringUtils.isNotEmpty(welloOrderSelectDto.getStartDate()) && StringUtils.isNotEmpty(welloOrderSelectDto.getEndDate())) {
            LocalDate localDate = LocalDate.parse(welloOrderSelectDto.getStartDate());
            welloOrderSelectDto.setStartDate(String.valueOf(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()));
            localDate = LocalDate.parse(welloOrderSelectDto.getEndDate()).plusDays(1);
            welloOrderSelectDto.setEndDate(String.valueOf(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()));
        }

        Long userId = SecurityUtils.getLoginUser().getUserId();
//        Long userId = 10000639L;
        welloOrderSelectDto.setUserId(userId);
        SysUser sysUser = iSysUserService.selectUserById(userId);
        String language = sysUser.getLanguage();
        if (StringUtils.isEmpty(language)){
            language = "en";
        }

        SysDictData dictData = new SysDictData();
        dictData.setDictType("coin_purchase_order_status");
        Page<SysDictData> sysDictDataPage = sysDictDataService.selectDictDataList(dictData);
        List<SysDictData> sysDictDataList = sysDictDataPage.getContent();

        String PENDING_ORDER_SUBMISSION = "";
        String PENDING_DEPOSIT = "";
        String PENDING_ENTRY = "";
        String SUCCESS = "";
        String FAILURE = "";
        String PENDING_REFUND = "";

        for (SysDictData sysDictData : sysDictDataList) {
            switch (sysDictData.getDictValue()){
                case "PENDING_ORDER_SUBMISSION" : {
                    if (language.equals("cn")) PENDING_ORDER_SUBMISSION = sysDictData.getDictLabel();
                    else PENDING_ORDER_SUBMISSION = sysDictData.getDictLabelEn();
                    break;
                }
                case "PENDING_DEPOSIT" : {
                    if (language.equals("cn")) PENDING_DEPOSIT = sysDictData.getDictLabel();
                    else PENDING_DEPOSIT = sysDictData.getDictLabelEn();
                    break;
                }
                case "PENDING_ENTRY" : {
                    if (language.equals("cn")) PENDING_ENTRY = sysDictData.getDictLabel();
                    else PENDING_ENTRY = sysDictData.getDictLabelEn();
                    break;
                }
                case "SUCCESS" : {
                    if (language.equals("cn")) SUCCESS = sysDictData.getDictLabel();
                    else SUCCESS = sysDictData.getDictLabelEn();
                    break;
                }
                case "FAILURE" : {
                    if (language.equals("cn")) FAILURE = sysDictData.getDictLabel();
                    else FAILURE = sysDictData.getDictLabelEn();
                    break;
                }
                case "PENDING_REFUND" : {
                    if (language.equals("cn")) PENDING_REFUND = sysDictData.getDictLabel();
                    else PENDING_REFUND = sysDictData.getDictLabelEn();
                    break;
                }
            }
        }

        Page<MetaExchangeOrderInfoVo> orderPage = metaExchangeOrderInfoDao.getOrderPage(welloOrderSelectDto);
        List<MetaExchangeOrderInfoVo> list = orderPage.getContent();
        for (MetaExchangeOrderInfoVo metaExchangeOrderInfoVo : list) {
            switch (metaExchangeOrderInfoVo.getOrderStatus()){
                case "PENDING_ORDER_SUBMISSION" : {
                    metaExchangeOrderInfoVo.setOrderStatusDetails(PENDING_ORDER_SUBMISSION);
                    break;
                }
                case "PENDING_DEPOSIT" : {
                    metaExchangeOrderInfoVo.setOrderStatusDetails(PENDING_DEPOSIT);
                    break;
                }
                case "PENDING_ENTRY" : {
                    metaExchangeOrderInfoVo.setOrderStatusDetails(PENDING_ENTRY);
                    break;
                }
                case "SUCCESS" : {
                    metaExchangeOrderInfoVo.setOrderStatusDetails(SUCCESS);
                    break;
                }
                case "FAILURE" : {
                    metaExchangeOrderInfoVo.setOrderStatusDetails(FAILURE);
                    break;
                }
                case "PENDING_REFUND" : {
                    metaExchangeOrderInfoVo.setOrderStatusDetails(PENDING_REFUND);
                    break;
                }
            }
        }

        return new PageImpl<>(list, orderPage.getPageable(), orderPage.getTotalElements());
    }

    /**
     * 根据订单号取消订单
     * @param welloOrderDto
     * @return
     */
    @Override
    public AjaxResult cancelOrder(WelloOrderDto welloOrderDto) {
        String merchantOrderId = welloOrderDto.getMerchantOrderId();
        if (StringUtils.isEmpty(merchantOrderId)){
            return AjaxResult.error("订单ID不能为空");
        }

        MetaExchangeOrderInfo metaExchangeOrderInfo = metaExchangeOrderInfoDao.findByMerchantOrderId(merchantOrderId);
        if (metaExchangeOrderInfo == null){
            return AjaxResult.error("订单不存在");
        }
        if (!metaExchangeOrderInfo.getOrderStatus().equals("PENDING_ORDER_SUBMISSION")){
            return AjaxResult.error("该订单状态不是待支付");
        }

        metaExchangeOrderInfo.setDelFlag("delete");
        metaExchangeOrderInfoDao.save(metaExchangeOrderInfo);

        redisTemplate.delete(Constants.WELLO_ORDER_EXPIRATION_KEY + welloOrderDto.getMerchantOrderId());

        return AjaxResult.success(true);
    }

    /**
     * 根据商户订单ID获取订单
     * @param welloOrderDto
     * @return
     */
    @Override
    public AjaxResult getOrderByMerchantOrderId(WelloOrderDto welloOrderDto) {
        if (StringUtils.isEmpty(welloOrderDto.getMerchantOrderId())){
            return AjaxResult.error("订单ID不能为空");
        }
        MetaExchangeOrderInfo order = metaExchangeOrderInfoDao.findByMerchantOrderId(welloOrderDto.getMerchantOrderId());
        if (order == null){
            return AjaxResult.error("订单不存在");
        }

        String orderStatusDetails = sysDictDataService.getLabel("coin_purchase_order_status", order.getOrderStatus());
        order.setOrderStatusDetail(orderStatusDetails);


        return AjaxResult.success(order);
    }

    /**
     * 用户从wello跳转回来 验证状态
     * @param welloOrderDto
     * @return
     */
    @Override
    public AjaxResult checkout(WelloOrderDto welloOrderDto) {
        if (StringUtils.isEmpty(welloOrderDto.getToken())){
            return AjaxResult.error("token不能为空");
        }

        Long userId = SecurityUtils.getLoginUser().getUserId();
//        Long userId = 10000639L;
        String token = redisTemplate.opsForValue().get(Constants.WELLO_CHECKOUT_TOKEN_KEY + welloOrderDto.getToken());
        if (token == null || StringUtils.isEmpty(token) || !token.contains(":")){
            return AjaxResult.success(false);
        }
        String[] split = token.split(":");
        String userId1 = split[0];
        String loginToken = redisTemplate.opsForValue().get(com.meta.common.constant.Constants.LOGIN_USERID_KEY + userId1);
        if (StringUtils.isEmpty(userId1) || !Objects.equals(userId1, String.valueOf(userId)) || StringUtils.isEmpty(loginToken)){
            return AjaxResult.success(false);
        }

        return AjaxResult.success(null, split[1]);
    }


    /**
     * 复制属性从 WelloOrderDto 到 MetaWelloLog
     * @param welloOrderDto
     * @param metaWelloLog
     */
    public void copyPropertiesFromWoDtoToWLog(WelloOrderDto welloOrderDto, MetaWelloLog metaWelloLog) {
        if (welloOrderDto.getMerchantOrderId() != null) {
            metaWelloLog.setMerchantOrderId(welloOrderDto.getMerchantOrderId());
        }
        if (welloOrderDto.getWelloPreOrderId() != null) {
            metaWelloLog.setWelloPreOrderId(welloOrderDto.getWelloPreOrderId());
        }
        if (welloOrderDto.getOrderReferenceId() != null) {
            metaWelloLog.setOrderReferenceId(welloOrderDto.getOrderReferenceId());
        }
        if (welloOrderDto.getMerchantCode() != null) {
            metaWelloLog.setMerchantCode(welloOrderDto.getMerchantCode());
        }
        if (welloOrderDto.getSide() != null) {
            metaWelloLog.setSide(welloOrderDto.getSide());
        }
        if (welloOrderDto.getCryptoCurrency() != null) {
            metaWelloLog.setCryptoCurrency(welloOrderDto.getCryptoCurrency());
        }
        if (welloOrderDto.getFiatCurrency() != null) {
            metaWelloLog.setFiatCurrency(welloOrderDto.getFiatCurrency());
        }
        if (welloOrderDto.getRequestCurrency() != null) {
            metaWelloLog.setRequestCurrency(welloOrderDto.getRequestCurrency());
        }
        if (welloOrderDto.getRequestAmount() != null) {
            metaWelloLog.setRequestAmount(welloOrderDto.getRequestAmount());
        }
        if (welloOrderDto.getOrderStatus() != null) {
            metaWelloLog.setOrderStatus(welloOrderDto.getOrderStatus());
        }
        if (welloOrderDto.getQuotePrice() != null) {
            metaWelloLog.setQuotePrice(welloOrderDto.getQuotePrice());
        }
        if (welloOrderDto.getFiatAmount() != null) {
            metaWelloLog.setFiatAmount(welloOrderDto.getFiatAmount());
        }
        if (welloOrderDto.getCryptoAmount() != null) {
            metaWelloLog.setCryptoAmount(welloOrderDto.getCryptoAmount());
        }
        if (welloOrderDto.getFailCode() != null) {
            metaWelloLog.setFailCode(welloOrderDto.getFailCode());
        }
        if (welloOrderDto.getFailReason() != null) {
            metaWelloLog.setFailReason(welloOrderDto.getFailReason());
        }
        if (welloOrderDto.getCreatedAt() != null) {
            metaWelloLog.setCreatedAt(welloOrderDto.getCreatedAt());
        }
        if (welloOrderDto.getUpdatedAt() != null) {
            metaWelloLog.setUpdatedAt(welloOrderDto.getUpdatedAt());
        }
        if (welloOrderDto.getFees() != null) {
            WelloOrderDto.Fees fees = welloOrderDto.getFees();
            if (fees.getFiatFee() != null) {
                metaWelloLog.setFiatFee(fees.getFiatFee());
            }
            if (fees.getTradeFee() != null) {
                metaWelloLog.setTradeFee(fees.getTradeFee());
            }
            if (fees.getMerchantFee() != null) {
                metaWelloLog.setMerchantFee(fees.getMerchantFee());
            }
            if (fees.getNetworkFee() != null) {
                metaWelloLog.setNetworkFee(fees.getNetworkFee());
            }
            if (fees.getTotal() != null) {
                metaWelloLog.setTotal(fees.getTotal());
            }
        }
        if (welloOrderDto.getSettlement() != null) {
            WelloOrderDto.Settlement settlement = welloOrderDto.getSettlement();
            if (settlement.getMerchantSettlementFee() != null) {
                metaWelloLog.setMerchantSettlementFee(settlement.getMerchantSettlementFee());
            }
            if (settlement.getMerchantSettlementCurrency() != null) {
                metaWelloLog.setMerchantSettlementCurrency(settlement.getMerchantSettlementCurrency());
            }
        }
        if (welloOrderDto.getPayment() != null) {
            WelloOrderDto.Payment payment = welloOrderDto.getPayment();
            if (payment.getMethod() != null) {
                metaWelloLog.setMethod(payment.getMethod());
            }
            if (payment.getAccountNumber() != null) {
                metaWelloLog.setAccountNumber(payment.getAccountNumber());
            }
        }
        if (welloOrderDto.getCrypto() != null) {
            WelloOrderDto.Crypto crypto = welloOrderDto.getCrypto();
            if (crypto.getNetwork() != null) {
                metaWelloLog.setNetwork(crypto.getNetwork());
            }
            if (crypto.getAddress() != null) {
                metaWelloLog.setAddress(crypto.getAddress());
            }
            if (crypto.getTxId() != null) {
                metaWelloLog.setTxId(crypto.getTxId());
            }
        }
    }

    /**
     * 保存请求日志
     * @param request
     * @param response
     * @param apicode
     * @param code
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveWelloRequestLog(String request, String response, String apicode, String code, String detail) {
        MetaWelloRequestLog log = new MetaWelloRequestLog();

        SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateTimePart = dateTimeFormat.format(new Date());
        Random random = new Random();
        String serialNumber = String.format("%010d", random.nextInt(999999999));
        String requestNo = dateTimePart + serialNumber;

        log.setRequestNo(requestNo);
        log.setRequest(request);
        log.setResponse(response);
        log.setApicode(apicode);
        log.setCode(code);
        log.setCreatedAt(LocalDateTime.now());
        log.setDetail(detail);
        metaWelloRequestLogDao.save(log);
    }
}
