package com.meta.web.service;

import com.alibaba.fastjson.JSONObject;
import com.meta.common.core.domain.AjaxResult;
import com.meta.system.domain.CreditCard;
import com.meta.system.domain.app.CardConfig;
import com.meta.system.domain.app.VccReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/20/20:03
 */
public interface CreateCardSevice {
    AjaxResult applyCard(VccReq req, Long userId, CardConfig cc);

    AjaxResult recharge(VccReq req);

    List<CreditCard> getCreditCards(Long userId);

    AjaxResult getVccCardNo(VccReq req, boolean flag);
}
