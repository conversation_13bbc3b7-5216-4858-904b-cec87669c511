package com.meta.web.service.impl;

import com.meta.system.dao.app.CardConfigDao;
import com.meta.system.domain.app.CardConfig;
import com.meta.web.service.CardConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CardConfigServiceImpl implements CardConfigService {


    @Autowired
    private CardConfigDao cardConfigDao;

    @Override
    public List<CardConfig> findCardConfigAll() {
        return cardConfigDao.findAll();
    }

    @Override
    public List<CardConfig> findCardEquConvertNum() {
        return cardConfigDao.findCardEquConvertNum();
    }

    @Override
    public CardConfig findCardConfigByCardCfgId(Long cardCfgId) {
        return cardConfigDao.findById(cardCfgId).orElse(null);
    }

    @Override
    public List<String> findFeeBylevel(String level) {
        return cardConfigDao.findFeeBylevel(level);
    }
//    @Override
//    public CardConfig findCardConfigByCardTypeAndCardLevel(String cardType, String cardLevel) {
//        return cardConfigDao.findByCardTypeAndCardLevel(cardType,cardLevel);
//    }

//    @Override
//    public CardConfig findCardConfigByCardTypeAndCardLevelAndChannel(String cardType, String cardLevel, String channel) {
//        return cardConfigDao.findCardConfigByCardTypeAndCardLevelAndChannel(cardType,cardLevel,channel);
//    }


    @Override
    public List<CardConfig> getFiatCardConfig() {
        return  cardConfigDao.getFiatCardConfig();
    }

    @Override
    public CardConfig findCardConfigByCardTypeAndCardLevel(String cardType, String cardLevel) {
        return cardConfigDao.findCardConfigByCardTypeAndCardLevel(cardType,cardLevel);
    }

    /**
     * 通过cardBin查找卡配置
     * @param no
     * @param isB
     * @return
     */
    @Override
    public CardConfig findByCardBin(String no,String isB) {
        return cardConfigDao.findByCardBin(no,isB);
    }
}
