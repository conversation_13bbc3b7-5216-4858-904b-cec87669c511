package com.meta.web.service;

import com.meta.common.core.domain.AjaxResult;
import com.meta.system.domain.app.MetaExchangeOrderInfoAlixpay;
import com.meta.system.dto.AlixpayOrderSelectDto;
import com.meta.system.dto.AlixpayPlaceOrderDto;
import com.meta.system.dto.alixpay.ParseQrContentDto;
import com.meta.system.dto.alixpay.QRCodeInfo;
import com.meta.system.vo.MetaExchangeOrderInfoAlixpayVo;
import org.springframework.data.domain.Page;

/**
 * <AUTHOR>
 * @Date 2025/3/7
 */

public interface AlixpayService {

    AjaxResult placeAnOrder(AlixpayPlaceOrderDto alixpayPlaceOrderDto);

    Page<MetaExchangeOrderInfoAlixpayVo> getOrderPage(AlixpayOrderSelectDto dto);

    AjaxResult getOrderByMerchantOrderId(MetaExchangeOrderInfoAlixpay dto);

    AjaxResult getInformationForQrContentAndVerify(ParseQrContentDto parseQrContentDto);

    AjaxResult calculateOffer(QRCodeInfo qrCodeInfo);
}
