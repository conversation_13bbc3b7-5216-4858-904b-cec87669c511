package com.meta.web.service.impl;

import com.meta.web.dao.UserFaceFeatureDao;
import com.meta.web.entity.UserFaceFeature;
import com.meta.web.service.UserFaceFeatureService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Transactional(readOnly = true)
@Service
public class UserFaceFeatureServiceImpl implements UserFaceFeatureService {
    @Resource
    private UserFaceFeatureDao userFaceFeatureDao;

    /**
     * 保存人脸特征数据
     *
     * @param userFaceFeature
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(UserFaceFeature userFaceFeature) {
        // 判断是否已存在
        UserFaceFeature faceFeature = userFaceFeatureDao.findByUserId(userFaceFeature.getUserId());
        if (faceFeature != null) {
            userFaceFeature.setId(faceFeature.getId());
        }
        userFaceFeatureDao.save(userFaceFeature);
    }

    /**
     * 根据Userid 获取人脸特征数据
     *
     * @param userId 用户id
     * @return 人脸特征值
     */
    @Override
    public UserFaceFeature findByUserId(Long userId) {
        return  userFaceFeatureDao.findByUserId(userId);
    }
}
