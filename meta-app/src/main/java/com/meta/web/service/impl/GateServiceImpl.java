package com.meta.web.service.impl;

import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.StringUtils;
import com.meta.system.domain.app.MetaGatePrePay;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.MetaGatePrePayService;
import com.meta.web.service.GateService;
import com.meta.web.utils.GateUtil;
import com.meta.web.vo.RechangeVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2024/03/27/14:23
 */
@Service
public class GateServiceImpl implements GateService {

    @Autowired
    private MetaGatePrePayService metaGatePrePayService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private GateUtil gateUtil;

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
    private static final AtomicLong SEQUENCE_NUMBER = new AtomicLong(0);

    @Override
    public MetaGatePrePay getPrePayId(RechangeVo rechangeVo) {
        String goodsType = "recharge";
        String goodsName = "钱包充值";
        String goodsDetail = "钱包充值";
        String terminalType = "MINIAPP";
        String merchantTradeNo = generate();
        String appUrl = sysConfigService.selectConfigByKey("gate_to_kz_url");
        String returnUrl = appUrl + "pages/apply/exhibit";
        String prepayID = gateUtil.CreatePayOrder(merchantTradeNo, terminalType, rechangeVo.getCoin(), rechangeVo.getAmount(), goodsType, goodsName, goodsDetail, returnUrl);
        logger.info("=====prepayID=======" + prepayID);
        if (StringUtils.isNotEmpty(prepayID)) {
            logger.info("save");
            MetaGatePrePay metaGatePrePay = new MetaGatePrePay();
            metaGatePrePay.setMerchantTradeNo(merchantTradeNo);
            metaGatePrePay.setUserId(SecurityUtils.getLoginUser().getUserId());
            metaGatePrePay.setTerminalType(terminalType);
            metaGatePrePay.setCurrency(rechangeVo.getCoin());
            metaGatePrePay.setOrderAmount(rechangeVo.getAmount());
            metaGatePrePay.setGoodsType(goodsType);
            metaGatePrePay.setGoodsName(goodsName);
            metaGatePrePay.setGoodsDetail(goodsDetail);
            metaGatePrePay.setPrepayId(prepayID);
            metaGatePrePay.setCreateTime(new Date());
            logger.info("=====metaGatePrePay=======" + metaGatePrePay);
            metaGatePrePayService.save(metaGatePrePay);
            return metaGatePrePay;
        }
        return null;
    }

    /**
     * 订单号
     *
     * @return
     */
    public String generate() {
        // 获取当前时间戳
        String timestamp = LocalDateTime.now().format(DATE_TIME_FORMATTER);

        // 获取递增序列号
        long sequenceNumber = SEQUENCE_NUMBER.incrementAndGet();

        // 返回拼接后的订单号
        return timestamp + String.format("%04d", sequenceNumber);
    }
}
