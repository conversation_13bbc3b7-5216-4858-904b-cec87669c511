package com.meta.web.service;

import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.page.TableDataInfo;
import com.meta.web.vo.*;

/**
 * <AUTHOR>
 * @date 2024/12/18/19:17
 */
public interface TgService {
    AjaxResult sendBindEmail(TgEmailVo tgEmailVo);

    AjaxResult bindEmail(TgEmailVo tgEmailVo);

    AjaxResult isBindEmail(TgEmailVo tgEmailVo);

    AjaxResult getRechargeAddress(CoinWalletVo coinWalletVo);

    AjaxResult getSecnario(TgCardInfo tgCardInfo);

    AjaxResult getCardInfo(TgCardInfo tgCardInfo);

    AjaxResult getUserInfo(TgVo tgVo);

    AjaxResult sendTradeEmail(TgEmailVo tgEmailVo);

    AjaxResult applyCard(TgApplyCard tgApplyCard);

    AjaxResult getTgToken(TgVo vo);

    AjaxResult rechargeCard(TgRechargeVo tgRechargeVo);

    AjaxResult getCardList(TgVo vo);

    AjaxResult getCardDetail(TgVo vo);

    AjaxResult withdrawal(TgDwVo tgDwVo);

    AjaxResult lock(TgVo vo);

    AjaxResult unlock(TgVo vo);

    AjaxResult updateBalance(TgVo vo);

    AjaxResult editAlias(TgVo vo);

    AjaxResult getInviteCode(TgVo vo);

    AjaxResult totalCommission(TgVo vo);

    TableDataInfo teamCommissionDetail(TgPageVo vo);

    TableDataInfo  coinRecord(TgPageVo vo,String code,String recordType);

    AjaxResult coinTxnDetailById(TgPageVo vo);

    TableDataInfo txnlist(TgPageVo vo);

    AjaxResult txnDetail(TgPageVo vo);

    TableDataInfo assetsPage(TgPageVo vo);

    AjaxResult assetsDetail(TgPageVo vo);

    AjaxResult exchange(TgCoinExVo vo);

    TableDataInfo coinFlow(TgPageVo vo);

    AjaxResult getCouponInfo(TgCoupon vo);

    TableDataInfo couponInfoPageByUser(TgCouponPageVo vo);

    AjaxResult openDiscountAmount(TgCoupon vo);

    AjaxResult rechargeDiscountAmount(TgCoupon vo);

    AjaxResult getNodeConfig();

    AjaxResult applyNode(TgNodeVo vo) throws Exception;

    AjaxResult isUser(String email);

    AjaxResult appAuth(String initData, String email);

    AjaxResult isInviteCode(String inviteCode);

    AjaxResult merchantCode(String sysId, String type);

//    AjaxResult md5Verify(String id, String md5sum, String channel);
//
//    boolean emailVerify(String code, SysUser sysUser);
}
