package com.meta.web.service.impl;


import com.meta.common.core.domain.AjaxResult;
import com.meta.common.enums.app.*;
import com.meta.common.enums.vcc.CardStatus;
import com.meta.common.utils.DateUtils;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.StringUtils;
import com.meta.system.dao.app.CoinBalanceDao;
import com.meta.system.domain.CreditCard;
import com.meta.system.domain.Wallet;
import com.meta.system.domain.app.*;
import com.meta.system.domain.log.TxnDtlUSD;
import com.meta.system.dto.VccReqDto;
import com.meta.system.dto.node.WalletExchangeDto;
import com.meta.system.email.Mail;
import com.meta.system.email.SendEmail;
import com.meta.system.service.*;
import com.meta.web.contants.Constants;
import com.meta.web.dto.WithdrawalDto;
import com.meta.web.service.CardConfigService;
import com.meta.web.service.CoinBalanceService;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

@Transactional(readOnly = true)
@Service
public class CoinBalanceServiceImpl implements CoinBalanceService {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private CoinBalanceDao coinBalanceDao;

    @Autowired
    private ExchangeRateService exchangeRateService;

    @Autowired
    private WalletService walletService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private CoinTxnDtlService coinTxnDtlService;

    @Autowired
    private CreditCardService creditCardService;

    @Autowired
    private CardConfigService cardConfigService;

    @Autowired
    private MetaCodeDeductionService metaCodeDeductionService;

    @Autowired
    private TxnService txnService;

    @Autowired
    private SendEmail sendEmail;

    @Override
    @Transactional
    public void save(CoinBalance c) {
        coinBalanceDao.save(c);
    }

    @Override
    public CoinBalance findById(CoinBalancePK p) {
        Optional<CoinBalance> op = coinBalanceDao.findById(p);
        if (op.isPresent()) {
            return op.get();
        }
        return null;
    }


    @Override
    public void updateCoinBalance(CoinBalance cb) {
        coinBalanceDao.updateCoinBalance(cb.getUserId(), cb.getCoinCode(), cb.getCoinBalance());
    }

    @Override
    public void updateCoinBalanceData(CoinBalance cb) {
        coinBalanceDao.updateCoinBalanceData(cb.getUserId(), cb.getCoinCode(), cb.getCoinBalance(), cb.getFreezeBalance(), cb.getUncltCoinBalance());
    }

    /**
     * 根据数据查询兑换所需的余额是否充足
     *
     * @param exchange 兑换详情
     * @return 余额是否足够
     */
    @Override
    public AjaxResult checkExchange(WalletExchangeDto exchange) {
        if (ExchangeType.U_TO_B.getOrdinal().equals(exchange.getExchangeType())) {
            // 查找用户钱包余额
            Wallet wallet = walletService.findByUserId(SecurityUtils.getLoginUser().getUserId());
            return wallet.getUsdBalance().compareTo(exchange.getQuantity()) >= 0 ? AjaxResult.success() : AjaxResult.error(MessageUtils.message("wallet.account.insufficient.balance"));
        } else if (ExchangeType.B_TO_U.getOrdinal().equals(exchange.getExchangeType())) {
            // 获取币余额
            CoinBalancePK coinBalancePK = new CoinBalancePK(SecurityUtils.getLoginUser().getUserId(), exchange.getCoinCode(), "");
            Optional<CoinBalance> coinBalance = coinBalanceDao.findById(coinBalancePK);
            if (coinBalance.isPresent()) {
                CoinBalance balance = coinBalance.get();
                return balance.getCoinBalance().compareTo(exchange.getQuantity()) >= 0 ? AjaxResult.success() : AjaxResult.error(MessageUtils.message("wallet.coin.insufficient.balance", exchange.getCoinCode()));
            }
            return AjaxResult.error(MessageUtils.message("wallet.coin.not.exist", exchange.getCoinCode()));
        }
        return AjaxResult.error(MessageUtils.message("wallet.coin.exchange.type.not.exist", exchange.getCoinCode()));
    }

    /**
     * 兑换
     *
     * @param exchange 兑换信息
     */
    @Override
    public AjaxResult exchange(WalletExchangeDto exchange) throws Exception {
        AjaxResult result = AjaxResult.success();

        // 公司账户id
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        if (ExchangeType.U_TO_B.getOrdinal().equals(exchange.getExchangeType())) {

            // 1.计算兑换数量
            // 获取兑换利率
            ExchangeRate rate = exchangeRateService.findByFromCurrencyCodeAndToCurrencyCode("USD", exchange.getCoinCode());
            // USD 余额
            // 扣款 客户usd余额
            VccReqDto vccReqC = new VccReqDto();
            vccReqC
                    .setActiveCodeNum(0) //需要处理的激活码数量
                    .setUserId(SecurityUtils.getLoginUser().getUserId()) // 用户id
                    .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                    .setWalletAmount(exchange.getQuantity()) //钱包需要处理的金额
                    .setFreezeType(FreezeType.NO) // 冻结类型
                    .setTxnType(TxnType.WALLET_RECHARGE_C) // 交易类型
                    .setFromUserId(SecurityUtils.getLoginUser().getUserId()) // 支出账户id
                    .setToUserId(accountId) // 收入账户id
                    .setFromCoin("USD")
                    .setFromAmount(exchange.getQuantity())
                    .setExchgRate(rate.getRateVal())
                    .setFee(BigDecimal.ZERO); // 手续费

            AjaxResult res = walletService.update(vccReqC);
            if (!res.isSuccess()) {
                throw new Exception(MessageUtils.message("wallet.deduct.funds.failed"));
            }

            // 入账 加到公司总账
            VccReqDto vccReqP = new VccReqDto();
            vccReqP
                    .setActiveCodeNum(0) //需要处理的激活码数量
                    .setActiveCodeAlgorithmType(AlgorithmType.ADD)
                    .setUserId(accountId) // 公司总账id
                    .setWalletAlgorithmType(AlgorithmType.ADD) //钱包需要处理的类型：加法或者减法
                    .setWalletAmount(exchange.getQuantity()) //钱包需要处理的金额
                    .setFreezeType(FreezeType.NO) // 冻结类型
                    .setTxnType(TxnType.WALLET_RECHARGE_D) // 交易类型
                    .setFromUserId(SecurityUtils.getLoginUser().getUserId()) // 支出账户id
                    .setToUserId(accountId) // 收入账户id
                    .setFromCoin("USD")
                    .setFromAmount(exchange.getQuantity().negate())
                    .setExchgRate(rate.getRateVal())
                    .setFee(BigDecimal.ZERO); // 手续费

            res = walletService.updateComAcc(vccReqP);
            if (!res.isSuccess()) {
                throw new Exception(MessageUtils.message("wallet.deduct.funds.failed"));
            }

            // 钱包
            // 1.计算兑换数量
            // 获取兑换利率
            //ExchangeRate rate = exchangeRateDao.findByFromCurrencyCodeAndToCurrencyCode("USD", exchange.getCoinCode());

            // 最终入账
            BigDecimal amount = exchange.getQuantity().multiply(rate.getRateVal());

            // 2.更新

            // 充币 客户钱包
            CoinBalance balanceC = coinBalanceDao.findByUserIdAndCoinCode(SecurityUtils.getLoginUser().getUserId(), exchange.getCoinCode());
            checkCoinExist(balanceC, exchange.getCoinCode());

            coinBalanceDao.updateCoinBalance(SecurityUtils.getLoginUser().getUserId(), exchange.getCoinCode(), balanceC.getCoinBalance().add(amount));
        } else if (ExchangeType.B_TO_U.getOrdinal().equals(exchange.getExchangeType())) {
            // 扣币(个人账户)
            CoinBalance balanceC = coinBalanceDao.findByUserIdAndCoinCode(SecurityUtils.getLoginUser().getUserId(), exchange.getCoinCode());
            checkCoinExist(balanceC, exchange.getCoinCode());
            coinBalanceDao.updateCoinBalance(SecurityUtils.getLoginUser().getUserId(), exchange.getCoinCode(), balanceC.getCoinBalance().subtract(exchange.getQuantity()));

            // 加钱
            // 获取兑换利率
            // 最终入账
            ExchangeRate rate = exchangeRateService.findByFromCurrencyCodeAndToCurrencyCode(exchange.getCoinCode(), "USD");
            BigDecimal amount = exchange.getQuantity().multiply(rate.getRateVal());
            amount = amount.setScale(2, RoundingMode.DOWN); // 保留两位小数,且向下取整

            result.put("amount", amount);
            VccReqDto vccReqP = new VccReqDto();
            vccReqP
                    .setActiveCodeNum(0) //需要处理的激活码数量
                    .setActiveCodeAlgorithmType(AlgorithmType.ADD)
                    .setUserId(accountId) // 公司总帐
                    .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                    .setWalletAmount(amount) //钱包需要处理的金额
                    .setFreezeType(FreezeType.NO) // 冻结类型
                    .setTxnType(TxnType.WALLET_WITHDRAW_C) // 交易类型
                    .setFromUserId(accountId) // 支出账户id
                    .setToUserId(SecurityUtils.getLoginUser().getUserId()) // 收入账户id
                    .setFromCoin(exchange.getCoinCode())
                    .setFromAmount(exchange.getQuantity())
                    .setExchgRate(rate.getRateVal())
                    .setFee(BigDecimal.ZERO); // 手续费
            AjaxResult res = walletService.updateComAcc(vccReqP);
            if (!res.isSuccess()) {
                throw new Exception(MessageUtils.message("wallet.deduct.funds.failed"));
            }

            VccReqDto vccReqC = new VccReqDto();
            vccReqC
                    .setActiveCodeNum(0) //需要处理的激活码数量
                    .setActiveCodeAlgorithmType(AlgorithmType.ADD)
                    .setUserId(SecurityUtils.getLoginUser().getUserId()) // 用户id
                    .setWalletAlgorithmType(AlgorithmType.ADD) //钱包需要处理的类型：加法或者减法
                    .setWalletAmount(amount) //钱包需要处理的金额
                    .setFreezeType(FreezeType.NO) // 冻结类型
                    .setTxnType(TxnType.WALLET_WITHDRAW_D) // 交易类型
                    .setFromUserId(accountId) // 支出账户id
                    .setToUserId(SecurityUtils.getLoginUser().getUserId()) // 收入账户id
                    .setFromCoin(exchange.getCoinCode())
                    .setFromAmount(exchange.getQuantity().negate())
                    .setExchgRate(rate.getRateVal())
                    .setFee(BigDecimal.ZERO); // 手续费
            res = walletService.update(vccReqC);
            if (!res.isSuccess()) {
                throw new Exception(MessageUtils.message("wallet.deduct.funds.failed"));
            }
        }

        return result;
    }

    /**
     * 获取兑换汇率
     *
     * @param fromCurrencyCode 本币币种
     * @param toCurrencyCode   兑换币种
     * @return 兑换汇率
     */
    @Override
    public ExchangeRate getExchangeRate(String fromCurrencyCode, String toCurrencyCode) {
        return exchangeRateService.findByFromCurrencyCodeAndToCurrencyCode(fromCurrencyCode, toCurrencyCode);
    }

    /**
     * 根据 用户id获取所有的数字货币钱包
     *
     * @param userId 用户id
     * @return 数字货币钱包
     */
    @Override
    public List<CoinBalance> findAllByUserId(Long userId) {
        List<CoinBalance> list = coinBalanceDao.findAllByUserId(userId);
        for (CoinBalance coinBalance : list) {
            ExchangeRate rate = exchangeRateService.findByFromCurrencyCodeAndToCurrencyCode(coinBalance.getCoinCode(), "USD");
            BigDecimal coinAmount = coinBalance.getCoinBalance().multiply(rate.getRateVal()).setScale(2, RoundingMode.CEILING);//充值coin转USD的值
            coinBalance.setUsdAmount(coinAmount);
        }

        return list;
    }

    /**
     * @param withdrawal 提现
     * @return 余额是否充足
     */
    @SneakyThrows
    @Override
    public boolean checkWithdrawal(WithdrawalDto withdrawal) {
        CoinBalance coinBalance = coinBalanceDao.findByUserIdAndCoinCode(SecurityUtils.getLoginUser().getUserId(), withdrawal.getCoinCode());
        if (coinBalance == null) {
            return false;
        }
        checkCoinExist(coinBalance, withdrawal.getCoinCode());
        return coinBalance.getCoinBalance().compareTo(withdrawal.getQuantity()) >= 0; // 对比时不需要算上手续费(手续费从本次的提现数量中扣)
    }

    /**
     * @param withdrawal 提款
     */
    @Override
    public AjaxResult withdrawal(WithdrawalDto withdrawal) {

        if (withdrawal.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            return AjaxResult.error(MessageUtils.message("wallet.withdrawal.number"));
        }


        CoinBalance balance = coinBalanceDao.findByUserIdAndCoinCode(SecurityUtils.getLoginUser().getUserId(), withdrawal.getCoinCode());
        BigDecimal amountReceived = withdrawal.getQuantity().subtract(withdrawal.getFee());// 到账金额等于本次提现数量减去本次手续费
        //gate用户提币操作
        //1.gate来源用户充值未做其他操作进行提币
        //       回收卡码
        //2.gate来源用户充值后开卡，卡未激活进行提币
        //       判断是否用开卡码开卡，①是的情况下提币金额扣去金/银卡的金额， ②不是的情况下回收卡码
        //3.gate来源用户充值后开卡并激活后进行提币
        //       判断是否用开卡码开卡，①是的情况下提币金额扣去（金/银卡的金额减去充值的金额），卡充值的金额大于金/银卡的金额就不扣提币金额，②不是的情况下回收卡码，卡充值的金额大于金/银卡的金额就不扣卡码
        Long userId = SecurityUtils.getLoginUser().getUserId();
        Wallet w = walletService.findByUserId(userId);
        BigDecimal gateCodeFee = BigDecimal.ZERO;
        MetaCodeDeduction metaCodeDeduction = null;
        //只有gate充值赠送卡提币才做撤销卡码
        if (w.getGateUid() != null && w.getGateUid().contains("gate.io") && balance.getGateRecharge() != null && balance.getGateRecharge().compareTo(new BigDecimal(200)) >= 0) {
            List<MetaCodeDeduction> codeDeductionList = metaCodeDeductionService.findByUserId(userId);
            if (codeDeductionList.size() > 0) {
                //不做撤销

            } else {
                metaCodeDeduction = new MetaCodeDeduction();
                metaCodeDeduction.setUserId(userId);
                metaCodeDeduction.setStatue("0");
                //判断是否用了开卡码
                if (balance.getGateRecharge().compareTo(new BigDecimal(500)) == 0 && w.getSilverActiveCodeNum() > 0) {
                    //没用银卡开卡码
                    metaCodeDeduction.setNum(1);
                    metaCodeDeduction.setCodeType("01");
                    metaCodeDeduction.setType("code");
                    w.setSilverActiveCodeNum(w.getSilverActiveCodeNum() - 1);


                } else if (balance.getGateRecharge().compareTo(new BigDecimal(2000)) == 0 && w.getGoldenActiveCodeNum() > 0) {
                    //没用金卡开卡码
                    metaCodeDeduction.setNum(1);
                    metaCodeDeduction.setCodeType("02");
                    metaCodeDeduction.setType("code");
                    w.setGoldenActiveCodeNum(w.getGoldenActiveCodeNum() - 1);


                } else if (balance.getGateRecharge().compareTo(new BigDecimal(5000)) == 0 && w.getGoldenActiveCodeNum() > 1) {
                    //没用金卡开卡码
                    metaCodeDeduction.setNum(2);
                    metaCodeDeduction.setCodeType("02");
                    metaCodeDeduction.setType("code");
                    w.setGoldenActiveCodeNum(w.getGoldenActiveCodeNum() - 2);


                } else {
                    //判断是否拥有卡
                    List<CreditCard> creditCards = creditCardService.listByUserId(userId);
                    if (creditCards.size() < 1) {//没开卡

                        if (balance.getGateRecharge().compareTo(new BigDecimal("500")) == 0) {
                            //扣除银卡
                            metaCodeDeduction.setNum(1);
                            metaCodeDeduction.setCodeType("01");
                            metaCodeDeduction.setType("amount");
                            List<String> feelist = cardConfigService.findFeeBylevel("01");
                            if (feelist.size() > 0) {
                                amountReceived = amountReceived.subtract(new BigDecimal(feelist.get(0)));
                                gateCodeFee = new BigDecimal(feelist.get(0));
                            }
                        } else if (balance.getGateRecharge().compareTo(new BigDecimal("2000")) == 0) {
                            //扣除金卡
                            metaCodeDeduction.setCodeType("02");
                            metaCodeDeduction.setType("amount");
                            metaCodeDeduction.setNum(1);
                            List<String> feelist = cardConfigService.findFeeBylevel("02");
                            if (feelist.size() > 0) {
                                amountReceived = amountReceived.subtract(new BigDecimal(feelist.get(0)));
                                gateCodeFee = new BigDecimal(feelist.get(0));
                            }
                        } else if (balance.getGateRecharge().compareTo(new BigDecimal("5000")) == 0) {
                            //扣除金卡
                            metaCodeDeduction.setCodeType("02");
                            metaCodeDeduction.setType("amount");

                            List<String> feelist = cardConfigService.findFeeBylevel("02");
                            if (feelist.size() > 0) {

                                if (w.getGoldenActiveCodeNum() == 1) {
                                    metaCodeDeduction.setNum(1);
                                    w.setGoldenActiveCodeNum(w.getGoldenActiveCodeNum() - 1);
                                    amountReceived = amountReceived.subtract(new BigDecimal(feelist.get(0)));
                                    gateCodeFee = new BigDecimal(feelist.get(0));
                                } else {
                                    metaCodeDeduction.setNum(2);
                                    amountReceived = amountReceived.subtract(new BigDecimal(feelist.get(0)).multiply(new BigDecimal("2")));
                                    gateCodeFee = new BigDecimal(feelist.get(0)).multiply(new BigDecimal("2"));
                                }


                            }
                        }


                    } else if (creditCards.size() == 1) {
                        //已开卡
                        CreditCard creditCard = creditCards.get(0);
                        CardConfig cardConfig = cardConfigService.findCardConfigByCardCfgId(creditCard.getCardCfgId());
                        metaCodeDeduction.setCodeType(cardConfig.getCardLevel());
                        metaCodeDeduction.setType("amount");
                        if (creditCard.getCardStatus().equals(CardStatus.TBA)) {

                            amountReceived = amountReceived.subtract(cardConfig.getCardFee());
                            gateCodeFee = cardConfig.getCardFee();

                        } else {

                            List<TxnDtlUSD> rechargeList = txnService.findAllByCardIdAndStatusAndTxnTypeForUsd(creditCard.getCardId(), String.valueOf(TxnStatus.SUCCESS.getValue()), new String[]{TxnType.CARD_RECHARGE_C.getValue(), TxnType.CARD_RECHARGE_FEE_C.getValue()});
                            BigDecimal totalTxnAmount = BigDecimal.ZERO;
                            for (TxnDtlUSD txn : rechargeList) {
                                totalTxnAmount = totalTxnAmount.add(txn.getTxnAmount());
                            }

                            // 对累加的结果取绝对值
                            BigDecimal absoluteTotal = totalTxnAmount.abs();
                            if (absoluteTotal.compareTo(cardConfig.getCardFee()) < 0) {
                                amountReceived = amountReceived.subtract(cardConfig.getCardFee().subtract(absoluteTotal));
                                gateCodeFee = cardConfig.getCardFee().subtract(absoluteTotal);
                            }
                        }
                    }

                }
            }
        }
        //手续费+开卡消费amountReceived  是否大于余额
        if (balance.getCoinBalance().compareTo(amountReceived) < 0) {
            logger.info("gate用户提币的时候扣去卡码费之后余额不足，不允许提币");
            return AjaxResult.error(MessageUtils.message("wallet.coin.insufficient.balance", withdrawal.getCoinCode()));
        }

        walletService.updateCode(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());
        balance.setCoinBalance(balance.getCoinBalance().subtract(withdrawal.getQuantity()));  // 原本的余额减去本次提现数量
        balance.setFreezeBalance(balance.getFreezeBalance().add(withdrawal.getQuantity())); // 原本冻结的余额 加上本次提现数量
        coinBalanceDao.save(balance);
        // 插入提币记录
        CoinTxnDtl coinTxnDtl = new CoinTxnDtl();
        coinTxnDtl.setUserId(SecurityUtils.getLoginUser().getUserId());
        coinTxnDtl.setRecordType(0);
        coinTxnDtl.setTxnCoin(balance.getCoinCode());
        coinTxnDtl.setTxnCode(Constants.txnCode.c1010);
        coinTxnDtl.setReceiptNo("");
//        coinTxnDtl.setFromAddress();
        coinTxnDtl.setToAddress(withdrawal.getAddress());
        coinTxnDtl.setTxnStatus(2);
        coinTxnDtl.setTxnDesc("提币");
        coinTxnDtl.setTxnAmount(amountReceived);  // 到账金额等于本次提现数量减去本次手续费
        coinTxnDtl.setTxnFee(withdrawal.getFee());
        // coinTxnDtl.setTxnTime(DateUtils.getNowDateOld());
        coinTxnDtl.setCreateTime(DateUtils.getNowDateOld());
        coinTxnDtl.setUserBalance(balance.getCoinBalance());
        coinTxnDtl.setGateCodeFee(gateCodeFee);
        coinTxnDtl.setCoinNet(withdrawal.getType());//币种网络
        coinTxnDtlService.save(coinTxnDtl);
        //卡扣减
        if (metaCodeDeduction != null) {
            metaCodeDeduction.setTxnId(coinTxnDtl.getTxnId());
            metaCodeDeductionService.save(metaCodeDeduction);
        }
        sendEmail.sendAminEail("提币审核","提币审核:" + SecurityUtils.getLoginUser().getUserId());
        return AjaxResult.success();

    }

    @Override
    @Transactional
    public CoinBalance findByUserIdAndCoinCode(Long userId, String coinCode) {
        return coinBalanceDao.findByUserIdAndCoinCode(userId, coinCode);
    }

    @Override
    public CoinBalance findByUserIdAndCoinCodeForUpdate(Long userId, String coinCode) {
        return coinBalanceDao.findOneForUpdate(userId, coinCode);
    }

    /**
     * 判断钱包是否存在
     *
     * @param coin
     */
    public AjaxResult checkCoinExist(CoinBalance coin, String coinCode) {
        if (coin == null) {
            return AjaxResult.error(MessageUtils.message("wallet.coin.not.exist", coinCode));
        }
        return AjaxResult.success();
    }


}
