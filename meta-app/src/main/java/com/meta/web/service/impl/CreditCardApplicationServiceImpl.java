package com.meta.web.service.impl;


import com.meta.system.dao.CreditCardApplicationDao;
import com.meta.system.domain.CreditCardApplication;
import com.meta.web.service.CreditCardApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Transactional(readOnly = true)
@Service
public class CreditCardApplicationServiceImpl implements CreditCardApplicationService {
    @Autowired
    private CreditCardApplicationDao creditCardApplicationDao;
    @Override
    public void save(CreditCardApplication c) {
        creditCardApplicationDao.save(c);
    }

    @Override
    public CreditCardApplication findByCardId(String cardId) {
        return creditCardApplicationDao.findByCardId(cardId);
    }

    @Override
    public CreditCardApplication findByRequestNo(String requestNo) {
        Optional<CreditCardApplication> op = creditCardApplicationDao.findById(requestNo);
        if(op.isPresent())
            return op.get();
        return null;
    }

    @Override
    public void updateCardId(String oldCardId, String cardId,String requestNo) {
        creditCardApplicationDao.updateCardId( oldCardId,  cardId,requestNo);
    }
}
