package com.meta.web.service;

import com.meta.common.core.domain.AjaxResult;
import com.meta.system.dto.wello.WelloOrderDto;
import com.meta.system.dto.wello.WelloOrderSelectDto;
import com.meta.system.dto.wello.WelloPlaceOrderDto;
import com.meta.system.dto.wello.WelloQuoteDto;
import com.meta.system.vo.MetaExchangeOrderInfoVo;
import org.springframework.data.domain.Page;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/2/14
 */


public interface WelloService {
    AjaxResult getTradingPairList();

    AjaxResult getQuote(WelloQuoteDto welloQuoteDto);

    AjaxResult placeAnOrder(WelloPlaceOrderDto welloPlaceOrderDto);

    AjaxResult updateWebhookURL(String webhookURL);

    Page<MetaExchangeOrderInfoVo> getOrderPage(WelloOrderSelectDto welloOrderSelectDto);

    AjaxResult cancelOrder(WelloOrderDto welloOrderDto);

    AjaxResult getOrderByMerchantOrderId(WelloOrderDto welloOrderDto);

    AjaxResult checkout(WelloOrderDto welloOrderDto);
}
