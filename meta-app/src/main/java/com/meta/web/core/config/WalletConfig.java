package com.meta.web.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 *  钱包 配置
 *
 */
@Component
@ConfigurationProperties(prefix = "wallet")
public class WalletConfig {
    /**
     * 地址
     */
    public static String url;
    /**
     * 协商key
     */
    public static String key;

    public static String mainaddress;



    @Value("${wallet.userAddress.url}")
    public void setUrl(String url) {
        WalletConfig.url = url;
    }


    @Value("${wallet.userAddress.key}")
    public void setKey(String key) {
        WalletConfig.key = key;
    }

    @Value("${wallet.mainaddress}")
    public void setMainaddress(String mainaddress) {
        WalletConfig.mainaddress = mainaddress;
    }

}
