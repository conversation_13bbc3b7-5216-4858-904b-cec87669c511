package com.meta.web.scheduler;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meta.common.core.domain.AjaxResult;
import com.meta.system.config.WelloConfig;
import com.meta.system.constant.Constants;
import com.meta.system.dao.MetaExchangeOrderInfoDao;
import com.meta.system.dao.MetaWelloLogDao;
import com.meta.system.dao.MetaWelloRequestLogDao;
import com.meta.system.dao.app.CoinBalanceDao;
import com.meta.system.dao.app.CoinTxnDtlDao;
import com.meta.system.domain.app.*;
import com.meta.system.dto.wello.WelloOrderDto;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.impl.SysConfigServiceImpl;
import com.meta.web.service.impl.WelloServiceImpl;
import com.meta.system.uitls.WelloUtils;
import com.meta.web.utils.QuikNodeApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/17
 */

@Component
@EnableScheduling
@Slf4j
public class MyScheduler {

    private final MyScheduler self;

    @Autowired
    public MyScheduler(@Lazy MyScheduler self) {
        this.self = self;
    }

    @Autowired
    private SysConfigServiceImpl sysConfigService;
    @Autowired
    private WelloUtils welloUtils;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private MetaWelloRequestLogDao metaWelloRequestLogDao;
    @Autowired
    private MetaExchangeOrderInfoDao metaExchangeOrderInfoDao;
    @Autowired
    private MetaWelloLogDao metaWelloLogDao;
    @Autowired
    private QuikNodeApi quikNodeApi;
    @Autowired
    private ISysConfigService iSysConfigService;
    @Autowired
    private CoinBalanceDao coinBalanceDao;
    @Autowired
    private CoinTxnDtlDao coinTxnDtlDao;

    @Scheduled(cron = "0 0 * * * ?")
    public void welloUpdateTradingPair() {
        log.info("正在更新Wello交易对");
        RestTemplate restTemplate = new RestTemplate();
        String url = sysConfigService.selectConfigByKey("wello_trading_pair_url");

        HttpHeaders httpHeaders = welloUtils.createHttpHeaders(null);

        HttpEntity<String> entity = new HttpEntity<>(httpHeaders);

        try {
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            JSONObject body = JSONUtil.parseObj(response.getBody());
            if (body.get("success").equals(true)) {
                List<Map<String, Object>> result = new ArrayList<>();
                JSONObject data = body.getJSONObject("data");
                JSONArray buySelectors = data.getJSONArray("buySelectors");
                for (Object o : buySelectors) {
                    JSONObject object = (JSONObject) o;
                    // 法定货币资产
                    JSONObject fiatAsset = object.getJSONObject("fiatAsset");
                    // 货币代码
                    String assetCode = fiatAsset.getStr("assetCode");
                    Map<String, Object> map = new HashMap<>();
                    map.put("legalTenderCode", assetCode);
                    // 加密货币资产列表
                    List<Map<String, Object>> cryptocurrencyList = new ArrayList<>();

                    // USDT币种
                    Map<String, Object> USDTCurrency = new HashMap<>();
                    // USDT网络列表
                    List<Map<String, Object>> USDTNetworkList = new ArrayList<>();
                    // USDC币种
                    Map<String, Object> USDCCurrency = new HashMap<>();
                    // USDC网络列表
                    List<Map<String, Object>> USDCNetworkList = new ArrayList<>();
//                    // USDC币种
//                    Map<String, Object> USDCCurrency = new HashMap<>();
//                    // USDC网络列表
//                    List<Map<String, Object>> USDCNetworkList = new ArrayList<>();


                    JSONArray cryptoSelectors = object.getJSONArray("cryptoSelectors");
                    for (Object c : cryptoSelectors) {
                        JSONObject crypto = (JSONObject) c;
                        JSONObject asset = crypto.getJSONObject("asset");
                        // 加密货币代码
                        String assetCode1 = asset.getStr("assetCode");
                        if (crypto.get("enabled").equals(true) && assetCode1.equals("USDT")) {
                            // 加密货币资产
                            Map<String, Object> network = new HashMap<>();
                            network.put("network", crypto.get("network"));
                            network.put("minLimit", crypto.get("minLimit"));
                            network.put("maxLimit", crypto.get("maxLimit"));
                            USDTNetworkList.add(network);
                        }
                        if (crypto.get("enabled").equals(true) && assetCode1.equals("USDC")){
                            // 加密货币资产
                            Map<String, Object> network = new HashMap<>();
                            network.put("network", crypto.get("network"));
                            network.put("minLimit", crypto.get("minLimit"));
                            network.put("maxLimit", crypto.get("maxLimit"));
                            USDCNetworkList.add(network);
                        }
//                    if (crypto.get("enabled").equals(true) && assetCode1.equals("USDC")){
//                        // 加密货币资产
//                        Map<String, Object> network = new HashMap<>();
//                        network.put("network", crypto.get("network"));
//                        network.put("minLimit", crypto.get("minLimit"));
//                        network.put("maxLimit", crypto.get("maxLimit"));
//                        USDCNetworkList.add(network);
//                    }
                    }
                    USDTCurrency.put("currencyCode", "USDT");
                    USDTCurrency.put("network", USDTNetworkList);
                    USDCCurrency.put("currencyCode", "USDC");
                    USDCCurrency.put("network", USDCNetworkList);
//                    USDCCurrency.put("currencyCode", "USDC");
//                    USDCCurrency.put("network", USDCNetworkList);


                    cryptocurrencyList.add(USDTCurrency);
                    cryptocurrencyList.add(USDCCurrency);
//                    cryptocurrencyList.add(USDCCurrency);
                    map.put("cryptocurrency", cryptocurrencyList);
                    result.add(map);

                }

                // 过滤掉 network 为空的对象
                List<Map<String, Object>> filteredResult = result.stream()
                        .filter(WelloServiceImpl::hasValidNetwork)
                        .collect(Collectors.toList());

                redisTemplate.opsForValue().set(Constants.WELLO_TRADING_PAIR_KEY, JSONUtil.toJsonStr(filteredResult));
                log.info("更新完成");
            }
        } catch (HttpStatusCodeException e) {
            JSONObject object = JSONUtil.parseObj(e.getResponseBodyAsString());
            saveWelloRequestLog(httpHeaders.toString(), object.toString(), url, object.getStr("code"), object.getStr("message"));
            log.error("更新失败: {}", object.getStr("message"));
        }
    }

    public void saveWelloRequestLog(String request, String response, String apicode, String code, String detail) {
        MetaWelloRequestLog log = new MetaWelloRequestLog();

        SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateTimePart = dateTimeFormat.format(new Date());
        Random random = new Random();
        String serialNumber = String.format("%010d", random.nextInt(999999999));
        String requestNo = dateTimePart + serialNumber;

        log.setRequestNo(requestNo);
        log.setRequest(request);
        log.setResponse(response);
        log.setApicode(apicode);
        log.setCode(code);
        log.setCreatedAt(LocalDateTime.now());
        log.setDetail(detail);
        metaWelloRequestLogDao.save(log);

    }

    @Scheduled(cron = "0 */5 * * * ?")
    public void welloMonitorOrder() {
        log.info("------检测wello和kazepay双方订单状态有无差异定时任务开始------");
        List<MetaExchangeOrderInfo> orders = metaExchangeOrderInfoDao.getOrdersWithSuspectedProblems();
        for (MetaExchangeOrderInfo order : orders) {
            RestTemplate restTemplate = new RestTemplate();
            String url = sysConfigService.selectConfigByKey("wello_order_details_url")
                    + "?merchantOrderId=" + order.getMerchantOrderId();
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("merchantOrderId", order.getMerchantOrderId());
            HttpHeaders httpHeader = welloUtils.createHttpHeaders(requestBody);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(httpHeader);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            JSONObject body = JSONUtil.parseObj(response.getBody());
            log.info("------wello订单状态查询结果------订单号：{},body：{}------", order.getMerchantOrderId(), body);
            if (body.get("success").equals(true)) {
                JSONObject data = body.getJSONObject("data");

                if (data.getStr("orderStatus").equals("SUCCESS")){
                    log.warn("------wello订单和kazepay订单状态有差异，正在自动处理订单，订单号为{}------", order.getMerchantOrderId());
                    WelloOrderDto welloOrderDto = JSONUtil.toBean(data, WelloOrderDto.class);
                    self.recordWelloOrder(welloOrderDto);
                    self.handingBusiness(welloOrderDto);
                }
            }
        }
        log.info("------检测wello和kazepay双方订单状态有无差异定时任务结束------");
    }

    /**
     * 记录wello订单
     */
    public void recordWelloOrder(WelloOrderDto welloOrderDto) {
        MetaWelloLog metaWelloLog = new MetaWelloLog();
        copyPropertiesFromWoDtoToWLog(welloOrderDto, metaWelloLog);
        metaWelloLog.setSysId("99"); // kazepay为99，后续增加其他平台再修改为动态
        log.info("------接收到wello的消息，正在更新货币订单记录:{}------", metaWelloLog);
        metaWelloLogDao.save(metaWelloLog);
    }

    /**
     * 处理业务
     */
    @Transactional
    public void handingBusiness(WelloOrderDto welloOrderDto) {
        // 删除订单过期时间
        redisTemplate.delete(Constants.WELLO_ORDER_EXPIRATION_KEY + welloOrderDto.getMerchantOrderId());

//        welloOrderDto.setOrderStatus(welloOrderDto.getOrderStatus().equals("PENDING_WITHDRAW") ? "SUCCESS" : welloOrderDto.getOrderStatus());

        MetaExchangeOrderInfo metaExchangeOrderInfo = metaExchangeOrderInfoDao.findByMerchantOrderId(welloOrderDto.getMerchantOrderId());
        if (metaExchangeOrderInfo.getOrderStatus().equals("SUCCESS")){
            log.warn("------wello订单已经处理过，订单为{}------", metaExchangeOrderInfo);
            return;
        }

        copyPropertiesFromWoDtoToEOInfo(welloOrderDto, metaExchangeOrderInfo);
        log.info("------接收到wello的消息，正在处理业务，订单为{}------", metaExchangeOrderInfo);

        CoinBalance coinBalance = coinBalanceDao.findOneForUpdate(metaExchangeOrderInfo.getUserId(), metaExchangeOrderInfo.getCryptoCurrency());
        CoinTxnDtl txn = coinTxnDtlDao.findByTxnProduct(metaExchangeOrderInfo.getMerchantOrderId());
        String receiverAddress = ""; // 收款地址
        BigDecimal amountUSDT = BigDecimal.ZERO; // 加密货币金额
        String fromAddress = null; // 发送方地址

        if (Objects.equals(welloOrderDto.getOrderStatus(),"SUCCESS")){
            log.info("------正在处理SUCCESS订单------");

            metaExchangeOrderInfo.setPayTime(welloOrderDto.getUpdatedAt());
            metaExchangeOrderInfo.setDealTime(welloOrderDto.getUpdatedAt());


            String url = iSysConfigService.selectConfigByKey("quicknode_url");
//            String url = "https://docs-demo.bsc.quiknode.pro/";
            String result = quikNodeApi.ethGetTransactionReceipt(metaExchangeOrderInfo.getTxId(), url);
//            String result = quikNodeApi.ethGetTransactionReceipt("0x0d757aeedb98727e4acb86c9b14d7829cfc0196c28e08389b507937f090a105e", url);
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = null;
            if (result != null){
                try {
                    rootNode = objectMapper.readTree(result);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                log.info("------resultNode: {}------", rootNode);
            }

            if (rootNode != null) {
                // 交易状态
                String statusHex = rootNode.get("status").asText();
                boolean transactionStatus = statusHex.equals("0x1");

                // 发送方地址
                fromAddress = rootNode.get("from").asText();

                // 合约地址
                String toAddress = rootNode.get("to").asText();

                // 提取 `logs` 中的收款人和金额
                JsonNode logsArray = rootNode.get("logs");

                if (logsArray.isArray() && logsArray.size() > 0) {
                    JsonNode firstLog = logsArray.get(0);
                    JsonNode topicsArray = firstLog.get("topics");

                    // 获取收款地址（topics[2]）
                    if (topicsArray.isArray() && topicsArray.size() >= 3) {
                        String rawReceiverAddress = topicsArray.get(2).asText();
                        receiverAddress = "0x" + rawReceiverAddress.substring(26);
//                        receiverAddress = "******************************************";
                    }

                    // 获取转账金额（data）
                    String amountHex = firstLog.get("data").asText();
                    BigInteger amountWei = new BigInteger(amountHex.substring(2), 16);
                    amountUSDT = new BigDecimal(amountWei).divide(BigDecimal.TEN.pow(18));
                }


                String configWMA = WelloConfig.walletAddress;
                String redisMWA = redisTemplate.opsForValue().get(Constants.WELLO_MERCHANT_WALLET_ADDRESS_KEY);
                if (redisMWA == null || redisMWA.isEmpty()) {
                    redisTemplate.opsForValue().set(Constants.WELLO_MERCHANT_WALLET_ADDRESS_KEY, configWMA);
                    redisMWA = configWMA;
                }
                String dbMWA = iSysConfigService.selectConfigByKey("wello_merchant_wallet_address");

                log.info("商户钱包地址匹配检查 - Redis: {}, DB: {}, Config: {}", redisMWA, dbMWA, configWMA);
                log.info("数据匹配检查 - 合约地址(toAddress): {}, 解析出的收款地址(kazepay钱包地址): {}, wello传过来的收获地址(kazepay钱包地址): {}" +
                                ", 解析出的金额(用户购买的加密货币金额): {}, wello传过来的金额(用户购买的加密货币金额): {}",
                        toAddress, receiverAddress, welloOrderDto.getCrypto().getAddress(), amountUSDT, metaExchangeOrderInfo.getCryptoAmount());

                // 判断是否为我们的合约地址，金额状态等都符合再入账
                if (transactionStatus && Objects.equals(toAddress,"******************************************")
                        && Objects.equals(receiverAddress, welloOrderDto.getCrypto().getAddress())
                        && Objects.equals(receiverAddress, redisMWA)
                        && Objects.equals(redisMWA, dbMWA) && Objects.equals(dbMWA, configWMA)
                        && amountUSDT.compareTo(new BigDecimal(metaExchangeOrderInfo.getCryptoAmount())) == 0)
                {
                    log.info("------数据匹配成功，正在入账------");
                    // 入账
                    if (coinBalance != null && metaExchangeOrderInfo.getDelFlag().equals("exist")) {
                        coinBalance.setCoinBalance(coinBalance.getCoinBalance().add(amountUSDT));
                        coinBalanceDao.save(coinBalance);
                    } else {
                        log.warn("------找不到用户钱包或者订单已经过期，订单状态更改为等待入账------");
                        metaExchangeOrderInfo.setOrderStatus("PENDING_ENTRY");
                    }
                } else {
                    log.warn("------数据匹配失败，订单状态更改为等待入账------");
                    metaExchangeOrderInfo.setOrderStatus("PENDING_ENTRY");
                }
            } else {
                log.warn("------解析交易哈希失败，订单状态更改为等待入账------");
                metaExchangeOrderInfo.setOrderStatus("PENDING_ENTRY");
            }

            log.info("------正在更新充值记录------");
            saveCoinTxnDtl(txn, metaExchangeOrderInfo, coinBalance, amountUSDT, fromAddress, receiverAddress);

            log.info("------订单处理完成，正在保存订单------");
            int flag = metaExchangeOrderInfoDao.updateOrder(
                    metaExchangeOrderInfo.getCryptoCurrency(),
                    metaExchangeOrderInfo.getFiatCurrency(),
                    metaExchangeOrderInfo.getOrderStatus(),
                    metaExchangeOrderInfo.getQuotePrice(),
                    metaExchangeOrderInfo.getFiatAmount(),
                    metaExchangeOrderInfo.getCryptoAmount(),
                    metaExchangeOrderInfo.getFiatFee(),
                    metaExchangeOrderInfo.getTradeFee(),
                    metaExchangeOrderInfo.getMerchantFee(),
                    metaExchangeOrderInfo.getNetworkFee(),
                    metaExchangeOrderInfo.getTotal(),
                    metaExchangeOrderInfo.getMethod(),
                    metaExchangeOrderInfo.getAccountNumber(),
                    metaExchangeOrderInfo.getTxId(),
                    metaExchangeOrderInfo.getCreatedAt(),
                    metaExchangeOrderInfo.getUpdatedAt(),
                    metaExchangeOrderInfo.getPayTime(),
                    metaExchangeOrderInfo.getDealTime(),
                    metaExchangeOrderInfo.getNetwork(),
                    metaExchangeOrderInfo.getMerchantOrderId()
            );
            if (flag != 0){
                throw new RuntimeException("------订单已经处理过，订单号：" + metaExchangeOrderInfo.getMerchantOrderId() + "------");
            }
            return;
        }

        log.info("------正在更新充值记录------");
        saveCoinTxnDtl(txn, metaExchangeOrderInfo, coinBalance, null, null, null);
        log.info("------订单处理完成，正在保存订单------");
        int flag = metaExchangeOrderInfoDao.updateOrder(
                metaExchangeOrderInfo.getCryptoCurrency(),
                metaExchangeOrderInfo.getFiatCurrency(),
                metaExchangeOrderInfo.getOrderStatus(),
                metaExchangeOrderInfo.getQuotePrice(),
                metaExchangeOrderInfo.getFiatAmount(),
                metaExchangeOrderInfo.getCryptoAmount(),
                metaExchangeOrderInfo.getFiatFee(),
                metaExchangeOrderInfo.getTradeFee(),
                metaExchangeOrderInfo.getMerchantFee(),
                metaExchangeOrderInfo.getNetworkFee(),
                metaExchangeOrderInfo.getTotal(),
                metaExchangeOrderInfo.getMethod(),
                metaExchangeOrderInfo.getAccountNumber(),
                metaExchangeOrderInfo.getTxId(),
                metaExchangeOrderInfo.getCreatedAt(),
                metaExchangeOrderInfo.getUpdatedAt(),
                metaExchangeOrderInfo.getPayTime(),
                metaExchangeOrderInfo.getDealTime(),
                metaExchangeOrderInfo.getNetwork(),
                metaExchangeOrderInfo.getMerchantOrderId()
        );
        if (flag != 0){
            log.warn("------订单已经处理过，订单号：{}------", metaExchangeOrderInfo.getMerchantOrderId());
            throw new RuntimeException();
        }
    }

    public void saveCoinTxnDtl(CoinTxnDtl txn, MetaExchangeOrderInfo metaExchangeOrderInfo, CoinBalance coinBalance, BigDecimal amountUSDT, String fromAddress, String receiverAddress) {
        log.info("------正在保存充值记录------");

        int txnStatus;
        if (metaExchangeOrderInfo.getOrderStatus().equals("SUCCESS")){
            txnStatus = 1;
        } else if (metaExchangeOrderInfo.getOrderStatus().equals("FAILURE")){
            txnStatus = 0;
        } else {
            txnStatus = 2;
        }

        if (txn == null){
            CoinTxnDtl coinTxnDtl = new CoinTxnDtl();
            coinTxnDtl.setUserId(metaExchangeOrderInfo.getUserId());
            coinTxnDtl.setRecordType(1);
            coinTxnDtl.setTxnCode("b1010");
            coinTxnDtl.setTxnCoin(metaExchangeOrderInfo.getCryptoCurrency());
            coinTxnDtl.setTxnStatus(txnStatus);
            coinTxnDtl.setTxnDesc("Buy Coins");
            coinTxnDtl.setTxnFee(new BigDecimal(metaExchangeOrderInfo.getTotal()));
            coinTxnDtl.setTxnTime(new Date(Long.parseLong(metaExchangeOrderInfo.getCreatedAt())));
            coinTxnDtl.setToCoin(metaExchangeOrderInfo.getCryptoCurrency());
            coinTxnDtl.setToAmount(new BigDecimal(metaExchangeOrderInfo.getCryptoAmount()));
            coinTxnDtl.setExchgRate(new BigDecimal(metaExchangeOrderInfo.getQuotePrice()));
            coinTxnDtl.setCreateTime(new Date());
            coinTxnDtl.setCoinNet("wello");
            coinTxnDtl.setTxnProduct(metaExchangeOrderInfo.getMerchantOrderId());
            coinTxnDtl.setFromCoin(metaExchangeOrderInfo.getFiatCurrency());
            coinTxnDtl.setFromAmount(new BigDecimal(metaExchangeOrderInfo.getFiatAmount()));
            coinTxnDtl.setTotalPrice(new BigDecimal(metaExchangeOrderInfo.getTotalQuoteAmount()));
            if (metaExchangeOrderInfo.getOrderStatus().equals("SUCCESS")) {
                coinTxnDtl.setUserBalance(coinBalance == null ? null : coinBalance.getCoinBalance());
                coinTxnDtl.setFromAddress(fromAddress);
                coinTxnDtl.setToAddress(receiverAddress);
                coinTxnDtl.setTxnAmount(amountUSDT);
                coinTxnDtl.setReceiptNo(metaExchangeOrderInfo.getTxId());
            }
            coinTxnDtlDao.save(coinTxnDtl);
        } else if (metaExchangeOrderInfo.getOrderStatus().equals("SUCCESS")){
            txn.setTxnStatus(txnStatus);
            txn.setFromAddress(fromAddress);
            txn.setToAddress(receiverAddress);
            txn.setTxnAmount(amountUSDT);
            txn.setReceiptNo(metaExchangeOrderInfo.getTxId());
            txn.setUserBalance(coinBalance == null ? null : coinBalance.getCoinBalance());
            coinTxnDtlDao.save(txn);
        } else {
            txn.setTxnStatus(txnStatus);
            coinTxnDtlDao.save(txn);
        }
    }


    /**
     * 复制属性从 WelloOrderDto 到 MetaExchangeOrderInfo
     * @param welloOrderDto
     * @param metaExchangeOrderInfo
     */
    public void copyPropertiesFromWoDtoToEOInfo(WelloOrderDto welloOrderDto, MetaExchangeOrderInfo metaExchangeOrderInfo) {
        if (welloOrderDto.getSide() != null) {
            metaExchangeOrderInfo.setSide(welloOrderDto.getSide());
        }
        if (welloOrderDto.getCryptoCurrency() != null) {
            metaExchangeOrderInfo.setCryptoCurrency(welloOrderDto.getCryptoCurrency());
        }
        if (welloOrderDto.getFiatCurrency() != null) {
            metaExchangeOrderInfo.setFiatCurrency(welloOrderDto.getFiatCurrency());
        }
        if (welloOrderDto.getFiatAmount() != null) {
            metaExchangeOrderInfo.setFiatAmount(welloOrderDto.getFiatAmount());
        }
        if (welloOrderDto.getCryptoAmount() != null) {
            metaExchangeOrderInfo.setCryptoAmount(welloOrderDto.getCryptoAmount());
        }
        if (welloOrderDto.getOrderStatus() != null) {
            metaExchangeOrderInfo.setOrderStatus(welloOrderDto.getOrderStatus());
        }
        if (welloOrderDto.getQuotePrice() != null) {
            metaExchangeOrderInfo.setQuotePrice(welloOrderDto.getQuotePrice());
        }
        if (welloOrderDto.getFailCode() != null) {
            metaExchangeOrderInfo.setFailCode(welloOrderDto.getFailCode());
        }
        if (welloOrderDto.getFailReason() != null) {
            metaExchangeOrderInfo.setFailReason(welloOrderDto.getFailReason());
        }
        if (welloOrderDto.getCreatedAt() != null) {
            metaExchangeOrderInfo.setCreatedAt(welloOrderDto.getCreatedAt());
        }
        if (welloOrderDto.getUpdatedAt() != null) {
            metaExchangeOrderInfo.setUpdatedAt(welloOrderDto.getUpdatedAt());
        }
        if (welloOrderDto.getFees() != null) {
            WelloOrderDto.Fees fees = welloOrderDto.getFees();
            if (fees.getFiatFee() != null) {
                metaExchangeOrderInfo.setFiatFee(fees.getFiatFee());
            }
            if (fees.getTradeFee() != null) {
                metaExchangeOrderInfo.setTradeFee(fees.getTradeFee());
            }
            if (fees.getMerchantFee() != null) {
                metaExchangeOrderInfo.setMerchantFee(fees.getMerchantFee());
            }
            if (fees.getNetworkFee() != null) {
                metaExchangeOrderInfo.setNetworkFee(fees.getNetworkFee());
            }
            if (fees.getTotal() != null) {
                metaExchangeOrderInfo.setTotal(fees.getTotal());
            }
        }
        if (welloOrderDto.getPayment() != null) {
            WelloOrderDto.Payment payment = welloOrderDto.getPayment();
            if (payment.getMethod() != null) {
                metaExchangeOrderInfo.setMethod(payment.getMethod());
            }
            if (payment.getAccountNumber() != null) {
                metaExchangeOrderInfo.setAccountNumber(payment.getAccountNumber());
            }
        }
        if (welloOrderDto.getCrypto() != null) {
            WelloOrderDto.Crypto crypto = welloOrderDto.getCrypto();
            if (crypto.getTxId() != null) {
                metaExchangeOrderInfo.setTxId(crypto.getTxId());
            }
        }
    }

    /**
     * 复制属性从 WelloOrderDto 到 MetaWelloLog
     * @param welloOrderDto
     * @param metaWelloLog
     */
    public void copyPropertiesFromWoDtoToWLog(WelloOrderDto welloOrderDto, MetaWelloLog metaWelloLog) {
        if (welloOrderDto.getMerchantOrderId() != null) {
            metaWelloLog.setMerchantOrderId(welloOrderDto.getMerchantOrderId());
        }
        if (welloOrderDto.getWelloPreOrderId() != null) {
            metaWelloLog.setWelloPreOrderId(welloOrderDto.getWelloPreOrderId());
        }
        if (welloOrderDto.getOrderReferenceId() != null) {
            metaWelloLog.setOrderReferenceId(welloOrderDto.getOrderReferenceId());
        }
        if (welloOrderDto.getMerchantCode() != null) {
            metaWelloLog.setMerchantCode(welloOrderDto.getMerchantCode());
        }
        if (welloOrderDto.getSide() != null) {
            metaWelloLog.setSide(welloOrderDto.getSide());
        }
        if (welloOrderDto.getCryptoCurrency() != null) {
            metaWelloLog.setCryptoCurrency(welloOrderDto.getCryptoCurrency());
        }
        if (welloOrderDto.getFiatCurrency() != null) {
            metaWelloLog.setFiatCurrency(welloOrderDto.getFiatCurrency());
        }
        if (welloOrderDto.getRequestCurrency() != null) {
            metaWelloLog.setRequestCurrency(welloOrderDto.getRequestCurrency());
        }
        if (welloOrderDto.getRequestAmount() != null) {
            metaWelloLog.setRequestAmount(welloOrderDto.getRequestAmount());
        }
        if (welloOrderDto.getOrderStatus() != null) {
            metaWelloLog.setOrderStatus(welloOrderDto.getOrderStatus());
        }
        if (welloOrderDto.getQuotePrice() != null) {
            metaWelloLog.setQuotePrice(welloOrderDto.getQuotePrice());
        }
        if (welloOrderDto.getFiatAmount() != null) {
            metaWelloLog.setFiatAmount(welloOrderDto.getFiatAmount());
        }
        if (welloOrderDto.getCryptoAmount() != null) {
            metaWelloLog.setCryptoAmount(welloOrderDto.getCryptoAmount());
        }
        if (welloOrderDto.getFailCode() != null) {
            metaWelloLog.setFailCode(welloOrderDto.getFailCode());
        }
        if (welloOrderDto.getFailReason() != null) {
            metaWelloLog.setFailReason(welloOrderDto.getFailReason());
        }
        if (welloOrderDto.getCreatedAt() != null) {
            metaWelloLog.setCreatedAt(welloOrderDto.getCreatedAt());
        }
        if (welloOrderDto.getUpdatedAt() != null) {
            metaWelloLog.setUpdatedAt(welloOrderDto.getUpdatedAt());
        }
        if (welloOrderDto.getFees() != null) {
            WelloOrderDto.Fees fees = welloOrderDto.getFees();
            if (fees.getFiatFee() != null) {
                metaWelloLog.setFiatFee(fees.getFiatFee());
            }
            if (fees.getTradeFee() != null) {
                metaWelloLog.setTradeFee(fees.getTradeFee());
            }
            if (fees.getMerchantFee() != null) {
                metaWelloLog.setMerchantFee(fees.getMerchantFee());
            }
            if (fees.getNetworkFee() != null) {
                metaWelloLog.setNetworkFee(fees.getNetworkFee());
            }
            if (fees.getTotal() != null) {
                metaWelloLog.setTotal(fees.getTotal());
            }
        }
        if (welloOrderDto.getSettlement() != null) {
            WelloOrderDto.Settlement settlement = welloOrderDto.getSettlement();
            if (settlement.getMerchantSettlementFee() != null) {
                metaWelloLog.setMerchantSettlementFee(settlement.getMerchantSettlementFee());
            }
            if (settlement.getMerchantSettlementCurrency() != null) {
                metaWelloLog.setMerchantSettlementCurrency(settlement.getMerchantSettlementCurrency());
            }
        }
        if (welloOrderDto.getPayment() != null) {
            WelloOrderDto.Payment payment = welloOrderDto.getPayment();
            if (payment.getMethod() != null) {
                metaWelloLog.setMethod(payment.getMethod());
            }
            if (payment.getAccountNumber() != null) {
                metaWelloLog.setAccountNumber(payment.getAccountNumber());
            }
        }
        if (welloOrderDto.getCrypto() != null) {
            WelloOrderDto.Crypto crypto = welloOrderDto.getCrypto();
            if (crypto.getNetwork() != null) {
                metaWelloLog.setNetwork(crypto.getNetwork());
            }
            if (crypto.getAddress() != null) {
                metaWelloLog.setAddress(crypto.getAddress());
            }
            if (crypto.getTxId() != null) {
                metaWelloLog.setTxId(crypto.getTxId());
            }
        }
    }
}
