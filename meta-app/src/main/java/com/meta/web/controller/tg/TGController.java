package com.meta.web.controller.tg;

import com.meta.common.annotation.RepeatSubmit;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.valid.TgYz;
import com.meta.system.dao.MetaMainAddressDao;
import com.meta.system.dao.MetaPartnerAssetPoolDao;
import com.meta.system.domain.SysNotice;
import com.meta.system.domain.partner.MetaPartnerAssetPool;
import com.meta.system.dto.TgAppAuthDto;
import com.meta.system.service.ISysNoticeService;
import com.meta.web.service.TgService;
import com.meta.web.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/18/17:19
 */
@RestController
@RequestMapping("/tg")
public class TGController {


    private static final Logger log = LoggerFactory.getLogger(TGController.class);
    @Autowired
    private TgService tgService;
    @Autowired
    private ISysNoticeService sysNoticeService;

    /**
     * 申请卡片
     *
     * @return
     */
    @GetMapping("/isUser")
    @RepeatSubmit
    public AjaxResult isUser(@RequestParam("email") String email) {
        return tgService.isUser(email);
    }

    /**
     * 判断是否绑定过
     *
     * @return
     */
    @PostMapping("/isBindEmail")
    @RepeatSubmit
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult isBindEmail(@Validated(TgYz.class) @RequestBody TgEmailVo tgEmailVo) {
        return tgService.isBindEmail(tgEmailVo);
    }

    /**
     * 发送绑定邮件
     *
     * @return
     */
    @PostMapping("/sendBindEmail")
    @RepeatSubmit
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult sendBindEmail(@Validated(TgYz.class) @RequestBody TgEmailVo tgEmailVo) {

        return tgService.sendBindEmail(tgEmailVo);
    }

    /**
     * 绑定邮件
     *
     * @return
     */
    @PostMapping("/bindEmail")
    @RepeatSubmit
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult bindEmail(@Validated(TgYz.class) @RequestBody TgEmailVo tgEmailVo) {

        return tgService.bindEmail(tgEmailVo);
    }

    /**
     * 获取充值地址
     *
     * @param coinWalletVo
     * @return
     */
    @GetMapping("/getRechargeAddress")
    @RepeatSubmit
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult getRechargeAddress(@Validated(TgYz.class) CoinWalletVo coinWalletVo) {
        return tgService.getRechargeAddress(coinWalletVo);
    }

    /**
     * 获取可申请卡片信息
     *
     * @param tgCardInfo
     * @return
     */
    @GetMapping("/getCardInfo")
    @RepeatSubmit
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult getCardInfo(@Validated(TgYz.class) TgCardInfo tgCardInfo) {
        return tgService.getCardInfo(tgCardInfo);
    }

    /**
     * 获取卡片使用场景
     *
     * @param tgCardInfo
     * @return
     */
    @GetMapping("/getSecnario")
    @RepeatSubmit
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult getSecnario(@Validated(TgYz.class) TgCardInfo tgCardInfo) {

        return tgService.getSecnario(tgCardInfo);
    }

    /**
     * 获取用户个人信息
     *
     * @param tgVo
     * @return
     */
    @PostMapping("/getUserInfo")
    @RepeatSubmit
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult getSecnario(@Validated(TgYz.class) @RequestBody TgVo tgVo) {

        return tgService.getUserInfo(tgVo);
    }

    /**
     * 发送交易的邮件验证码
     *
     * @return
     */
    @PostMapping("/sendTradeEmail")
    @RepeatSubmit
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult sendTradeEmail(@Validated(TgYz.class) @RequestBody TgEmailVo tgEmailVo) {

        return tgService.sendTradeEmail(tgEmailVo);
    }


    /**
     * 获取token
     *
     * @param vo
     * @return
     */
    @PostMapping("/getTgToken")
    @RepeatSubmit
    public AjaxResult getTgToken(@Validated(TgYz.class) @RequestBody TgVo vo) {

        return tgService.getTgToken(vo);
    }


    /**
     * 获取卡片列表
     *
     * @param vo
     * @return
     */
    @PostMapping("/getCardList")
    @RepeatSubmit
    public AjaxResult getCardList(@Validated(TgYz.class) @RequestBody TgVo vo) {

        return tgService.getCardList(vo);
    }

    /**
     * 锁定
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/lock")
    @RepeatSubmit
    public AjaxResult lock(@Validated(TgYz.class) @RequestBody TgVo vo) {
        return tgService.lock(vo);
    }

    /**
     * 解锁
     */
    @PostMapping("/unlock")
    @RepeatSubmit
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult unlock(@Validated(TgYz.class) @RequestBody TgVo vo) {
        return tgService.unlock(vo);
    }

    /**
     * 刷新卡余额
     *
     * @param vo
     * @return
     */
    @PostMapping("/updateBalance")
    @RepeatSubmit
    @Transactional
    public AjaxResult updateBalance(@Validated(TgYz.class) @RequestBody TgVo vo) {
        return tgService.updateBalance(vo);
    }

    /**
     * 修改卡别名
     *
     * @param vo
     * @return
     */
    @PostMapping("/editAlias")
    @RepeatSubmit
    @Transactional
    public AjaxResult editAlias(@Validated(TgYz.class) @RequestBody TgVo vo) {
        return tgService.editAlias(vo);
    }

    /**
     * 获取用户邀请码
     */
    @RepeatSubmit
    @PostMapping("/getInviteCode")
    public AjaxResult getInviteCode(@Validated(TgYz.class) @RequestBody TgVo vo) {
        return tgService.getInviteCode(vo);

    }

    /**
     * 获取我的总佣金和总邀请人数
     */
    @RepeatSubmit
    @PostMapping("/totalCommission")
    public AjaxResult totalCommission(@Validated(TgYz.class) @RequestBody TgVo vo) {
        return tgService.totalCommission(vo);

    }

    /**
     * 获取佣金信息
     */
    @RepeatSubmit
    @PostMapping("/teamCommissionDetail")
    public TableDataInfo teamCommissionDetail(@Validated(TgYz.class) @RequestBody TgPageVo vo) {
        return tgService.teamCommissionDetail(vo);

    }

    /**
     * 币种充值记录
     */
    @RepeatSubmit
    @PostMapping("/coinDepositRecord")
    public TableDataInfo coinDepositRecord(@Validated(TgYz.class) @RequestBody TgPageVo vo) {
        return tgService.coinRecord(vo, "d1010","1");

    }

    /**
     * 币种提币记录
     */
    @RepeatSubmit
    @PostMapping("/coinWithdrawalRecord")
    public TableDataInfo coinWithdrawalRecord(@Validated(TgYz.class) @RequestBody TgPageVo vo) {
        return tgService.coinRecord(vo, "c1010","0");

    }

    /**
     * 币种充值/提币记录详情
     */
    @RepeatSubmit
    @PostMapping("/coinTxnDetailById")
    public AjaxResult coinTxnDetailById(@Validated(TgYz.class) @RequestBody TgPageVo vo) {
        return tgService.coinTxnDetailById(vo);

    }
    /**
     * 卡交易记录
     */
    @RepeatSubmit
    @PostMapping("/txnlist")
    public TableDataInfo txnlist(@Validated(TgYz.class) @RequestBody TgPageVo vo) {
        return tgService.txnlist(vo);

    }

    /**
     * 卡交易记录详情
     */
    @RepeatSubmit
    @PostMapping("/txnDetail")
    public AjaxResult txnDetail(@Validated(TgYz.class) @RequestBody TgPageVo vo) {
        return tgService.txnDetail(vo);

    }
    /**
     * 用户资产明细
     */
    @RepeatSubmit
    @PostMapping("/assetsPage")
    public TableDataInfo assetsPage(@Validated(TgYz.class) @RequestBody TgPageVo vo) {
        return tgService.assetsPage(vo);

    }
    /**
     * 用户资产明细记录详情
     */
    @RepeatSubmit
    @PostMapping("/assetsDetail")
    public AjaxResult assetsDetail(@Validated(TgYz.class) @RequestBody TgPageVo vo) {
        return tgService.assetsDetail(vo);

    }

    /**
     * 激活币兑换
     * @param vo
     * @return
     */
    @RepeatSubmit
    @PostMapping("/exchange")
    @Transactional
    public AjaxResult exchange(@Validated(TgYz.class) @RequestBody TgCoinExVo vo) {
        return tgService.exchange(vo);

    }
    /**
     * 用户资产明细
     */
    @RepeatSubmit
    @PostMapping("/coinFlow")
    public TableDataInfo coinFlow(@Validated(TgYz.class) @RequestBody TgPageVo vo) {
        return tgService.coinFlow(vo);

    }

    /**
     * 领取优惠券
     */
    @RepeatSubmit
    @PostMapping("/getCouponInfo")
    @Transactional
    public AjaxResult getCouponInfo(@Validated(TgYz.class) @RequestBody TgCoupon vo) {

        return tgService.getCouponInfo(vo);

    }
    /**
     * 获取自己发放的券以及自己拥有的券列表
     */
    @RepeatSubmit
    @PostMapping("/couponInfoPageByUser")
    public TableDataInfo couponInfoPageByUser(@Validated(TgYz.class) @RequestBody TgCouponPageVo vo) {
        return tgService.couponInfoPageByUser(vo);

    }


    /**
     * 优惠券可折扣金额（开卡）
     */
    @RepeatSubmit
    @PostMapping("/openDiscountAmount")
    @Transactional
    public AjaxResult openDiscountAmount(@Validated(TgYz.class) @RequestBody TgCoupon vo) {

        return tgService.openDiscountAmount(vo);

    }

    /**
     * 优惠券可折扣金额（卡片充值）
     */
    @RepeatSubmit
    @PostMapping("/rechargeDiscountAmount")
    @Transactional
    public AjaxResult rechargeDiscountAmount(@Validated(TgYz.class) @RequestBody TgCoupon vo) {

        return tgService.rechargeDiscountAmount(vo);

    }

    /**
     * 获取所有节点信息
     * @return
     */
    @RepeatSubmit
    @GetMapping("/getNodeConfig")
    public AjaxResult getNodeConfig() {
        return tgService.getNodeConfig();
    }

    /**
     * app授权
     * @param tgAppAuthDto
     * @return
     */
    @PostMapping("/appAuth")
    public AjaxResult appAuth(@RequestBody TgAppAuthDto tgAppAuthDto) {
        log.info("app授权,参数:{}", tgAppAuthDto);
        return tgService.appAuth(tgAppAuthDto.getInitData(), tgAppAuthDto.getEmail());
    }


    /**
     * 判断邀请码是否存在
     *
     * @return
     */
    @GetMapping("/isInviteCode")
    @RepeatSubmit
    public AjaxResult isInviteCode(@RequestParam("inviteCode") String inviteCode) {
        return tgService.isInviteCode(inviteCode);
    }

    /**
     * 记录mq日志
     *
     * @param sysNotice
     * @return
     */
    @PostMapping("/recordInform")
    public int appAuth(@RequestBody SysNotice sysNotice) {
        return sysNoticeService.insertNotice(sysNotice);
    }

    /**
     * 根据sysId和type 获取商户代码 和 mainAddress
     * @return
     */
    @GetMapping("/merchantCode")
    public AjaxResult merchantCode(@RequestParam("sysId") String sysId, @RequestParam("type") String type) {
        return tgService.merchantCode(sysId, type);
    }

}
