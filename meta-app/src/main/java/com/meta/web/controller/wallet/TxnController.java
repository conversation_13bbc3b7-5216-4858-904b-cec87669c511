package com.meta.web.controller.wallet;

import com.meta.common.annotation.ApiEncrypt;
import com.meta.common.constant.Constants;
import com.meta.common.core.controller.BaseController;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.enums.app.TxnType;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.system.domain.CreditCard;
import com.meta.system.domain.CreditCardLog;
import com.meta.system.domain.MetaCreditCardLogSub;
import com.meta.system.domain.app.AllTransaction;
import com.meta.system.domain.app.ExchangeRate;
import com.meta.system.domain.app.MetaUserFavoriteAddress;
import com.meta.system.domain.log.TxnDtlCode;
import com.meta.system.domain.log.TxnDtlUSD;
import com.meta.system.dto.AllTxnDto;
import com.meta.system.service.*;
import com.meta.system.vo.CoinTxnDtlVo;
import com.meta.common.exception.ServiceException;
import com.meta.web.dto.MultiParamWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/13/16:15
 */
@RestController
@RequestMapping("/txn")
public class TxnController extends BaseController {
    @Autowired
    private AllTransactionService allTransactionService;


    @Autowired
    private MetaCreditCardLogSubService metaCreditCardLogSubService;


    @Autowired
    private MetaUserFavoriteAddressService metaUserFavoriteAddressService;

    @Autowired
    private TxnService txnService;

    @Autowired
    private CreditCardLogService creditCardLogService;

    @Autowired
    private CoinTxnDtlService coinTxnDtlService;

    @Autowired
    private CreditCardService creditCardService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ExchangeRateService exchangeRateService;

    /**
     * 所有交易列表
     *
     * @param txnDto
     * @return
     */
    @ApiEncrypt
    @PostMapping("/txnlist")
    public TableDataInfo listForUSD(@RequestBody AllTxnDto txnDto) {


        Long userId = SecurityUtils.getLoginUser().getUserId();
        txnDto.setUserId(userId);

        if (StringUtils.isNotEmpty(txnDto.getTxns())) {
            List<TxnType> list = new ArrayList<>();
            if (txnDto.getTxns().contains(",")) {
                String[] arr = txnDto.getTxns().split(",");
                for (String s : arr) {
                    TxnType t = TxnType.toType(s);
                    if (t != null)
                        list.add(t);
                }
            } else {
                TxnType t = TxnType.toType(txnDto.getTxns());
                if (t != null)
                    list.add(t);
            }
            txnDto.setTxnTypes(list);
        }
        System.out.println("txnDto = " + txnDto);

        //获取数据
        Page<AllTransaction> page = allTransactionService.page(txnDto);
        List<AllTransaction> list = page.getContent();
        for (AllTransaction allTransaction : list) {
            if ("usd".equals(allTransaction.getType())) {
                allTransaction.setTxnCodeDetail(MessageUtils.message("usd_txn_type_" + allTransaction.getTxnCode()));
                allTransaction.setStatueDetail(allTransaction.getTxnStatus());
                allTransaction.setStatueDetailShow(MessageUtils.message("STATUS_" + allTransaction.getTxnStatus()));
            } else if ("coin".equals(allTransaction.getType())) {
                allTransaction.setTxnCodeDetail(MessageUtils.message("coin_txn_type_" + allTransaction.getTxnCode()));
                allTransaction.setStatueDetailShow(MessageUtils.message("STATUS_" + allTransaction.getTxnStatus()));
                allTransaction.setStatueDetail(allTransaction.getTxnStatus());
            } else if ("code".equals(allTransaction.getType())) {
                allTransaction.setTxnCodeDetail(MessageUtils.message("code_txn_type_" + allTransaction.getTxnCode()));
                allTransaction.setStatueDetailShow(MessageUtils.message("STATUS_" + allTransaction.getTxnStatus()));
                allTransaction.setTxnCurrency(MessageUtils.message("credit_card_level_" + allTransaction.getTxnCurrency()));
                allTransaction.setStatueDetail(allTransaction.getTxnStatus());
            } else {
                if (StringUtils.isNotEmpty(allTransaction.getTxnCode())) {
                    allTransaction.setTxnCodeDetail(MessageUtils.message("vcc_trade_type_" + allTransaction.getTxnCode()));
                }
                allTransaction.setStatueDetail(allTransaction.getTxnStatus());
                if (StringUtils.isNotEmpty(allTransaction.getTxnStatus())) {
                    allTransaction.setStatueDetailShow(MessageUtils.message("STATUS_" + allTransaction.getTxnStatus()));
                }
                //判断是不是kun
                if (StringUtils.isNotEmpty(allTransaction.getProduct())) {
                    CreditCard card = creditCardService.findByCardId(allTransaction.getProduct());
                    if ("89".equals(card.getCardType())) {

                        List<MetaCreditCardLogSub> subList = metaCreditCardLogSubService.findList(allTransaction.getReceiptNo());
                        for (MetaCreditCardLogSub sub : subList) {
                            if ("CLEARING".equals(sub.getTransactionType()) && allTransaction.getTxnAmount().compareTo(sub.getCardTransactionAmount()) != 0) {
                                BigDecimal amount = sub.getCardTransactionAmount();
                                if (amount != null) {
                                    allTransaction.setRefundAmount(allTransaction.getTxnAmount().subtract(amount));
                                }

                            } else if ("REVERSAL".equals(sub.getTransactionType())) {
                                allTransaction.setRefundAmount(sub.getCardTransactionAmount());
                            } else if ("REFUND".equals(sub.getTransactionType())) {
                                allTransaction.setRefundAmount(sub.getCardTransactionAmount());
                            }

                        }

                    }
                }

            }

        }


        return getDataTable(page);
    }


    private String getStatue(String statue) {
        String statueDetail = "";
        if ("APPROVED".equals(statue) || "posted".equals(statue) || "SUCCESS".equals(statue.toUpperCase())) {
            statueDetail = "SUCCESS";
        } else if ("DECLINED".equals(statue.toUpperCase()) || "FAIL".equals(statue.toUpperCase()) || "FAILED".equals(statue.toUpperCase())) {
            statueDetail = "FAIL";
        } else if ("PENDING".equals(statue.toUpperCase()) || "PROCESSING".equals(statue.toUpperCase())) {
            statueDetail = "PENDING";
        } else if ("OTHER".equals(statue)) {
            statueDetail = "OTHER";
        } else if ("CANCEL".equals(statue) || "vodi".equals(statue)) {
            statueDetail = "CANCEL";
        } else if ("CONFIRM".equals(statue)) {
            statueDetail = "CONFIRM";
        }
        return statueDetail;
    }

    /**
     * 交易详情
     *
     * @param id
     * @param type
     * @return
     */
    @GetMapping("/txnDetail")
    public AjaxResult txnDetail(@RequestParam("id") String id, @RequestParam("type") String type) {
        throw new ServiceException(MessageUtils.message("api.version.upgrade"));
    }

    /**
     * 交易详情
     *
     * @param wrapper 包含参数的包装对象
     * @return
     */
    @ApiEncrypt
    @PostMapping("/txnDetail")
    public AjaxResult txnDetailPost(@RequestBody MultiParamWrapper wrapper) {
        String id = wrapper.getString("id");
        String type = wrapper.getString("type");

        if ("usd".equals(type)) {
            TxnDtlUSD usd = txnService.findByTxnId(id);
            usd.setTxnCodeDetail(MessageUtils.message("usd_txn_type_" + usd.getTxnType()));
            usd.setStatueDetailShow(MessageUtils.message("txn_status_" + usd.getTxnStatus()));
            if (usd.getTxnStatus() != null) {
                if ("0".equals(usd.getTxnStatus().toString())) {
                    usd.setStatueDetail("FAIL");
                } else if ("1".equals(usd.getTxnStatus().toString())) {
                    usd.setStatueDetail("SUCCESS");
                } else if ("2".equals(usd.getTxnStatus().toString())) {
                    usd.setStatueDetail("PENDING");
                }
            }
            if (usd.getFromUserId() != null) {
                SysUser from = sysUserService.selectUserById(usd.getFromUserId());
                if (from != null) {
                    usd.setFromAddress(from.getUserName());
                }
            }

            if (usd.getToUserId() != null) {
                SysUser to = sysUserService.selectUserById(usd.getToUserId());
                if (to != null) {
                    usd.setToAddress(to.getUserName());
                }
            }

            return AjaxResult.success(usd);

        } else if ("coin".equals(type)) {
            CoinTxnDtlVo coin = coinTxnDtlService.getCoinTxnDetail(Long.valueOf(id));
            coin.setTxnCodeDetail(MessageUtils.message("coin_txn_type_" + coin.getTxnCode()));

            coin.setStatueDetailShow(MessageUtils.message("coin_txn_status_" + coin.getTxnStatus()));
            if ("2".equals(coin.getTxnStatus())) {
                coin.setStatueDetail("PENDING");
            } else if ("1".equals(coin.getTxnStatus())) {
                coin.setStatueDetail("SUCCESS");
            } else if ("0".equals(coin.getTxnStatus())) {
                coin.setStatueDetail("FAIL");
            }
            if (!(coin.getTxnCode().equals("c1010") || coin.getTxnCode().equals("d1010"))) {
                if (coin.getFromAddress() != null) {
                    SysUser from = sysUserService.selectUserById(Long.valueOf(coin.getFromAddress()));
                    if (from != null) {
                        coin.setFromAddress(from.getUserName());
                    }
                }

                if (coin.getToAddress() != null) {
                    SysUser to = sysUserService.selectUserById(Long.valueOf(coin.getToAddress()));
                    if (to != null) {
                        coin.setToAddress(to.getUserName());
                    }
                }
            }

            return AjaxResult.success(coin);

        } else if ("code".equals(type)) {
            TxnDtlCode usd = txnService.findByTxnIdForCode(id);
            usd.setTxnCodeDetail(MessageUtils.message("code_txn_type_" + usd.getTxnType()));
            usd.setStatueDetailShow(MessageUtils.message("txn_status_" + usd.getTxnStatus()));
            if (usd.getTxnStatus() != null) {
                if ("0".equals(usd.getTxnStatus().toString())) {
                    usd.setStatueDetail("FAIL");
                } else if ("1".equals(usd.getTxnStatus().toString())) {
                    usd.setStatueDetail("SUCCESS");
                } else if ("2".equals(usd.getTxnStatus().toString())) {
                    usd.setStatueDetail("PENDING");
                }
            }
            if (usd.getFromUserId() != null) {
                SysUser from = sysUserService.selectUserById(usd.getFromUserId());
                if (from != null) {
                    usd.setFromAddress(from.getUserName());
                }
            }

            if (usd.getToUserId() != null) {
                SysUser to = sysUserService.selectUserById(usd.getToUserId());
                if (to != null) {
                    usd.setToAddress(to.getUserName());
                }
            }
            usd.setCodeType(MessageUtils.message("credit_card_level_" + usd.getCodeType()));
            return AjaxResult.success(usd);

        } else if ("card".equals(type)) {
            CreditCardLog log = creditCardLogService.findById(id);

            CreditCard card = creditCardService.findByCardId(log.getCardId());
            String cardNo = "";
            if (card != null && StringUtils.isNotEmpty(card.getCardNo())) {
                if (card.getCardNo().contains("*")) {
                    cardNo = card.getCardNo();
                    log.setCardNo(cardNo);
                } else {
                    cardNo = AESUtils.aesDecrypt(Constants.card_key, card.getCardNo());
                    log.setCardNo(cardNo.substring(0, 4) + "********" + cardNo.substring(cardNo.length() - 4, cardNo.length()));
                }
            }
            log.setTxnCodeDetail(log.getTransactionType());
            if (StringUtils.isNotEmpty(log.getTransactionStatus())) {
                log.setStatueDetail(getStatue(log.getTransactionStatus()));
            }

            if (StringUtils.isNotEmpty(log.getTransactionType())) {
                log.setTxnCodeDetail(MessageUtils.message("vcc_trade_type_" + log.getTransactionType()));
            }
            if (StringUtils.isNotEmpty(log.getTransactionStatus())) {
                log.setStatueDetail(getStatue(log.getTransactionStatus()));
                if (StringUtils.isNotEmpty(log.getStatueDetail())) {
                    log.setStatueDetailShow(MessageUtils.message("STATUS_" + log.getStatueDetail()));
                }
            }
            if (StringUtils.isNotEmpty(log.getTransactionDate())) {
                if (!"USD".equals(log.getCardCurrency()) && StringUtils.isNotEmpty(log.getCardCurrency())) {
                    ExchangeRate rate = exchangeRateService.findByRate(log.getCardCurrency(), "USD", log.getTransactionDate());
                    if (rate != null) {
                        log.setRate(rate.getRateVal());
                    }

                }

            }
            if ("89".equals(card.getCardType())) {
                List<MetaCreditCardLogSub> list = metaCreditCardLogSubService.findList(log.getTransactionId());
                for (MetaCreditCardLogSub sub : list) {
                    sub.setTransactionTypeDetail(MessageUtils.message("vcc_trade_type_" + sub.getTransactionType()));
                    sub.setTransactionStatusDetail(MessageUtils.message("STATUS_" + sub.getTransactionStatus()));
                    if ("CLEARING".equals(sub.getTransactionType()) && log.getCardTransactionAmount().compareTo(sub.getCardTransactionAmount()) != 0) {
                        BigDecimal amount = sub.getCardTransactionAmount();
                        if (amount != null) {
                            log.setRefundAmount(log.getCardTransactionAmount().subtract(amount));
                        }
                    } else if ("REVERSAL".equals(sub.getTransactionType())) {
                        log.setRefundAmount(sub.getCardTransactionAmount());
                    } else if ("REFUND".equals(sub.getTransactionType())) {
                        log.setRefundAmount(sub.getCardTransactionAmount());
                    }
                }
                if (list.size() > 0) {
                    log.setSubList(list);
                }
            }


            return AjaxResult.success(log);
        }
        return AjaxResult.error(MessageUtils.message("operation.failed"));
    }

    /**
     * 近期地址 - 旧版API，提示用户升级
     */
    @GetMapping("/recentAddress")
    public AjaxResult recentAddress(@RequestParam("countryCode") String countryCode) {
        throw new ServiceException(MessageUtils.message("api.version.upgrade"));
    }

    /**
     * 近期地址 - 新版API
     */
    @PostMapping("/recentAddress")
    public AjaxResult recentAddress(@RequestBody AllTxnDto txnDto) {


        Long userId = SecurityUtils.getLoginUser().getUserId();

        return AjaxResult.success(allTransactionService.recentAddress(userId, txnDto.getType()));
    }

    /**
     * 查询收藏地址
     *
     * @param address
     * @return
     */
    @GetMapping("/findAddress")
    public AjaxResult findAddress(@RequestParam("address") String address) {

        Long userId = SecurityUtils.getLoginUser().getUserId();

        return AjaxResult.success(metaUserFavoriteAddressService.findAddress(address, userId));
    }

    /**
     * 收藏地址
     *
     * @param metaUserFavoriteAddress
     * @return
     */
    @ApiEncrypt
    @PostMapping("/increaseFavoriteAddress")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult increaseFavoriteAddress(@RequestBody MetaUserFavoriteAddress metaUserFavoriteAddress) {
        Long userId = SecurityUtils.getLoginUser().getUserId();

        MetaUserFavoriteAddress address = metaUserFavoriteAddressService.findAddress(metaUserFavoriteAddress.getAddress(), userId);
        if (address != null) {
            return AjaxResult.error(MessageUtils.message("favorite.address.exists"));
        }
        metaUserFavoriteAddress.setUserId(userId);
        metaUserFavoriteAddress.setCreateTime(new Date());
        metaUserFavoriteAddressService.save(metaUserFavoriteAddress);

        return AjaxResult.success();
    }

    /**
     * 删除收藏地址
     *
     * @param metaUserFavoriteAddress
     * @return
     */
    @ApiEncrypt
    @PostMapping("/delAddress")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult delAddress(@RequestBody MetaUserFavoriteAddress metaUserFavoriteAddress) {
        Long userId = SecurityUtils.getLoginUser().getUserId();

        MetaUserFavoriteAddress address = metaUserFavoriteAddressService.findAddress(metaUserFavoriteAddress.getAddress(), userId);
        if (address == null) {
            return AjaxResult.error(MessageUtils.message("favorite.address.notexists"));
        }
        metaUserFavoriteAddressService.delAddress(address.getId());
        return AjaxResult.success();
    }

    /**
     * 收藏地址 - 旧版API，提示用户升级
     */
    @GetMapping("/getFavoritePage")
    public TableDataInfo getFavoritePage(@RequestParam("countryCode") String countryCode) {
        throw new ServiceException(MessageUtils.message("api.version.upgrade"));
    }

    /**
     * 收藏地址 - 新版API
     */
    @ApiEncrypt
    @PostMapping("/getFavoritePage")
    public TableDataInfo getFavoritePagePost(@RequestBody MetaUserFavoriteAddress metaUserFavoriteAddress) {

        Long userId = SecurityUtils.getLoginUser().getUserId();
        metaUserFavoriteAddress.setUserId(userId);
        return getDataTable(metaUserFavoriteAddressService.page(metaUserFavoriteAddress));
    }

}
