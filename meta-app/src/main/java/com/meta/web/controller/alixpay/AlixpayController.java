package com.meta.web.controller.alixpay;

import com.meta.common.annotation.ApiEncrypt;
import com.meta.common.annotation.RepeatSubmit;
import com.meta.common.core.controller.BaseController;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.enums.SecurityAuthType;
import com.meta.framework.web.service.app.PreSecurityAuth;
import com.meta.system.domain.app.MetaExchangeOrderInfoAlixpay;
import com.meta.system.dto.AlixpayOrderSelectDto;
import com.meta.system.dto.AlixpayPlaceOrderDto;
import com.meta.system.dto.alixpay.ParseQrContentDto;
import com.meta.system.dto.alixpay.QRCodeInfo;
import com.meta.web.service.AlixpayService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2025/3/7
 */
@RestController
@RequestMapping("/alixpay")
@RequiredArgsConstructor
public class AlixpayController extends BaseController {

    private final AlixpayService alixpayService;

    /**
     * 解析qrContent
     */
    @ApiEncrypt
    @PostMapping("/parseQrContent")
    public AjaxResult parseQrContent(@RequestBody ParseQrContentDto parseQrContentDto) {
        return alixpayService.getInformationForQrContentAndVerify(parseQrContentDto);
    }

    /**
     * 计算报价
     */
    @ApiEncrypt
    @PostMapping("/calculateOffer")
    public AjaxResult calculateOffer(@RequestBody QRCodeInfo qrCodeInfo) {
        return alixpayService.calculateOffer(qrCodeInfo);
    }

    /**
     * 下卖单
     *
     * @param alixpayPlaceOrderDto
     * @return
     */
    @ApiEncrypt
    @PostMapping("/order")
    @RepeatSubmit
    @PreSecurityAuth(type = SecurityAuthType.ALL)
    public AjaxResult placeAnOrder(@RequestBody AlixpayPlaceOrderDto alixpayPlaceOrderDto) {
        return alixpayService.placeAnOrder(alixpayPlaceOrderDto);
    }

    /**
     * 根据条件查询 订单分页列表
     *
     * @param dto
     * @return
     */
    @ApiEncrypt
    @PostMapping("/orderPage")
    public TableDataInfo getOrderPage(@RequestBody AlixpayOrderSelectDto dto) {
        return getDataTable(alixpayService.getOrderPage(dto));
    }

    /**
     * 根据商户订单ID获取订单
     *
     * @param
     * @return
     */
    @ApiEncrypt
    @PostMapping("/getOrder")
    public AjaxResult getOrderByMerchantOrderId(@RequestBody MetaExchangeOrderInfoAlixpay dto) {
        return alixpayService.getOrderByMerchantOrderId(dto);
    }
}
