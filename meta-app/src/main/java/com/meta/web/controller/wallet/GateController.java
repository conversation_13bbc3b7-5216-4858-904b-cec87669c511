package com.meta.web.controller.wallet;

import com.alibaba.fastjson.JSONObject;
import com.meta.common.annotation.RepeatSubmit;
import com.meta.common.constant.Constants;
import com.meta.common.constant.HttpStatus;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.core.page.TableSupport;
import com.meta.common.valid.CardRecharge;
import com.meta.framework.web.service.SysLoginService;
import com.meta.system.domain.Wallet;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.ISysUserService;
import com.meta.system.service.MetaGatePrePayService;
import com.meta.system.service.WalletService;
import com.meta.web.service.GateService;
import com.meta.web.utils.GateUtil;
import com.meta.web.vo.GateAccessVo;
import com.meta.web.vo.GateTokenVo;
import com.meta.web.vo.RechangeVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/03/26/18:07
 */
@RestController
@RequestMapping("/gate")
public class GateController {

    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private GateService gateService;

    @Autowired
    private GateUtil gateUtil;

    @Autowired
    private WalletService walletService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private MetaGatePrePayService metaGatePrePayService;

    /**
     * 预订单
     *
     * @param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/gateRecharge")
    @RepeatSubmit
    public AjaxResult gateRecharge(@Validated(CardRecharge.class) @RequestBody RechangeVo rechangeVo) {

        if (!"USDT".equals(rechangeVo.getCoin())) {
            return AjaxResult.error("Only USDT can be purchased");
        }
        return AjaxResult.success(gateService.getPrePayId(rechangeVo));
    }

    /**
     * 授权
     *
     * @param
     * @return
     */
    @GetMapping("/getOauth")
    public AjaxResult getOauth(@RequestParam("agent") String agent) {

        return AjaxResult.success(gateUtil.gateOauth(agent));
    }

    /**
     * 授权
     *
     * @param
     * @return
     */
    @PostMapping("/getGateToken")
    public AjaxResult getGateToken(@RequestBody GateTokenVo gateTokenVo) {
        GateAccessVo token = gateUtil.getToken(gateTokenVo.getGrantType(), gateTokenVo.getCode());
        logger.info("====token===============" + token);
        return AjaxResult.success(token);
    }

    /**
     * 获取用户邮箱和uuid
     *
     * @param gateAccessVo
     * @return
     */
    @PostMapping("/getGateUserEmail")
    public AjaxResult getGateUserEmail(@RequestBody GateAccessVo gateAccessVo) {
        logger.info("========gateAccessVo:" + gateAccessVo);
        JSONObject jsonObject = gateUtil.getGateUserEmail(gateAccessVo.getAccessToken(), gateAccessVo.getTokenType());
        AjaxResult ajax = AjaxResult.success();
        ajax.put("isUser", "N");
        ajax.put("uid", null);
        ajax.put("email", null);
        if (jsonObject != null) {
            ajax.put("uid", jsonObject.getString("uid"));
            ajax.put("email", jsonObject.getString("email"));
            redisTemplate.opsForValue().set("token" + jsonObject.getString("uid") + "_gate.io", gateAccessVo.getTokenType() + " " + gateAccessVo.getAccessToken(), 1, TimeUnit.DAYS);
            logger.info("查询是否注册过的gate用户");
            Wallet wallet = walletService.findByGateUid("gate.io_" + jsonObject.getString("uid"));
            if (wallet != null) {
                ajax.put("isUser", "Y");
                logger.info("是用户");
                SysUser sysUser = sysUserService.selectUserById(wallet.getUserId());
                ajax.put("sys_email", sysUser.getUserName());
            } else {
                SysUser sysUser = sysUserService.selectUserByUserName(jsonObject.getString("email"));
                if (sysUser != null) {
                    ajax.put("isUser", "Y");
                    logger.info("是用户");
                    ajax.put("sys_email", sysUser.getUserName());
                }
            }
        }
        return ajax;
    }


    /**
     * gate用户免登录操作
     *
     * @param gateAccessVo
     * @return
     */
    @PostMapping("/gateUserLogin")
    public AjaxResult gateUserLogin(@RequestBody GateAccessVo gateAccessVo) {
        if (redisTemplate.hasKey("token" + gateAccessVo.getUid() + "_gate.io")) {
            String s = redisTemplate.opsForValue().get("token" + gateAccessVo.getUid() + "_gate.io");
            if (s.equals(gateAccessVo.getTokenStr())) {

                AjaxResult ajax = AjaxResult.success();
                String token = loginService.gateLoginByEmail(gateAccessVo.getEmail());
                ajax.put(Constants.TOKEN, token);
                return ajax;
            } else {
                return AjaxResult.error("Error");
            }
        } else {
            return AjaxResult.error("Error");
        }
    }


    /**
     * gate.io 订单通知接口
     */
    @PostMapping("/payBack")
    public ResponseEntity<String> payBack(@RequestBody String requestBody, HttpServletRequest request) {

        String timestamp = request.getHeader("x-gatepay-timestamp");
        String nonce = request.getHeader("x-gatepay-nonce");
        String signature = request.getHeader("x-gatepay-signature");
        return gateUtil.dealNotice(timestamp, nonce, signature, requestBody);
    }

    /**
     * 授权回调地址
     * @param code
     * @param state
     * @return
     */
    @GetMapping("/authBack")
    public RedirectView authBack(@RequestParam("code") String code, @RequestParam("state") String state) {

        String appUrl = sysConfigService.selectConfigByKey("gate_to_kz_url");
        String url = appUrl + "pages/apply/exhibit?code=" + code + "&state" + state;
        RedirectView redirectView = new RedirectView(url);
        return redirectView;

    }
    /**
     * 下单总金额
     * @param email
     * @return
     */
    @GetMapping("/tradeSum")
    public AjaxResult tradeSum(@RequestParam("email") String email) {
        return AjaxResult.success(metaGatePrePayService.sumData(email));
    }
    /**
     * 下单总金额
     * @param email
     * @return
     */
    @GetMapping("/tradeList")
    public TableDataInfo tradeList(@RequestParam("email") String email) {

        return getDataTable( metaGatePrePayService.tradeList(email));
    }
    /**
     * 响应请求分页数据
     */
    protected TableDataInfo getDataTable(Page<?> page) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(page.getContent());
        rspData.setTotal(page.getTotalElements());
        rspData.setSize(pageReq.getPageSize());
        rspData.setPages(page.getTotalPages());
        rspData.setCurrent(pageReq.getPageNo()+1);
        return rspData;
    }
}
