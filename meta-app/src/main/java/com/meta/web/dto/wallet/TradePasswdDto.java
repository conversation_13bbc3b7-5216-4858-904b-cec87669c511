package com.meta.web.dto.wallet;

import com.meta.common.utils.request.IInsert;
import com.meta.common.utils.request.IUpdate;
import com.meta.framework.web.service.app.PreSecurityAuth;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class TradePasswdDto {
    @NotEmpty(message = "旧交易密码不能为空",groups = {IUpdate.class})
    private String oldPassword;
    @NotEmpty(message = "新交易密码不能为空",groups = {IUpdate.class, IInsert.class})
    private String newPassword;

    @NotEmpty(message = "验证码不能为空",groups = {IInsert.class})
    private String code;


    /**
     * 校验交易密码的时候
     */
    @NotEmpty(message = "交易密码不能为空",groups = {PreSecurityAuth.class})
    private String tradePasswd;
}