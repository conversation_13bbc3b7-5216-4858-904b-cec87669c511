package com.meta.web.dto;

/**
 * 通用字符串包装类，用于接收POST请求中的字符串内容
 */
public class StringWrapper {
    private String value;

    /**
     * 默认构造函数
     */
    public StringWrapper() {}

    /**
     * 带参数的构造函数
     * @param value 字符串值
     */
    public StringWrapper(String value) {
        this.value = value;
    }

    /**
     * 获取字符串值
     * @return 字符串值
     */
    public String getValue() {
        return value;
    }

    /**
     * 设置字符串值
     * @param value 字符串值
     */
    public void setValue(String value) {
        this.value = value;
    }
}
