package com.meta.web.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class WithdrawalDto {
    @NotEmpty(message = "数字货币代码不能为空")
    private String coinCode;

    @NotNull(message = "提现数量不能为空")
    private BigDecimal quantity;

    @NotEmpty(message = "提现地址不能为空")
    private String address;

    // 手续费
    private BigDecimal fee;

    //币种网络
    private String type;

}
