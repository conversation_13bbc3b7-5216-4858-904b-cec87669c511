package com.meta.web.dto.user;

import com.meta.common.constant.Constants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserRegister {
    // 邮箱
    @NotNull
    @Email(regexp = Constants.EMAIL_REGULAR,message = "邮箱格式错误!")
    private String email;
    // 密码
    @NotNull(message = "密码不能为空")
    @Length(min = 6,message = "密码长度不能小于6位")
    private String password;
    // 验证码
    @NotNull(message = "验证码不能为空")
    private String code;
    // 邀请码
    private String inviteCode;

    //gate life 上注册用户的uid
    private String gateUid;

    //图形验证码
    private String strCode;


    @Override
    public String toString() {
        return "UserRegister{" +
                "email='" + email + '\'' +
                ", password='******'" +
                ", code='" + code + '\'' +
                ", inviteCode='" + inviteCode + '\'' +
                '}';
    }
}
