package com.meta.web.dto.user;

import com.meta.common.constant.Constants;
import com.meta.common.utils.request.IInsert;
import com.meta.common.utils.request.IUpdate;
import com.meta.framework.web.service.app.PreSecurityAuth;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;

/**
 * 重置密码DTO
 */
@Data
public class RestPasswdDto {
    @Email(regexp = Constants.EMAIL_REGULAR,message = "邮箱格式错误!")
    @NotEmpty(message = "邮箱不能为空",groups = {IInsert.class})
    private String email;

    @NotEmpty(message = "新密码不能为空",groups = {IInsert.class, PreSecurityAuth.class})
    private String password;
    @NotEmpty(message = "验证码不能为空",groups = {IInsert.class})
    private String code;

    @NotEmpty(message = "旧密码不能为空",groups = {IUpdate.class})
    private String oldPassword;

    @NotEmpty(message = "新密码不能为空",groups = {IUpdate.class})
    private String newPassword;

    private String strCode;
}
