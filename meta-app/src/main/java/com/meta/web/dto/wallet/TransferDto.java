package com.meta.web.dto.wallet;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class TransferDto {
    @NotEmpty(message = "对方邮箱不能为空")
    private String email;

    @NotNull(message = "金额不能为空")
    private BigDecimal amount;

//    @NotNull(message = "币种不能为空")c
    private String  coin;

    private String txnDesc;
}
