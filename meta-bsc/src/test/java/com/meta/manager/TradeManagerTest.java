package com.meta.manager;

import com.meta.system.service.ISysConfigService;
import com.meta.utils.QuikNodeApi;
import com.meta.utils.WeiToCoinUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.math.BigInteger;

import static org.mockito.Mockito.*;

class TradeManagerTest {
//
//    @InjectMocks
//    private TradeManager tradeManager;
//    @Mock
//    private ISysConfigService configService;
//    @Mock
//    private QuikNodeApi quikNodeApi;
//
//    @BeforeEach
//    void setUp() {
//        MockitoAnnotations.openMocks(this);
//        /*
//         * 网络
//         */
//        // base测试网
//        when(configService.selectConfigByKey("quick_node_url_for_base"))
//                .thenReturn("https://docs-demo.base-sepolia.quiknode.pro");
//        when(configService.selectConfigByKey("BASE_CHAIN_ID"))
//                .thenReturn("84532");
//        // //base主网
//        // when(configService.selectConfigByKey("quick_node_url_for_base")).thenReturn("https://docs-demo.base-mainnet.quiknode.pro");
//        //arb测试网
////        when(configService.selectConfigByKey("quick_node_url_for_arb")).thenReturn("https://docs-demo.arbitrum-sepolia.quiknode.pro");
////        when(configService.selectConfigByKey("ARB_CHAIN_ID"))
////                .thenReturn("421614");
//        // //arb主网
//        // when(configService.selectConfigByKey("quick_node_url_for_arb")).thenReturn("https://docs-demo.arbitrum-mainnet.quiknode.pro");
//        /*
//         * 合约地址
//         */
//        when(configService.selectConfigByKey("USDT_CONTRACT_ADDRESS_FOR_BASE"))
//                .thenReturn("******************************************");
//        when(quikNodeApi.ethNewFilter(any(), any(), any(), any())).thenCallRealMethod();
//        when(quikNodeApi.ethGetFilterLogs(any(), any())).thenCallRealMethod();
//        when(quikNodeApi.ethGetBalance(any(), any())).thenCallRealMethod();
//        /*
//         * 配置
//         */
//        when(configService.selectConfigByKey("BSC_GAS_PRICE"))
//                .thenReturn("5"); // Gas price in GWEI
//
//        when(configService.selectConfigByKey("BSC_GAS_LIMIT"))
//                .thenReturn("52000"); // Standard gas limit
//    }
//
//    @Test
//    void getTokenBalance() {
//        BigInteger usdt = tradeManager.getTokenBalance(
//                configService.selectConfigByKey("quick_node_url_for_base"),
//                configService.selectConfigByKey("USDT_CONTRACTA_DDRESS"),
//                configService.selectConfigByKey("BSC_WALLET_ADDRESS"));
//        System.out.println(usdt);
//    }
//
//    @Test
//    void transferToken() {
//        String url = configService.selectConfigByKey("quick_node_url");
//        String fromAddress = "******************************************";
//        String fromPrivateKey = "275c54cde66c36144de4195d20916f50701f01995afb51600c1239fd8a964d0a"; // Replace
//        // with
//        // actual
//        // test
//        // private
//        // key
//        long chainId = 84532L; // Mainnet chain ID
//        String toAddress = "******************************************"; // Replace with test recipient address
//        BigDecimal amount = new BigDecimal("0.01"); // Test amount
//        String contractAddress = configService.selectConfigByKey("USDT_CONTRACTA_DDRESS");
//
//        // Mock necessary responses
//        when(quikNodeApi.ethGetTransactionCount(anyString(), anyString(), anyString()))
//                .thenCallRealMethod(); // Mock nonce response
//        when(quikNodeApi.decodeQuantity(anyString()))
//                .thenCallRealMethod(); // Mock nonce response
//        when(quikNodeApi.ethSendRawTransaction(anyString(), anyString()))
//                .thenCallRealMethod(); // Mock transaction hash response
//
//        // Execute transfer
//        String txHash = tradeManager.transferToken(
//                url,
//                fromAddress,
//                fromPrivateKey,
//                chainId,
//                toAddress,
//                amount.multiply(new BigDecimal("1000000")).toBigInteger(),
//                contractAddress, configService.selectConfigByKey("GAS_PRICE_FOR_BASE"), configService.selectConfigByKey("GAS_LIMIT_FOR_ META"));
//
//        // Verify response
//        Assertions.assertNotNull(txHash);
//    }
//
//    @Test
//    void transfer() {
//        String url = configService.selectConfigByKey("quick_node_url_for_arb");
//        String fromAddress = "******************************************";
//        String fromPrivateKey = "275c54cde66c36144de4195d20916f50701f01995afb51600c1239fd8a964d0a"; // Replace
//        // with
//        // actual
//        // test
//        // private
//        // key
//        long chainId = Long.parseLong(configService.selectConfigByKey("ARB_CHAIN_ID")); // Mainnet chain ID
//        String toAddress = "******************************************"; // Replace with test recipient address
//        BigDecimal amount = new BigDecimal("0.0005"); // Test amount
//        // Mock necessary responses
//        when(quikNodeApi.ethGetTransactionCount(anyString(), anyString(), anyString()))
//                .thenCallRealMethod(); // Mock nonce response
//        when(quikNodeApi.ethSendRawTransaction(anyString(), anyString()))
//                .thenCallRealMethod(); // Mock transaction hash response
//        when(quikNodeApi.decodeQuantity(anyString()))
//                .thenCallRealMethod();
//
//        // Execute transfer
//        String txHash = tradeManager.transfer(
//                url,
//                fromAddress,
//                fromPrivateKey,
//                chainId,
//                toAddress,
//                amount);
//
//        // Verify response
//        Assertions.assertNotNull(txHash);
//    }
//
//    @Test
//    void testEstimateGas() {
//        String url = configService.selectConfigByKey("quick_node_url_for_base");
//        String fromAddress = "******************************************";
//        String toAddress = "******************************************";
//        BigInteger value = BigInteger.valueOf(1000000000000000000L); // 1 ETH in Wei
//
//        BigInteger gas = tradeManager.estimateGas(url, fromAddress, toAddress, value);
//        BigDecimal bigDecimal = WeiToCoinUtils.convertWeiToCoin(new BigDecimal(gas));
//
//        // Verify gas estimation is greater than zero
//        Assertions.assertTrue(gas.compareTo(BigInteger.ZERO) > 0);
//    }
}
