package com.meta.manager;

import com.alibaba.fastjson.JSONArray;
import com.meta.system.service.ISysConfigService;
import com.meta.utils.QuikNodeApi;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigInteger;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

class ScanningManagerTest {

//    @InjectMocks
//    private ScanningManager scanningManager;
//    @Mock
//    private ISysConfigService configService;
//    @Mock
//    private QuikNodeApi quikNodeApi;
//
//    @BeforeEach
//    void setUp() {
//        MockitoAnnotations.openMocks(this);
//        /*
//         * 网络
//         */
////        //base测试网
////        when(configService.selectConfigByKey("quicknode_url")).thenReturn("https://docs-demo.base-sepolia.quiknode.pro");
//        //base主网
//        when(configService.selectConfigByKey("quick_node_url")).thenReturn("https://docs-demo.base-mainnet.quiknode.pro");
////        //arb测试网
////        when(configService.selectConfigByKey("quicknode_url")).thenReturn("https://docs-demo.arbitrum-sepolia.quiknode.pro");
////        //arb主网
////        when(configService.selectConfigByKey("quicknode_url")).thenReturn("https://docs-demo.arbitrum-mainnet.quiknode.pro");
//        /*
//         *合约地址
//         */
//        when(configService.selectConfigByKey("USDT_CONTRACTA_DDRESS")).thenReturn("******************************************");
//
//
//        when(quikNodeApi.ethNewFilter(any(), any(), any(), any())).thenCallRealMethod();
//        when(quikNodeApi.ethGetFilterLogs(any(), any())).thenCallRealMethod();
//        when(quikNodeApi.ethGetBalance(any(), any())).thenCallRealMethod();
//
//    }
//
//    @Test
//    void createFilterId() {
//        String url = configService.selectConfigByKey("quick_node_url");
//        String filterId = scanningManager.createFilterId(new BigInteger("28003602"), new BigInteger("28003607"), url);
//        System.out.println(filterId);
//    }
//
//    @Test
//    void getTransactionById() {
//        String url = configService.selectConfigByKey("quick_node_url");
//        String filterId = scanningManager.createFilterId(new BigInteger("28003602"), new BigInteger("28003607"), url);
//        JSONArray jsonArray = scanningManager.getTransactionById(filterId, url);
//        System.out.println(jsonArray);
//        new BigInteger(jsonArray.getJSONObject(0).getString("data").substring(2), 16);
//
//
//    }
//
//
//    @Test
//    void trueTest() {
//        when(quikNodeApi.ethBlockNumber(any())).thenCallRealMethod();
//        when(quikNodeApi.ethBlockNumberForBigInteger(any())).thenCallRealMethod();
//        BigInteger quickNodeLastBlockNumber = quikNodeApi.ethBlockNumberForBigInteger(configService.selectConfigByKey("quick_node_url"));
//        System.out.println(quickNodeLastBlockNumber);
//        // 28006967
//        // 28007039
//        // 28007262
//        // 318987497
//        // 135366060
//
//    }
}
