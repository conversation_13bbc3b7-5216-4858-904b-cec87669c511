package com.meta.config;

import com.meta.service.MainSysIdAddressService;
import com.meta.system.domain.MetaMainAddress;
import com.meta.system.service.BepCstaddressService;
import com.meta.system.service.MetaMainAddressService;
import com.meta.utils.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class MyApplicationRunner implements ApplicationRunner {

    @Autowired
    private BepCstaddressService bepCstaddressService;

    @Autowired
    private MainSysIdAddressService mainSysIdAddressService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ConfigurableApplicationContext context;

    @Autowired
    private MetaMainAddressService metaMainAddressService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 在服务启动时要执行的代码
//        log.info("服务启动时开始刷新bep用户钱包");
//        List<MetaMainAddress> bep20Addresses = mainSysIdAddressService.getMainAddressList("BEP20");
//        //kazepay和H5的数据
//        for (MetaMainAddress metaMainAddress : bep20Addresses) {
//            MetaMainAddress data = metaMainAddressService.getData(metaMainAddress.getType(), metaMainAddress.getSysId());
//            if (data == null || !data.getMainaddress().equals(metaMainAddress.getMainaddress())) {
//                context.close();
//                log.info("服务启动异常，配置钱包不一致，服务关闭！！！");
//                break;
//            }
//            String sysId = metaMainAddress.getSysId();
//            String keyBep20 = Constants.BEP_ADDRESS + sysId;
//            redisTemplate.delete(keyBep20);
//            bepCstaddressService.refreshAddress(metaMainAddress.getMainaddress(), sysId);
//        }


    }
}
