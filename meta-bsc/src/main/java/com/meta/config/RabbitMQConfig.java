package com.meta.config;

import com.meta.common.constant.Constants;
import org.springframework.amqp.core.*;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2024/08/23/11:30
 */
@Component
public class RabbitMQConfig {

    @Bean
    public MessageConverter messageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    @Bean
    public Queue WorkQueue1() {

        return QueueBuilder.durable(Constants.queue_h5_listening).build();

    }

    @Bean
    public DirectExchange directExchange() {

        return new DirectExchange(Constants.exchange_name_notify, true, false);

    }

    @Bean
    public Binding bindingWorkQueue1(Queue WorkQueue1, DirectExchange directExchange) {
        return BindingBuilder.bind(WorkQueue1).to(directExchange).with(Constants.routing_key_h5_listening);
    }


}
