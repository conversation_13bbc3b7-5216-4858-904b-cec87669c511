package com.meta.config;

import com.meta.common.constant.Constants;
import org.springframework.amqp.core.*;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @date 2024/08/23/11:30
 */
@Configuration
public class RabbitConfig {

    @Bean
    public DirectExchange alarmExchange() {
        return new DirectExchange(Constants.exchange_name_notify, true, false);
    }

    @Bean
    public Queue alarmQueue() {
//        Map<String, Object> args = new HashMap<>();
//        args.put("x-dead-letter-exchange", Constants.exchange_name_dlx);
//        args.put("x-dead-letter-routing-key", Constants.routing_key_dlx_amount_alert);

        return QueueBuilder.durable(Constants.queue_amount_alert)
//                .withArguments(args)
                .build();
    }

    @Bean
    public Binding binding( Queue alarmQueue, DirectExchange alarmExchange) {
        return BindingBuilder.bind(alarmQueue).to(alarmExchange).with(Constants.routing_key_amount_alert);
    }

    /**
     * 声明死信队列
     */
    @Bean
    public Queue deadLetterQueue() {
        return new Queue(Constants.routing_key_dlx_amount_alert);
    }

    /**
     * 声明死信交换机
     */
    @Bean
    public DirectExchange deadLetterExchange() {
        return new DirectExchange(Constants.exchange_name_dlx);
    }

    /**
     * 将死信队列绑定到死信交换机
     */
    @Bean
    public Binding deadLetterBinding(Queue deadLetterQueue, DirectExchange deadLetterExchange) {
        return BindingBuilder.bind(deadLetterQueue).to(deadLetterExchange).with(Constants.routing_key_dlx_amount_alert);
    }

}
