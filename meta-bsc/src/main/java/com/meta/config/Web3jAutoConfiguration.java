package com.meta.config;

import com.meta.system.email.SendEmail;
import com.meta.system.service.ISysConfigService;
import com.meta.utils.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.admin.Admin;
import org.web3j.protocol.core.methods.response.Web3ClientVersion;
import org.web3j.protocol.http.HttpService;

import java.io.IOException;

/**
 * web3j auto configuration for Spring Boot.
 */
@Configuration
@ConditionalOnClass(Web3j.class)
public class Web3jAutoConfiguration {

    private static Log log = LogFactory.getLog(Web3jAutoConfiguration.class);

    @Autowired
    private ISysConfigService configService;
    @Autowired
    private SendEmail sendEmail;

    @Bean
    @ConditionalOnMissingBean
    public Web3j web3j() {


        String url_1 = configService.selectConfigByKey("BSC_RPC_URL");
        String url_2 = configService.selectConfigByKey("BSC_RPC_URL_2");
        Web3j web3j = null;
        //节点1
        web3j = Web3j.build(new HttpService(url_1));
        boolean isConnected = tryConnect(web3j);
        if (!isConnected) {
            log.info("BSC无法连接节点1，节点1失败");
//            sendEmail.remindMail("BSC无法连接节点1，节点1失败！"+url_1, "BSC监听连接节点错误警告");

            //节点2
            web3j = Web3j.build(new HttpService(url_2));
            isConnected = tryConnect(web3j);
            if (!isConnected) {
//                sendEmail.remindMail("BSC无法连接所有节点！", "BSC监听连接节点错误警告");
            }
        }

        return web3j;
    }

    @Bean
    @ConditionalOnMissingBean
    public Admin admin() {
        Admin admin = Admin.build(new HttpService(configService.selectConfigByKey("BSC_RPC_URL")));
        return admin;
    }

    private boolean tryConnect(Web3j web3j) {
//        try {
            // 尝试连接节点
//            Web3ClientVersion web3ClientVersion = web3j.web3ClientVersion().send();
//            if (web3ClientVersion.hasError()) {
//                log.info("连接节点失败：" + web3ClientVersion.getError().getMessage());
                return false;
//            } else {
//                log.info("连接节点成功");
//                return true;
//            }

//        } catch (IOException e) {
//            log.info("连接节点失败：" + e.getMessage());
//            return false;
//        }
    }

}
