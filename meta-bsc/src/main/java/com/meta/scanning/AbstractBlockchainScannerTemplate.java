package com.meta.scanning;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meta.common.exception.CustomException;
import com.meta.common.utils.ThreadPoolUtil;
import com.meta.manager.TradeManager;
import com.meta.producer.NormalRabbitProducer;
import com.meta.service.MainSysIdAddressService;
import com.meta.system.domain.MetaMainAddress;
import com.meta.system.domain.app.AbstractCstaddress;
import com.meta.system.domain.app.AbstractTransactions;
import com.meta.system.dto.MqMonitorDto;
import com.meta.system.enums.CoinType;
import com.meta.system.service.ISysConfigService;
import com.meta.utils.CoinDataUtil;
import com.meta.utils.QuikNodeApi;
import com.meta.utils.WeiToCoinUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

import static com.alibaba.fastjson.JSON.parseArray;

/**
 * <AUTHOR>
 * @date 2025/3/25 17:03
 **/
@Slf4j
public abstract class AbstractBlockchainScannerTemplate<T1 extends AbstractCstaddress, T2 extends AbstractTransactions> {

    public static final String TOPICS = "topics";
    public static final String BLOCK_NUMBER = "blockNumber";

    protected abstract BigInteger getInterval();

    protected abstract void saveTransactions(T2 transactions);

    // 获取Redis key
    protected abstract String getRedisBlockNumberKey();

    // 获取quicknode Url
    protected abstract String getQuickNodeUrl();

    // 获取Usdt合约地址
    protected abstract String getUsdtContractAddress();

    // 获取Usdc合约地址
    protected abstract String getUsdcContractAddress();

    // 获取关联合约地址
    protected String getCurrentContractAddress(CoinType coinType) {
        if (coinType.equals(CoinType.USDC)) {
            return getUsdcContractAddress();
        } else if (coinType.equals(CoinType.USDT)) {
            return getUsdtContractAddress();
        } else {
            throw new CustomException("合约地址不匹配");
        }
    }

    // 获取quickNodeApi
    protected abstract QuikNodeApi getQuikNodeApi();

    // 获取区块链名称
    protected abstract String getChainName();

    // 模板方法
    public final void scan() {
        // 1. 获取最新区块
        BigInteger latestBlock = getLatestBlock();

        // 2. 获取上次处理的区块
        BigInteger beginBlock = getBeginBlock();
        if (beginBlock == null) {
            beginBlock = latestBlock.subtract(getInterval());
        }

        // 3. 计算本次要处理的区块范围
        BigInteger endBlock = beginBlock.add(getInterval()).compareTo(latestBlock) <= 0
                ? beginBlock.add(getInterval())
                : latestBlock;

        call(beginBlock, endBlock);
    }

    private void call(BigInteger doneBlock, BigInteger latestBlock) {
        // 4. 创建过滤器
        String filterId = createFilterId(doneBlock, latestBlock);

        // 5. 获取交易记录
        //获取当前区块链名称
        log.info("区块链:{} 开始获取交易记录，开始区块:{}，结束区块:{}", getChainName(), doneBlock, latestBlock);
        JSONArray transactions = getTransactions(filterId);
        if (transactions != null && !transactions.isEmpty()) {
            log.debug("区块链:{} 获取到交易记录，数量:{}", getChainName(), transactions.size());
            // 6. 处理交易记录
            try {
                handleTransactions(transactions);
            } catch (Exception e) {
                log.error("区块链:{} 处理交易记录失败，开始区块:{}，结束区块:{},错误信息:{}", getChainName(), doneBlock, latestBlock, e.getMessage(), e);
            }
        }

        // 7. 保存处理进度
        saveDoneBlock(latestBlock);
    }


    // 抽象方法，由具体链的实现类实现
    protected BigInteger getLatestBlock() {
        return getQuikNodeApi().ethBlockNumberForBigInteger(getQuickNodeUrl());
    }

    protected String createFilterId(BigInteger fromBlock, BigInteger toBlock) {
        List<String> tokenList = new ArrayList<>();

        String usdtContractAddress = getUsdtContractAddress();
        if (StringUtils.isNotBlank(usdtContractAddress)) {
            tokenList.add(usdtContractAddress);
        }
        String usdcContractAddress = getUsdcContractAddress();
        if (StringUtils.isNotBlank(usdcContractAddress)) {
            tokenList.add(usdcContractAddress);
        }

        String fromBlockHex = "0x" + fromBlock.toString(16).toUpperCase();
        String toBlockHex = "0x" + toBlock.toString(16).toUpperCase();

        return getQuikNodeApi().ethNewFilter(fromBlockHex, toBlockHex, tokenList, getQuickNodeUrl());

    }

    protected JSONArray getTransactions(String filterId) {
        String filterLogs = getQuikNodeApi().ethGetFilterLogs(filterId, getQuickNodeUrl());
        if (org.springframework.util.StringUtils.hasText(filterLogs)) {
            return parseArray(filterLogs);
        }
        throw new CustomException(String.format("区块链:%s,获取交易记录失败", getChainName()));
    }

    protected void handleTransactions(@NotNull JSONArray transactions) {
        if (transactions.isEmpty()) {
            log.error("区块链:{} 交易记录为空", getChainName());
            return;
        }

        if (getUserWalletAddressSet(null).isEmpty()) {
            log.warn("区块链:{} 用户地址集合为空", getChainName());
            return;
        }

        Set<String> mainWalletAddressSet = getMainWallletAddressSet();
        Set<String> userWalletAddressSet = getUserWalletAddressSet(null);

        transactions.forEach(item -> {
            JSONObject jsonObject = (JSONObject) item;
            String txid = jsonObject.getString("transactionHash");
            try {

                if (jsonObject.getJSONArray(TOPICS).size() < 3) {
                    log.info("区块链:{} ,交易记录不符合要求,txid:{}", getChainName(), txid);
                    return;
                }
                String from = "0x" + jsonObject.getJSONArray(TOPICS).get(1).toString().substring(26);
                String to = "0x" + jsonObject.getJSONArray(TOPICS).get(2).toString().substring(26);

                //判断是哪种合约地址
                String token = jsonObject.getString("address"); //这是Token合约地址
                String usdtContractAddress = getUsdtContractAddress();
                String usdcContractAddress = getUsdcContractAddress();
                CoinType coinType;
                if (token.equals(usdtContractAddress.toLowerCase())) {
                    coinType = CoinType.USDT;
                } else if (token.equals(usdcContractAddress.toLowerCase())) {
                    coinType = CoinType.USDC;
                } else {
                    log.error("区块链:{} ,合约地址不匹配,txid:{}", getChainName(), txid);
                    return;
                }


                //主钱包入账(collect)
                if (mainWalletAddressSet.contains(to)) {
                    mainWalletReceiveHandler(from, txid, jsonObject, to, userWalletAddressSet, coinType);
                }
                //主钱包出账(send)
                else if (mainWalletAddressSet.contains(from)) {
                    mainWalletSendHandler(from, txid, jsonObject, to, coinType);
                }
                //用户钱包入账(receive)
                else if (userWalletAddressSet.contains(to)) {
                    userWalletReceiveHandler(to, txid, jsonObject, from, coinType);
                }
                //用户钱包出账(报错)
                else if (userWalletAddressSet.contains(from)) {
                    log.error("warn");
                }

            } catch (Exception e) {
                log.error("处理交易记录失败，txid:{}，message:{}", txid, e.getMessage(), e);
            }
        });

    }

    protected abstract String getNetWork();

    private void mainWalletSendHandler(String from, String txid, JSONObject jsonObject, String to, CoinType coinType) {
        T2 transaction;
        log.debug("区块链:{} 检测到主钱包出账记录，主钱包地址:{}，另一方地址:{}，txid:{}", getChainName(), from, to, txid);
        transaction = createTransaction(getQuickNodeUrl(), txid, jsonObject, to, from);
        transaction.setType("send");
        transaction.setIsSync(2);
        saveTransactions(transaction);

        MainSysIdAddressService mainSysIdAddressService = SpringUtil.getBean(MainSysIdAddressService.class);
        MetaMainAddress mainWallet = mainSysIdAddressService.findAddress(from);
        alert(from, to, transaction, mainWallet.getSysId(), coinType);
    }

    private void mainWalletReceiveHandler(String from, String txid, JSONObject jsonObject, String to, Set<String> userWalletAddressSet, CoinType coinType) {
        T1 userWalletByAddress;
        T2 transaction;
        log.debug("区块链:{} 检测到主钱包入账记录，主钱包地址:{}，另一方地址:{}，txid:{}", getChainName(), to, from, txid);
        transaction = createTransaction(getQuickNodeUrl(), txid, jsonObject, to, from);
        transaction.setType("collect");
        transaction.setIsSync(2);
        MainSysIdAddressService mainSysIdAddressService = SpringUtil.getBean(MainSysIdAddressService.class);
        MetaMainAddress mainWallet = mainSysIdAddressService.findAddress(to);
        saveTransactions(transaction);
        alert(from, to, transaction, mainWallet.getSysId(), coinType);

        //来源是用户钱包，属于归集，需要操作入账
        if (userWalletAddressSet.contains(from)) {
            log.debug("区块链:{} 该主钱包入账属于用户归集，txid:{}", getChainName(), transaction.getTxid());
            userWalletByAddress = getUserWalletByAddress(from);
            //如果是第三方，则需要发送处理
            boolean isThirdParty = !mainWallet.getSysId().equals(getKazePaySysId());
            if (isThirdParty && isSupportThirdParty()) {
                TradeManager tradeManager = SpringUtil.getBean(TradeManager.class);
                tradeManager.pushData(mainWallet.getSysId(), transaction, true, getNetWork(), coinType);
            }
            //不是第三方则直接操作入账
            else {
                handlerTxn(transaction, userWalletByAddress, coinType);
            }

        }

    }

    protected abstract boolean isSupportThirdParty();

    private void userWalletReceiveHandler(String to, String txid, JSONObject jsonObject, String from, CoinType coinType) {
        T1 userWalletByAddress = getUserWalletByAddress(to);
        T2 transaction;
        log.debug("区块链:{} 检测到用户地址入账交易记录，地址:{}", getChainName(), to);
        transaction = createTransaction(getQuickNodeUrl(), txid, jsonObject, to, from);
        transaction.setType("receive");
        transaction.setIsSync(0);
        TradeManager tradeManager = SpringUtil.getBean(TradeManager.class);
        BigInteger tokenBalanceBigInt = tradeManager.searchTokenBalance(getQuickNodeUrl(), getCurrentContractAddress(coinType), to);
        BigInteger data = new BigInteger(jsonObject.getString("data").substring(2), 16);
        saveTransactions(transaction);
        alert(to, from, transaction, userWalletByAddress.getSysId(), coinType);


        //如果交易正常，处理入账
        if (!isInvalidTransactionInput(txid) && !isIsAbnormalForBalance(to, tokenBalanceBigInt, data, transaction)) {
            //如果是第三方，则需要发送处理
            boolean isThirdParty = !userWalletByAddress.getSysId().equals(getKazePaySysId());
            if (isThirdParty) {
                if (isSupportThirdParty()) {
                    tradeManager.pushData(userWalletByAddress.getSysId(), transaction, true, getNetWork(), coinType);
                }
            } else {
                handlerTxn(transaction, userWalletByAddress, coinType);
                ThreadPoolUtil.execute(() -> {
                    log.info("区块链:{} 尝试自动归集,txid:{}", getChainName(), txid);
                    aggregation(userWalletByAddress, transaction.getAmount(), coinType);
                });
            }
        }
    }

    private boolean isIsAbnormalForBalance(String to, BigInteger tokenBalanceBigInt, BigInteger data, T2 transaction) {
        boolean isAbnormalForBalance = false;
        //判断钱包余额是否正常
        if (tokenBalanceBigInt.compareTo(data) < 0) {
            log.error("查询到的钱包金额小于入账的金额，不允许入账，入账金额：{},查询余额:{}", WeiToCoinUtils.convertWeiToCoin(data), WeiToCoinUtils.convertWeiToCoin(tokenBalanceBigInt));
            isAbnormalForBalance = true;
        }
        //判断是否达到入账金额阈值
        if (transaction.getAmount().compareTo(getReceiveThreshold()) < 0) {
            log.info("区块链:{} 检测到用户地址入账交易记录，地址:{}，金额:{}，未达到入账金额阈值:{}，跳过", getChainName(), to, transaction.getAmount(), getReceiveThreshold());
            isAbnormalForBalance = true;
        }
        return isAbnormalForBalance;
    }

    protected abstract String getKazePaySysId();

    protected abstract BigDecimal getReceiveThreshold();

    private void alert(String userAddress, String otherAddress, T2 transaction, String sysId, CoinType coinType) {
        //超过多少钱入账，需要通知管理员检查
        // 获取eth余额
        TradeManager tradeManager = SpringUtil.getBean(TradeManager.class);
        BigDecimal eth = tradeManager.searchBalance(getQuickNodeUrl(), userAddress);
        BigInteger tokenBalanceBigInt = tradeManager.searchTokenBalance(getQuickNodeUrl(), getCurrentContractAddress(coinType), userAddress);
        NormalRabbitProducer rabbitProducer = SpringUtil.getBean(NormalRabbitProducer.class);
        String type;
        switch (transaction.getType()) {
            case "receive":
                type = "转入";
                break;
            case "send":
                type = "转出";
                break;
            case "collect":
                type = "归集转出";
                break;
            default:
                type = "异常类型";
                break;
        }
        rabbitProducer.sendMonitor(
                new MqMonitorDto(
                        transaction.getTimestamp(),
                        getChainName(),
                        userAddress,
                        otherAddress,
                        type,
                        transaction.getAmount(),
                        CoinType.USDT.getCode(),
                        eth,
                        "ETH",
                        convertDivide(tokenBalanceBigInt),
                        CoinType.USDT.getCode(),
                        sysId)
        );
    }


    /**
     * 处理流水（入账）
     */
    private void handlerTxn(T2 abstractTransactions, T1 userWalletByAddress, CoinType coinType) {
        Instant instant = Instant.ofEpochSecond(abstractTransactions.getTimestamp());
        LocalDateTime transactionTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        Date time = Date.from(transactionTime.atZone(ZoneId.systemDefault()).toInstant());
        CoinDataUtil coinDataUtil = SpringUtil.getBean(CoinDataUtil.class);
        coinDataUtil.doData(
                abstractTransactions.getFromaddress(),
                abstractTransactions.getAddress(),
                abstractTransactions.getTxid(),
                abstractTransactions.getAmount(),
                abstractTransactions.getFee(),
                time,
                abstractTransactions,
                coinType,
                getMainWallletAddressSet(userWalletByAddress.getSysId()),
                new ArrayList<>(getUserWalletAddressSet(userWalletByAddress.getSysId())),
                getChainName()
        );
    }

    protected abstract Set<String> getUserWalletAddressSet(String sysId);

    protected abstract Set<String> getMainWallletAddressSet();

    protected abstract String getMainWallletAddressSet(String sysId);

    protected abstract BigInteger getAggregationThreshold();

    protected void aggregation(T1 userWallet, BigDecimal coinBigDecimal, CoinType coinType) {
        TradeManager tradeManager = SpringUtil.getBean(TradeManager.class);
        ISysConfigService configService = SpringUtil.getBean(ISysConfigService.class);
        if (userWallet == null) {
            log.error("区块链:{} 归集用户钱包不存在", getChainName());
            return;
        }
        log.info("区块链:{} 触发自动归集,地址:{}", getChainName(), userWallet.getCstAdress());

        MainSysIdAddressService mainSysIdAddressService = SpringUtil.getBean(MainSysIdAddressService.class);
        MetaMainAddress aggregationWallet = mainSysIdAddressService.getMainSysIdAddress("BEP20", userWallet.getSysId());
        String burnEthAmount = configService.selectConfigByKey("collect_eth_amount_for_base");
        tradeManager.aggregation(
                getQuickNodeUrl(),
                getCurrentContractAddress(coinType),
                userWallet.getCstAdress(),
                userWallet.getCstTrcPrivate(),
                getAggregationThreshold(),
                new BigDecimal(burnEthAmount),
                aggregationWallet,
                getGiveAddress(),
                getGivePrivate(),
                getChainId(),
                coinBigDecimal,
                getGasPrice(),
                getGasLimit()
        );

    }

    protected abstract String getGiveAddress();

    protected abstract String getGivePrivate();

    protected abstract String getGasLimit();

    protected abstract String getGasPrice();

    protected abstract long getChainId();

    private BigInteger getBeginBlock() {
        String lastBlock = SpringUtil.getBean(StringRedisTemplate.class).opsForValue().get(getRedisBlockNumberKey());
        if (StringUtils.isBlank(lastBlock)) {
            return null;
        }
        return new BigInteger(lastBlock);
    }

    private void saveDoneBlock(BigInteger lastBlock) {
        SpringUtil.getBean(StringRedisTemplate.class).opsForValue().set(getRedisBlockNumberKey(), lastBlock.add(BigInteger.ONE).toString());
    }

    protected abstract T1 getUserWalletByAddress(String userAddress);

    protected abstract T2 createTransactionInstance();

    protected T2 createTransaction(String url, String txid, JSONObject jsonObject,
                                   String to, String from) {
        T2 transactionInstance = createTransactionInstance();
        if (jsonObject.getString(BLOCK_NUMBER).startsWith("0x")) {
            transactionInstance.setBlockHeight(new BigInteger(jsonObject.getString(BLOCK_NUMBER).substring(2), 16).longValue());
        } else {
            transactionInstance.setBlockHeight(jsonObject.getLong(BLOCK_NUMBER));
        }
        transactionInstance.setTxid(txid);
        transactionInstance.setAddress(to);
        transactionInstance.setFromaddress(from);
        transactionInstance.setContract(jsonObject.getString("address"));
        transactionInstance.setAmount(WeiToCoinUtils.convertWeiToCoin(new BigInteger(jsonObject.getString("data").substring(2), 16)));
        transactionInstance.setFee(getFee(url, txid));
        transactionInstance.setTimestamp(getTimestamp(url, jsonObject));
        return transactionInstance;
    }

    protected BigDecimal getFee(String url, String txid) {
        // 获取交易对象
        JSONObject transactionByHash = getQuikNodeApi().ethGetTransactionByHashReturnJson(txid, url);
        BigInteger gasPrice = new BigInteger(transactionByHash.getString("gasPrice").substring(2), 16);
        // 获取实际使用的 gas 数量
        JSONObject receipt = getQuikNodeApi().ethGetTransactionReceiptForJsonObject(txid, url);
        BigInteger gasUsed = new BigInteger(receipt.getString("gasUsed").substring(2), 16);
        BigInteger transactionFee = gasPrice.multiply(gasUsed);
        return WeiToCoinUtils.convertWeiToCoin(new BigDecimal(transactionFee));
    }

    protected long getTimestamp(String url, JSONObject jsonObject) {
        // 时间
        String blockNumber16 = jsonObject.getString(BLOCK_NUMBER);
        String blockStr = getQuikNodeApi().ethGetBlockByNumber(blockNumber16, url);
        JSONObject jsonObjectBlock = JSON.parseObject(blockStr);
        return Long.parseLong(jsonObjectBlock.getString("timestamp").substring(2), 16);
    }

    private static BigDecimal convertDivide(BigInteger originBalance) {
        return new BigDecimal(originBalance).divide(new BigDecimal("1000000"), 6, RoundingMode.DOWN);
    }

    private boolean isInvalidTransactionInput(String txid) {
        String transactionByHash = getQuikNodeApi().ethGetTransactionByHash(txid, getQuickNodeUrl());
        JSONObject transactionJson = JSON.parseObject(transactionByHash);
        String input = transactionJson.getString("input").substring(0, 10);
        if (!("0x23b872dd".equals(input) || "0xa9059cbb".equals(input))) {
            log.error("区块链:{},txid:{},签名错误", getChainName(), txid);
            return true;
        }
        return false;
    }


}
