package com.meta.scanning;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/3/25 17:03
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class BlockchainScanningTask {

    private final BaseBlockchainScanner baseScanningHandler;
    private final ArbBlockchainScanner arbScanningHandler;


//    @Scheduled(cron = "0/5 * * * * ?")
//    public void scanBlockchains() {
//        baseScanningHandler.scanAsync();
////        log.debug("异步测试");
//        arbScanningHandler.scanAsync();
//    }

    @Scheduled(cron = "0/5 * * * * ?")
    public void scanBlockchainsForBase() {
        baseScanningHandler.scan();
    }

    @Scheduled(cron = "0/5 * * * * ?")
    public void scanBlockchainsForArb() {
        arbScanningHandler.scan();
    }

}
