package com.meta.controller;

import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.exception.CustomException;
import com.meta.config.WalletConfig;
import com.meta.dto.MetaSolanaTransactionVo;
import com.meta.manager.TradeManager;
import com.meta.service.MainSysIdAddressService;
import com.meta.service.TradeService;
import com.meta.system.domain.MetaMainAddress;
import com.meta.system.domain.app.BaseCstaddress;
import com.meta.system.enums.CoinType;
import com.meta.system.service.BaseCstaddressService;
import com.meta.system.service.ISysConfigService;
import com.meta.utils.CoinDataUtil;
import com.meta.utils.Md5Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Slf4j
@RestController
@RequestMapping("/walletManager")
public class WalletController {


    @Resource
    private CoinDataUtil coinDataUtil;
    @Resource
    private ISysConfigService configService;
    @Resource
    private MainSysIdAddressService mainSysIdAddressService;
    @Resource
    private TradeService tradeService;
    @Resource
    private BaseCstaddressService baseCstaddressService;
    @Resource
    private TradeManager tradeManager;

    /**
     * 发送智能合约交易（归集交易）
     *
     * @param address 用户钱包地址
     * @param md5sum  md5
     * @param type    币种
     * @return 交易id
     */
    @PostMapping("/sendTradeByCst")
    public AjaxResult sendTradeByCst(@RequestParam("address") String address, @RequestParam("md5sum") String md5sum, @RequestParam("type") String type, @RequestParam(value = "coinType", required = false) String coinType) {
        String md = Md5Utils.hash(address + WalletConfig.key);
        if (!md.equals(md5sum)) {
            return AjaxResult.error("md5sun验证失败");
        }
        if (coinType == null) {
            coinType = CoinType.USDT.getCode();
        }
        String txid;
        if (type.equalsIgnoreCase("bep20")) {
            txid = tradeService.sendTradeByCst(address, coinType);
        } else {
            BaseCstaddress wallet = baseCstaddressService.findByAddress(address);
            BigDecimal fee;
            String gas_price;
            String gas_limit;
            String url;
            String contractAddress;
            String aggregationThreshold;
            long chainId;
            String giveAddress;
            String givePrivate;
            if (type.equalsIgnoreCase("base")) {
                fee = new BigDecimal(configService.selectConfigByKey("collect_eth_amount_for_base"));
                gas_price = configService.selectConfigByKey("GAS_PRICE_FOR_BASE");
                gas_limit = configService.selectConfigByKey("GAS_LIMIT_FOR_BASE");
                url = configService.selectConfigByKey("quick_node_url_for_base");
                if (coinType.equals("USDC")) {
                    contractAddress = configService.selectConfigByKey("USDC_CONTRACT_ADDRESS_FOR_BASE");
                } else if (coinType.equals("USDT")) {
                    contractAddress = configService.selectConfigByKey("USDT_CONTRACT_ADDRESS_FOR_BASE");
                } else {
                    throw new CustomException("类型错误");
                }
                aggregationThreshold = configService.selectConfigByKey("aggregation_threshold_for_base");
                chainId = Long.parseLong(configService.selectConfigByKey("CHAIN_ID_FOR_BASE"));
                giveAddress = configService.selectConfigByKey("give_address_for_base");
                givePrivate = configService.selectConfigByKey("give_private_for_base");
            } else if (type.equalsIgnoreCase("arb")) {
                fee = new BigDecimal(configService.selectConfigByKey("collect_eth_amount_for_arb"));
                gas_price = configService.selectConfigByKey("GAS_PRICE_FOR_ARB");
                gas_limit = configService.selectConfigByKey("GAS_LIMIT_FOR_ARB");
                url = configService.selectConfigByKey("quick_node_url_for_arb");
                if (coinType.equals("USDC")) {
                    contractAddress = configService.selectConfigByKey("USDC_CONTRACT_ADDRESS_FOR_ARB");
                } else if (coinType.equals("USDT")) {
                    contractAddress = configService.selectConfigByKey("USDT_CONTRACT_ADDRESS_FOR_ARB");
                } else {
                    throw new CustomException("类型错误");
                }
                aggregationThreshold = configService.selectConfigByKey("aggregation_threshold_for_arb");
                chainId = Long.parseLong(configService.selectConfigByKey("CHAIN_ID_FOR_ARB"));
                giveAddress = configService.selectConfigByKey("give_address_for_arb");
                givePrivate = configService.selectConfigByKey("give_private_for_arb");
            } else {
                throw new CustomException("类型错误");
            }

            MetaMainAddress aggregationWallet = mainSysIdAddressService.getMainSysIdAddress("BEP20", wallet.getSysId());
            txid = tradeManager.aggregation(
                    url,
                    contractAddress,
                    wallet.getCstAdress(),
                    wallet.getCstTrcPrivate(),
                    new BigDecimal(aggregationThreshold).multiply(new BigDecimal("1000000")).toBigInteger(),
                    fee,
                    aggregationWallet,
                    giveAddress,
                    givePrivate,
                    chainId,
                    null,
                    gas_price,
                    gas_limit
            );

        }

        return AjaxResult.success(txid);
    }


    /**
     * 接收transaction进行入账操作
     *
     * @return 交易id
     */
    @PostMapping("/coinData")
    public AjaxResult coinData(@RequestBody MetaSolanaTransactionVo vo) {
        return coinDataUtil.doDataForSol(vo) ? AjaxResult.success() : AjaxResult.error();
    }

}
