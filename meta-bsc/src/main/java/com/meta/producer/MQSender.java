package com.meta.producer;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Slf4j
@Component
public class MQSender {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 通用 MQ 消息发送方法，支持任意类型对象
     *
     * @param payload     消息体（对象、Map、List、String 等）
     * @param exchange    交换机名称
     * @param routingKey  路由键
     */
    public void send(Object payload, String exchange, String routingKey) {
        try {
            MessageConverter converter = rabbitTemplate.getMessageConverter();
            MessageProperties messageProperties = new MessageProperties();
            Message message = converter.toMessage(payload, messageProperties);

            // 生成唯一 ID，便于 confirm 回调追踪
            String messageId = UUID.randomUUID().toString();
            CustomCorrelationData correlationData =
                    new CustomCorrelationData(messageId, message, exchange, routingKey);

            log.info("发送 MQ 消息：exchange={}, routingKey={}, messageId={}, payload={}",
                    exchange, routingKey, messageId, payload);

            rabbitTemplate.send(exchange, routingKey, message, correlationData);
        } catch (Exception e) {
            log.error("发送 MQ 消息失败：payload={}, error={}", payload, e.getMessage(), e);
        }
    }

    /**
     * 自定义 CorrelationData
     */
    @Getter
    public static class CustomCorrelationData extends CorrelationData {
        private final Message message;
        private final String exchange;
        private final String routingKey;

        public CustomCorrelationData(String id, Message message, String exchange, String routingKey) {
            super(id);
            this.message = message;
            this.exchange = exchange;
            this.routingKey = routingKey;
        }

    }
}

