package com.meta.utils;

import com.meta.system.email.SendEmail;
import com.meta.system.service.ISysConfigService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.admin.Admin;
import org.web3j.protocol.core.methods.response.Web3ClientVersion;
import org.web3j.protocol.http.HttpService;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Component
public class Web3jUtils {

    private static Log log = LogFactory.getLog(Web3jUtils.class);
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private SendEmail sendEmail;

    @Autowired
    private StringRedisTemplate redisTemplate;

    public Web3j getWeb3() {


        String url_1 = configService.selectConfigByKey("BSC_RPC_URL");
        String url_2 = configService.selectConfigByKey("BSC_RPC_URL_2");
        Web3j web3j = null;
        //节点1
        web3j = Web3j.build(new HttpService(url_1));
        boolean isConnected = tryConnect(web3j);
        if (!isConnected) {
            log.info("BSC无法连接节点1，节点1失败");

            String sendKey="biz_sendmail:BSC_NODE_WARNNING_1";
            if(!redisTemplate.hasKey(sendKey)){
                redisTemplate.opsForValue().set(sendKey,"1",1, TimeUnit.HOURS);
//                sendEmail.remindMail("BSC无法连接节点1，节点1失败！"+url_1, "BSC监听连接节点错误警告");
            }

            //节点2
            web3j = Web3j.build(new HttpService(url_2));
            isConnected = tryConnect(web3j);
            if (!isConnected) {
                String sendKey2="biz_sendmail:BSC_NODE_WARNNING_2";
                if(!redisTemplate.hasKey(sendKey2)){
                    redisTemplate.opsForValue().set(sendKey2,"1",1, TimeUnit.HOURS);
//                    sendEmail.remindMail("BSC无法连接所有节点！", "BSC监听连接节点错误警告");
                }

            }
        }

        return web3j;
    }

    public boolean tryConnect(Web3j web3j) {
        try {
            // 尝试连接节点
            Web3ClientVersion web3ClientVersion = web3j.web3ClientVersion().send();
            if (web3ClientVersion.hasError()) {
                log.info("连接节点失败：" + web3ClientVersion.getError().getMessage());
                return false;
            } else {
                log.info("连接节点正常");
                return true;
            }

        } catch (IOException e) {
            log.info("连接节点失败：" + e.getMessage());
            return false;
        }
    }

}
