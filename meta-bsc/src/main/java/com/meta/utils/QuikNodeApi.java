package com.meta.utils;

import com.alibaba.fastjson.JSONObject;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.web3j.abi.EventEncoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Event;
import org.web3j.abi.datatypes.generated.Uint256;

import java.math.BigInteger;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/01/30/10:29
 */
@Component
public class QuikNodeApi {

    private static final String jsonrpc = "2.0";
    private static final String id = "1";


    public HttpHeaders getHttpHeaders() {
        HttpHeaders standardHeaders = new HttpHeaders();
        standardHeaders.setContentType(MediaType.APPLICATION_JSON);
        standardHeaders.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
        return standardHeaders;
    }

    /**
     * 获取余额
     */
    public BigInteger ethGetBalance(String address, String url) {
        HttpHeaders standardHeaders = getHttpHeaders();
        Map<String,Object> params = new HashMap<>();
        params.put("jsonrpc", jsonrpc);
        params.put("id", id);
        params.put("method", "eth_getBalance");
        List<String> addressList = new ArrayList<>();
        addressList.add(address);
        addressList.add("latest");
        params.put("params", addressList);
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(params, standardHeaders);
        ResponseEntity<String> responseEntity = new RestTemplate().exchange(
                url,
                HttpMethod.POST, httpEntity, String.class);
        HttpStatus statusCode = responseEntity.getStatusCode();
        if (statusCode.is2xxSuccessful()) {
            JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
            String hex = jsonObject.getString("result");
            return new BigInteger(hex.substring(2), 16);
        }
        return null;
    }


    /**
     * 获取最新的桶
     *
     * @param url 节点
     */
    public BigInteger ethBlockNumberForBigInteger(String url) {
        String result = ethBlockNumber(url);
        if (result != null) {
            return new BigInteger(result.substring(2), 16);
        }
        return null;
    }

    /**
     * 获取最新的桶
     *
     * @param url 节点
     */
    public String ethBlockNumber(String url) {
        HttpHeaders standardHeaders = getHttpHeaders();
        Map<String, String> params = new HashMap<>();
        params.put("jsonrpc", jsonrpc);
        params.put("method", "eth_blockNumber");
        params.put("params", null);
        params.put("id", id);
        HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(params, standardHeaders);
        ResponseEntity<String> responseEntity = new RestTemplate().exchange(
                url,
                HttpMethod.POST, httpEntity, String.class);
        HttpStatus statusCode = responseEntity.getStatusCode();
        if (statusCode.is2xxSuccessful()) {
            JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
            return jsonObject.getString("result");
        }
        return null;
    }

    /**
     * 获取过滤器id
     *
     * @param fromBlock 开始的桶
     * @param toBlock   结束的桶
     * @param tokenList 合约地址
     * @param url       节点
     * @return
     */
    public String ethNewFilter(String fromBlock, String toBlock, List<String> tokenList, String url) {
        HttpHeaders standardHeaders = getHttpHeaders();
        Map params = new HashMap<>();
        params.put("jsonrpc", jsonrpc);
        params.put("id", id);
        params.put("method", "eth_newFilter");
        List<Object> paramsList = new ArrayList<>();
        Map<String, Object> innerParams = new HashMap<>();
        innerParams.put("fromBlock", fromBlock);
        innerParams.put("toBlock", toBlock);
        innerParams.put("address", tokenList);
        Event event = new Event("Transfer",
                Arrays.<TypeReference<?>>asList(
                        new TypeReference<Address>(true) {
                        }, new TypeReference<Address>(true) {
                        }, new TypeReference<Uint256>(false) {
                        }
                )
        );
        String eventTopic = EventEncoder.encode(event);
        List<String> topics = new ArrayList<>();
        topics.add(eventTopic);
        innerParams.put("topics", new ArrayList<String>());
        paramsList.add(innerParams);
        params.put("params", paramsList);

        HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(params, standardHeaders);
        ResponseEntity<String> responseEntity = new RestTemplate().exchange(
                url,
                HttpMethod.POST, httpEntity, String.class);
        HttpStatus statusCode = responseEntity.getStatusCode();
        if (statusCode.is2xxSuccessful()) {
            JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
            String result = jsonObject.getString("result");
            return result;
        }
        return null;
    }

    /**
     * 获取数据
     *
     * @param filterId 过滤器id
     * @param url      节点
     * @return
     */
    public String ethGetFilterLogs(String filterId, String url) {
        HttpHeaders standardHeaders = getHttpHeaders();
        Map params = new HashMap<>();
        params.put("jsonrpc", jsonrpc);
        params.put("id", id);
        params.put("method", "eth_getFilterLogs");
        List<String> filterList = new ArrayList<>();
        filterList.add(filterId);
        params.put("params", filterList);
        HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(params, standardHeaders);
        ResponseEntity<String> responseEntity = new RestTemplate().exchange(
                url,
                HttpMethod.POST, httpEntity, String.class);
        HttpStatus statusCode = responseEntity.getStatusCode();
        if (statusCode.is2xxSuccessful()) {
            JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
            String result = jsonObject.getString("result");

            return result;
        }
        return null;
    }

    /**
     * 通过哈希获取合约交易
     *
     * @param txid 交易id
     * @param url  节点
     * @return
     */
    public JSONObject ethGetTransactionReceiptForJsonObject(String txid, String url) {
        String receiptStr = ethGetTransactionReceipt(txid, url);
        return JSONObject.parseObject(receiptStr);
    }

    /**
     * 通过哈希获取合约交易
     *
     * @param txid 交易id
     * @param url  节点
     * @return
     */
    public String ethGetTransactionReceipt(String txid, String url) {
        HttpHeaders standardHeaders = getHttpHeaders();
        Map params = new HashMap<>();
        params.put("jsonrpc", jsonrpc);
        params.put("id", id);
        params.put("method", "eth_getTransactionReceipt");
        List<String> txList = new ArrayList<>();
        txList.add(txid);
        params.put("params", txList);
        HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(params, standardHeaders);
        ResponseEntity<String> responseEntity = new RestTemplate().exchange(
                url,
                HttpMethod.POST, httpEntity, String.class);
        HttpStatus statusCode = responseEntity.getStatusCode();
        if (statusCode.is2xxSuccessful()) {
            JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
            String result = jsonObject.getString("result");

            return result;
        }
        return null;

    }

    /**
     * 通过哈希获取交易
     *
     * @param txid 交易id
     * @param url  节点
     */
    public JSONObject ethGetTransactionByHashReturnJson(String txid, String url) {
        String transactionByHashStr = ethGetTransactionByHash(txid, url);
        return JSONObject.parseObject(transactionByHashStr);
    }

    /**
     * 通过哈希获取交易
     *
     * @param txid 交易id
     * @param url  节点
     * @return
     */
    public String ethGetTransactionByHash(String txid, String url) {
        HttpHeaders standardHeaders = getHttpHeaders();
        Map params = new HashMap<>();
        params.put("jsonrpc", jsonrpc);
        params.put("id", id);
        params.put("method", "eth_getTransactionByHash");
        List<String> txList = new ArrayList<>();
        txList.add(txid);
        params.put("params", txList);
        HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(params, standardHeaders);
        ResponseEntity<String> responseEntity = new RestTemplate().exchange(
                url,
                HttpMethod.POST, httpEntity, String.class);
        HttpStatus statusCode = responseEntity.getStatusCode();
        if (statusCode.is2xxSuccessful()) {
            JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
            String result = jsonObject.getString("result");

            return result;
        }
        return null;

    }


    /**
     * 获取桶信息
     *
     * @param block 桶
     * @param url   节点
     * @return
     */
    public String ethGetBlockByNumber(String block, String url) {
        HttpHeaders standardHeaders = getHttpHeaders();
        String requestBody = "{\"method\":\"eth_getBlockByNumber\",\"params\":[\"" + block + "\",false],\"id\":" + id + ",\"jsonrpc\":\"" + jsonrpc + "\"}";
        HttpEntity<String> entity = new HttpEntity<>(requestBody, standardHeaders);
        ResponseEntity<String> responseEntity = new RestTemplate().exchange(url, HttpMethod.POST, entity, String.class);
        HttpStatus statusCode = responseEntity.getStatusCode();
        if (statusCode.is2xxSuccessful()) {
            JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
            return jsonObject.getString("result");
        }
        return null;
    }

    /**
     * 获取交易笔数
     *
     * @param url 节点
     * @return
     */
    public String ethGetTransactionCount(String address, String url, String blockparam) {
        HttpHeaders standardHeaders = getHttpHeaders();
        String requestBody = "{\"method\":\"eth_getTransactionCount\",\"params\":[\"" + address + "\",\"" + blockparam + "\"],\"id\":" + id + ",\"jsonrpc\":\"" + jsonrpc + "\"}";
        HttpEntity<String> entity = new HttpEntity<>(requestBody, standardHeaders);
        ResponseEntity<String> responseEntity = new RestTemplate().exchange(url, HttpMethod.POST, entity, String.class);
        HttpStatus statusCode = responseEntity.getStatusCode();
        if (statusCode.is2xxSuccessful()) {
            JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
            String result = jsonObject.getString("result");

            return result;
        }
        return null;
    }

    public String ethSendRawTransaction(String hexValue, String url) {
        HttpHeaders standardHeaders = getHttpHeaders();
        Map params = new HashMap<>();
        params.put("jsonrpc", jsonrpc);
        params.put("id", id);
        params.put("method", "eth_sendRawTransaction");
        List<String> txList = new ArrayList<>();
        txList.add(hexValue);
        params.put("params", txList);
        HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(params, standardHeaders);
        ResponseEntity<String> responseEntity = new RestTemplate().exchange(
                url,
                HttpMethod.POST, httpEntity, String.class);
        HttpStatus statusCode = responseEntity.getStatusCode();
        if (statusCode.is2xxSuccessful()) {
            JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
            String result = jsonObject.getString("result");
            return result;
        }
        return null;
    }


    /**
     * string 转为BigInteger
     *
     * @param quantity
     * @return
     */
    public BigInteger decodeQuantity(String quantity) {
        // 去掉字符串开头的 "0x" 并解析为 BigInteger
        if (quantity.startsWith("0x")) {
            return new BigInteger(quantity.substring(2), 16); // 16 表示十六进制
        }
        return new BigInteger(quantity); // 默认处理为十进制
    }

    public static void main(String[] args) {
//        BigInteger integer=new BigInteger("35660224");
//        System.out.println("0x"+integer.toString(16).toUpperCase());
//
//        System.out.println(new BigInteger(String.valueOf(Integer.parseInt("0x1D2E7C9".substring(2), 16))));


        String url = "https://silent-palpable-asphalt.bsc.quiknode.pro/df30cce76c6aa16b0d72d7d2bcbb48cac6e51f63/";
        QuikNodeApi api = new QuikNodeApi();
////        String blockNumber = api.ethBlockNumber(url);
////        BigInteger number = new BigInteger(String.valueOf(Integer.parseInt(blockNumber.substring(2), 16)));
////        System.out.println(number);
//
        List<String> tokenList = new ArrayList<>();
        tokenList.add("******************************************");
        String filterId = api.ethNewFilter("0x1D2E7C9", "0x1D2E7C9", tokenList, url);
        System.out.println(filterId);

        String filterLogs = api.ethGetFilterLogs(filterId, url);
        System.out.println(filterLogs);
//
//        JSONArray jsonArray = JSONArray.parseArray(filterLogs);
//        for (int i = 0; i < jsonArray.size(); i++) {
//            JSONObject jsonObject = jsonArray.getJSONObject(i);
//            String transactionHash = jsonObject.getString("transactionHash");
//            if ("0x8744e19e90cd2d678e32662eaa7dbc1e09eeba0dee99ef9f4cd504d5d5281f5b".equals(transactionHash)) {
//
//                JSONArray topics = jsonObject.getJSONArray("topics");
//                String from = topics.get(1).toString();
//                String to = topics.get(2).toString();
//            }
//
//
//        }


    }
}
