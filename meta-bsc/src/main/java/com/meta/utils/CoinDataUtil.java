package com.meta.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meta.common.annotation.RepeatSubmit;
import com.meta.common.exception.CustomException;
import com.meta.common.utils.ThreadPoolUtil;
import com.meta.dto.MetaSolanaTransactionVo;
import com.meta.entity.CoinBalance;
import com.meta.entity.CoinBalancePK;
import com.meta.entity.MisttrackLog;
import com.meta.service.CoinBalanceService;
import com.meta.service.TradeService;
import com.meta.service.impl.MisttrackLogServiceImpl;
import com.meta.system.domain.app.*;
import com.meta.system.domain.partner.MetaPartnerAssetPool;
import com.meta.system.email.SendEmail;
import com.meta.system.enums.CoinType;
import com.meta.system.service.*;
import com.meta.system.service.impl.SysDictDataServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Component
public class CoinDataUtil {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    BepCstaddressService bepCstaddressService;

    @Autowired
    private CoinTxnDtlService coinTxnDtlService;

    @Autowired
    private SysDictDataServiceImpl dataService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private CoinBalanceService coinBalanceService;


    @Autowired
    private MisttrackLogServiceImpl misttrackLogService;

    @Autowired
    private SendEmail sendEmail;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private BepTransactionsService bepTransactionsService;

    @Autowired
    private TradeService tradeService;

    @Autowired
    private ExchangeRateService exchangeRateService;


    @Autowired
    private MetaPartnerAssetPoolService metaPartnerAssetPoolService;

    /**
     * 设计钱包归集
     *
     * @param
     * @param to
     */
    public void calculateAsync(String to, String coinType) {
        ThreadPoolUtil.execute(() -> {


            logger.info("判断是否归集");
            BepCstaddress bepCstaddress = bepCstaddressService.findByAddress(to);

            if (bepCstaddress == null) {
                return;
            }


            logger.info("查询代币余额:" + to);
            //查询代币余额
            String ye = tradeService.getTokenBalance(bepCstaddress.getCstAdress(), coinType);
            //钱包归集金额触发的值
            String collectionAmount = configService.selectConfigByKey("collectionAmount");
            BigDecimal trueAmount = new BigDecimal(ye);
            BigDecimal amount2 = new BigDecimal(collectionAmount);


            //代币余额是否超过触发钱包归集的金额
            int result = trueAmount.compareTo(amount2);

            if (result < 0) {
                logger.info("不触发归集:{}，余额为:{}，归集阈值为:{}", to, trueAmount, amount2);
                return;
            }
            tradeService.sendTradeByCst(to, coinType);

        });
    }

    //异步归集
    public void collectdata(BepCstaddress bepCstaddress, String coinType, BigDecimal amount1) {
        CompletableFuture.runAsync(() -> {

            try {
                logger.info("异步开始钱包归集先等待5s:");
                Thread.sleep(5000);
                tradeService.sendTradeByCst(bepCstaddress, coinType, amount1);
                logger.info("异步开钱包归集成功");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }


        });
    }

    @Resource
    private BaseTransactionsService baseTransactionsService;

    @Resource
    private ArbTransactionsService arbTransactionsService;

    @Resource
    private BaseCstaddressService baseCstaddressService;

    @Resource
    private ArbCstaddressService arbCstaddressService;


    /**
     * @param addressList
     * @param from        from地址
     * @param to          to地址
     * @param txid        交易号
     * @param account     金额
     * @param use         交易费
     * @param time        时间
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    public Boolean doData(
            String from,
            String to,
            String txid,
            BigDecimal account,
            BigDecimal use,
            Date time,
            AbstractTransactions abstractTransactions,
            CoinType coinType,
            String mainAddress,
            List<String> addressList,
            String chainType
    ) {

        //判断to是否是客户地址或者公司钱包地址-redis
        //公司钱包地址
        if (!addressList.contains(mainAddress)) {
            addressList.add(mainAddress);
        }

        if (!addressList.contains(to) && !mainAddress.equals(to)) {
            //不是公司钱包归集，to不是表里的数据
            return false;
        }
        //MistTrack的API开关
        String apiSwitch = configService.selectConfigByKey("MistTrack_API_switch");
        boolean apiFlag = apiSwitch.equals("1");

        CoinTxnDtl dtl = new CoinTxnDtl();
        dtl.setReceiptNo(txid);
        CoinBalance cb = null;
        //先去meta_bep20_cstaddressinfo查询用户id
        String addre = "";
        if (to.equals(mainAddress)) {
            addre = from;
        } else {
            addre = to;
        }


        AbstractCstaddress abstractCstaddress;
        switch (chainType) {
            case "BEP":
                abstractCstaddress = bepCstaddressService.findByAddress(addre);
                break;
            case "BASE":
                abstractCstaddress = baseCstaddressService.findByAddress(addre);
                break;
            case "ARB":
                abstractCstaddress = arbCstaddressService.findByAddress(addre);
                break;
            default:
                throw new CustomException("chainType不能为空");

        }

        // 用户id查余额
        CoinBalancePK balancePK = new CoinBalancePK();
        balancePK.setUserId(abstractCstaddress.getCstId());
        balancePK.setCoinCode(coinType.getCode());
        //根据钱包地址查询用户
        cb = coinBalanceService.findById(balancePK);
        if (cb == null) {
            /*********************用户钱包地址不同，校验失败.***********************/
            logger.debug("eeeeeeeeeeeeeee5");
            return false;
        }

        //判断是否是公司归集钱包地址
        if (to.equals(mainAddress)) {


            //归集扣减
            logger.info("公司钱包归集" + account);
            BigDecimal subtract;
            switch (chainType) {
                case "BEP":
                    subtract = cb.getUncltBep20CoinBalance().subtract(account);
                    if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                        cb.setUncltBep20CoinBalance(BigDecimal.ZERO);
                    } else {
                        cb.setUncltBep20CoinBalance(subtract);
                    }
                    coinBalanceService.editCoinBalanceByEBP20(cb);
                    return true;
                case "BASE":
                    subtract = cb.getUncltErc20BaseCoinBalance().subtract(account);
                    if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                        cb.setUncltErc20BaseCoinBalance(BigDecimal.ZERO);
                    } else {
                        cb.setUncltErc20BaseCoinBalance(subtract);
                    }
                    coinBalanceService.editCoinBalanceByErc20Base(cb);
                    return true;
                case "ARB":
                    subtract = cb.getUncltErc20ArbCoinBalance().subtract(account);
                    if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                        cb.setUncltErc20ArbCoinBalance(BigDecimal.ZERO);
                    } else {
                        cb.setUncltErc20ArbCoinBalance(subtract);
                    }
                    coinBalanceService.editCoinBalanceByErc20Arb(cb);
                    return true;
            }
        }


        //钱包余额信息
        CoinBalance ccb = cb;

        CoinTxnDtl r = new CoinTxnDtl();
        r.setUserId(cb.getUserId().longValue());
        r.setFromAddress(from);
        r.setToAddress(to);
        r.setReceiptNo(txid);
        r.setRecordType(Constants.RECORD_TYPE_RECEIPTS);
        r.setTxnCode(Constants.txnCode.d1010);
        r.setTxnCoin(coinType.getCode());
        r.setTxnDesc("充值");

        switch (chainType) {
            case "BEP":
                r.setCoinNet("BEP20");
                break;
            case "BASE":
                r.setCoinNet("ERC20_BASE");
                break;
            case "ARB":
                r.setCoinNet("ERC20_ARB");
                break;
        }
        r.setTxnAmount(account);
        r.setUserBalance(ccb.getCoinBalance().add(account));
        r.setTxnFee(use);


        r.setTxnTime(time);
        r.setCreateTime(new Date()); // 获取时间错

        int txnStatus = 1;//交易状态0待审核1已完成2已拒绝--> 0失败 1完成 2处理中
        boolean apiResult = false;//api是否获取到结果

        if (apiFlag) {
            int mistTrackMillion = Integer.valueOf(configService.selectConfigByKey("MistTrack_million"));
            String key = Constants.redisLock.REDIS_LOCK + "misttrack_api:transfer_time";

            synchronized (this) {

                boolean bkflag = redisTemplate.opsForValue().setIfAbsent(key, "1", mistTrackMillion * 1000, TimeUnit.MILLISECONDS);

                if (!bkflag) {
                    long millis = redisTemplate.opsForValue().getOperations().getExpire(key, TimeUnit.MILLISECONDS);

                    if (millis > 0) {
                        try {
                            Thread.sleep(millis);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }

                    }
                    redisTemplate.opsForValue().setIfAbsent(key, "1", mistTrackMillion * 1000, TimeUnit.MILLISECONDS);
                }

            }

            //获取地址标签url
            String url_1 = configService.selectConfigByKey("MistTrack_Url") + "/address_labels";
            logger.info("获取地址标签url：" + url_1);
            //获取风险评分url
            String url_2 = configService.selectConfigByKey("MistTrack_Url") + "/risk_score";
            logger.info("获取风险评分url：" + url_2);
            String api_key = configService.selectConfigByKey("MistTrack_api_key");
            String coin = "USDT-TRC20";
            String address = from;
            logger.info("api_key：" + api_key);
            logger.info("coin：" + coin);
            logger.info("address：" + address);


            MultiValueMap<String, String> paramMap = new LinkedMultiValueMap<>();
            paramMap.add("api_key", api_key);
            paramMap.add("coin", coin);
            paramMap.add("address", address);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
            HttpEntity<MultiValueMap<String, Object>> hEntity = new HttpEntity<MultiValueMap<String, Object>>(null, headers);


            //获取地址标签
            URI uri = UriComponentsBuilder.fromHttpUrl(url_1).queryParams(paramMap).build().encode().toUri();
            ResponseEntity<String> response = new RestTemplate().exchange(uri, HttpMethod.GET, hEntity, String.class);
            String addressMes = response.getBody();
            JSONObject addressObject = JSONObject.parseObject(addressMes);
            MisttrackLog misttrackLog = new MisttrackLog();
            misttrackLog.setFromAddress(from);
            misttrackLog.setToAddress(to);
            misttrackLog.setCreateTime(new Date());
            misttrackLog.setAmount(r.getTxnAmount());

            misttrackLog.setCoin(coin);
            misttrackLog.setTxid(txid);
            misttrackLog.setUserId(cb.getUserId().longValue());
            if (addressObject.get("success").equals(true)) {
                JSONObject dataObject = JSONObject.parseObject(addressObject.get("data").toString());
                JSONArray dataArr = dataObject.getJSONArray("label_list");
                //判断是否存在
                if (dataArr.size() > 0 && StringUtils.isNotEmpty(dataService.selectDictLabel("misttrack_address_label", dataArr.get(0).toString()))) {
                    //未归集金额增加
                    logger.info("获取地址标签存在：" + dataArr.get(0).toString());
                    logger.info("1未归集金额增加:" + r.getTxnAmount());
                    switch (chainType) {
                        case "BEP":
                            ccb.setUncltBep20CoinBalance(ccb.getUncltBep20CoinBalance().add(r.getTxnAmount()));
                            break;
                        case "BASE":
                            ccb.setUncltErc20BaseCoinBalance(ccb.getUncltErc20BaseCoinBalance().add(r.getTxnAmount()));
                            break;
                        case "ARB":
                            ccb.setUncltErc20ArbCoinBalance(ccb.getUncltErc20ArbCoinBalance().add(r.getTxnAmount()));
                            break;
                    }
                    misttrackLog.setAddressLabel(dataArr.get(0).toString());
                    apiResult = true;
                }
            }
            if (!apiResult) {
                //获取风险评分
                URI uri2 = UriComponentsBuilder.fromHttpUrl(url_2).queryParams(paramMap).build().encode().toUri();
                ResponseEntity<String> response2 = new RestTemplate().exchange(uri2, HttpMethod.GET, hEntity, String.class);
                String storeMes = response2.getBody();
                JSONObject scoreObject = JSONObject.parseObject(storeMes);
                if (scoreObject.get("success").equals(true)) {

                    String data = scoreObject.get("data").toString();
                    if (!(data.equals("{}") || data.equals("{}\n") || StringUtils.isEmpty(data))) {
                        String dataScore = JSONObject.parseObject(data).get("score").toString();
                        String riskLevel = JSONObject.parseObject(data).get("risk_level").toString();

                        apiResult = true;
                        int score = Integer.valueOf(dataScore);
                        logger.info("获取风险评分:" + score);
                        logger.info("获取风险等级:" + riskLevel);
                        misttrackLog.setScore(String.valueOf(score));
                        misttrackLog.setLevel(riskLevel);
                        int unclt = Integer.valueOf(dataService.selectDictLabel("misttrack_score", "unclt"));
                        logger.info("未归集金额最大值设置" + unclt);
                        int freeze = Integer.valueOf(dataService.selectDictLabel("misttrack_score", "freeze"));
                        logger.info("冻结金额最大值设置" + freeze);


                        if (score >= 0 && score <= unclt) {//低风险
                            //未归集金额增加
                            logger.info("2未归集金额增加:" + r.getTxnAmount());
                            switch (chainType) {
                                case "BEP":
                                    ccb.setUncltBep20CoinBalance(ccb.getUncltBep20CoinBalance().add(r.getTxnAmount()));
                                    break;
                                case "BASE":
                                    ccb.setUncltErc20BaseCoinBalance(ccb.getUncltErc20BaseCoinBalance().add(r.getTxnAmount()));
                                    break;
                                case "ARB":
                                    ccb.setUncltErc20ArbCoinBalance(ccb.getUncltErc20ArbCoinBalance().add(r.getTxnAmount()));
                                    break;
                            }


                        } else if (score > unclt && score <= freeze) {//中风险
                            //冻结金额增加
                            logger.info("冻结金额增加:" + r.getTxnAmount());
                            txnStatus = 2;
                            ccb.setFreezeBalance(ccb.getFreezeBalance().add(r.getTxnAmount()));
                            switch (chainType) {
                                case "BEP":
                                    ccb.setUncltBep20CoinBalance(ccb.getUncltBep20CoinBalance().add(r.getTxnAmount()));
                                    break;
                                case "BASE":
                                    ccb.setUncltErc20BaseCoinBalance(ccb.getUncltErc20BaseCoinBalance().add(r.getTxnAmount()));
                                    break;
                                case "ARB":
                                    ccb.setUncltErc20ArbCoinBalance(ccb.getUncltErc20ArbCoinBalance().add(r.getTxnAmount()));
                                    break;
                            }
                            sendEmail.remindMail("调用MistTrack的API，评分中风险：" + score + "，txid:" + txid + "，冻结金额：" + r.getTxnAmount() + "。", "调用MistTrack-API的信息提醒");

                        } else if (score > freeze) {//高风险
                            //交易状态
                            r.setTxnStatus(0);
                            //后台未加服务费
                            coinTxnDtlService.save(r);
                            misttrackLogService.save(misttrackLog);
                            sendEmail.remindMail("调用MistTrack的API，评分高风险：" + score + "，txid:" + txid + "。", "调用MistTrack-API的信息提醒");
                            return false;
                        }

                    }


                }

            }

            //两个接口调用api没成功
            if (!apiResult) {
                logger.info("调用MistTrack的API，标签地址和风险评分没获取数据，冻结金额增加:" + r.getTxnAmount());
                txnStatus = 2;
                ccb.setFreezeBalance(ccb.getFreezeBalance().add(r.getTxnAmount()));
                switch (chainType) {
                    case "BEP":
                        ccb.setUncltBep20CoinBalance(ccb.getUncltBep20CoinBalance().add(r.getTxnAmount()));
                        break;
                    case "BASE":
                        ccb.setUncltErc20BaseCoinBalance(ccb.getUncltErc20BaseCoinBalance().add(r.getTxnAmount()));
                        break;
                    case "ARB":
                        ccb.setUncltErc20ArbCoinBalance(ccb.getUncltErc20ArbCoinBalance().add(r.getTxnAmount()));
                        break;
                }
                sendEmail.remindMail("调用MistTrack的API，标签地址和风险评分没获取数据，txid:" + txid + "，冻结金额：" + r.getTxnAmount() + "。", "调用MistTrack-API的信息提醒");
            }

            misttrackLogService.save(misttrackLog);
        } else {
            //MistTrack的api关闭的时候--未归集金额增加
            logger.info("MistTrack的api关闭的时候--未归集金额增加:" + r.getTxnAmount());
            switch (chainType) {
                case "BEP":
                    ccb.setUncltBep20CoinBalance(ccb.getUncltBep20CoinBalance().add(r.getTxnAmount()));
                    break;
                case "BASE":
                    ccb.setUncltErc20BaseCoinBalance(ccb.getUncltErc20BaseCoinBalance().add(r.getTxnAmount()));
                    break;
                case "ARB":
                    ccb.setUncltErc20ArbCoinBalance(ccb.getUncltErc20ArbCoinBalance().add(r.getTxnAmount()));
                    break;
            }
        }
        //交易状态
        r.setTxnStatus(txnStatus);
        coinTxnDtlService.save(r);

        if (!apiFlag || txnStatus == 1) {
            if (ccb.getCoinBalance() == null) {
                ccb.setCoinBalance(new BigDecimal(0));
            }
            //判断是否是B端用户
            MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartner(cb.getUserId());
            if (metaPartnerAssetPool != null) {
                //判断是否自动入池子
                //并且金额大于等于入池子的金额
                if (metaPartnerAssetPool.getAuto() == '0' && metaPartnerAssetPool.getAutoAmt() != null && r.getTxnAmount().compareTo(metaPartnerAssetPool.getAutoAmt()) >= 0) {

                    // 获取兑换利率
                    ExchangeRate rate = exchangeRateService.findByFromCurrencyCodeAndToCurrencyCode(coinType.getCode(), "USD");
                    BigDecimal rateAmount = r.getTxnAmount().multiply(rate.getRateVal());
                    rateAmount = rateAmount.setScale(2, RoundingMode.DOWN); // 保留两位小数,且向下取整
                    //个人兑换充值流水
                    metaPartnerAssetPool.setUsdtBalacne(metaPartnerAssetPool.getUsdtBalacne().add(rateAmount));

                    metaPartnerAssetPoolService.toPupdealDtl(metaPartnerAssetPool, coinType.getCode(), rateAmount, txid);
                    metaPartnerAssetPoolService.updateBalance(cb.getUserId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());

                } else {
                    ccb.setCoinBalance(ccb.getCoinBalance().add(r.getTxnAmount()));
                }
            } else {
                ccb.setCoinBalance(ccb.getCoinBalance().add(r.getTxnAmount()));
            }

        }

        switch (chainType) {
            case "BEP":
                coinBalanceService.editCoinBalanceByEBP20(ccb);
                if (abstractTransactions.getIsSync() == 0) {
                    abstractTransactions.setIsSync(1);
                    bepTransactionsService.updateBepTransactions((BepTransactions) abstractTransactions);
                }
                break;
            case "BASE":
                coinBalanceService.editCoinBalanceByErc20Base(ccb);
                if (abstractTransactions.getIsSync() == 0) {
                    abstractTransactions.setIsSync(1);
                    baseTransactionsService.updateTransactions((BaseTransactions) abstractTransactions);
                }
                break;
            case "ARB":
                coinBalanceService.editCoinBalanceByErc20Arb(ccb);
                if (abstractTransactions.getIsSync() == 0) {
                    abstractTransactions.setIsSync(1);
                    arbTransactionsService.updateTransactions((ArbTransactions) abstractTransactions);
                }
                break;
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    public boolean doDataForSol(MetaSolanaTransactionVo vo) {
        //MistTrack的API开关
        String apiSwitch = configService.selectConfigByKey("MistTrack_API_switch");
        boolean apiFlag = apiSwitch.equals("1");

        CoinTxnDtl dtl = new CoinTxnDtl();
        dtl.setReceiptNo(vo.getTxid());
        CoinBalance cb;

        // 用户id查余额
        CoinBalancePK balancePK = new CoinBalancePK();
        balancePK.setUserId(vo.getCstId());
        balancePK.setCoinCode(vo.getCoinType());
        //根据钱包地址查询用户
        cb = coinBalanceService.findById(balancePK);
        if (cb == null) {
            logger.debug("e5");
            return false;
        }

        //判断是否是公司归集钱包地址
        if (vo.getType().equals("collect")) {
            //归集扣减
            logger.info("公司钱包归集" + vo.getAmount());
            BigDecimal subtract;
            subtract = cb.getUncltSolCoinBalance().subtract(vo.getAmount());
            cb.setUncltSolCoinBalance(subtract);
            coinBalanceService.editCoinBalanceBySol(cb);
            return true;
        }

        //钱包余额信息
        CoinBalance ccb = cb;

        CoinTxnDtl r = new CoinTxnDtl();
        r.setUserId(cb.getUserId());
        r.setFromAddress(vo.getFromaddress());
        r.setToAddress(vo.getAddress());
        r.setReceiptNo(vo.getTxid());
        r.setRecordType(Constants.RECORD_TYPE_RECEIPTS);
        r.setTxnCode(Constants.txnCode.d1010);
        r.setTxnCoin(vo.getCoinType());
        r.setTxnDesc("充值");

        r.setCoinNet("SOL");
        r.setTxnAmount(vo.getAmount());
        r.setUserBalance(ccb.getCoinBalance().add(vo.getAmount()));
        r.setTxnFee(vo.getFee());


        r.setTxnTime(vo.getTime());
        r.setCreateTime(new Date()); // 获取时间错

        int txnStatus = 1;//交易状态0待审核1已完成2已拒绝--> 0失败 1完成 2处理中
        boolean apiResult = false;//api是否获取到结果

        if (apiFlag) {
            int mistTrackMillion = Integer.parseInt(configService.selectConfigByKey("MistTrack_million"));
            String key = Constants.redisLock.REDIS_LOCK + "misttrack_api:transfer_time";

            synchronized (this) {

                boolean bkflag = redisTemplate.opsForValue().setIfAbsent(key, "1", mistTrackMillion * 1000, TimeUnit.MILLISECONDS);

                if (!bkflag) {
                    long millis = redisTemplate.opsForValue().getOperations().getExpire(key, TimeUnit.MILLISECONDS);

                    if (millis > 0) {
                        try {
                            Thread.sleep(millis);
                        } catch (InterruptedException e) {
                            logger.error("线程sleep失败", e);
                        }

                    }
                    redisTemplate.opsForValue().setIfAbsent(key, "1", mistTrackMillion * 1000L, TimeUnit.MILLISECONDS);
                }

            }

            //获取地址标签url
            String url_1 = configService.selectConfigByKey("MistTrack_Url") + "/address_labels";
            logger.info("获取地址标签url：" + url_1);
            //获取风险评分url
            String url_2 = configService.selectConfigByKey("MistTrack_Url") + "/risk_score";
            logger.info("获取风险评分url：" + url_2);
            String api_key = configService.selectConfigByKey("MistTrack_api_key");
            String coin = "USDT-TRC20";
            logger.info("api_key：" + api_key);
            logger.info("coin：" + coin);
            logger.info("address：" + vo.getFromaddress());


            MultiValueMap<String, String> paramMap = new LinkedMultiValueMap<>();
            paramMap.add("api_key", api_key);
            paramMap.add("coin", coin);
            paramMap.add("address", vo.getFromaddress());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<MultiValueMap<String, Object>> hEntity = new HttpEntity<MultiValueMap<String, Object>>(null, headers);


            //获取地址标签
            URI uri = UriComponentsBuilder.fromHttpUrl(url_1).queryParams(paramMap).build().encode().toUri();
            ResponseEntity<String> response = new RestTemplate().exchange(uri, HttpMethod.GET, hEntity, String.class);
            String addressMes = response.getBody();
            JSONObject addressObject = JSONObject.parseObject(addressMes);
            MisttrackLog misttrackLog = new MisttrackLog();
            misttrackLog.setFromAddress(vo.getFromaddress());
            misttrackLog.setToAddress(vo.getAddress());
            misttrackLog.setCreateTime(new Date());
            misttrackLog.setAmount(r.getTxnAmount());

            misttrackLog.setCoin(coin);
            misttrackLog.setTxid(vo.getTxid());
            misttrackLog.setUserId(cb.getUserId());
            if (addressObject.get("success").equals(true)) {
                JSONObject dataObject = JSONObject.parseObject(addressObject.get("data").toString());
                JSONArray dataArr = dataObject.getJSONArray("label_list");
                //判断是否存在
                if (!dataArr.isEmpty() && StringUtils.isNotEmpty(dataService.selectDictLabel("misttrack_address_label", dataArr.get(0).toString()))) {
                    //未归集金额增加
                    logger.info("获取地址标签存在：" + dataArr.get(0).toString());
                    logger.info("1未归集金额增加:" + r.getTxnAmount());
                    ccb.setUncltSolCoinBalance(ccb.getUncltSolCoinBalance().add(r.getTxnAmount()));
                    misttrackLog.setAddressLabel(dataArr.get(0).toString());
                    apiResult = true;
                }
            }
            if (!apiResult) {
                //获取风险评分
                URI uri2 = UriComponentsBuilder.fromHttpUrl(url_2).queryParams(paramMap).build().encode().toUri();
                ResponseEntity<String> response2 = new RestTemplate().exchange(uri2, HttpMethod.GET, hEntity, String.class);
                String storeMes = response2.getBody();
                JSONObject scoreObject = JSONObject.parseObject(storeMes);
                if (scoreObject.get("success").equals(true)) {

                    String data = scoreObject.get("data").toString();
                    if (!(data.equals("{}") || data.equals("{}\n") || StringUtils.isEmpty(data))) {
                        String dataScore = JSONObject.parseObject(data).get("score").toString();
                        String riskLevel = JSONObject.parseObject(data).get("risk_level").toString();

                        apiResult = true;
                        int score = Integer.parseInt(dataScore);
                        logger.info("获取风险评分:" + score);
                        logger.info("获取风险等级:" + riskLevel);
                        misttrackLog.setScore(String.valueOf(score));
                        misttrackLog.setLevel(riskLevel);
                        int unclt = Integer.parseInt(dataService.selectDictLabel("misttrack_score", "unclt"));
                        logger.info("未归集金额最大值设置" + unclt);
                        int freeze = Integer.parseInt(dataService.selectDictLabel("misttrack_score", "freeze"));
                        logger.info("冻结金额最大值设置" + freeze);


                        if (score >= 0 && score <= unclt) {//低风险
                            //未归集金额增加
                            logger.info("2未归集金额增加:" + r.getTxnAmount());
                            ccb.setUncltSolCoinBalance(ccb.getUncltSolCoinBalance().add(r.getTxnAmount()));

                        } else if (score > unclt && score <= freeze) {//中风险
                            //冻结金额增加
                            logger.info("冻结金额增加:" + r.getTxnAmount());
                            txnStatus = 2;
                            ccb.setFreezeBalance(ccb.getFreezeBalance().add(r.getTxnAmount()));
                            ccb.setUncltSolCoinBalance(ccb.getUncltSolCoinBalance().add(r.getTxnAmount()));
                            sendEmail.remindMail("调用MistTrack的API，评分中风险：" + score + "，txid:" + vo.getTxid() + "，冻结金额：" + r.getTxnAmount() + "。", "调用MistTrack-API的信息提醒");

                        } else if (score > freeze) {//高风险
                            //交易状态
                            r.setTxnStatus(0);
                            //后台未加服务费
                            coinTxnDtlService.save(r);
                            misttrackLogService.save(misttrackLog);
                            sendEmail.remindMail("调用MistTrack的API，评分高风险：" + score + "，txid:" + vo.getTxid() + "。", "调用MistTrack-API的信息提醒");
                            return false;
                        }

                    }


                }

            }

            //两个接口调用api没成功
            if (!apiResult) {
                logger.info("调用MistTrack的API，标签地址和风险评分没获取数据，冻结金额增加:" + r.getTxnAmount());
                txnStatus = 2;
                ccb.setFreezeBalance(ccb.getFreezeBalance().add(r.getTxnAmount()));
                ccb.setUncltSolCoinBalance(ccb.getUncltSolCoinBalance().add(r.getTxnAmount()));
                sendEmail.remindMail("调用MistTrack的API，标签地址和风险评分没获取数据，txid:" + vo.getTxid() + "，冻结金额：" + r.getTxnAmount() + "。", "调用MistTrack-API的信息提醒");
            }

            misttrackLogService.save(misttrackLog);
        }
        //MistTrack的api关闭的时候--未归集金额增加
        else {
            logger.info("MistTrack的api关闭的时候--未归集金额增加:" + r.getTxnAmount());
            ccb.setUncltSolCoinBalance(ccb.getUncltSolCoinBalance().add(r.getTxnAmount()));
        }
        //交易状态
        r.setTxnStatus(txnStatus);
        coinTxnDtlService.save(r);

        if (!apiFlag || txnStatus == 1) {
            if (ccb.getCoinBalance() == null) {
                ccb.setCoinBalance(new BigDecimal(0));
            }
            //判断是否是B端用户
            MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartner(cb.getUserId());
            if (metaPartnerAssetPool != null) {
                //判断是否自动入池子
                //并且金额大于等于入池子的金额
                if (metaPartnerAssetPool.getAuto() == '0' && metaPartnerAssetPool.getAutoAmt() != null && r.getTxnAmount().compareTo(metaPartnerAssetPool.getAutoAmt()) >= 0) {

                    // 获取兑换利率
                    ExchangeRate rate = exchangeRateService.findByFromCurrencyCodeAndToCurrencyCode(vo.getCoinType(), "USD");
                    BigDecimal rateAmount = r.getTxnAmount().multiply(rate.getRateVal());
                    rateAmount = rateAmount.setScale(2, RoundingMode.DOWN); // 保留两位小数,且向下取整
                    //个人兑换充值流水
                    metaPartnerAssetPool.setUsdtBalacne(metaPartnerAssetPool.getUsdtBalacne().add(rateAmount));

                    metaPartnerAssetPoolService.toPupdealDtl(metaPartnerAssetPool, vo.getCoinType(), rateAmount, vo.getTxid());
                    metaPartnerAssetPoolService.updateBalance(cb.getUserId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());

                } else {
                    ccb.setCoinBalance(ccb.getCoinBalance().add(r.getTxnAmount()));
                }
            } else {
                ccb.setCoinBalance(ccb.getCoinBalance().add(r.getTxnAmount()));
            }

        }

        return true;
    }

}
