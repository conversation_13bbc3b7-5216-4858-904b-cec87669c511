package com.meta.dao;

import com.meta.entity.CoinBalance;
import com.meta.entity.CoinBalancePK;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;


/**
 *  客户数字货币余额 数据层
 *
 * <AUTHOR>
 */
@Repository
public interface CoinBalanceDataDao extends JpaRepository<CoinBalance, CoinBalancePK>, JpaSpecificationExecutor<CoinBalance> {


    @Modifying
    @Query(value = "update meta_coin_balance set unclt_bep20_coin_balance=?5,freeze_balance=?4,coin_balance=?3 where user_id=?1 and coin_code=?2 ", nativeQuery = true)
    void updateCoinBalanceData(Long userId, String coinCode, BigDecimal coinBalance, BigDecimal freezeBalance, BigDecimal uncltCoinBalance);



    @Modifying
    @Query(value = "update meta_coin_balance set unclt_bep20_coin_balance=?5,freeze_balance=?4,coin_balance=?3 where user_id=?1 and coin_code=?2 ", nativeQuery = true)
    void editCoinBalanceByEBP20(Long userId, String coinCode, BigDecimal coinBalance, BigDecimal freezeBalance, BigDecimal uncltBep20CoinBalance);

    @Modifying
    @Query(value = "update meta_coin_balance set unclt_sol_coin_balance=?5,freeze_balance=?4,coin_balance=?3 where user_id=?1 and coin_code=?2 ", nativeQuery = true)
    void editCoinBalanceBySol(Long userId, String coinCode, BigDecimal coinBalance, BigDecimal freezeBalance, BigDecimal uncltSolCoinBalance);


    @Modifying
    @Query(value = "update meta_coin_balance set unclt_erc20_base_coin_balance=?5,freeze_balance=?4,coin_balance=?3 where user_id=?1 and coin_code=?2 ", nativeQuery = true)
    void editCoinBalanceByErc20Base(Long userId, String coinCode, BigDecimal coinBalance, BigDecimal freezeBalance, BigDecimal uncltBep20CoinBalance);

    @Modifying
    @Query(value = "update meta_coin_balance set unclt_erc20_arb_coin_balance=?5,freeze_balance=?4,coin_balance=?3 where user_id=?1 and coin_code=?2 ", nativeQuery = true)
    void editCoinBalanceByErc20Arb(Long userId, String coinCode, BigDecimal coinBalance, BigDecimal freezeBalance, BigDecimal uncltBep20CoinBalance);
}
