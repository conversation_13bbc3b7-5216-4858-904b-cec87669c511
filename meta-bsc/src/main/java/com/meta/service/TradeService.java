package com.meta.service;

import com.meta.system.domain.app.BepCstaddress;

import java.math.BigDecimal;

public interface TradeService {
    String getTokenBalance(String bepCstaddress, String type);

    String getBalance(String address);

    String sendTradeByCst(BepCstaddress bepCstaddress, String type, BigDecimal amount1);

    String getBalance(BepCstaddress bepCstaddress);

    String sendTradeBnb(BepCstaddress bepCstaddress);

    String sendTradeBnbQuick(BepCstaddress bepCstaddress);

    String sendTrade(String from, String to, String value, String type);

    String sendTradeByCst(String address, String type);
}
