package com.meta.service.impl;

import com.meta.common.exception.CustomException;
import com.meta.config.WalletConfig;
import com.meta.service.CoinBalanceService;
import com.meta.service.MainSysIdAddressService;
import com.meta.service.TradeService;
import com.meta.system.domain.MetaMainAddress;
import com.meta.system.domain.app.BepCstaddress;
import com.meta.system.service.BepCstaddressService;
import com.meta.system.service.ISysConfigService;
import com.meta.utils.QuikNodeApi;
import com.meta.utils.StringUtils;
import com.meta.utils.Web3jUtils;
import com.meta.utils.WeiToCoinUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.FunctionReturnDecoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Function;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.crypto.Credentials;
import org.web3j.crypto.RawTransaction;
import org.web3j.crypto.TransactionEncoder;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.admin.Admin;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.request.Transaction;
import org.web3j.protocol.core.methods.response.EthCall;
import org.web3j.protocol.core.methods.response.EthGetBalance;
import org.web3j.protocol.core.methods.response.EthGetTransactionCount;
import org.web3j.protocol.core.methods.response.EthSendTransaction;
import org.web3j.protocol.http.HttpService;
import org.web3j.utils.Convert;
import org.web3j.utils.Numeric;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Service
public class TradeServiceImpl implements TradeService {
    @Autowired
    Web3j web3j;

    @Autowired
    Admin admin;

    @Autowired
    BepCstaddressService bepCstaddressService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private CoinBalanceService coinBalanceService;

    @Autowired
    private Web3jUtils web3jUtils;

    @Autowired
    private QuikNodeApi quikNodeApi;

    @Autowired
    private MainSysIdAddressService mainSysIdAddressService;

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 查询代币余额
     */
    public String getTokenBalance(String bepCstaddress, String type) {
        //判断是否启用QuickNode
        String enable_quicknode = configService.selectConfigByKey("enable_quicknode");
        if ("Y".equals(enable_quicknode)) {
            logger.info("执行quicknode节点");
            String url = configService.selectConfigByKey("quicknode_url");
            web3j = Web3j.build(new HttpService(url));
        } else {
            logger.info("执行搭建节点");
            boolean isok = web3jUtils.tryConnect(web3j);
            if (!isok) {
                web3j = web3jUtils.getWeb3();
            }
        }
        String contractAddress = "";
        if (type.equals("USDT")) {
            contractAddress = configService.selectConfigByKey("USDT_CONTRACTA_DDRESS");
        } else if (type.equals("USDC")) {
            contractAddress = configService.selectConfigByKey("USDC_CONTRACTA_DDRESS");
        } else {
            throw new CustomException("不支持的代币类型");
        }


        String fromAddress = bepCstaddress;

        String methodName = "balanceOf";
        List<Type> inputParameters = new ArrayList<>();
        List<TypeReference<?>> outputParameters = new ArrayList<>();
        Address address = new Address(fromAddress);
        inputParameters.add(address);

        TypeReference<Uint256> typeReference = new TypeReference<Uint256>() {
        };
        outputParameters.add(typeReference);
        Function function = new Function(methodName, inputParameters, outputParameters);
        String data = FunctionEncoder.encode(function);
        Transaction transaction = Transaction.createEthCallTransaction(fromAddress, contractAddress, data);

        EthCall ethCall;
        BigInteger balanceValue = BigInteger.ZERO;
        try {
            ethCall = web3j.ethCall(transaction, DefaultBlockParameterName.LATEST).send();
            List<Type> results = FunctionReturnDecoder.decode(ethCall.getValue(), function.getOutputParameters());
            balanceValue = (BigInteger) results.get(0).getValue();
        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println("-----" + balanceValue);
        return String.valueOf(WeiToCoinUtils.convertWeiToCoin(new BigDecimal(balanceValue)));
    }

    /**
     * 获取bnb余额
     *
     * @param bepCstaddress
     * @return
     */
    @Override
    public String getBalance(BepCstaddress bepCstaddress) {
        return getBalance(bepCstaddress.getCstAdress());
    }

    @Override
    public String getBalance(String address) {
        boolean isok = web3jUtils.tryConnect(web3j);
        if (!isok) {
            web3j = web3jUtils.getWeb3();
        }


        try {


            EthGetBalance ethGetBalance = web3j.ethGetBalance(address, DefaultBlockParameterName.LATEST).send();
            BigInteger balance = ethGetBalance.getBalance();
            BigDecimal bal = WeiToCoinUtils.convertWeiToCoin(new BigDecimal(balance));
            System.out.println(bal);
            return String.valueOf(bal);


        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }


    @Override
    public String sendTradeByCst(BepCstaddress bepCstaddress, String type, BigDecimal amount1) {


        String fromAddress = bepCstaddress.getCstAdress();
        String fromPrivateKey = bepCstaddress.getCstTrcPrivate();
        String toAddress = WalletConfig.bnbmainaddress;

        String contractAddress = "";

        if (type.equals("USDC")) {
            contractAddress = configService.selectConfigByKey("USDC_CONTRACTA_DDRESS");
        } else if (type.equals("USDT")) {
            contractAddress = configService.selectConfigByKey("USDT_CONTRACTA_DDRESS");
        }
        return sendTrade(fromAddress, fromPrivateKey, toAddress, contractAddress, amount1);

    }


    /**
     * 发送智能合约交易(归集交易)
     *
     * @param fromAddress 发送钱包地址
     * @return
     */
    public String sendTrade(String fromAddress, String fromPrivateKey, String toAddress, String contractAddress, BigDecimal amount1) {

        try {

            boolean isok = web3jUtils.tryConnect(web3j);
            if (!isok) {
                web3j = web3jUtils.getWeb3();
            }
            //转账的凭证，需要传入私钥
            Credentials credentials = Credentials.create(fromPrivateKey);
            //获取交易笔数
            BigInteger nonce;
            EthGetTransactionCount ethGetTransactionCount = web3j.ethGetTransactionCount(fromAddress, DefaultBlockParameterName.PENDING).send();
            if (ethGetTransactionCount == null) {
                return null;
            }
            nonce = ethGetTransactionCount.getTransactionCount();

            //手续费
            //注意手续费的设置，这块很容易遇到问题
            String gas_price = configService.selectConfigByKey("BSC_GAS_PRICE");
            String gas_limit = configService.selectConfigByKey("BSC_GAS_LIMIT");

            BigInteger gasPrice = Convert.toWei(gas_price, Convert.Unit.GWEI).toBigInteger();
            BigInteger gasLimit = Convert.toWei(gas_limit, Convert.Unit.WEI).toBigInteger();
            System.out.println(gasPrice);
            System.out.println(gasLimit);

            BigInteger amount = WeiToCoinUtils.convertToWei(amount1).toBigInteger();

            Function function = new Function(
                    "transfer",
                    Arrays.asList(new Address(toAddress), new Uint256(amount)),
                    Collections.singletonList(new TypeReference<Type>() {
                    }));
            //创建交易对象
            String encodedFunction = FunctionEncoder.encode(function);
            RawTransaction rawTransaction = RawTransaction.createTransaction(nonce, gasPrice, gasLimit,
                    contractAddress, encodedFunction);

            //进行签名操作
            byte[] signMessage = TransactionEncoder.signMessage(rawTransaction, credentials);
            String hexValue = Numeric.toHexString(signMessage);
            //发起交易
            EthSendTransaction ethSendTransaction = web3j.ethSendRawTransaction(hexValue).sendAsync().get();
            String hash = ethSendTransaction.getTransactionHash();
            if (hash != null) {

                System.out.println("======" + ethSendTransaction);
                //执行业务
                System.out.printf("执行成功：" + hash);
                return hash;
            } else {
                throw new Exception("发送失败，from：" + fromAddress + "，to:" + toAddress);
            }
        } catch (Exception ex) {
            System.out.printf("失败");

            //报错应进行错误处理
            ex.printStackTrace();
        }
        return null;
    }

    /**
     * 发送智能合约交易(归集交易)-QuickNode
     *
     * @param fromAddress 发送钱包地址
     * @return
     */
    public String sendTradeQuick(String fromAddress, String fromPrivateKey, String toAddress, String contractAddress, BigDecimal amount1) {

        try {
            String url = configService.selectConfigByKey("quicknode_url");

            //转账的凭证，需要传入私钥
            Credentials credentials = Credentials.create(fromPrivateKey);
            //获取交易笔数
            BigInteger nonce = quikNodeApi.decodeQuantity(quikNodeApi.ethGetTransactionCount(fromAddress, url, DefaultBlockParameterName.PENDING.getValue()));

            //手续费
            //注意手续费的设置，这块很容易遇到问题
            String gas_price = configService.selectConfigByKey("BSC_GAS_PRICE");
            String gas_limit = configService.selectConfigByKey("BSC_GAS_LIMIT");

            BigInteger gasPrice = Convert.toWei(gas_price, Convert.Unit.GWEI).toBigInteger();
            BigInteger gasLimit = Convert.toWei(gas_limit, Convert.Unit.WEI).toBigInteger();
            System.out.println(gasPrice);
            System.out.println(gasLimit);

            BigInteger amount = WeiToCoinUtils.convertToWei(amount1).toBigInteger();

            Function function = new Function(
                    "transfer",
                    Arrays.asList(new Address(toAddress), new Uint256(amount)),
                    Collections.singletonList(new TypeReference<Type>() {
                    }));
            //创建交易对象
            String encodedFunction = FunctionEncoder.encode(function);
            RawTransaction rawTransaction = RawTransaction.createTransaction(nonce, gasPrice, gasLimit,
                    contractAddress, encodedFunction);
            String chain = configService.selectConfigByKey("BNB_CHAIN_ID");
            int chainInt = Integer.parseInt(chain);
            byte chainId = (byte) chainInt;
            //进行签名操作
            byte[] signMessage = TransactionEncoder.signMessage(rawTransaction, chainId, credentials);
            String hexValue = Numeric.toHexString(signMessage);
            //发起交易
            String hash = quikNodeApi.ethSendRawTransaction(hexValue, url);
            if (hash != null) {

                logger.info("智能合约交易hash：" + hash);
                //执行业务
                System.out.printf("执行成功：" + hash);
                return hash;
            } else {
                throw new Exception("发送失败，from：" + fromAddress + "，to:" + toAddress);
            }
        } catch (Exception ex) {
            System.out.printf("失败");

            //报错应进行错误处理
            ex.printStackTrace();
        }
        return null;
    }

    /**
     * 给用户bnb
     *
     * @param bepCstaddress
     * @return
     */
    @Override
    public String sendTradeBnb(BepCstaddress bepCstaddress) {

        try {
            boolean isok = web3jUtils.tryConnect(web3j);
            if (!isok) {
                web3j = web3jUtils.getWeb3();
            }

            String toAddress = bepCstaddress.getCstAdress();
            String fromAddress = WalletConfig.bnbgiveaddress;
            String mainbscaddressprivate = WalletConfig.bnbgiveaddressprivate;
            String value = configService.selectConfigByKey("collect_bnb_amount");
            String gas_price = configService.selectConfigByKey("BSC_GAS_PRICE");
            String gas_limit = configService.selectConfigByKey("BSC_GAS_LIMIT");
            BigInteger gasPrice = Convert.toWei(gas_price, Convert.Unit.GWEI).toBigInteger();
            BigInteger gasLimit = Convert.toWei(gas_limit, Convert.Unit.WEI).toBigInteger();

            //转账的凭证，需要传入私钥
            BigInteger nonce = web3j.ethGetTransactionCount(fromAddress, DefaultBlockParameterName.LATEST).send().getTransactionCount();
            // 创建交易
            BigInteger amount = WeiToCoinUtils.convertCoinToWei(new BigDecimal(value)).toBigInteger();
            RawTransaction rawTransaction = RawTransaction.createEtherTransaction(nonce, gasPrice, gasLimit, toAddress, amount);
            // 签名交易
            Credentials credentials = Credentials.create(mainbscaddressprivate);
            byte[] signedMessage = TransactionEncoder.signMessage(rawTransaction, credentials);
            String hexValue = Numeric.toHexString(signedMessage);

            // 发送交易
            EthSendTransaction ethSendTransaction = web3j.ethSendRawTransaction(hexValue).send();
            return ethSendTransaction.getTransactionHash();

        } catch (Exception ex) {
            //报错应进行错误处理
            ex.printStackTrace();
        }
        return null;
    }

    /**
     * 给用户bnb
     *
     * @param bepCstaddress
     * @return
     */
    @Override
    public String sendTradeBnbQuick(BepCstaddress bepCstaddress) {

        try {
            String url = configService.selectConfigByKey("quicknode_url");
            String toAddress = bepCstaddress.getCstAdress();
            String fromAddress = WalletConfig.bnbgiveaddress;
            String mainbscaddressprivate = WalletConfig.bnbgiveaddressprivate;
            String value = configService.selectConfigByKey("collect_bnb_amount");
            String gas_price = configService.selectConfigByKey("BSC_GAS_PRICE");
            String gas_limit = configService.selectConfigByKey("BSC_GAS_LIMIT");
            BigInteger gasPrice = Convert.toWei(gas_price, Convert.Unit.GWEI).toBigInteger();
            BigInteger gasLimit = Convert.toWei(gas_limit, Convert.Unit.WEI).toBigInteger();
            String chain = configService.selectConfigByKey("BNB_CHAIN_ID");
            int chainInt = Integer.parseInt(chain);
            byte chainId = (byte) chainInt;

            //转账的凭证，需要传入私钥
            BigInteger nonce = quikNodeApi.decodeQuantity(quikNodeApi.ethGetTransactionCount(fromAddress, url, DefaultBlockParameterName.LATEST.getValue()));
            // 创建交易
            BigInteger amount = WeiToCoinUtils.convertCoinToWei(new BigDecimal(value)).toBigInteger();

            String methodName = "transfer";
            Function function = new Function(
                    methodName,
                    Arrays.asList(new Uint256(amount)),
                    Arrays.asList(new TypeReference<Type>() {
                    }));
            String data = FunctionEncoder.encode(function);

            RawTransaction rawTransaction = RawTransaction.createTransaction(
                    nonce,
                    gasPrice,
                    gasLimit,
                    toAddress,
                    amount,
                    data);

            // 签名交易
            Credentials credentials = Credentials.create(mainbscaddressprivate);
            byte[] signedMessage = TransactionEncoder.signMessage(rawTransaction, chainId, credentials);
            String hexValue = Numeric.toHexString(signedMessage);
            // 发送交易
            String transactionHash = quikNodeApi.ethSendRawTransaction(hexValue, url);
            logger.info("bnb交易hash：" + transactionHash);
            return transactionHash;

        } catch (Exception ex) {
            //报错应进行错误处理
            ex.printStackTrace();
        }
        return null;
    }

    /**
     * 发送智能合约交易
     *
     * @param from  from地址
     * @param to    to地址
     * @param value 金额
     * @param type  类型
     * @return 结果
     */
    public String sendTrade(String from, String to, String value, String type) {

        try {
            boolean isok = web3jUtils.tryConnect(web3j);
            if (!isok) {
                web3j = web3jUtils.getWeb3();
            }

            String contractAddress = "";

            if (type.equals("USDC")) {
                contractAddress = configService.selectConfigByKey("USDC_CONTRACTA_DDRESS");
            } else if (type.equals("USDT")) {
                contractAddress = configService.selectConfigByKey("USDT_CONTRACTA_DDRESS");
            }

            BepCstaddress bepCstaddress = bepCstaddressService.findByAddress(from);
            if (bepCstaddress == null) {
                return null;
            }


            String fromPrivateKey = bepCstaddress.getCstTrcPrivate();
            //转账的凭证，需要传入私钥
            Credentials credentials = Credentials.create(fromPrivateKey);
            //获取交易笔数
            BigInteger nonce;
            EthGetTransactionCount ethGetTransactionCount = web3j.ethGetTransactionCount(from, DefaultBlockParameterName.PENDING).send();
            if (ethGetTransactionCount == null) {
                return null;
            }
            nonce = ethGetTransactionCount.getTransactionCount();

            //手续费
            //注意手续费的设置，这块很容易遇到问题
            String gas_price = configService.selectConfigByKey("BSC_GAS_PRICE");
            String gas_limit = configService.selectConfigByKey("BSC_GAS_LIMIT");

            BigInteger gasPrice = Convert.toWei(gas_price, Convert.Unit.GWEI).toBigInteger();
            BigInteger gasLimit = Convert.toWei(gas_limit, Convert.Unit.WEI).toBigInteger();
            System.out.println(gasPrice);
            System.out.println(gasLimit);
            BigInteger val = WeiToCoinUtils.convertToWei(new BigDecimal(value)).toBigInteger();
            Function function = new Function(
                    "transfer",
                    Arrays.asList(new Address(to), new Uint256(val)),
                    Collections.singletonList(new TypeReference<Type>() {
                    }));
            //创建交易对象
            String encodedFunction = FunctionEncoder.encode(function);
            RawTransaction rawTransaction = RawTransaction.createTransaction(nonce, gasPrice, gasLimit,
                    contractAddress, encodedFunction);

            //进行签名操作
            byte[] signMessage = TransactionEncoder.signMessage(rawTransaction, credentials);
            String hexValue = Numeric.toHexString(signMessage);
            //发起交易
            EthSendTransaction ethSendTransaction = web3j.ethSendRawTransaction(hexValue).sendAsync().get();
            String hash = ethSendTransaction.getTransactionHash();
            if (hash != null) {

                System.out.println("======" + ethSendTransaction);
                //执行业务
                System.out.printf("执行成功：" + hash);
                return hash;
            } else {
                throw new Exception("发送失败，from：" + from + "，to:" + to);
            }
        } catch (Exception ex) {
            System.out.printf("失败");

            //报错应进行错误处理
            ex.printStackTrace();
        }
        return null;
    }

    @Override
    public String sendTradeByCst(String address, String type) {
        BepCstaddress bepCstaddress = bepCstaddressService.findByAddress(address);
        if (bepCstaddress == null) {
            throw new CustomException("钱包地址不存在");
        }
        String fromAddress = bepCstaddress.getCstAdress();
        String fromPrivateKey = bepCstaddress.getCstTrcPrivate();
        MetaMainAddress bep20 = mainSysIdAddressService.getMainSysIdAddress("BEP20", bepCstaddress.getSysId());
        String toAddress = bep20.getMainaddress();

        String contractAddress = "";

        if (type.equals("USDC")) {
            contractAddress = configService.selectConfigByKey("USDC_CONTRACTA_DDRESS");
        } else if (type.equals("USDT")) {
            contractAddress = configService.selectConfigByKey("USDT_CONTRACTA_DDRESS");
        } else {
            throw new CustomException("不支持的代币类型");
        }
        String ye = this.getTokenBalance(bepCstaddress.getCstAdress(), type);
        //钱包归集金额触发的值
        BigDecimal amount1 = new BigDecimal(ye);
        int result = amount1.compareTo(BigDecimal.ZERO);
        logger.info("归集金额为" + ye);
        if (!(result > 0)) {
            logger.info("未归集，余额没有大于0");
            return "未归集，余额没有大于0";
        }

        //判读bnb是否充足
        //查询bnb余额
        String bnbAmount = this.getBalance(bepCstaddress);
        String collect_bnb_amount = configService.selectConfigByKey("collect_bnb_amount");
        logger.info("查询BNB余额:" + toAddress);
        BigDecimal amount3 = new BigDecimal(bnbAmount);
        BigDecimal amount4 = new BigDecimal(collect_bnb_amount);

        //bnb余额是否充足
        int result1 = amount3.compareTo(amount4);
        String enable_quicknode = configService.selectConfigByKey("enable_quicknode");
        if (result1 < 0) {
            String hx = "";
            logger.info("bnb余额不足就触发充值:" + toAddress);
            logger.info("开始bnb充值");

            if ("Y".equals(enable_quicknode)) {
                logger.info("执行quicknode节点");
                hx = this.sendTradeBnbQuick(bepCstaddress);
            } else {
                logger.info("执行搭建节点");
                hx = this.sendTradeBnb(bepCstaddress);
            }


            logger.info("hx:" + hx);
            if (StringUtils.isEmpty(hx)) {
                logger.info("bnb充值失败了");
                return null;
            }
            logger.info("开始异步处理钱包归集:" + toAddress);
            collectdata(fromAddress, fromPrivateKey, toAddress, contractAddress, amount1);
            return "ok";

        } else {
            //钱包归集
            logger.info("bnb充足，无需充值，开始钱包归集:" + toAddress);
            if ("Y".equals(enable_quicknode)) {
                logger.info("执行quicknode节点");
                return sendTradeQuick(fromAddress, fromPrivateKey, toAddress, contractAddress, amount1);
            } else {
                logger.info("执行搭建节点");
                return sendTrade(fromAddress, fromPrivateKey, toAddress, contractAddress, amount1);
            }

        }

    }

    //异步归集
    public void collectdata(String fromAddress, String fromPrivateKey, String toAddress, String contractAddress, BigDecimal amount1) {
        CompletableFuture.runAsync(() -> {

            try {
                logger.info("异步开始钱包归集先等待5s:");
                Thread.sleep(5000);

                String enable_quicknode = configService.selectConfigByKey("enable_quicknode");
                if ("Y".equals(enable_quicknode)) {
                    logger.info("执行quicknode节点");
                    sendTradeQuick(fromAddress, fromPrivateKey, toAddress, contractAddress, amount1);
                } else {
                    logger.info("执行搭建节点");
                    sendTrade(fromAddress, fromPrivateKey, toAddress, contractAddress, amount1);
                }


                logger.info("异步开钱包归集成功");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }


        });
    }

}
