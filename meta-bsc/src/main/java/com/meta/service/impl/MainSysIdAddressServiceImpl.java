package com.meta.service.impl;

import com.meta.config.WalletConfig;
import com.meta.service.MainSysIdAddressService;
import com.meta.system.domain.MetaMainAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/02/11/13:56
 */
@Service
public class MainSysIdAddressServiceImpl implements MainSysIdAddressService {

    @Autowired
    private WalletConfig walletConfig;


    /**
     * 根据配置获取kazepay和H5的主钱包地址
     *
     * @return
     */
    public List<MetaMainAddress> getMainAddressList(String type) {
        List<MetaMainAddress> mainAddressList = walletConfig.getMainAddressList();
        return mainAddressList.stream()
                .filter(address -> "BEP20".equals(address.getType()))
                .map(address -> {
                    // 创建一个新的 MetaMainAddress 对象，并将 mainaddress 转为小写
                    address.setMainaddress(address.getMainaddress().toLowerCase());
                    return address;
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据sysId获取主钱包地址
     */
    public MetaMainAddress getMainSysIdAddress(String type, String sysId) {
        List<MetaMainAddress> mainAddressList = walletConfig.getMainAddressList();
        List<MetaMainAddress> collect = mainAddressList.stream()
                .filter(address -> type.equals(address.getType()))
                .filter(address -> sysId.equals(address.getSysId()))
                .peek(address -> {
                    // 创建一个新的 MetaMainAddress 对象，并将 mainaddress 转为小写
                    String mainAddress = address.getMainaddress();
                    if ("BEP20".equals(address.getType())) {
                        address.setMainaddress(mainAddress.toLowerCase()); // 仅当 type 为 BEP20 时转换为小写
                    }
                })
                .collect(Collectors.toList());
        return collect.get(0);
    }

    /**
     * 通过主钱包吃查找
     *
     * @param address
     * @return
     */
    @Override
    public MetaMainAddress findAddress(String address) {
        List<MetaMainAddress> bep20 = getMainAddressList("BEP20");
        for (MetaMainAddress metaMainAddress : bep20) {
            if (address.equals(metaMainAddress.getMainaddress())) {
                return metaMainAddress;
            }
        }
        return null;
    }
}
