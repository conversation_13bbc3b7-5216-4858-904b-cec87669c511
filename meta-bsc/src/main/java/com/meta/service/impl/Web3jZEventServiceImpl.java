package com.meta.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meta.common.enums.BizError;
import com.meta.common.exception.CustomException;
import com.meta.common.utils.MessageUtils;
import com.meta.producer.NormalRabbitProducer;
import com.meta.redis.BizReidsLock;
import com.meta.service.MainSysIdAddressService;
import com.meta.service.TradeService;
import com.meta.service.Web3jZEventService;
import com.meta.system.domain.MetaMainAddress;
import com.meta.system.domain.app.BepCstaddress;
import com.meta.system.domain.app.BepTransactions;
import com.meta.system.domain.partner.MetaPartnerAssetPool;
import com.meta.system.domain.partner.MetaPushData;
import com.meta.system.dto.MqMonitorDto;
import com.meta.system.email.SendEmail;
import com.meta.system.enums.CoinType;
import com.meta.system.service.*;
import com.meta.utils.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.web3j.protocol.Web3j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

@Service
public class Web3jZEventServiceImpl implements Web3jZEventService {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    Web3j web3j;
    @Autowired
    private BizReidsLock bizRedisLock;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    BepCstaddressService bepCstaddressService;

    @Autowired
    private Web3jUtils web3jUtils;

    @Autowired
    private SendEmail sendEmail;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private BepTransactionsService bepTransactionsService;

    @Autowired
    private QuikNodeApi quikNodeApi;


    @Autowired
    private CoinDataUtil coinDataUtil;

    @Autowired
    private MainSysIdAddressService mainSysIdAddressService;

    @Autowired
    private MetaPushDataService metaPushDataService;

    @Autowired
    private MetaPartnerAssetPoolService metaPartnerAssetPoolService;

    @Autowired
    private MQutils mQutils;

    @Autowired
    private TradeService tradeService;

    @Resource
    private NormalRabbitProducer rabbitProducer;

    private static SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.sss");


    @Override
    @Scheduled(cron = "*/3 * * * * ?")
    public void eventWeb3j() {
        logger.info("执行定时任务开始" + df.format(System.currentTimeMillis()));
        //判断是否启用QuickNode
        String enable_quicknode = configService.selectConfigByKey("enable_quicknode");
        if ("Y".equals(enable_quicknode)) {
            logger.info("执行quicknode节点");
            executeQuickNode();
        } else {
            logger.info("执行搭建节点,代码需调整");
//            executeBsc();
        }
        logger.info("执行定时任务结束" + df.format(System.currentTimeMillis()));
    }

    /**
     * 执行QuickNode的节点
     */
    public void executeQuickNode() {

        String url = configService.selectConfigByKey("quicknode_url");


        //判断是否存在blocknumber的key
        String blockKey = Constants.BLOCKNUMBER + "blockNumber";
        BigInteger startBlock = null;
        boolean hskey = redisTemplate.hasKey(blockKey);
        if (hskey) {
            startBlock = new BigInteger(redisTemplate.opsForValue().get(blockKey));
            logger.info("开始的桶startBlock：" + startBlock);
        }

        String blockNum = quikNodeApi.ethBlockNumber(url);
        if (StringUtils.isNull(blockNum)) {
            logger.info("获取当前节点的桶信息失败");
            return;
        }
        BigInteger latestBlockNum = new BigInteger(String.valueOf(Integer.parseInt(blockNum.substring(2), 16)));
        logger.info("结束的桶latestBlockNum：" + latestBlockNum);


        //添加合约
        List<String> tokenList = new ArrayList<>();
        String dco_contracta_ddress = "";//configService.selectConfigByKey("DCO_CONTRACTA_DDRESS");
        if (StringUtils.isNotEmpty(dco_contracta_ddress)) {
            tokenList.add(dco_contracta_ddress);
        }
        String usdt_contracta_ddress = configService.selectConfigByKey("USDT_CONTRACTA_DDRESS");
        String usdc_contracta_ddress = configService.selectConfigByKey("USDC_CONTRACTA_DDRESS");
        if (StringUtils.isNotEmpty(usdt_contracta_ddress)) {
            tokenList.add(usdt_contracta_ddress);
        }
        if (StringUtils.isNotEmpty(usdc_contracta_ddress)) {
            tokenList.add(usdc_contracta_ddress);
        }

        String fromBlock = "0x" + startBlock.toString(16).toUpperCase();//开始的桶16进制
        String toBlock = blockNum;//结束的桶16进制

        //判断开始的桶和结束的桶的差距是否大于20000
        BigInteger dif = latestBlockNum.subtract(startBlock);
        BigInteger interval = new BigInteger(configService.selectConfigByKey("INTERVAL_FOR_BEP20"));
        int rs = dif.compareTo(interval);
        if (rs > 0) {
            latestBlockNum = startBlock.add(interval);
            toBlock = "0x" + latestBlockNum.toString(16).toUpperCase();//开始的桶16进制
            logger.info("两个相差大于{}，结束的桶latestBlockNum：{}", interval, latestBlockNum);
        }

        redisTemplate.opsForValue().set(blockKey, String.valueOf(latestBlockNum));
        String filterId = quikNodeApi.ethNewFilter(fromBlock, toBlock, tokenList, url);
        logger.info("filterId：" + filterId);
        if (StringUtils.isNull(filterId)) {
            logger.info("开始桶：" + fromBlock + "结束的桶：" + latestBlockNum + "，获取过滤器失败");
            return;
        }
        String filterLogs = quikNodeApi.ethGetFilterLogs(filterId, url);
        if (StringUtils.isNull(filterLogs)) {
            logger.info("没获取到数据");
        }

        List<MetaMainAddress> bep20Addresses = mainSysIdAddressService.getMainAddressList("BEP20");
        //kazepay和H5的数据
//        Map<String, List<String>> map = new HashMap<>();
        List<String> allList = new ArrayList<>();
        for (MetaMainAddress metaMainAddress : bep20Addresses) {

            String sysId = metaMainAddress.getSysId();

            String keyBep20 = Constants.BEP_ADDRESS + sysId;
            boolean hskey20 = Boolean.TRUE.equals(redisTemplate.hasKey(keyBep20));
            if (!hskey20) {
                bepCstaddressService.refreshAddress(metaMainAddress.getMainaddress().toLowerCase(), sysId);
            }
            List<String> addressList = redisTemplate.opsForList().range(keyBep20, 0, -1);
            String mainaddress = metaMainAddress.getMainaddress().toLowerCase();
            if (!addressList.contains(mainaddress)) {
                addressList.add(mainaddress);
            }
            //                map.put(sysId, addressList);
            allList.addAll(addressList);


        }
        //判断to是否是客户地址或者公司钱包地址-redis

        //判断有没有这个key。没有就重新拉取数据


        //处理交易数据
        JSONArray jsonArray = JSONArray.parseArray(filterLogs);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);


            JSONArray topics = jsonObject.getJSONArray("topics");

            String from = "0x" + topics.get(1).toString().substring(26);
            String to = "0x" + topics.get(2).toString().substring(26);
            //判断是否存在数据
            String txid = jsonObject.getString("transactionHash");
            String blockNumber16 = jsonObject.getString("blockNumber");
            String blockNumber = String.valueOf(Integer.parseInt(blockNumber16.substring(2), 16));
//            boolean isOk = false;//是否监听到数据
//            List<String> dataList = new ArrayList<>();//各个模块的数据集合
//            String mainaddress = "";//主钱包地址
//            String sysId = "";
//            BigDecimal rechargeAmount = BigDecimal.ZERO;
//
//            for (String key : map.keySet()) {
//
//                List<String> list = map.get(key);
//                if (list != null && list.size() > 0 && list.contains(to)) {
//                    isOk = true;
//                    dataList = list;
//                    sysId = key;
//                    MetaMainAddress metaMainAdd = mainSysIdAddressService.getMainSysIdAddress("BEP20",sysId);
//                    mainaddress = metaMainAdd.getMainaddress();
//                    rechargeAmount = metaMainAdd.getMinimumRecharge();
//                    break;
//                }
//            }

            //监听到数据
            if (allList.size() > 0 && allList.contains(to)) {
                logger.info("交易充值/钱包归集");

                try {
                    //限制多次请求同一个Txid
                    logger.info("txid为：" + txid + "，桶为：" + blockNumber);
                    if (!bizRedisLock.locks(txid, Constants.redisLock.TXID)) {
                        throw new CustomException(MessageUtils.message(BizError.SYS_RECHARGE_BUSY.value()));
                    }
                    BepTransactions bepTransactions = new BepTransactions();
                    String sysId = "";
                    //判断交易是否已存在
                    List<BepTransactions> bcList = bepTransactionsService.findByTxid(txid);
                    boolean isAbnormalForBalance = false;

                    if (bcList.isEmpty()) {
                        MetaMainAddress metaMainAddress = null;
                        //判断是否是钱包归集
                        MetaMainAddress maddress = mainSysIdAddressService.findAddress(to);
                        if (maddress != null) {
                            sysId = maddress.getSysId();
                            metaMainAddress = maddress;
                        } else {
                            BepCstaddress bepCstaddress = bepCstaddressService.findByAddress(to);
                            sysId = bepCstaddress.getSysId();
                            metaMainAddress = mainSysIdAddressService.getMainSysIdAddress("BEP20", sysId);
                        }


                        String mainaddress = metaMainAddress.getMainaddress();//主钱包地址
                        BigDecimal rechargeAmount = metaMainAddress.getMinimumRecharge();

                        String token = jsonObject.getString("address"); //这是Token合约地址

                        //金额
                        String value = jsonObject.getString("data");
                        System.out.println(value);
                        BigInteger big = new BigInteger(value.substring(2), 16);
                        System.out.println(big);
                        BigDecimal amount = WeiToCoinUtils.convertWeiToCoin(new BigDecimal(big))
                                .setScale(6, RoundingMode.DOWN);


                        //交易费用wei
                        BigDecimal use = null;
                        //时间戳
                        String timestamp = "";
                        Date time = null;

                        //得到交易桶信息
                        String blockStr = quikNodeApi.ethGetBlockByNumber(blockNumber16, url);
                        JSONObject jsonObjectBlcok = JSONObject.parseObject(blockStr);
                        timestamp = String.valueOf(Long.parseLong(jsonObjectBlcok.getString("timestamp").substring(2), 16));
                        Instant instant = Instant.ofEpochSecond(Long.valueOf(timestamp));
                        LocalDateTime transactionTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
                        logger.info("Transaction Time: " + transactionTime);
                        time = Date.from(transactionTime.atZone(ZoneId.systemDefault()).toInstant());

                        //交易费
                        //单位Gwei
                        //转换为对应的金额
                        // 计算交易费用
                        // 获取交易对象
                        String transactionByHashStr = quikNodeApi.ethGetTransactionByHash(txid, url);
                        JSONObject transactionByHash = JSONObject.parseObject(transactionByHashStr);
                        //判断金额是否正确
                        CoinType coinType;//币种
                        if (StringUtils.isNotEmpty(usdt_contracta_ddress) && token.equals(usdt_contracta_ddress.toLowerCase())) {
                            coinType = CoinType.USDT;
                        } else if (StringUtils.isNotEmpty(usdc_contracta_ddress) && token.equals(usdc_contracta_ddress.toLowerCase())) {
                            coinType = CoinType.USDC;
                        } else {
                            throw new CustomException("错误币种");
                        }
                        if (!to.equals(mainaddress)) {
                            BepCstaddress bepCstaddress = bepCstaddressService.findByAddress(to);
                            String ye = tradeService.getTokenBalance(bepCstaddress.getCstAdress(), coinType.getCode());
                            BigDecimal amount1 = new BigDecimal(ye);

                            if (amount1.compareTo(amount) < 0) {
                                logger.error("查询到的钱包金额小于入账的金额，不允许入账，入账金额：" + amount + ",查询余额:" + ye);
                                isAbnormalForBalance = true;
                            }
                        }

                        // 获取交易的 gas 价格和 gas 限制
                        BigInteger gasPrice = new BigInteger(String.valueOf(Long.parseLong(transactionByHash.getString("gasPrice").substring(2), 16)));
                        // 获取实际使用的 gas 数量
                        String receiptStr = quikNodeApi.ethGetTransactionReceipt(txid, url);
                        JSONObject receipt = JSONObject.parseObject(receiptStr);
                        BigInteger gasUsed = new BigInteger(String.valueOf(Long.parseLong(receipt.getString("gasUsed").substring(2), 16)));
                        BigInteger transactionFee = gasPrice.multiply(gasUsed);
                        use = WeiToCoinUtils.convertWeiToCoin(new BigDecimal(String.valueOf(transactionFee)));
                        bepTransactions.setFee(use);


                        bepTransactions.setAddress(to);
                        bepTransactions.setFromaddress(from);
                        bepTransactions.setAmount(amount);
                        bepTransactions.setContract(token);
                        bepTransactions.setBlockHeight(Long.valueOf(String.valueOf(blockNumber)));
                        bepTransactions.setTxid(txid);
                        if (timestamp != null && timestamp != "") {

                            bepTransactions.setTimestamp(Long.valueOf(timestamp));
                        }

                        bepTransactions.setIsSync(0);

                        //是否是可信签名
                        boolean isInvalidTransactionInput = isInvalidTrasactionInput(transactionByHash.getString("input"));

                        JSONObject data = new JSONObject();
                        data.put("txid", txid);
                        data.put("netWork", "BEP20");

                        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
                        String kazePaySysId = String.valueOf(accountId);

                        BepCstaddress bepCstaddress;

                        if (to.equals(mainaddress)) {
                            bepTransactions.setType("collect");
                            bepTransactions.setIsSync(2);
                            if (allList.contains(from)) {
                                bepCstaddress = bepCstaddressService.findByAddress(from);
                            } else {
                                bepTransactionsService.save(bepTransactions);
                                alert(to, from, bepTransactions, to, coinType, sysId);

                                if (!sysId.equals(kazePaySysId)) {
                                    data.put("bepTransactions", (JSONObject) JSONObject.toJSON(bepTransactions));
                                    data.put("isRecorded", false);//是否入账
                                    SpringUtil.getBean(this.getClass()).pushData("H5_LISTENING_DATA", sysId, data);
                                }
                                return;
                            }

                        } else {
                            bepCstaddress = bepCstaddressService.findByAddress(to);
                            bepTransactions.setType("receive");

                        }
                        bepTransactionsService.save(bepTransactions);
                        alert(to, from, bepTransactions, bepCstaddress.getCstAdress(), coinType, sysId);
                        if (!sysId.equals(kazePaySysId)) {
                            data.put("bepTransactions", (JSONObject) JSONObject.toJSON(bepTransactions));
                        }

                        //充值小于10的情况下不对数据做操作
                        if (bepTransactions.getType().equals("receive")) {
                            if (amount.compareTo(rechargeAmount) < 0) {
                                logger.info("sysId:{}充值小于{}的情况下不入账", sysId, rechargeAmount);
                                isAbnormalForBalance = true;
                            }
                        }

                        if (!isInvalidTransactionInput && !isAbnormalForBalance) {
                            if (!sysId.equals(kazePaySysId)) {
                                data.put("isRecorded", true);//是否入账
                                data.put("from", from);
                                data.put("to", to);
                                data.put("amount", amount);
                                data.put("use", use);
                                data.put("time", time);
                                data.put("coinType", coinType.getCode());
                                data.put("mainAddress", mainaddress);
                                logger.info("推送的数据:" + data);
                                SpringUtil.getBean(this.getClass()).pushData("H5_LISTENING_DATA", sysId, data);
                                if (bepTransactions.getIsSync() == 0) {
                                    bepTransactions.setIsSync(1);
                                    bepTransactionsService.save(bepTransactions);
                                }
                            } else {
                                String keyBep20 = Constants.BEP_ADDRESS + sysId;
                                List<String> addressList = redisTemplate.opsForList().range(keyBep20, 0, -1);
                                boolean result = coinDataUtil.doData(from, to, txid, amount, use, time, bepTransactions, coinType, mainaddress, addressList, "BEP");
                                if (result && !to.equals(mainaddress)) {
                                    coinDataUtil.calculateAsync(to, coinType.getCode());
                                }
                            }
                        }

                    }

                } finally {
                    bizRedisLock.unlocks(txid, Constants.redisLock.TXID);
                }

            }
        }

    }

    private boolean isInvalidTrasactionInput(String input) {
        //判断是否是转账
        logger.info("input:" + input);
        if (StringUtils.isEmpty(input)) {
            logger.error("未找到签名");
            return true;
        }
        String methodId = input.substring(0, 10);
        HashSet<String> supportsMethodId = new HashSet<>();
        supportsMethodId.add("0x23b872dd");
        supportsMethodId.add("0xa9059cbb");
        supportsMethodId.add("0xe6930a22");

        if (!supportsMethodId.contains(methodId)) {
            logger.error("不被支持的methodId");
            return true;
        }
        return false;

    }

    /**
     * 推送数据
     *
     * @param type
     * @param sysId
     * @param json
     */
    @Transactional
    public void pushData(String type, String sysId, JSONObject json) {
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartnerBySysId(sysId);
        String apiUrl = metaPartnerAssetPool.getApiUrl();
        MetaPushData metaPushData = metaPushDataService.creatPashData(sysId, "", "", apiUrl, JSON.toJSONString(json), type);
        metaPushDataService.save(metaPushData);
        if (StringUtils.isNotEmpty(apiUrl)) {
            // 发送消息（确保在事务提交后发送）
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    String routingKey = "";
                    if ("H5_LISTENING_DATA".equals(type)) {
                        routingKey = com.meta.common.constant.Constants.routing_key_h5_listening;
                    }
                    mQutils.send(metaPushData, routingKey, 3);
                }
            });
        }
    }

    private void alert(String to, String from, BepTransactions transaction, String userWalletByAddress, CoinType coinType, String sysId) {
        //超过多少钱入账，需要通知管理员检查
        // 获取eth余额
        String userAddress;
        String otherAddress;
        String eth = tradeService.getBalance(userWalletByAddress);
        String tokenBalanceBigInt = tradeService.getTokenBalance(userWalletByAddress, coinType.getCode());
        String type;
        switch (transaction.getType()) {
            case "receive":
                type = "转入";
                userAddress = to;
                otherAddress = from;
                break;
            case "send":
                type = "转出";
                userAddress = from;
                otherAddress = to;
                break;
            case "collect":
                type = "归集转出";
                userAddress = from;
                otherAddress = to;
                break;
            default:
                type = "异常类型";
                userAddress = to;
                otherAddress = from;
                break;
        }
        rabbitProducer.sendMonitor(
                new MqMonitorDto(
                        transaction.getTimestamp(),
                        "BNB",
                        userAddress,
                        otherAddress,
                        type,
                        transaction.getAmount(),
                        coinType.getCode(),
                        new BigDecimal(eth),
                        "ETH",
                        new BigDecimal(tokenBalanceBigInt),
                        coinType.getCode(),
                        sysId)
        );
    }
}
