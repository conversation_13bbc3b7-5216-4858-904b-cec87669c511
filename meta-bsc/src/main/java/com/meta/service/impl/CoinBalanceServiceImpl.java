package com.meta.service.impl;


import com.meta.dao.CoinBalanceDataDao;
import com.meta.entity.CoinBalance;
import com.meta.entity.CoinBalancePK;
import com.meta.service.CoinBalanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Transactional(readOnly = true)
@Service
public class CoinBalanceServiceImpl implements CoinBalanceService {
    @Autowired
    private CoinBalanceDataDao coinBalanceDataDao;


    @Override
    public CoinBalance findById(CoinBalancePK p) {
        Optional<CoinBalance> op = coinBalanceDataDao.findById(p);
        if (op.isPresent()) {
            return op.get();
        }
        return null;
    }

    @Override
    @Transactional
    public void save(CoinBalance c) {
        coinBalanceDataDao.save(c);
    }





//    @Override
//    public void updateCoinBalanceData(CoinBalance cb) {
//        coinBalanceDao.updateCoinBalanceData(cb.getUserId(), cb.getCoinCode(), cb.getCoinBalance(), cb.getFreezeBalance(), cb.getUncltCoinBalance());
//    }

    @Override
    public void editCoinBalanceByEBP20(CoinBalance cb) {
        coinBalanceDataDao.editCoinBalanceByEBP20(cb.getUserId(), cb.getCoinCode(), cb.getCoinBalance(), cb.getFreezeBalance(), cb.getUncltBep20CoinBalance());
    }

    @Override
    public void editCoinBalanceBySol(CoinBalance cb) {
        coinBalanceDataDao.editCoinBalanceBySol(cb.getUserId(), cb.getCoinCode(), cb.getCoinBalance(), cb.getFreezeBalance(), cb.getUncltSolCoinBalance());
    }


    @Override
    public void editCoinBalanceByErc20Base(CoinBalance cb) {
        coinBalanceDataDao.editCoinBalanceByErc20Base(cb.getUserId(), cb.getCoinCode(), cb.getCoinBalance(), cb.getFreezeBalance(), cb.getUncltErc20BaseCoinBalance());
    }

    @Override
    public void editCoinBalanceByErc20Arb(CoinBalance cb) {
        coinBalanceDataDao.editCoinBalanceByErc20Arb(cb.getUserId(), cb.getCoinCode(), cb.getCoinBalance(), cb.getFreezeBalance(), cb.getUncltErc20ArbCoinBalance());
    }



}
