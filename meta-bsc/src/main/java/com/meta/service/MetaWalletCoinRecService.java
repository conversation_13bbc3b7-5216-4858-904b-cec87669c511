package com.meta.service;


import com.meta.entity.MetaWalletCoinRec;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 多链用户钱包代币余额记录服务接口
 */
public interface MetaWalletCoinRecService {

    /**
     * 保存余额记录
     *
     * @param metaWalletCoinRec 余额记录对象
     * @return 保存后的余额记录
     */
    MetaWalletCoinRec save(MetaWalletCoinRec metaWalletCoinRec);

    /**
     * 批量保存余额记录
     *
     * @param metaWalletCoinRecList 余额记录列表
     * @return 保存后的余额记录列表
     */
    List<MetaWalletCoinRec> saveAll(List<MetaWalletCoinRec> metaWalletCoinRecList);

    /**
     * 根据ID查询余额记录
     *
     * @param id 记录ID
     * @return 余额记录
     */
    MetaWalletCoinRec findById(Long id);

    /**
     * 查询指定时间范围内的余额记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 余额记录列表
     */
    List<MetaWalletCoinRec> findByTimeRange(Date startTime, Date endTime);

    /**
     * 根据代币符号查询余额记录
     *
     * @param tokenSymbol 代币符号
     * @return 余额记录列表
     */
    List<MetaWalletCoinRec> findByTokenSymbol(String tokenSymbol);

    /**
     * 根据链类型查询余额记录
     *
     * @param chainType 链类型
     * @return 余额记录列表
     */
    List<MetaWalletCoinRec> findByChainType(String chainType);

    /**
     * 根据链类型和代币符号查询余额记录
     *
     * @param chainType 链类型
     * @param tokenSymbol 代币符号
     * @return 余额记录列表
     */
    List<MetaWalletCoinRec> findByChainTypeAndTokenSymbol(String chainType, String tokenSymbol);

    /**
     * 删除余额记录
     *
     * @param id 记录ID
     */
    void deleteById(Long id);

    /**
     * 获取24小时内有记录的钱包地址集合
     *
     * @return 钱包地址集合
     */
    Set<String> getRecentWalletAddresses();

    /**
     * 获取指定链类型24小时内有记录的钱包地址集合
     *
     * @param chainType 链类型
     * @return 钱包地址集合
     */
    Set<String> getRecentWalletAddressesByChainType(String chainType);
}
