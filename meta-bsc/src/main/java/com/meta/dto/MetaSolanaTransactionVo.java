package com.meta.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * solDto
 *
 * <AUTHOR>
 * @date 2025/5/8 16:24
 **/
@Data
public class MetaSolanaTransactionVo {

    /**
     * ID标识
     */
    private Long id;

    /**
     * 交易hash
     */
    private String txid;

    /**
     * 交易区块高度
     */
    private Long blockheight;

    /**
     * 钱包转入地址
     */
    private String address;

    /**
     * 钱包转出地址
     */
    private String fromaddress;

    /**
     * 合约
     */
    private String contract;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 交易手续费
     */
    private BigDecimal fee;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 交易类型(collect-归集, send-转出, receive-转入, receive_other-归集钱包接收转账)
     */
    private String type;

    /**
     * 后端服务是否已处理(1-已处理,0-未处理,2-不处理)
     */
    private Integer issync;

    /**
     * 时间
     */
    private Date time;

    /**
     * 币种
     */
    private String coinType;

    /**
     * 用户id
     */
    private Long cstId;



}
