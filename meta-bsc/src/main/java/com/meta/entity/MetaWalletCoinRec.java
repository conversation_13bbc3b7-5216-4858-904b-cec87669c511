package com.meta.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import javax.persistence.*;
import java.math.BigInteger;
import java.util.Date;

/**
 * 多链用户钱包代币余额记录表
 */
@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "meta_wallet_coin_rec")
public class MetaWalletCoinRec {

    @Id
    @GenericGenerator(name = "snowflake", strategy = "com.meta.system.config.HibernateSnowflakeIdGenerator",
        parameters = {
            @Parameter(name = "worker_id", value = "1"),
            @Parameter(name = "datacenter_id", value = "1")
        })
    @GeneratedValue(generator = "snowflake")
    @Column(name = "id")
    private Long id; // ID标识(雪花算法)

    @Column(name = "wallet_address", length = 34, nullable = false)
    private String walletAddress; // 钱包地址

    @Column(name = "token_address", length = 34, nullable = false)
    private String tokenAddress; // 代币合约地址 (原生代币固定值，如TRX为"_TRX_NATIVE_")

    @Column(name = "token_symbol", length = 20)
    private String tokenSymbol; // 代币符号 (例如: TRX, USDT, USDC)

    @Column(name = "raw_balance", nullable = false)
    private BigInteger rawBalance; // 原始余额 (链上最小单位，使用BigInteger存储)

    @Column(name = "decimals", nullable = false)
    private Integer decimals; // 代币小数位数

    @Column(name = "chain_type", length = 20)
    private String chainType; // 链类型 (例如: TRON, ETH, BSC)

    // 审计字段
    @Column(name = "create_dept")
    private Long createDept; // 创建部门

    @Column(name = "create_by")
    private Long createBy; // 创建者

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time")
    private Date createTime; // 创建时间 / 余额快照记录时间

    @Column(name = "update_by")
    private Long updateBy; // 更新者

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime; // 更新时间

    @Column(name = "remark", length = 500)
    private String remark; // 备注
}
