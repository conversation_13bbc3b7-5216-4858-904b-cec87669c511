package com.meta.controller;

import com.meta.common.core.domain.AjaxResult;
import com.meta.framework.utils.SseUtils;
import com.meta.system.uitls.RedisConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/11/12/18:58
 */
@RestController
@Slf4j
@CrossOrigin
@RequestMapping("/sse")
public class SSEController {

    @Autowired
    private SseUtils sseUtils;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @GetMapping(value = "/createSseConnect", produces = "text/event-stream;charset=UTF-8")
    public SseEmitter createSseConnect(@RequestParam(name = "clientId", required = false) String clientId) {
        String clientValue = RedisConstants.sseClient + clientId + "_card_3ds_client";
        redisTemplate.opsForValue().set(clientId, clientId, 5, TimeUnit.MINUTES);
        return sseUtils.connect(clientId);
    }


    @PostMapping("/sendMessage")
    public void sendMessage(@RequestParam("clientId") String clientId, @RequestParam("message") String message) {
        sseUtils.sendMessage(clientId, "3ds", message);
    }

    @GetMapping(value = "/listSseConnect")
    public AjaxResult listSseConnect() {
        Map<String, SseEmitter> sseEmitterMap = sseUtils.listSseConnect();
        System.out.println("listSseConnect");
        return AjaxResult.success(sseEmitterMap);
    }


    /**
     * 关闭SSE连接
     *
     * @param clientId 客户端ID
     **/
    @GetMapping("/closeSseConnect")
    public AjaxResult closeSseConnect(String clientId) {
        sseUtils.deleteUser(clientId);
        return AjaxResult.success();
    }

}

