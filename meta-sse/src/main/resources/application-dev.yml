# 项目相关配置
meta:
  # 名称
  name: metabank
  # 版本
  version: 3.2.1
  # 版权年份
  copyrightYear: 2022
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/meta/uploadPath，Linux配置 /home/<USER>/uploadPath）
  #  profile: ./Users/<USER>/meta/file
  profile: D:/home/<USER>/file
  # 获取ip地址开关
  addressEnabled: false
  # 登录风控规则
  loginRuleEnabled: false
  # 交易风控规则
  tradeRuleEnabled: true
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
#  resetRedisIp: **************

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8894
  servlet:
    # 应用的访问路径
    context-path: /meta
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

# 日志配置
logging:
  level:
    com.meta: debug
    org.springframework: warn
  file:
    path: ./Users/<USER>/meta/logs
#    path: G:/home/<USER>/logs

# Spring配置
spring:
  config:
    additional-location: classpath:/config/
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  jpa:
    database: mysql
    generate-ddl: false
    hibernate:
      ddlAuto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
        jdbc:
          #为spring data jpa saveAll方法提供批量插入操作 此处可以随时更改大小 建议500
          batch_size: 500
          batch_versioned_data: true
        order_inserts: true
        order_updates: true
    show-sql: true
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        #        url: ***************************************************************************************************************************
        #        username: metabank
        #        password: 555666
        #        url: ************************************************************************************************************************************************
        #        username: metabank
        #        password: Z;J1!q2}AGU:5
        #        url: **************************************************************************************************************************************************
        #        username: metabank
        #        password: 555666
        url: *********************************************************************************************************************************************
        username: root
        password: root
      #        url: *****************************************************************************************************************************************************************************************
      #        username: metabank
      #        password: TC]n}(E$43Tr2
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size:  10MB
      # 设置总上传的文件大小
      max-request-size:  20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址 **************
    host: localhost
    # 端口
    port: 6379
    # 密码
    password: Meta@Bank
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
    database: 10
  boot:
    filters:
      # 这里假设你的Filter类的包名为com.meta.system.filter
      # Filter的名称可以根据你的需要进行修改
      filter-name:
        order: -1
        class: com.meta.system.filter.IPBlacklistFilter

#vcc 配置
vcc:
  url: https://test-open.whalet.com/api
  version: 1.0
  partnerId: 202305061716298892600001
  #test服务器私钥
  #  privateStr: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC9E7fupXdmwZx5fGQZgj+FczgCEI9u6LyFxY/RFrkYXHojY7qPgyjaznqCrN3xfGolWO7iq9EbpoRetR5/ovLG8eWlMfWVkYfKZeQ6pfv1eVl+9p4O0rlBpVGVNzbDtLTuwX7Xw0RxFFJyZVR2EQzo0cb8XyZG3Z7XxDPMnOzqGIPVcHp0nL8vE8IPFyrg1z1AYoWUy90B7Kx05h/nNX7jtjNrVnpxHNpmK8kx5DQXrEjZZPVrunXzHRXg2vGkpb6DkRPHUCfuMpFTTL5Oge8O9sjM71tJeGPSmb++k8KL/6j97S4/a69PUH3ZldIMvgsKuzMYCY0O7Pwwrf5B6iTrAgMBAAECggEALlM7eHwQAhwjs1w3xkw0NgUhztex3NGnBvt9nhP8K6zUvAD+P5U6GEoImCW0hysdcqMUfHLuW+Dzg6TKoSkSZI313wCblBbA92T5gykRz3X46HOSDD2y6BOSJoYNo+uNfQXphwGvrij1flO3WuoYiJ6FK2ZAoZJBDcpjiplULpKBOih/MhvQ6CyN3fIyjvaKWJnFNnb5k+lG9i8ff95tuJVBey1OPFNuJ/TvDysAJkzUV9pl8MItlVdYjLLbWrBnVLojpdOI2/OcKjp/TJk+Y+3pw4R1kAH29LvH5nSO8kaaPu186BbSQOLl9/Dw43FwtPudWoePbucZCyQI/TTmqQKBgQDhyQ+e6gnCIvRSwgqBFUduZ5Pk/PWRA7NTKxTF8gRA9vsPju5vll3H3NXJdEwHA7MNlLXgWDsur/dwa9LUkdWPkrzPZiPZK8p5LcudVmpLgvoDzg9paSflA9tKVFyS2BRWoT91BZ+0k8lYkiuZ/iOfC4IF7UZocIaEpEBvQKOl/QKBgQDWYRrcfP2JYSwkDplSdKty1iB3fN/ISb/Q1s1azYrx5DOnWoVCfHauveY0eqXh1lZksZyr1SQ0kY6N6IihgUWGZy8bAezsKKy+b8oHVLa0UPoMUZGRF/KjZu0RBEyfoHGec1J7tSttCLTff4U+06fsYX37X8ZZJOuoSHoOVuh3BwKBgQDH9B6QVqWTtw72p39T97tNzA7O0TLMXSGXeuSntIAN5GxMyADi86BT2n++K+8UmzMbyOIVLy4iV5XjiqmotQoTXxk09ziyIDTsgiD7UsdJ3lF5wygk6wp4p6Sxu+pL5W6FlcGz6eoYqnS6qqBQfR+gvzlD9HDRFy4aE7g4jl8fJQKBgHxaAVnKSsroidE0gq04rcbD/DszPR+R3+kE4EN+nM0pIOk/cbMaPUGpN6JTTmuMh7qK6CUoUoMHt5gLPU3pjWmj6sgQutxVz7X7ZEYSVpSLsC97FSQDryQnbPYE92lUiPNvU3Ycpd7uQPSvpJvH7E0KVCh+6rAE4YlQ2TP+J4P7AoGBANd0ZmRORBn7tXJZpRwJPrliKwyFj4dGIyWCgaUiVYwWSYg3TkHm4LU+zMLcUfF1Cu7qBPhFM/zdhFEnaffqAv4WH9P/BKVba9BhL6z6nhWEI7be2waFUSDNhLkJhxmot0NUpdlsSau+x60HGuaOY1weEZf6bLarn4TI1Le+vxtI
  #我本地公钥
  #  publicStr: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt7Bct5InySYB0lyYeMkwJtCnF/q0eHNQ2S7UiquPb4VA8SHvu52RQO37RFViPpqmWp7qtke3QSN3VizZlT/oBKifWW4p1mDTK7uRVSNWOnBkEdWijpGXcD1m6dvCAKoiyPSM+C696viETdTrfx7RanLcfWoKbe/TQliIMzUkdVG2o/gXboI/fw83cG9vAhh7bjB3ONuAd/Dr1LYoJNkhroBWj40yDC49Sw8xVlZGBO8dtDrnm+1EEyvju1iIIQr1me8ucZs5uPFQheeGhlgjeV12s1eDIgp3kdAQN/mu9sGADeYkiDgTRMqCnAl2GFUOdaJsacjWwflxCSzOvwPeSQIDAQAB
  privateStr: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC3sFy3kifJJgHSXJh4yTAm0KcX+rR4c1DZLtSKq49vhUDxIe+7nZFA7ftEVWI+mqZanuq2R7dBI3dWLNmVP+gEqJ9ZbinWYNMru5FVI1Y6cGQR1aKOkZdwPWbp28IAqiLI9Iz4Lr3q+IRN1Ot/HtFqctx9agpt79NCWIgzNSR1Ubaj+Bdugj9/Dzdwb28CGHtuMHc424B38OvUtigk2SGugFaPjTIMLj1LDzFWVkYE7x20Oueb7UQTK+O7WIghCvWZ7y5xmzm48VCF54aGWCN5XXazV4MiCneR0BA3+a72wYAN5iSIOBNEyoKcCXYYVQ51omxpyNbB+XEJLM6/A95JAgMBAAECggEAB9ehvbyhsjbLMR3+7Hk6cw3JdH6hodragss7C8iTUSGRSjo2r/3kK699YMd8cEvN+mR6hNm++yr2d1lm2LZnw3sngnvvkVWE7oMNi2L81XXi79f9HrUckBAvCqD1QW4CGi5GrnngZd3CWEhU3ZKFQlQxEbGrTFJG5Lg+6GDGc7O83Ybt3DpRdBu3xyKqstio3Bnyi726xgoFvyapVxMA80NqHygp3I0xQM7+xcqcpToA76BnzEwxrcJA46fVz6ARpeoZJZm2LEpLjwtUUPP4nPGWaFbpvJWBVrYtrIUsbdvU/k872MJrGU2HKM2Y3pKE9ftf3r9BsUmjhg4Qqz3EAQKBgQDivK++jRWQg76C5tjomhTtdHP3UyY5NNepfxWU/fXGI2pXGxI8JFvMXTmA2mcOw3smPsXJTzRVh5rsLFZcAI1cNkgr7St0Cq83sbes2qgWLUU/9hwD/44zcK2AbGCUA/l1XjshHdzBUjYN/Te90HSE6POhdrRus9Mk7H6L2wyWiQKBgQDPZWFKIrwhIImf9vlaiKT6DT3+61sErPnOB6MXee7iMqtfSdFrry6vU+tR/YvQP5BQm5RHaFqx+wMXlv5g4HSzAlN4Y5pt2KslVRyx77uesFqDyKPK8AzVZuXGQwL35zqyeR3qWMQAiyJpbYa5i9JOlOL1NWa19VMOG2/6rHsZwQKBgAUSZ3QBv+u9prNS6qM5XyJ4qmdr6rz426Ik/5yXmbnW7PgJ2PyTa6JEq5agTBHeeZC/crkwFmGbaPHDhCMGuCLJ7A9ffMtZudWrGgq50Wy9koD9xl6ohsvLx3XJ9tcYx6nCc9wnyNpiNmdVtLuAQDsA4wJHn32idCStTZ9fRQbRAoGBAImA4scfTnH8O6LmQR6oOBFdVDw+WMM52AmkgJohCqPICl0L+SAXLrGpxvw+SUNR0WHQNLg/VNpp3Pv37UHXrye8JBFOoGWx4I7I5lSG6HLm46w6C3aSP0ABj9gqN76a/Qy1RoNIRPNDTBZwOoGsVUwqZGtE84syUtWolU6yqo9BAoGAAedNwM57PufFJHSA11rFM1nIOyTeG83bQInAjacLsdQDe12kFZeC0jTZKC/8R6S+BpsDGO2NLUhOJ5QstID7qX+ba+bqkcUzVTzrJJM2/U/wmeSIRKBvb//XGcMKy+A+pnlwV6ODQHzEE5h0BSRW1hgwWICYPGBBGIKGEsScz7c=
  publicStr: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvw2IzvWHwCAWbVYbh/lOv1fUGR5AtHvwLQLZuyrY0GiNSovSuGbhsWQ/ImTqqn5UtSPHt+2ANGg6wibHOIYG87uqp2q/HLLgs5IYh39tHcyX+saRBAADpFShgeM+VZEfOKa97HvNfJskqIj0JsCjZsNRardxbvJtQpn6hMuo0l7w0cY9qvzjKn0kcslKFYafz9h24wQTyjJrd+G/VjPt/EDn3zbY8OzGv6nI8j0uQSRN5SkIzV09Yk2Ymzivb/gHNOr6qU9r2pGsjn+ObmL1avVXA4niL6yoqe5YQ0QOMJ5hIey3WBF9PRgBljz64hoyPB8r1mvz6uTmnCQlcOntOwIDAQAB
wallet:
  userAddress:
    url: http://************:8290/user/address
    key: ja88@uP660088#Meta
  # TRC归集钱包地址
  mainaddress: "TRX7g5FMgLHi1htFbBh8pmYvMjZNVJbLbE"

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 300000
  # 是否允许账户多终端登录
  soloLogin: false


# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

arcFace:
  # 应用ID
  appId: BuQo8C4eBakpMMhTEtDJp1xEKcDBrDGca2PtDW1oK9Tw
  # 应用密钥
  sdkKeyForWindows: A8CdeNmhc1LjfYm6T1X8aJTMA5N4i9T2rBNzGq5xpLoj
  sdkKeyForLinux: A8CdeNmhc1LjfYm6T1X8aJTM1xnwytPEH8A6fLhXP2J5
  isAbsolutePath: false
  ## SDK路径
  sdkPath: /sdk/

mail:
  username: <EMAIL>
  password: Ve2b441a9
  authorizationCode:
  serverHost: smtp.kazepay.io
  serverPort: 465
  reconnectSecond: 600
#mail:
#  username: <EMAIL>
#  password: TGGLTiJnN2KrrcZ
#  authorizationCode: FFTPBUWAVPNIMZDC
#  serverHost: smtp.163.com
#  serverPort: 465
#  reconnectSecond: 600


#moonbank 配置
moonbank:
  gateway: https://test.asinx.io/api-web
  appId: app_36142
  appSecret: a635dd5c87f7bf73357929203321b1e1
  notifyTimeout: 20000
  notifyConnectTimeout: 20000
  useProxy: false
  proxyAddress:
  proxyPort:

#kun 配置 + 配置文件
kun:
  environment: test
  ip: *************
