package com.meta.web.config;


import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/08/24/10:20
 */
@Slf4j
@Configuration
public class CommonConfig implements ApplicationContextAware {

    private static final String HEADER_RETRY_COUNT = "x-retry-count";
    private static final int MAX_RETRY_COUNT = 3;  // 最大重发次数

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        // 获取RabbitTemplate对象
        RabbitTemplate rabbitTemplate = applicationContext.getBean(RabbitTemplate.class);

        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                log.info("数据发送成功："+ correlationData);
            } else {
                log.error("数据发送失败："+ cause);
            }
        });

        // 配置ReturnCallback
        rabbitTemplate.setReturnCallback((message, replyCode, replyText, exchange, routingKey) -> {
            // 判断是否是延迟消息
            Integer receivedDelay = message.getMessageProperties().getReceivedDelay();
            if (receivedDelay != null && receivedDelay > 0) {
                // 是一个延迟消息，忽略这个错误提示
                return;
            }

            // 获取当前消息的重发次数
            Map<String, Object> headers = message.getMessageProperties().getHeaders();
            Integer retryCount = (Integer) headers.get(HEADER_RETRY_COUNT);
            if (retryCount == null) {
                retryCount = 0;
            }

            // 记录日志
            log.error("消息发送到队列失败，响应码：{}, 失败原因：{}, 交换机: {}, 路由key：{}, 重试次数: {}, 消息: {}",
                    replyCode, replyText, exchange, routingKey, retryCount, message.toString());
            // 判断是否超过最大重发次数
            if (retryCount < MAX_RETRY_COUNT) {
                // 重发消息，并增加重发次数
                retryCount++;
                message.getMessageProperties().setHeader(HEADER_RETRY_COUNT, retryCount);
                try {
                    log.info("等待一分钟后重试...");
                    Thread.sleep(60000);
                    rabbitTemplate.send(exchange, routingKey, message);
                    log.info("消息重发成功，交换机: {}, 路由key: {}, 重试次数: {}, 消息: {}", exchange, routingKey, retryCount, message.toString());
                } catch (Exception e) {
                    log.error("消息重发失败，交换机: {}, 路由key: {}, 消息: {}, 错误: {}",
                            exchange, routingKey, message.toString(), e.getMessage());
                }
            } else {
                log.error("消息重发达到最大次数，放弃重发。交换机: {}, 路由key: {}, 消息: {}",
                        exchange, routingKey, message.toString());
                // 可以在此处执行其他处理逻辑，例如发送告警、持久化到数据库等
            }
        });
        rabbitTemplate.setMandatory(true);
    }

}
