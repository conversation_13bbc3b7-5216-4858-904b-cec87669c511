package com.meta.web.config;

import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @date 2024/09/26/17:00
 */
@Component
@ConfigurationProperties(prefix = "spring")
public class MQConfig {

    private String host;
    private String port;
    private String userName;
    private String password;
    private String virtualHost;

    @Value("${spring.rabbitmq.host}")
    public  void setHost(String host) {
        this.host = host;
    }

    @Value("${spring.rabbitmq.port}")
    public  void setPort(String port) {
        this.port = port;
    }

    @Value("${spring.rabbitmq.username}")
    public  void setUserName(String userName) {
        this.userName = userName;
    }

    @Value("${spring.rabbitmq.password}")
    public  void setPassword(String password) {
        this.password = password;
    }

    @Value("${spring.rabbitmq.virtual-host}")
    public  void setVirtualHost(String virtualHost) {
        this.virtualHost = virtualHost;
    }
    @Bean
    public ConnectionFactory connectionFactory() {

        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();

        connectionFactory.setHost(host);
        connectionFactory.setPort(Integer.valueOf(port));
        connectionFactory.setUsername(userName);
        connectionFactory.setPassword(password);
        connectionFactory.setVirtualHost(virtualHost);

        try {
            // SSL配置
            connectionFactory.getRabbitConnectionFactory().useSslProtocol();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (KeyManagementException e) {
            e.printStackTrace();
        }
        connectionFactory.setPublisherReturns(true);
        return connectionFactory;
    }

}
