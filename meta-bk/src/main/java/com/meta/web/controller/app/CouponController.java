package com.meta.web.controller.app;

import com.meta.common.constant.HttpStatus;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.core.page.TableSupport;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.code.BusinessBizCode;
import com.meta.framework.web.service.PermissionService;
import com.meta.framework.web.service.SysPermissionService;
import com.meta.system.domain.app.MetaCouponGenCfg;
import com.meta.system.domain.app.MetaCouponInfo;
import com.meta.system.dto.CouponDto;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.MetaCouponGenCfgService;
import com.meta.system.service.MetaCouponInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/10/09/14:03
 */
@RestController
@RequestMapping("/coupon")
public class CouponController {

    @Autowired
    private MetaCouponInfoService metaCouponInfoService;

    @Autowired
    private MetaCouponGenCfgService metaCouponGenCfgService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private ISysConfigService sysConfigService;


    /**
     * 新增配置
     */
    @PostMapping("/insertCouponConfig")
    @PreAuthorize("@ss.hasPermi('coupon:config:insert')")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult insertCouponConfig(@RequestBody MetaCouponGenCfg metaCouponGenCfg) {
        //判断券码是否已经存在
        if (StringUtils.isNotEmpty(metaCouponGenCfg.getCouponNm())) {
            MetaCouponInfo byCouponNm = metaCouponInfoService.findByCouponNm(metaCouponGenCfg.getCouponNm());
            if (byCouponNm != null) {
                return AjaxResult.error(MessageUtils.message("coupon.error20"));
            }
        }

        //判断开卡的时候折扣率不等大于
        if (!"NEW".equals(metaCouponGenCfg.getCouponScenarios())) {
            if ("D".equals(metaCouponGenCfg.getCouponType())) {
                String couponRate = sysConfigService.selectConfigByKey("coupon_rate");
                BigDecimal rate = new BigDecimal(couponRate);

                if (metaCouponGenCfg.getDiscountRate() != null && metaCouponGenCfg.getDiscountRate().compareTo(rate) > 0) {
                    return AjaxResult.error(MessageUtils.message("coupon.error13", rate));
                }

            } else if ("M".equals(metaCouponGenCfg.getCouponType())) {
                if (metaCouponGenCfg.getDiscountAmt().compareTo(metaCouponGenCfg.getMinSpendAmt()) >= 0) {
                    return AjaxResult.error(MessageUtils.message("coupon.error14"));
                }
            }

        }
        if (metaCouponGenCfg.getDiscountRate() != null) {
            BigDecimal amount = metaCouponGenCfg.getDiscountRate().divide(new BigDecimal("100"));
            metaCouponGenCfg.setDiscountRate(amount);
        }
        metaCouponGenCfgService.insertCouponConfig(metaCouponGenCfg);
        return AjaxResult.success();
    }

    /**
     * 修改配置
     */
    @PutMapping("/updateCouponConfig")
    @PreAuthorize("@ss.hasPermi('coupon:config:update')")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateCouponConfig(@RequestBody MetaCouponGenCfg metaCouponGenCfg) {
        //判断开卡的时候折扣率不能小于
        if (!"NEW".equals(metaCouponGenCfg.getCouponScenarios())) {
            if ("D".equals(metaCouponGenCfg.getCouponType())) {
                String couponRate = sysConfigService.selectConfigByKey("coupon_rate");
                BigDecimal rate = new BigDecimal(couponRate);

                if (metaCouponGenCfg.getDiscountRate() != null && metaCouponGenCfg.getDiscountRate().compareTo(rate) > 0) {
                    return AjaxResult.error(MessageUtils.message("coupon.error13", rate));
                }

            } else if ("M".equals(metaCouponGenCfg.getCouponType())) {
                if (metaCouponGenCfg.getDiscountAmt().compareTo(metaCouponGenCfg.getMinSpendAmt()) >= 0) {
                    return AjaxResult.error(MessageUtils.message("coupon.error14"));
                }
            }

        }
        if (metaCouponGenCfg.getDiscountRate() != null) {
            BigDecimal amount = metaCouponGenCfg.getDiscountRate().divide(new BigDecimal("100"));
            metaCouponGenCfg.setDiscountRate(amount);
        }
        metaCouponGenCfgService.updateCouponConfig(metaCouponGenCfg);
        return AjaxResult.success();
    }

    /**
     * 删除配置
     */
    @PutMapping("/deleteCouponConfig")
    @PreAuthorize("@ss.hasPermi('coupon:config:delete')")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteCouponConfig(@RequestBody MetaCouponGenCfg metaCouponGenCfg) {
        metaCouponGenCfgService.deleteCouponConfig(metaCouponGenCfg.getId());
        return AjaxResult.success();
    }


    /**
     * 配置列表
     */
    @GetMapping("/couponConfigPage")
    @PreAuthorize("@ss.hasPermi('coupon:config:list')")
    public TableDataInfo couponConfigPage(CouponDto couponDto) {
        boolean admin = permissionService.hasRole("admin");
        if (!admin) {
            Long userId = SecurityUtils.getLoginUser().getUserId();
            couponDto.setAdmUserId(userId);
        }
        return getDataTable(metaCouponGenCfgService.getCouponConfigList(couponDto));
    }

    /**
     * 修改信息
     */
    @PutMapping("/updateCouponInfo")
    @PreAuthorize("@ss.hasPermi('coupon:info:update')")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateCouponInfo(@RequestBody MetaCouponInfo metaCouponInfo) {
        if (metaCouponInfo.getDiscountRate() != null) {
            BigDecimal amount = metaCouponInfo.getDiscountRate().divide(new BigDecimal("100"));
            metaCouponInfo.setDiscountRate(amount);
        }
        metaCouponInfoService.updateCouponInfo(metaCouponInfo);
        return AjaxResult.success();
    }

    /**
     * 删除信息
     */
    @PutMapping("/deleteCouponInfo")
    @PreAuthorize("@ss.hasPermi('coupon:info:delete')")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteCouponInfo(@RequestBody MetaCouponInfo metaCouponInfo) {
        metaCouponInfoService.deleteCouponInfoByAdm(metaCouponInfo.getId());
        return AjaxResult.success();
    }


    /**
     * 信息列表
     */
    @GetMapping("/couponInfoPage")
    @PreAuthorize("@ss.hasPermi('coupon:info:list')")
    @Transactional
    public TableDataInfo couponInfoPage(CouponDto couponDto) {
        boolean admin = permissionService.hasRole("admin");
        if (!admin) {
            Long userId = SecurityUtils.getLoginUser().getUserId();
            couponDto.setAdmUserId(userId);
        }
        sentCoupon(new CouponDto());
        return getDataTable(metaCouponInfoService.couponInfoPage(couponDto));
    }

    /**
     * 发放
     */
    @PostMapping("/grantCoupon")
    @PreAuthorize("@ss.hasPermi('coupon:info:grant')")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult grantCoupon(@RequestBody CouponDto couponDto) {

        return metaCouponInfoService.grantCoupon(couponDto.getId(), couponDto.getUserId());
    }


    /**
     * 响应请求分页数据
     */
    protected TableDataInfo getDataTable(Page<?> page) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(page.getContent());
        rspData.setTotal(page.getTotalElements());
        rspData.setSize(pageReq.getPageSize());
        rspData.setPages(page.getTotalPages());
        rspData.setCurrent(pageReq.getPageNo() + 1);
        return rspData;
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows) {
        return rows == BusinessBizCode.OPTION_SUCCESS.getCode() ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 根据实体卡id和用户id赠送金卡优惠券
     * 相当于免费送一张金虚拟卡
     */
    @PostMapping("/sendCoupon")
    @PreAuthorize("@ss.hasPermi('coupon:info:sentCoupon')")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult sentCoupon(@RequestBody CouponDto couponDto) {
        return metaCouponInfoService.sentCoupon(couponDto);
    }

}
