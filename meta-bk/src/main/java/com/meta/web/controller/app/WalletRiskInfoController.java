package com.meta.web.controller.app;

import com.meta.common.constant.HttpStatus;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.core.page.TableSupport;
import com.meta.common.utils.code.BusinessBizCode;
import com.meta.common.utils.request.IInsert;
import com.meta.common.utils.request.IUpdate;
import com.meta.system.domain.WalletRiskInfo;
import com.meta.system.dto.WalletRiskInfoDto;
import com.meta.web.service.WalletRiskInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/22/13:59
 */
@RestController
@RequestMapping("/walletRisk")
public class WalletRiskInfoController {

    @Autowired
    private WalletRiskInfoService walletRiskInfoService;

    /**
     * 获取列表
     */
    @PreAuthorize("@ss.hasPermi('walletRisk:list')")
    @GetMapping("/riskList")
    public TableDataInfo list(WalletRiskInfoDto walletRiskInfoDto) {
        Page<WalletRiskInfo> page = walletRiskInfoService.selectList(walletRiskInfoDto);
        return getDataTable(page);
    }

    /**
     * 通过归集地址获取风险地址列表
     */
    @GetMapping("/addressForlist")
    public AjaxResult addressForlist(String  address,String coinCode,String coinNet) {
        List<WalletRiskInfo> list= walletRiskInfoService.addressForlist(address,coinCode,coinNet);
        return AjaxResult.success(list);
    }

    /**
     * 新增
     */
    @PostMapping("/create")
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("@ss.hasPermi('walletRisk:create')")
    public AjaxResult create(@RequestBody @Validated(IInsert.class) WalletRiskInfo walletRiskInfo) {

        // 检查是否重复
        boolean flag = walletRiskInfoService.existData(walletRiskInfo.getWalletAddress(), walletRiskInfo.getCoin(), null);
        if (flag) {
            return AjaxResult.error("钱包地址：" + walletRiskInfo.getWalletAddress() + "，币种：" + walletRiskInfo.getCoin() + " 不可重复。");
        }
        walletRiskInfo.setCreateTm(new Date());
        return AjaxResult.success(walletRiskInfoService.save(walletRiskInfo));
    }

    /**
     * 修改
     */
    @PutMapping("/update")
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("@ss.hasPermi('walletRisk:update')")
    public AjaxResult update(@RequestBody @Validated(IUpdate.class) WalletRiskInfo walletRiskInfo) {
        // 检查symbol 是否重复
        boolean flag = walletRiskInfoService.existData(walletRiskInfo.getWalletAddress(), walletRiskInfo.getCoin(), walletRiskInfo.getId());
        if (flag) {
            return AjaxResult.error("钱包地址：" + walletRiskInfo.getWalletAddress() + "，币种：" + walletRiskInfo.getCoin() + " 不可重复。");
        }
        return AjaxResult.success(walletRiskInfoService.update(walletRiskInfo));
    }

    /**
     * 删除
     */

    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("@ss.hasPermi('walletRisk:delete')")
    @DeleteMapping("/delete/{id}")
    public AjaxResult delete(@PathVariable Integer id) {
        int res = walletRiskInfoService.delete(id);

        return toAjax(res);
    }

    /**
     * 响应请求分页数据
     */
    protected TableDataInfo getDataTable(Page<?> page) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(page.getContent());
        rspData.setTotal(page.getTotalElements());
        rspData.setSize(pageReq.getPageSize());
        rspData.setPages(page.getTotalPages());
        rspData.setCurrent(pageReq.getPageNo() + 1);
        return rspData;
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows) {
        return rows == BusinessBizCode.OPTION_SUCCESS.getCode() ? AjaxResult.success() : AjaxResult.error();
    }
}
