package com.meta.web.controller.card;

import com.meta.common.constant.Constants;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.core.page.TableSupport;
import com.meta.common.utils.MyAesUtil;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.poi.ExcelUtil;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.system.domain.app.MetaPhysicalCard;
import com.meta.system.service.MetaPhysicalCardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/03/15/9:49
 */


@Slf4j
@RestController
@RequestMapping("/physicalCard")
public class PhysicalCardController {

    @Autowired
    private MetaPhysicalCardService metaPhysicalCardService;

    @PreAuthorize("@ss.hasPermi('physical:card:save')")
    @PostMapping("/save")
    public AjaxResult save(@RequestBody MetaPhysicalCard metaPhysicalCard) {
        String cardNo = metaPhysicalCard.getCardNo();
        if (StringUtils.isNotEmpty(cardNo)) {
            MetaPhysicalCard byCardNo = metaPhysicalCardService.findByCardNo(cardNo);
            if (byCardNo != null) {
                return AjaxResult.error("卡片已经存在");
            }
        }
        metaPhysicalCard.setCreateTime(new Date());
        metaPhysicalCard.setUpdateTime(new Date());
        MetaPhysicalCard savedCard = metaPhysicalCardService.save(metaPhysicalCard);
        return AjaxResult.success();
    }

    @GetMapping("/getById")
    public AjaxResult findById(@RequestParam("id") Long id) {
        MetaPhysicalCard c = metaPhysicalCardService.findById(id);
        if (c != null) {
            dealNo(c);
        }

        return AjaxResult.success(c);
    }

    public String dealNo(MetaPhysicalCard c) {
        String cardNo = AESUtils.aesDecrypt(Constants.card_key, c.getCardNo());

        if (StringUtils.isNotEmpty(cardNo) && cardNo.length() > 8) {
            cardNo.substring(0, 4);

            String s = "";
            for (int i = 0; i < cardNo.length() - 8; i++) {
                s += "*";
            }
            cardNo = cardNo.substring(0, 4) + s + cardNo.substring(cardNo.length() - 4, cardNo.length());
            cardNo = MyAesUtil.encode((MyAesUtil.SALT + cardNo).getBytes());
            c.setCardNo(cardNo);
        }
        return cardNo;
    }

    @PreAuthorize("@ss.hasPermi('physical:card:update')")
    @PostMapping("updateData")
    public AjaxResult update(@RequestBody MetaPhysicalCard metaPhysicalCard) {
        MetaPhysicalCard card = metaPhysicalCardService.findById(metaPhysicalCard.getId());
        metaPhysicalCard.setUpdateTime(new Date());
        metaPhysicalCard.setCardNo(AESUtils.aesDecrypt(Constants.card_key, card.getCardNo()));
        metaPhysicalCardService.update(metaPhysicalCard);
        return AjaxResult.success();
    }

    @PreAuthorize("@ss.hasPermi('physical:card:delete')")
    @DeleteMapping("/{id}")
    public AjaxResult deleteById(@PathVariable Long id) {
        metaPhysicalCardService.deleteById(id);
        return AjaxResult.success();
    }

    @GetMapping("/getList")
    public TableDataInfo searchPhysicalCards(
            @RequestParam(value = "sysId", required = false) String sysId,
            @RequestParam(value = "cardNo", required = false) String cardNo,
            @RequestParam(value = "lastNo", required = false) String lastNo,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        Pageable pageable = PageRequest.of(pageNum-1, pageSize);
        Page<MetaPhysicalCard> cardPage = metaPhysicalCardService.searchPhysicalCards(sysId, cardNo, lastNo, pageable);
        for (MetaPhysicalCard card : cardPage.getContent()) {
            dealNo(card);
        }
        return getDataTable(cardPage);
    }

    /**
     * 实体卡导入入库
     *
     * @param file
     * @return
     * @throws Exception
     */
    @Transactional
    @PreAuthorize("@ss.hasPermi('physical:card:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<MetaPhysicalCard> util = new ExcelUtil<>(MetaPhysicalCard.class);
        List<MetaPhysicalCard> list = util.importExcel(file.getInputStream());
        if (list.size() < 1) {
            return AjaxResult.error();
        }
        metaPhysicalCardService.importData(list);
        return AjaxResult.success();
    }


    /**
     * 响应请求分页数据
     */
    protected TableDataInfo getDataTable(Page<?> page) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(com.meta.common.constant.HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(page.getContent());
        rspData.setTotal(page.getTotalElements());
        rspData.setSize(pageReq.getPageSize());
        rspData.setPages(page.getTotalPages());
        rspData.setCurrent(pageReq.getPageNo() + 1);
        return rspData;
    }

}
