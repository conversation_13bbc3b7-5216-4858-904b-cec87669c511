package com.meta.web.controller.app;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meta.common.constant.Constants;
import com.meta.common.core.controller.BaseController;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.enums.VccTxnType;
import com.meta.common.enums.app.PartnerTxnType;
import com.meta.common.enums.app.TxnStatus;
import com.meta.common.enums.vcc.ApiCode;
import com.meta.common.enums.vcc.CardStatus;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.MyAesUtil;
import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.uuid.UniqueIDGenerator;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.common.valid.CardId;
import com.meta.framework.web.controller.card.BaseCardCommon;
import com.meta.system.config.KunConfig;
import com.meta.system.config.MoonbankConfig;
import com.meta.system.constant.ChannelType;
import com.meta.system.domain.CreditCard;
import com.meta.system.domain.Wallet;
import com.meta.system.domain.app.CardConfig;
import com.meta.system.domain.app.ExchangeRate;
import com.meta.system.domain.app.VccReq;
import com.meta.system.domain.partner.MetaPartnerAssetPool;
import com.meta.system.domain.partner.MetaPartnerUser;
import com.meta.system.domain.vcc.VccCreateCardResp;
import com.meta.system.dto.CardUpdateLogDto;
import com.meta.system.email.SendEmail;
import com.meta.system.fornax.service.FornaxService;
import com.meta.system.kun.constants.KunMethod;
import com.meta.system.kun.constants.KunOrderStatus;
import com.meta.system.kun.constants.KunPriceSide;
import com.meta.system.kun.service.KunService;
import com.meta.system.kun.utils.KunUtils;
import com.meta.system.moonbank.models.ApiResponse;
import com.meta.system.moonbank.util.MoonbankEncryptUtil;
import com.meta.system.moonbank.util.MoonbankUtil;
import com.meta.system.service.*;
import com.meta.system.vcc.YJCardUtils;
import com.meta.system.vo.CardLogisticsVo;
import com.meta.system.vo.CreditCardVo;
import com.meta.system.vo.UserCommissionVo;
import com.meta.web.service.CardConfigService;
import com.meta.web.service.CreditDealService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/card")
public class CardController extends BaseController {

    @Autowired
    private CreditCardService creditCardService;

    @Autowired
    private YJCardUtils yjCardUtils;
    @Autowired
    private BaseCardCommon baseCardCommon;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private CardConfigService cardConfigService;

    @Autowired
    private MoonbankUtil moonbankUtil;

    @Autowired
    private WalletService walletService;
    @Autowired
    private MetaCardLogisticsService metaCardLogisticsService;

    @Autowired
    private MetaPartnerUserService metaPartnerUserService;

    @Autowired
    private MetaPartnerAssetPoolService metaPartnerAssetPoolService;

    @Autowired
    private MetaPartnerTxnDtlService metaPartnerTxnDtlService;

    @Autowired
    private KunService kunService;

    @Autowired
    private ExchangeRateService exchangeRateService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private KunUtils kunUtils;

    @Autowired
    private ISysDictDataService sysDictDataService;

    @Autowired
    private CreditDealService creditDealService;

    @Autowired
    private SendEmail sendEmail;

    @Autowired
    private FornaxService fornaxService;
    @Autowired
    private UserCommissionService userCommissionService;

    /**
     * 锁定
     */
    @Transactional(rollbackFor = Exception.class)
    @PutMapping("/lock")
    @PreAuthorize("@ss.hasPermi('user:card:lock')")
    public AjaxResult lock(@Validated(CardId.class) @RequestBody VccReq req) {
        CreditCard c = creditCardService.findByCardId(req.getCardId());
        if (c.getSource() != null && (c.getSource().equals("moonbank")) || c.getSource().equals("moonbank_Triplink")) {
            String uid = "";
            if (StringUtils.isEmpty(c.getSysId())) {
                Wallet w = walletService.findByUserId(c.getUserId());
                uid = w.getMoonbankUid();
            } else {
                MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(c.getCardHolder(), c.getSysId());
                uid = partnerUser.getUid();
            }
            return creditCardService.updCardStatus(c, uid, false);

        } else if ("kun".equals(c.getSource())) {
            if ("87".equals(c.getCardType())) {
                return AjaxResult.error(MessageUtils.message("card.lock.cards"));
            } else {
                kunService.cardfreeze(c, false, new JSONObject());
                kunService.getCardInfo(new JSONObject(), c, null);
                return AjaxResult.success();
            }
        } else if (ChannelType.FORNAX.getValue().equals(c.getSource())) {
            fornaxService.updateCard(c,"INACTIVE");
            creditCardService.getCardInfo(c);
            return AjaxResult.success();
        } else {
            return AjaxResult.error("不是上游1的卡");
        }
//        AjaxResult ar =  baseCardCommon.isMyCard(c,false);
//        if(ar != null)
//            return ar;
//        //1.调用vcc接口
//        req.setRequestNo(UniqueIDGenerator.generateID());
//        VccCreateCardResp res = yjCardUtils.card(req, ApiCode.SuspendCard);
//        //2.返回结果
//        if (res != null && res.getCode().equals("SUCCESS")) {
//            creditCardService.updateStatus(c.getCardId(), CardStatus.SUSPENDING.getValue());
//            return AjaxResult.success(res.getCode());
//        }
//        return AjaxResult.error(res.getCode(), res.getDetail());
    }

    /**
     * 解锁
     */
    @PutMapping("/unlock")
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("@ss.hasPermi('user:card:unlock')")
    public AjaxResult unlock(@Validated(CardId.class) @RequestBody VccReq req) {
        CreditCard c = creditCardService.findByCardId(req.getCardId());
        if (c.getSource() != null && (c.getSource().equals("moonbank")) || c.getSource().equals("moonbank_Triplink")) {
            String uid = "";
            if (StringUtils.isEmpty(c.getSysId())) {
                Wallet w = walletService.findByUserId(c.getUserId());
                uid = w.getMoonbankUid();
            } else {
                MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(c.getCardHolder(), c.getSysId());
                uid = partnerUser.getUid();
            }


            return creditCardService.updCardStatus(c, uid, true);

        } else if ("kun".equals(c.getSource())) {
            if ("87".equals(c.getCardType())) {
                return AjaxResult.error(MessageUtils.message("card.notUnfreeze.cards"));
            } else {
                kunService.cardUnlock(c, new JSONObject());
                kunService.cardfreeze(c, true, new JSONObject());
                kunService.getCardInfo(new JSONObject(), c, null);
                return AjaxResult.success();
            }

        } else if (ChannelType.FORNAX.getValue().equals(c.getSource())) {
            fornaxService.updateCard(c,"ACTIVE");
            creditCardService.getCardInfo(c);
            return AjaxResult.success();
        } else {
            return AjaxResult.error("不是上游1的卡");
        }


//        //1.调用vcc接口
//        req.setRequestNo(UniqueIDGenerator.generateID());
//        VccCreateCardResp res = yjCardUtils.card(req, ApiCode.UnSuspendCard);
//        //2.返回结果
//        if (res != null && res.getCode().equals("SUCCESS")) {
//            creditCardService.updateStatus(c.getCardId(), CardStatus.UNSUSPENDING.getValue());
//            return AjaxResult.success(res.getCode());
//        }
//        return AjaxResult.error(res.getCode(), res.getDetail());
    }

    /**
     * 卡列表
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermi('user:card:list')")
    public TableDataInfo list(CreditCard card) {
        Page<CreditCardVo> page = creditCardService.page(card);
        Page<CreditCardVo> updatedPage = page
                .stream()
                .map(c -> {
                    if (StringUtils.isNotEmpty(c.getCardNo())) {
                        String cardNo = "";
                        if (!c.getCardNo().contains("*")) {
                            cardNo = AESUtils.aesDecrypt(Constants.card_key, c.getCardNo());
                            String xx = "";

                            if (cardNo.length() > 8) {
                                for (int i = 0; i < cardNo.length() - 8; i++) {
                                    xx = xx + "*";
                                }
                                cardNo = cardNo.substring(0, 4) + xx + cardNo.substring(cardNo.length() - 4, cardNo.length());
                            }
                        } else {
                            cardNo = c.getCardNo();
                        }

                        cardNo = MyAesUtil.encode((MyAesUtil.SALT + cardNo).getBytes());
                        c.setCardNo(cardNo);
                    }
                    if (StringUtils.isNotEmpty(c.getCvv2())) {
                        String cvv2 = AESUtils.aesDecrypt(Constants.card_key, c.getCvv2());
                        cvv2 = "***";
                        cvv2 = MyAesUtil.encode((MyAesUtil.SALT + cvv2).getBytes());
                        c.setCvv2(cvv2);
                    }
                    if (StringUtils.isNotEmpty(c.getExpirationTime())) {
                        c.setExpirationTime(c.getExpirationTime().substring(0, 2) + "**");
                    }

                    return c;
                })
                .collect(Collectors.collectingAndThen(Collectors.toList(), list -> new PageImpl<>(list, page.getPageable(), page.getTotalElements())));
        return getDataTable(updatedPage);
    }


    /**
     * b端卡列表
     */
    @GetMapping("/listb")
    @PreAuthorize("@ss.hasPermi('user:card:listb')")
    public TableDataInfo listb(CreditCard card) {
        Long userId = SecurityUtils.getLoginUser().getUserId();
        card.setUserId(userId);
        Page<CreditCardVo> page = creditCardService.pageB(card);
        Page<CreditCardVo> updatedPage = page
                .stream()
                .map(c -> {
                    if (StringUtils.isNotEmpty(c.getCardNo())) {
                        String cardNo = "";
                        if (!c.getCardNo().contains("*")) {
                            cardNo = AESUtils.aesDecrypt(Constants.card_key, c.getCardNo());
                            String xx = "";

                            if (cardNo.length() > 8) {
                                for (int i = 0; i < cardNo.length() - 8; i++) {
                                    xx = xx + "*";
                                }
                                cardNo = cardNo.substring(0, 4) + xx + cardNo.substring(cardNo.length() - 4, cardNo.length());
                            }
                        } else {
                            cardNo = c.getCardNo();
                        }

                        cardNo = MyAesUtil.encode((MyAesUtil.SALT + cardNo).getBytes());
                        c.setCardNo(cardNo);
                    }
                    if (StringUtils.isNotEmpty(c.getCvv2())) {
                        String cvv2 = AESUtils.aesDecrypt(Constants.card_key, c.getCvv2());
                        cvv2 = "***";
                        cvv2 = MyAesUtil.encode((MyAesUtil.SALT + cvv2).getBytes());
                        c.setCvv2(cvv2);
                    }
                    if (StringUtils.isNotEmpty(c.getExpirationTime())) {
                        c.setExpirationTime(c.getExpirationTime().substring(0, 2) + "**");
                    }

                    return c;
                })
                .collect(Collectors.collectingAndThen(Collectors.toList(), list -> new PageImpl<>(list, page.getPageable(), page.getTotalElements())));
        return getDataTable(updatedPage);
    }

    /**
     * 客服查看卡列表
     */
    @GetMapping("/listkf")
    @PreAuthorize("@ss.hasPermi('user:card:listkf')")
    public TableDataInfo listkf(CreditCard card) {

        Page<CreditCardVo> page = creditCardService.page(card);
        Page<CreditCardVo> updatedPage = page
                .stream()
                .map(c -> {
                    if (StringUtils.isNotEmpty(c.getCardNo())) {
                        String cardNo = "";
                        if (!c.getCardNo().contains("*")) {
                            cardNo = AESUtils.aesDecrypt(Constants.card_key, c.getCardNo());
                            String xx = "";

                            if (cardNo.length() > 8) {
                                for (int i = 0; i < cardNo.length() - 8; i++) {
                                    xx = xx + "*";
                                }
                                cardNo = cardNo.substring(0, 4) + xx + cardNo.substring(cardNo.length() - 4, cardNo.length());
                            }
                        } else {
                            cardNo = c.getCardNo();
                        }

                        cardNo = MyAesUtil.encode((MyAesUtil.SALT + cardNo).getBytes());
                        c.setCardNo(cardNo);
                    }
                    if (StringUtils.isNotEmpty(c.getCvv2())) {
                        String cvv2 = AESUtils.aesDecrypt(Constants.card_key, c.getCvv2());
                        cvv2 = "***";
                        cvv2 = MyAesUtil.encode((MyAesUtil.SALT + cvv2).getBytes());
                        c.setCvv2(cvv2);
                    }
                    if (StringUtils.isNotEmpty(c.getExpirationTime())) {
                        c.setExpirationTime(c.getExpirationTime().substring(0, 2) + "**");
                    }
                    c.setSource("");
                    return c;
                })
                .collect(Collectors.collectingAndThen(Collectors.toList(), list -> new PageImpl<>(list, page.getPageable(), page.getTotalElements())));
        return getDataTable(updatedPage);
    }

    /**
     * 查询卡信息详情同步(客服)
     */
    @PostMapping("/updateCardDetaiKf")
    @PreAuthorize("@ss.hasPermi('user:card:updateCardDetaiKf')")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateCardDetaiKf(@Validated(CardId.class) @RequestBody VccReq req) {


        CreditCard c = creditCardService.findByCardId(req.getCardId());
        SysUser sysUser = sysUserService.selectUserById(req.getUserId());
        CreditCard cardInfo = creditCardService.getCardInfo(c);
        CreditCard cv = cardInfo.clone();
        cv.setCardNo(MyAesUtil.encode((MyAesUtil.SALT + creditCardService.dealAesCardInfo(cv.getCardNo())).getBytes()));
        cv.setCvv2(MyAesUtil.encode((MyAesUtil.SALT + "***").getBytes()));
        cv.setUsername(sysUser.getUserName());
        cv.setNickName(sysUser.getNickName());
        cv.setSource("");
        return AjaxResult.success(cv);


    }


    /**
     * 查询信息详情同步 B端
     */
    @PostMapping("/updateCardDetaiB")
    @PreAuthorize("@ss.hasPermi('user:card:updateCardDetaiB')")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateCardDetaiB(@Validated(CardId.class) @RequestBody VccReq req) {
        CreditCard c = creditCardService.findByCardId(req.getCardId());
        SysUser sysUser = sysUserService.selectUserById(req.getUserId());
        CreditCard ck = creditCardService.getCardInfo(c);
        CreditCard cv = ck.clone();
        cv.setCardNo(MyAesUtil.encode((MyAesUtil.SALT + creditCardService.dealAesCardInfo(cv.getCardNo())).getBytes()));
        cv.setCvv2("");
        cv.setUsername(sysUser.getUserName());
        return AjaxResult.success(cv);

    }


    /**
     * 卡交易记录更新（同步vcc/moonbank的最新200条）
     */
    @PostMapping("/updateLog")
    @PreAuthorize("@ss.hasPermi('user:card:updateLog')")
    public AjaxResult updateLog(@RequestBody CardUpdateLogDto dto) {

        CreditCard c = creditCardService.findByCardId(dto.getCardId());
        if (c.getSource() != null && ("moonbank".equals(c.getSource()) || c.getSource().equals("moonbank_Triplink"))) {
            dto.setPageSize(100);
            baseCardCommon.queryByTransactionPage(dto);
            return AjaxResult.success("数据正在后台同步中...");
        } else if ("kun".equals(c.getSource())) {
            kunService.orderDeal(c);
            return AjaxResult.success("4146数据正在后台同步中...");
        } else{
            return AjaxResult.success("vcc日志正在后台同步中...");
        }

    }

    /**
     * 查询vcc卡信息详情同步
     */
    @PostMapping("/updateCardDetai")
    @PreAuthorize("@ss.hasPermi('user:card:updateCardDetai')")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateCardDetai(@Validated(CardId.class) @RequestBody VccReq req) {
        CreditCard c = creditCardService.findByCardId(req.getCardId());
        SysUser sysUser = sysUserService.selectUserById(req.getUserId());
        CreditCard cardInfo = creditCardService.getCardInfo(c);
        CreditCard cv = cardInfo.clone();

        cv.setCardNo(MyAesUtil.encode((MyAesUtil.SALT + creditCardService.dealAesCardInfo(cv.getCardNo())).getBytes()));
        cv.setCvv2(MyAesUtil.encode((MyAesUtil.SALT + creditCardService.dealAesCardInfo(cv.getCvv2())).getBytes()));
        cv.setUsername(sysUser.getUserName());
        return AjaxResult.success(cv);


    }

//    /**
//     * 调用vcc卡提现
//     */
//    @PostMapping("/withdrawCard")
//    @PreAuthorize("@ss.hasPermi('user:card:withdrawCard')")
//    @Transactional(rollbackFor = Exception.class)
//    public AjaxResult withdrawCard(@Validated(CardId.class) @RequestBody VccReq req) {
//        CreditCard c = creditCardService.findByCardId(req.getCardId());
////        AjaxResult ar = baseCardCommon.isMyCard(c, true);
////        if (ar != null)
////            return ar;
////        CardConfig cc = cardConfigService.findCardConfigByCardTypeAndCardLevel(c.getCardType(), c.getCardLevel());
//
//        //3.调用vcc接口
//        req.setRequestNo(UniqueIDGenerator.generateID());
//        VccCreateCardResp res = yjCardUtils.card(req, ApiCode.WithdrawCard);
//        if (res != null && res.getCode().equals("SUCCESS")) {
//            //4.提现时的钱包处理
//            JSONObject j = JSON.parseObject(res.getRes());
//            String txnId = j.getString("transactionId");
//            AjaxResult ares = baseCardCommon.dealWalletWhenWithdrawForAdmin(Long.valueOf(c.getUserId()), req.getWithdrawAmount(), BigDecimal.ZERO, c.getCardId(), txnId);
//            //5.保存充值记录
//            baseCardCommon.saveCreditCardLog(VccTxnType.card_withdraw.getValue(), txnId, req.getWithdrawAmount(), c.getCardId());
//            //6.提现成功后的处理
////            baseCardCommon.dealAfterWithdraw(c.getCardId(),ares,txnId);
//            return AjaxResult.success();
//        }
//        //2.返回结果
//        return AjaxResult.error(res.getCode(), res.getDetail());
//
//
//    }

    /**
     * 注销卡
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/closeCard")
    @PreAuthorize("@ss.hasPermi('user:card:closeCard')")
    public AjaxResult closeCard(@Validated(CardId.class) @RequestBody VccReq req) {

        CreditCard c = creditCardService.findByCardId(req.getCardId());
        CardConfig cardConfig = cardConfigService.findCardConfigByCardCfgId(c.getCardCfgId());
        String status = sysDictDataService.getLabel("vcc_card_status", c.getCardStatus());
        if (c.getCardStatus().equals(CardStatus.TBA.getValue()) || c.getCardStatus().equals(CardStatus.CLOSE.getValue()) || c.getCardStatus().equals(CardStatus.CLOSE_PROCESSING.getValue())) {
            return AjaxResult.error(MessageUtils.message("closecard.error", status));
        }
        if ("yj".equals(c.getSource())) {
            //查卡详情
            req.setRequestNo(UniqueIDGenerator.generateID());
            VccCreateCardResp res = yjCardUtils.card(req, ApiCode.QueryCardDetail);

            //2.返回结果
            if (res != null && res.getCode().equals("SUCCESS")) {
                JSONObject j = JSON.parseObject(res.getRes());
                String cardStatus = j.getString("cardStatus");
                //卡片使用中和已锁定才能用
                if (cardStatus.equals(CardStatus.USED.getValue()) || cardStatus.equals(CardStatus.SUSPEND.getValue())) {
                    BigDecimal balance = j.getBigDecimal("balance");
                    if (balance.compareTo(BigDecimal.ZERO) > 0) {
                        return AjaxResult.error("卡内还有余额，请先进行卡提现。");
                    } else {
                        //注销卡
                        //1.调用vcc接口
                        req.setRequestNo(UniqueIDGenerator.generateID());
                        VccCreateCardResp res2 = yjCardUtils.card(req, ApiCode.CloseCard);
                        //2.返回结果
                        if (res2 != null && res2.getCode().equals("SUCCESS")) {
                            //注销中
                            creditCardService.updateStatus(c.getCardId(), CardStatus.REMOVE_PROCESSING.getValue());
                            return AjaxResult.success(res2.getCode());
                        }
                        return AjaxResult.error(res2.getCode(), "注销卡：" + res2.getDetail());
                    }

                } else {
                    return AjaxResult.error("卡片在使用中或者已锁定的状态下才能进行销卡。");
                }

            } else {
                return AjaxResult.error(res.getCode(), "查询卡详情：" + res.getDetail());
            }
        } else if (StringUtils.isNotEmpty(c.getSource()) && c.getSource().startsWith("moonbank")) {


            if (StringUtils.isEmpty(c.getSysId())) {
                //查询余额
                //销卡
                Wallet wallet = walletService.findByUserId(c.getUserId());
                ApiResponse<String> apiResponse = moonbankUtil.queryBankcardBalance(wallet.getMoonbankUid(), Integer.valueOf(c.getBankCardId()));
                if (apiResponse.isSuccess()) {
                    String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
                    logger.info("queryBankcardInfo encode result===>" + descStr);
                    JSONObject j = JSONObject.parseObject(descStr);
                    BigDecimal b = j.getBigDecimal("balance");
                    BigDecimal refundAmt = j.getBigDecimal("refundAmt");
                    logger.info("查询balance===>" + b);
                    logger.info("返回balance===>" + refundAmt);
                    //销卡
                    ApiResponse<String> apiResponse2 = moonbankUtil.close(wallet.getMoonbankUid(), Integer.valueOf(c.getBankCardId()));
                    if (apiResponse2.isSuccess()) {
                        String descStr2 = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse2.getResult());
                        logger.info("close card encode result===>" + descStr2);
                        JSONObject j2 = JSONObject.parseObject(descStr2);
                        String txnId = j2.getString("requestOrderId");
                        logger.info("requestOrderId===>" + txnId);
                        BigDecimal balance = j2.getBigDecimal("refundAmt");
                        logger.info("销卡返回余额balance===>" + balance);
                        if (balance == null) {
                            balance = refundAmt;
                        }
                        logger.info("销卡返回余额balance===>" + balance);



                        //更新卡状态
                        creditCardService.update(c.getCardId(), CardStatus.CLOSE_PROCESSING.getValue(), BigDecimal.ZERO);
                        if (balance.compareTo(BigDecimal.ZERO) < 0) {
                            sendEmail.sendAminEail("销卡的卡片余额异常", "卡id：" + c.getCardId() + ",返还余额：" + balance);
                            return AjaxResult.error(MessageUtils.message("closecard.error2", balance));
                        }
                        AjaxResult ares = baseCardCommon.dealWalletWhenWithdrawForAdmin(Long.valueOf(c.getUserId()), balance, BigDecimal.ZERO, c.getCardId(), txnId);
//                        baseCardCommon.dealCoinWhenClose(c.getUserId(),"USDT", balance, BigDecimal.ZERO, c.getCardId(), txnId, new BigDecimal("1"), c.getCurrency(), balance);
                        //5.保存提现记录
                        baseCardCommon.saveMoonbankCreditCardLog(VccTxnType.card_withdraw.getValue(), txnId, balance, c.getCardId(), "processing", "USD", c.getCurrency(), null);

                        //6.查询卡片状态（CLOSE）
                        JSONObject cardJson = creditCardService.getCardStutus(wallet.getMoonbankUid(), c.getBankCardId());
                        if (cardJson != null) {
                            String cardStatus = cardJson.getString("userBankCardStatus");
                            if (cardStatus.equals(CardStatus.CLOSE.getValue())) {
                                //7.提现成功后的处理
                                baseCardCommon.dealAfterWithdrawMoonbank(c.getCardId(), ares, txnId);
                            }
                        }

                        return AjaxResult.success();
                    } else {
                        return AjaxResult.error(apiResponse2.getMessage());
                    }

                }


            } else {
                //B端销卡
                creditCardService.closeCardB(c.getSysId(), new JSONObject(), c, cardConfig);
                return AjaxResult.success();

            }

        } else if ("kun".equals(c.getSource())) {
            JSONObject jsonObject = kunService.closeCard(new JSONObject(), c, cardConfig);
            BigDecimal balance = jsonObject.getBigDecimal("refundAmt");
            if (balance == null || balance.compareTo(BigDecimal.ZERO) < 0) {
                sendEmail.sendAminEail("销卡的卡片余额异常", "卡id：" + c.getCardId() + ",返还余额：" + balance);
                return AjaxResult.error(MessageUtils.message("closecard.error2", balance));
            }
            if (TxnStatus.DECLINED.getName().equals(jsonObject.getString("status"))) {
                return AjaxResult.error(MessageUtils.message("operation.failed"));
            }
            String currency = jsonObject.getString("currency");
            String txnId = jsonObject.getString("txnId");
            logger.info("balance====" + balance);
            if (!"USD".equals(currency)) {
                if ("HKD".equals(currency)) {
                    BigDecimal price = kunService.getPrice("USDT", c.getCurrency(), KunPriceSide.BUY.getValue(), balance);
                    if (price.compareTo(new BigDecimal("1")) > 0) {
                        balance = balance.divide(price, 2, RoundingMode.CEILING);//充值coin转USD的值
                    } else {
                        balance = balance.multiply(price).setScale(2, RoundingMode.CEILING);//充值coin转USD的值
                    }

                } else {
                    ExchangeRate rate = exchangeRateService.findByFromCurrencyCodeAndToCurrencyCode(cardConfig.getCurrency(), "USD");
                    balance = balance.multiply(rate.getRateVal()).setScale(2, RoundingMode.CEILING);//充值coin转USD的值
                }


            }
            if (StringUtils.isEmpty(c.getSysId())) {
                Wallet wallet = walletService.findByUserId(c.getUserId());
                AjaxResult ares = baseCardCommon.dealWalletWhenWithdrawForAdmin(Long.valueOf(c.getUserId()), balance, BigDecimal.ZERO, c.getCardId(), txnId);
                //5.保存提现记录
                baseCardCommon.saveMoonbankCreditCardLog(VccTxnType.card_withdraw.getValue(), txnId, balance, c.getCardId(), "success", "USD", c.getCurrency(), null);
                //6.提现成功后的处理
                baseCardCommon.dealAfterWithdrawMoonbank(c.getCardId(), ares, txnId);


            } else {
                //B端
                creditCardService.dealBalanceB(c.getSysId(), c, cardConfig, txnId, balance);
                return AjaxResult.success();
            }
            //更新卡片信息
            baseCardCommon.updateCardKun(c.getCardId());
        }
        return AjaxResult.error(MessageUtils.message("operation.failed"));

    }

    @GetMapping("/getCardConfig")
    @PreAuthorize("@ss.hasPermi('user:card:getCardConfig')")
    public AjaxResult getCardConfig(@RequestParam("cardId") String cardId) {
        CreditCard c = creditCardService.findByCardId(cardId);
        CardConfig cardConfig = cardConfigService.findCardConfigByCardCfgId(c.getCardCfgId());
        return AjaxResult.success(cardConfig);
    }

    /**
     * 充值
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/rechargeCard")
    @PreAuthorize("@ss.hasPermi('user:card:rechargeCard')")
    public AjaxResult rechargeCard(@Validated(CardId.class) @RequestBody VccReq req) {

        CreditCard c = creditCardService.findByCardId(req.getCardId());
        boolean checkRecharge = creditCardService.checkRecharge(c.getCardType());
        if (!checkRecharge) {
            return AjaxResult.error(MessageUtils.message("card.not.recharge.oldkun"));
        }


        if (c.getSource() != null && (c.getSource().equals("moonbank")) || c.getSource().equals("moonbank_Triplink") || "kun".equals(c.getSource())) {
            if (c.getCardId().startsWith("temp_")) {
                return AjaxResult.error(MessageUtils.message("card.status.execption"));
            }


            //充值金额
            BigDecimal rechargeAmount = req.getRechargeAmount();
            MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectByUserIdForUpdate(c.getUserId());

            //判断是用户的卡还是用户渠道的卡

            if (StringUtils.isNotEmpty(c.getSysId())) {
                MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(c.getCardHolder(), c.getSysId());
                CardConfig cardConfig = cardConfigService.findCardConfigByCardCfgId(c.getCardCfgId());


                //判断池子金额是否低于最低限额
                if (metaPartnerAssetPool.getMinDepositAmt() != null && metaPartnerAssetPool.getUsdtBalacne().compareTo(metaPartnerAssetPool.getMinDepositAmt()) <= 0) {
                    return AjaxResult.error(MessageUtils.message("wallet.pool.ltpool"));
                }

                //是否是首次激活金额
                if (c.getCardStatus().equals("TBA") && rechargeAmount.compareTo(cardConfig.getActiveAmtLimit()) < 0) {
                    //  首次不能小于激活金额
                    String message = "Card ID " + c.getCardId() + " The first recharge cannot be less than the activation amount of " + cardConfig.getActiveAmtLimit();
                    return AjaxResult.error(message);
                }

                if (rechargeAmount.compareTo(cardConfig.getRechargerAmtLimit()) < 0) {
                    //不能小于充值金额
                    String message = "Card ID " + c.getCardId() + "Amount cannot be less than  " + cardConfig.getRechargerAmtLimit();
                    return AjaxResult.error(message);
                }
                //最大的充值金额不能大于
                if (cardConfig.getRechargerAmtMax().compareTo(BigDecimal.ZERO) > 0 && cardConfig.getRechargerAmtMax().compareTo(rechargeAmount) < 0) {
                    String message = "Card ID " + c.getCardId() + "Amount cannot be greater than  " + cardConfig.getRechargerAmtMax();
                    return AjaxResult.error(message);
                }


                BigDecimal rechargeFee = BigDecimal.ZERO;
                if (cardConfig.getCardRechargeFee().compareTo(BigDecimal.ZERO) > 0) {
                    rechargeFee = rechargeAmount.multiply(cardConfig.getCardRechargeFee());
                    rechargeFee = rechargeFee.setScale(2, RoundingMode.CEILING);
                    logger.info("计算的手续费：" + rechargeFee);
                }
                if (rechargeAmount.add(rechargeFee).compareTo(metaPartnerAssetPool.getUsdtBalacne()) > 0) {
                    // 余额不足
                    return AjaxResult.error(MessageUtils.message("wallet.pool.notenough"));
                }

                if (c.getSource().startsWith("moonbank")) {


                    ApiResponse<String> applyBankcardRes = moonbankUtil.rechargeBankcardNew(partnerUser.getUid(), Integer.valueOf(c.getBankCardId()), rechargeAmount);

                    if (applyBankcardRes.isSuccess()) {

                        //激活卡
                        if (c.getCardStatus().equals(CardStatus.TBA.getValue())) {
                            //更新卡信息
                            baseCardCommon.updateCardMoonbank(c.getCardId(), metaPartnerAssetPool.getId().getPartnerId(), partnerUser.getUid());
                        }
                        String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, applyBankcardRes.getResult());
                        logger.info("rechargeBankcard encode result===>" + descStr);
                        JSONObject j = JSON.parseObject(descStr);
                        String txnId = j.getString("requestOrderId");
                        //充值到卡的金额
                        String sendAmount = j.getString("sendAmount");
                        //保存充值记录
                        baseCardCommon.saveMoonbankCreditCardLogNew(VccTxnType.card_deposit.getValue(), txnId, rechargeAmount, c.getCardId(), "success", "USD", cardConfig.getCurrency(), sendAmount, rechargeFee, "USD");

                        //成功充值后的处理
                        //用户充值流水
                        metaPartnerAssetPool.setUsdtBalacne(metaPartnerAssetPool.getUsdtBalacne().subtract(rechargeAmount).subtract(rechargeFee));
                        metaPartnerTxnDtlService.saveUSD(metaPartnerAssetPool, PartnerTxnType.CardTopup, c.getCardId(), "USD", 0, rechargeAmount.negate(), rechargeFee.negate(), TxnStatus.SUCCESS.getValue(), txnId);
                        metaPartnerAssetPoolService.updateBalance(metaPartnerAssetPool.getId().getPartnerId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());

                        return AjaxResult.success(MessageUtils.message("wallet.topup.suc"));
                    } else {

                        if (applyBankcardRes.getCode() == 99997) {
                            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, applyBankcardRes.getResult());
                            logger.info("rechargeBankcard encode result===>" + descStr);
                            JSONObject j = JSON.parseObject(descStr);
                            String txnId = j.getString("requestOrderId");
                            //充值到卡的金额
                            String sendAmount = j.getString("sendAmount");
                            //保存充值记录
                            baseCardCommon.saveMoonbankCreditCardLogNew(VccTxnType.card_deposit.getValue(), txnId, rechargeAmount, c.getCardId(), "processing", "USD", cardConfig.getCurrency(), sendAmount, rechargeFee, "USD");

                            //用户充值流水
                            metaPartnerAssetPool.setUsdtBalacne(metaPartnerAssetPool.getUsdtBalacne().subtract(rechargeAmount).subtract(rechargeFee));
                            metaPartnerAssetPool.setFreezeUstdBalacne(metaPartnerAssetPool.getFreezeUstdBalacne().add(rechargeAmount).add(rechargeFee));
                            metaPartnerTxnDtlService.saveUSD(metaPartnerAssetPool, PartnerTxnType.CardTopup, c.getCardId(), "USD", 0, rechargeAmount.negate(), rechargeFee.negate(), TxnStatus.PADDING.getValue(), txnId);
                            metaPartnerAssetPoolService.updateBalance(metaPartnerAssetPool.getId().getPartnerId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());

                            return AjaxResult.success(MessageUtils.message("wallet.topup.wait"));

                        } else {
                            //充值错误
                            String message = applyBankcardRes.getMessage();

                            return AjaxResult.error(message);
                        }
                    }
                } else if ("kun".equals(c.getSource())) {

                    logger.info("充值调用");
                    String coin = "HKD";

//                    ExchangeRate exchangeRate = exchangeRateService.findByFromCurrencyCodeAndToCurrencyCode("USD", coin);
                    String from = "USDT";
                    BigDecimal price = kunService.getPrice(from, coin, KunPriceSide.SELL.getValue(), rechargeAmount);
                    if (price == null || price.compareTo(BigDecimal.ZERO) <= 0) {
                        return AjaxResult.error(MessageUtils.message("wallet.topup.error"));
                    }
                    BigDecimal recharge = rechargeAmount.multiply(price).setScale(2, RoundingMode.CEILING);
                    String kun_customerId = configService.selectConfigByKey("kun_customerId");
                    String send_kun_ip = KunConfig.ip;
                    String kun_environment = KunConfig.environment;


                    JSONObject jsonKun = new JSONObject();
                    String requestNoKun = UniqueIDGenerator.generateID();
                    jsonKun.put("customerId", kun_customerId);

                    jsonKun.put("amount", recharge);
                    jsonKun.put("currency", coin);
                    jsonKun.put("ip", send_kun_ip);
                    jsonKun.put("requestNo", requestNoKun);

                    JSONObject result = null;
                    if ("87".equals(c.getCardType())) {
                        jsonKun.put("cardId", c.getBankCardId());
                        result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.card_recharge, "POST", c.getBankCardId());
                    } else {
                        jsonKun.put("cardId", c.getBankCardId());
                        result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.kcard_recharge, "POST", c.getBankCardId());
                    }


                    if (result != null) {

                        if (("87".equals(c.getCardType()) && "********".equals(result.getString("code")))
                                || (!"87".equals(c.getCardType()) && "000000".equals(result.getString("code")))) {
                            String status = result.getString("status");
                            if (StringUtils.isEmpty(status)) {
                                status = KunOrderStatus.PROCESSING.getValue();
                            }
                            Character orderStatus = kunService.getOrderStatus(status);
                            if (TxnStatus.PENDING.getValue() == orderStatus) {
                                //处理中
                                //保存充值记录
                                baseCardCommon.saveMoonbankCreditCardLogNew(VccTxnType.card_deposit.getValue(), requestNoKun, rechargeAmount, c.getCardId(), "processing", "USD", cardConfig.getCurrency(), String.valueOf(recharge), rechargeFee, "USD");

                                //用户充值流水
                                metaPartnerAssetPool.setUsdtBalacne(metaPartnerAssetPool.getUsdtBalacne().subtract(rechargeAmount).subtract(rechargeFee));
                                metaPartnerAssetPool.setFreezeUstdBalacne(metaPartnerAssetPool.getFreezeUstdBalacne().add(rechargeAmount).add(rechargeFee));
                                metaPartnerTxnDtlService.saveUSD(metaPartnerAssetPool, PartnerTxnType.CardTopup, c.getCardId(), "USD", 0, rechargeAmount.negate(), rechargeFee.negate(), TxnStatus.PADDING.getValue(), requestNoKun);
                                metaPartnerAssetPoolService.updateBalance(metaPartnerAssetPool.getId().getPartnerId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());
                                return AjaxResult.success(MessageUtils.message("wallet.topup.wait"));

                            } else if (TxnStatus.SUCCESS.getValue() == orderStatus) {

                                //充值成功
                                //保存充值记录
                                baseCardCommon.saveMoonbankCreditCardLogNew(VccTxnType.card_deposit.getValue(), requestNoKun, rechargeAmount, c.getCardId(), "success", "USD", cardConfig.getCurrency(), String.valueOf(recharge), rechargeFee, "USD");
                                //成功充值后的处理
                                //用户充值流水
                                metaPartnerAssetPool.setUsdtBalacne(metaPartnerAssetPool.getUsdtBalacne().subtract(rechargeAmount).subtract(rechargeFee));
                                metaPartnerTxnDtlService.saveUSD(metaPartnerAssetPool, PartnerTxnType.CardTopup, c.getCardId(), "USD", 0, rechargeAmount.negate(), rechargeFee.negate(), TxnStatus.SUCCESS.getValue(), requestNoKun);
                                metaPartnerAssetPoolService.updateBalance(metaPartnerAssetPool.getId().getPartnerId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());

                                return AjaxResult.success(MessageUtils.message("wallet.topup.suc"));
                            }

                        }

                    }
                    baseCardCommon.updateCardKun(c.getCardId());


                }

            } else {
                return AjaxResult.error(MessageUtils.message("wallet.topup.error"));
            }

        }
        return AjaxResult.error();


    }


    /**
     * moonbank旧卡换新
     */
    @GetMapping("/oldReplaceNew")
    @PreAuthorize("@ss.hasPermi('user:card:oldReplaceNew')")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult oldReplaceNew() {

//        return creditCardService.oldReplaceNew();

        return creditDealService.kcardeExchange();

    }

    /**
     * 实卡物流列表
     */
    @GetMapping("/logisticsList")
    @PreAuthorize("@ss.hasPermi('user:card:logisticsList')")
    public TableDataInfo logisticsList(String cstName, String cardHolder) {
        Page<CardLogisticsVo> page = metaCardLogisticsService.page(cstName, cardHolder);
        Page<CardLogisticsVo> updatedPage = page
                .stream()
                .map(c -> {
                    if (StringUtils.isNotEmpty(c.getCardNo())) {
                        String cardNo = "";
                        if (!c.getCardNo().contains("*")) {
                            cardNo = AESUtils.aesDecrypt(Constants.card_key, c.getCardNo());
                            String xx = "";

                            if (cardNo.length() > 8) {
                                for (int i = 0; i < cardNo.length() - 8; i++) {
                                    xx = xx + "*";
                                }
                                cardNo = cardNo.substring(0, 4) + xx + cardNo.substring(cardNo.length() - 4, cardNo.length());
                            }
                        } else {
                            cardNo = c.getCardNo();
                        }

                        cardNo = MyAesUtil.encode((MyAesUtil.SALT + cardNo).getBytes());
                        c.setCardNo(cardNo);
                    }


                    return c;
                })
                .collect(Collectors.collectingAndThen(Collectors.toList(), list -> new PageImpl<>(list, page.getPageable(), page.getTotalElements())));
        return getDataTable(updatedPage);
    }

    /**
     * 卡物流号更新
     */
    @PostMapping("/updateTrackingNumber")
    @PreAuthorize("@ss.hasPermi('user:card:updateNumber')")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateTrackingNumber(@RequestBody CardLogisticsVo req) {
        metaCardLogisticsService.updateTrackingNumber(req.getTrackingNumber(), req.getType(), req.getId());
        return AjaxResult.success();

    }

    /**
     * 卡片解锁
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/cardUnlock")
    @PreAuthorize("@ss.hasPermi('user:card:cardUnlock')")
    public AjaxResult cardUnlock(@Validated(CardId.class) @RequestBody VccReq req) {

        CreditCard c = creditCardService.findByCardId(req.getCardId());
        if ("89".equals(c.getCardType())) {
            JSONObject jsonObject = kunService.cardUnlock(c, new JSONObject());
            if (jsonObject != null) {
                AjaxResult ajaxResult = AjaxResult.success();
                String status = jsonObject.getString("status");
                ajaxResult.put("status", status);
                return ajaxResult;
            }

        }
        return AjaxResult.error();

    }


    @GetMapping("/orderHistory")
    public AjaxResult orderHistory(@RequestParam("cardId") String cardId) {
        if (StringUtils.isNotEmpty(cardId)) {
            CreditCard c = creditCardService.findByCardId(cardId);

            String kun_customerId = configService.selectConfigByKey("kun_customerId");
            String kun_environment = KunConfig.environment;
            String kun_start_day = "60";
            JSONObject jsonKun = new JSONObject();
            String requestNoKun = UniqueIDGenerator.generateID();
            jsonKun.put("customerId", kun_customerId);
            jsonKun.put("requestNo", requestNoKun);
            // 获取当前时间（UTC）
            ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);
            // 获取当前时间（UTC）
            // 定义 ISO 8601 格式
            DateTimeFormatter formatter = DateTimeFormatter.ISO_INSTANT;

            ZonedDateTime daysAgo = getZonedDateTime(kun_start_day, now);
            logger.info("当前时间: " + now.format(formatter));
            logger.info(kun_start_day + "天前的时间: " + daysAgo.format(formatter));
            DateTimeFormatter fm = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            DateTimeFormatter fm2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            // 转换为字符串
            String transactionStartTime = daysAgo.format(fm);


            jsonKun.put("pageNo", "1");
            jsonKun.put("pageSize", "100");
            JSONObject result = null;
            if ("87".equals(c.getCardType())) {
                jsonKun.put("transactionStartTime", daysAgo.format(formatter));
                jsonKun.put("transactionEndTime", now.format(formatter));
                jsonKun.put("cardId", c.getBankCardId());
                result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.card_order_history, "POST", c.getBankCardId());
            } else {
                jsonKun.put("transactionStartTime", daysAgo.format(fm2));
                jsonKun.put("transactionEndTime", now.format(fm2));
                jsonKun.put("cardId", c.getBankCardId());
                result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.kcard_order_history, "POST", c.getBankCardId());
            }
            return AjaxResult.success(result);
        }
        return AjaxResult.error();
    }

    @GetMapping("/orderDetail")
    public AjaxResult orderDetail(@RequestParam("orderId") String orderId) {
        if (StringUtils.isNotEmpty(orderId)) {
            JSONObject json = new JSONObject();
            String requestNo = UniqueIDGenerator.generateID();
            String kun_customerId = configService.selectConfigByKey("kun_customerId");
            String kun_environment = KunConfig.environment;
            json.put("customerId", kun_customerId);
            json.put("requestNo", requestNo);
            json.put("orderId", orderId);

            JSONObject result = kunUtils.result(kun_environment, json.toJSONString(), requestNo, KunMethod.kcard_order_detail, "POST", null);

            return AjaxResult.success(result);
        }
        return AjaxResult.error();
    }

    /**
     * 几天之前的时间
     *
     * @param kun_start_day
     * @param now
     * @return
     */
    private ZonedDateTime getZonedDateTime(String kun_start_day, ZonedDateTime now) {
        return now.minusDays(Integer.valueOf(kun_start_day));
    }

    /**
     * 获取佣金明细
     */
    @PostMapping("/commissionDataList")
    public TableDataInfo commissionDataList(@RequestBody UserCommissionVo userCommissionVo) {


        return getDataTable(userCommissionService.commissionDataList(userCommissionVo));
    }

}
