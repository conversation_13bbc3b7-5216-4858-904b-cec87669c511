package com.meta.web.controller.app;

import com.meta.common.annotation.RepeatSubmit;
import com.meta.common.constant.HttpStatus;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.core.page.TableSupport;
import com.meta.common.utils.code.BusinessBizCode;
import com.meta.system.dto.CoinBalanceDto;
import com.meta.system.vo.CoinBalanceVo;
import com.meta.web.config.WalletConfig;
import com.meta.web.service.CoinBalanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/11/23/10:33
 */
@RestController
@RequestMapping("/coinBalance")
public class CoinBalanceController {

    @Autowired
    private CoinBalanceService coinBalanceService;

    /**
     * 获取列表
     */
    @PreAuthorize("@ss.hasPermi('coinBalance:list')")
    @GetMapping("/list")
    public TableDataInfo list(CoinBalanceDto coinBalanceDto) {
        Page<CoinBalanceVo> page = coinBalanceService.selectList(coinBalanceDto);
        return getDataTable(page);
    }

    /**
     * 归集
     */
    @PreAuthorize("@ss.hasPermi('coinBalance:collect')")
    @PostMapping("/collect")
    @RepeatSubmit
    public AjaxResult collect(@RequestBody CoinBalanceVo coinBalanceVo) {

        coinBalanceVo.setWalletConfigKey(WalletConfig.key);
        return coinBalanceService.collect(coinBalanceVo);
    }




    /**
     * 响应请求分页数据
     */
    protected TableDataInfo getDataTable(Page<?> page) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(page.getContent());
        rspData.setTotal(page.getTotalElements());
        rspData.setSize(pageReq.getPageSize());
        rspData.setPages(page.getTotalPages());
        rspData.setCurrent(pageReq.getPageNo() + 1);
        return rspData;
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows) {
        return rows == BusinessBizCode.OPTION_SUCCESS.getCode() ? AjaxResult.success() : AjaxResult.error();
    }
}
