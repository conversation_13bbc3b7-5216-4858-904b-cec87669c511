package com.meta.web.controller.app;

import com.meta.common.constant.Constants;
import com.meta.common.core.controller.BaseController;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.utils.*;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.system.domain.TrcCstaddress;
import com.meta.system.domain.app.BepCstaddress;
import com.meta.system.domain.app.CoinBalance;
import com.meta.system.domain.app.CoinTxnDtl;
import com.meta.system.domain.app.ExchangeRate;
import com.meta.system.domain.partner.MetaPartnerAssetPool;
import com.meta.system.dto.CardLogDto;
import com.meta.system.dto.PoolTxnDto;
import com.meta.system.dto.TxnDto;
import com.meta.system.service.*;
import com.meta.system.vo.PoolTxnDtlUSDVo;
import com.meta.system.vo.WalletAddressVo;
import com.meta.web.config.WalletConfig;
import com.meta.web.service.CoinBalanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.PublicKey;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/txn")
public class TxnController extends BaseController {

    @Autowired
    private TxnService txnService;

    @Autowired
    private CoinTxnDtlService coinTxnDtlService;

    @Autowired
    private MetaPartnerAssetPoolService metaPartnerAssetPoolService;

    @Autowired
    private MetaPartnerTxnDtlService metaPartnerTxnDtlService;

    @Autowired
    private CoinBalanceService coinBalanceService;

    @Autowired
    private ExchangeRateService exchangeRateService;

    @Autowired
    private CreditCardLogService creditCardLogService;

    @Autowired
    private BepCstaddressService bepCstaddressService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private TrcCstaddressService trcCstaddressService;

    /**
     * 获取所有的USD交易记录(分页)
     */
    @GetMapping("/listForUSD")
    @PreAuthorize("@ss.hasPermi('txn:usd:list')")
    public TableDataInfo listForUSD(TxnDto txnDto) {
        return getDataTable(txnService.pageForUSD(txnDto));
    }

    /**
     * 获取所有的USD交易记录(分页)
     */
    @GetMapping("/listForUSD_B")
    @PreAuthorize("@ss.hasPermi('txn:usd:listb')")
    public TableDataInfo listForUSD_B(PoolTxnDto poolTxnDto) {
        Long userId = SecurityUtils.getLoginUser().getUserId();
        poolTxnDto.setUserId(userId);
        Page<PoolTxnDtlUSDVo> page = metaPartnerTxnDtlService.pageForUSD_B(poolTxnDto);
        for (PoolTxnDtlUSDVo vo : page.getContent()) {
            String cardNo = vo.getCardNo();
            if (StringUtils.isNotEmpty(vo.getCardNo()) && !vo.getCardNo().contains("*")) {
                cardNo = AESUtils.aesDecrypt(Constants.card_key, cardNo);
            }
            vo.setCardNo(MyAesUtil.encode((MyAesUtil.SALT + cardNo).getBytes()));
        }
        return getDataTable(page);
    }

    /**
     * 获取所有的USD交易记录(分页)
     */
    @GetMapping("/listForCode")
    @PreAuthorize("@ss.hasPermi('txn:code:list')")
    public TableDataInfo listForCode(TxnDto txnDto) {
        return getDataTable(txnService.pageForCode(txnDto));
    }

    /**
     * 获取所有的USD交易记录(分页)
     */
    @GetMapping("/listForCardLog")
    @PreAuthorize("@ss.hasPermi('txn:cardlog:list')")
    public TableDataInfo listForCardLog(CardLogDto vo) {

        Long userId = SecurityUtils.getLoginUser().getUserId();
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartner(userId);
        if(metaPartnerAssetPool!=null){
            vo.setSysId(metaPartnerAssetPool.getSysId());
        }

        return getDataTable(creditCardLogService.listForCardLog(vo));
    }
    /**
     * USD交易记录详情
     */
    @GetMapping("/detailByUSD")
    @PreAuthorize("@ss.hasPermi('txn:usd:detail')")
    public AjaxResult detailByUSD(@RequestParam("id") Long id) {
        return AjaxResult.success(txnService.detailByUSD(id, null));
    }

    /**
     * CODE记录详情
     */
    @GetMapping("/detailByCode")
    @PreAuthorize("@ss.hasPermi('txn:code:detail')")
    public AjaxResult detailByCode(@RequestParam("id") Long id) {
        return AjaxResult.success(txnService.detailByCode(id, null));
    }

    /**
     * 获取所有的Coin交易记录(分页)
     */
    @GetMapping("/listForCoin")
    @PreAuthorize("@ss.hasPermi('txn:coin:list')")
    public TableDataInfo listForCoin(CoinTxnDtl coinTxnDtl) {
        return getDataTable(coinTxnDtlService.page(coinTxnDtl));
    }

    /**
     * Coin交易记录详情
     */
    @GetMapping("/detailByCoin")
    @PreAuthorize("@ss.hasPermi('txn:coin:detail')")
    public AjaxResult detailByCoin(@RequestParam("id") Long id) {
        return AjaxResult.success(coinTxnDtlService.selectById(id));
    }

    /**
     * Coin交易记录详情
     */
    @GetMapping("/getPoolInfo")
//    @PreAuthorize("@ss.hasPermi('txn:pool:info')")
    public AjaxResult getPoolInfo() {
        Long userId = SecurityUtils.getLoginUser().getUserId();
        return AjaxResult.success(metaPartnerAssetPoolService.selectByPartnerId(userId));
    }

    /**
     * Coin交易记录详情
     */
    @GetMapping("/getCoinlist")
    @PreAuthorize("@ss.hasPermi('txn:pool:coin')")
    public AjaxResult getCoinlist() {
        Long userId = SecurityUtils.getLoginUser().getUserId();
        return AjaxResult.success(coinBalanceService.getCoinlist(userId));
    }

    /**
     * Coin交易记录详情
     */
    @GetMapping("/getCoinRate")
    @PreAuthorize("@ss.hasPermi('txn:coin:rate')")
    public AjaxResult getCoinRate(@RequestParam("fromCode") String fromCode, @RequestParam("toCode") String toCode) {
        return AjaxResult.success(exchangeRateService.findByFromCurrencyCodeAndToCurrencyCode(fromCode, toCode));
    }

    /**
     * 池子手动/自动
     *
     * @return
     */
    @PreAuthorize("@ss.hasPermi('txn:pool:atuo')")
    @PostMapping("/changeAuto")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult changeAuto(@RequestBody Map<String, String> requestBody) {
        String autoString = requestBody.get("auto");
        char autoChar = autoString.charAt(0); // 取第一个字符
        Character auto = autoChar; // 自动装箱为Character类型
        Long userId = SecurityUtils.getLoginUser().getUserId();
        metaPartnerAssetPoolService.updateAuto(auto, userId);
        return AjaxResult.success();
    }

    /**
     * 资金转入池子
     *
     * @param
     * @return
     */

    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("@ss.hasPermi('txn:pool:exchange')")
    @PostMapping("/exchangeToPool")
    public AjaxResult exchangeToPool(@RequestParam String currency, @RequestParam String amount) {


        Long userId = SecurityUtils.getLoginUser().getUserId();
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartner(userId);
        CoinBalance userUsdtBalance = coinBalanceService.findByUserIdAndCoinCode(userId, currency);
        BigDecimal amt = new BigDecimal(amount);
        if (amt.compareTo(userUsdtBalance.getCoinBalance()) > 0) {
            return AjaxResult.error(MessageUtils.message(""));
        }
        ExchangeRate rate = exchangeRateService.findByFromCurrencyCodeAndToCurrencyCode(currency, "USD");
        BigDecimal txnamout = amt.multiply(rate.getRateVal());
        txnamout = txnamout.setScale(2, RoundingMode.DOWN);
        userUsdtBalance.setCoinBalance(userUsdtBalance.getCoinBalance().subtract(amt));
        coinBalanceService.updateCoinBalance(userUsdtBalance);
        metaPartnerAssetPool.setUsdtBalacne(metaPartnerAssetPool.getUsdtBalacne().add(txnamout));
        metaPartnerAssetPoolService.updateBalance(metaPartnerAssetPool.getId().getPartnerId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());
        metaPartnerAssetPoolService.toPupdealDtl(metaPartnerAssetPool, currency, txnamout,null);
        return AjaxResult.success();
    }

    @GetMapping("/merchantsAddress")
    public AjaxResult merchantsAddress(){
        List<WalletAddressVo> list =new ArrayList<>();
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        String sysId=String.valueOf(accountId);
        Long userId = SecurityUtils.getLoginUser().getUserId();
        //trc20
        List<TrcCstaddress> trcCstaddressList = trcCstaddressService.findByCstId(userId,sysId);
        WalletAddressVo trc=new WalletAddressVo();
        if (trcCstaddressList.size() > 0) {
            trc.setType("TRC20");
            trc.setWalletAddress(trcCstaddressList.get(0).getCstAddress());
            list.add(trc);
        }else{
            logger.info("找不到trc数据");
            //如果找不到，需要去调用go语言的接口，生成钱包地址
            String address = trcCstaddressService.createWallet(userId, sysId, WalletConfig.key);
            if (StringUtils.isNotEmpty(address)) {
                trc.setType("TRC20");
                trc.setWalletAddress(address);
                list.add(trc);
            }
        }
        //bep20

        List<BepCstaddress> beplist = bepCstaddressService.findByCstId(userId,sysId);
        WalletAddressVo bep=new WalletAddressVo();
        if (beplist.size() > 0) {
            bep.setType("BEP20");
            bep.setWalletAddress(beplist.get(0).getCstAdress());
            list.add(bep);
        }else{
            logger.info("找不到bep数据");

            String address = bepCstaddressService.createWallet(userId, "USDT", sysId, WalletConfig.key);
            if (StringUtils.isNotEmpty(address)) {
                bep.setType("BEP20");
                bep.setWalletAddress(address);
                list.add(bep);
            }
        }

        return AjaxResult.success(list);


    }

}
