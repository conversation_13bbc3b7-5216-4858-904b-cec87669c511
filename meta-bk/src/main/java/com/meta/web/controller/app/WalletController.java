package com.meta.web.controller.app;

import com.meta.common.annotation.RepeatSubmit;
import com.meta.common.core.controller.BaseController;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.domain.entity.SysDictData;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.enums.app.AlgorithmType;
import com.meta.common.enums.app.FreezeType;
import com.meta.common.enums.app.NodeLevel;
import com.meta.common.enums.app.TxnType;
import com.meta.common.utils.DictUtils;
import com.meta.common.utils.MessageUtils;
import com.meta.system.domain.Wallet;
import com.meta.system.domain.app.NodeConfig;
import com.meta.system.dto.UsdExchangeOutDto;
import com.meta.system.dto.VccReqDto;
import com.meta.system.dto.node.CodeTransferDto;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.ISysUserService;
import com.meta.system.service.NodeService;
import com.meta.system.service.WalletService;
import com.meta.system.vo.WalletVo;
import com.meta.web.dto.TransferDto;
import com.meta.web.utils.AppCache;
import com.meta.web.vo.UserNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/wallet")
public class WalletController extends BaseController {
    @Autowired
    private WalletService walletService;

    @Autowired
    private ISysUserService userService;

    @Resource
    private NodeService nodeService;

    @Autowired
    private AppCache appCache;
    @Autowired
    private ISysConfigService configService;

    /**
     * 获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('wallet:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(String nickName,String username,String status,String startDate,String endDate,String nodeLevel,String channelName,String limitFlag) {
        Page<WalletVo> page = walletService.selectUserList(nickName,username,status,startDate,endDate,nodeLevel,channelName,limitFlag);
        return getDataTable(page);
    }

    /**
     * 节点升级/降级
     */
    @PreAuthorize("@ss.hasPermi('wallet:user:upgrade')")
    @PutMapping("/upgrade")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult upgrade(@RequestBody @Validated UserNode userNode) {
        List<SysDictData> nodeType = DictUtils.getDictCache("node_type");
        if (nodeType != null && !nodeType.isEmpty()){
            List<String> nodeLevels = nodeType.stream().map(SysDictData::getDictValue).collect(Collectors.toList());
            // 判断节点等级是否合规
            if(nodeLevels.contains(userNode.getNodeLevel())){
                walletService.upgrade(userNode.getUserId(),userNode.getNodeLevel());
                return AjaxResult.success();
            }
        }
        return AjaxResult.error("节点等级不合规");
    }


    /**
     * 转账
     */
    @PostMapping("/usdTransfer")
    @PreAuthorize("@ss.hasPermi('wallet:user:usdTransfer')")
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    public AjaxResult usdTransfer(@RequestBody @Validated TransferDto transfer){
        SysUser fromUser = userService.selectUserById(transfer.getFromUserId());
        if(fromUser == null){
            return AjaxResult.error("转出用户不存在!");
        }
        String email = fromUser.getEmail();
        if (transfer.getToEmail().equals(email)){
            return AjaxResult.error(MessageUtils.message("wallet.email.can.not.yourself"));
        }

        SysUser user = userService.selectUserByEmail(transfer.getToEmail());
        if (user == null){
            return AjaxResult.error(MessageUtils.message("mail.not.exists"));
        }

        //扣款
        VccReqDto source = new VccReqDto(
                fromUser.getUserId(),
                AlgorithmType.SUB,
                transfer.getAmount(),
                FreezeType.NO,
                TxnType.INTERNAL_TRANSFER_OUT_C,
                fromUser.getUserId(),
                user.getUserId(),
                "",
                BigDecimal.ZERO,null);

        // 收款
        VccReqDto target = new VccReqDto(
                user.getUserId(),
                AlgorithmType.ADD,
                transfer.getAmount(),
                FreezeType.NO,
                TxnType.INTERNAL_TRANSFER_IN_D,
                fromUser.getUserId(),
                user.getUserId(),
                "",
                BigDecimal.ZERO,null
        );

        AjaxResult ajaxResult = walletService.transfer(source,target);
        if (ajaxResult.isSuccess()) {
            return AjaxResult.success(MessageUtils.message("wallet.transfer.success"));
        }
        return AjaxResult.success(MessageUtils.message("wallet.transfer.failed"));
    }


    /**
     * 节点划转
     *
     * @param transfer 节点划转请求对象
     * @return AjaxResult
     */
    @PostMapping("/codeTransfer")
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('wallet:user:codeTransfer')")
    public AjaxResult codeTransfer(@Validated @RequestBody CodeTransferDto transfer) {

        SysUser fromUser = userService.selectUserById(transfer.getFromUserId());
        if(fromUser == null){
            return AjaxResult.error(MessageUtils.message("node.user.not.exist"));
        }

        SysUser otUser = userService.selectUserByUserName(transfer.getToUsername());

        if(otUser == null){
            return AjaxResult.error(MessageUtils.message("node.user.not.exist"));
        }
        Wallet wallet = walletService.findByUserId(otUser.getUserId());

        //  划转方
        Wallet currentUser = walletService.findByUserId(fromUser.getUserId());
        String currentNodeLevel = currentUser.getNodeLevel();

        //可划转最大数量
        Integer totalRightScardNum = 0;
        if(currentNodeLevel.equals("00")){
            NodeConfig currentNodeConfig= nodeService.findNodeConfigByNodeLevel(currentNodeLevel);
            totalRightScardNum=currentNodeConfig.getTotalRightScardNum();
        }else {
            NodeConfig previousNode = nodeService.findPreviousNode(currentUser.getNodeLevel());
            totalRightScardNum=previousNode.getTotalRightScardNum();
        }
        Integer userNum=wallet.getTotalExchangeSilverActiveCodeNum()+wallet.getSilverActiveCodeNum();
        logger.info("划转接收方持有银卡数量："+userNum);
        if (userNum+transfer.getTotalExchangeSilverActiveCodeNum() > totalRightScardNum){
            // 收款账户可划转数量不足
          //  return AjaxResult.error(MessageUtils.message("node.transfer.insufficient.num"));
        }
        //判断是否是个人用户
        if(wallet.getNodeLevel().equals("00") && currentUser.getNodeLevel().equals("00")){
            logger.info("个人用户向个人用户划转");
        }else {
            // 判断转入节点等级是否小于当前用户的节点等级
            if (!NodeLevel.isHigher(currentUser.getNodeLevel(),wallet.getNodeLevel())){
                return AjaxResult.error(MessageUtils.message("node.transfer.level"));
            }
        }



        // 判断剩余的激活码是否足够
        if (!walletService.checkCardBalance(transfer)){
            return AjaxResult.error(MessageUtils.message("wallet.activitiCode.insufficient.num"));
        }
        //扣款
        VccReqDto source = new VccReqDto();
        source
                .setCodeFromUserId(fromUser.getUserId()) // 扣款id
                .setCodeToUserId(otUser.getUserId()) // 收款id
                .setCardLevel(transfer.getCardLevel()) // 激活码的类型
                .setTxnTypeCode(TxnType.TRANSFER_EXPENDITURE) // 转让支出
                .setActiveCodeNum(transfer.getActiveCodeNum()) //需要处理的激活码数量
                .setActiveCodeAlgorithmType(AlgorithmType.SUB)
                .setUserId(fromUser.getUserId()) // 用户id
                .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                .setWalletAmount(BigDecimal.ZERO); // 预存金额

        // 收款
        VccReqDto target = new VccReqDto();
        target
                .setCodeFromUserId(fromUser.getUserId()) // 扣款id
                .setCodeToUserId(otUser.getUserId()) // 收款id
                .setCardLevel(transfer.getCardLevel()) // 激活码的类型
                .setTxnTypeCode(TxnType.TRANSFER_RECHARGE) // 转让入账
                .setActiveCodeNum(transfer.getActiveCodeNum()) //需要处理的激活码数量
                .setActiveCodeAlgorithmType(AlgorithmType.ADD)
                .setUserId(otUser.getUserId()) // 用户id
                .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                .setWalletAmount(BigDecimal.ZERO); // 预存金额
        AjaxResult ajaxResult = walletService.transfer(source, target);

        if (ajaxResult.isSuccess()) {
            return AjaxResult.success(MessageUtils.message("node.transfer.success"));
        }
        return AjaxResult.error(MessageUtils.message("node.transfer.failed"));
    }
    /**
     * 开卡码发放
     *
     * @param transfer 节点划转请求对象
     * @return AjaxResult
     */
    @PostMapping("/codeIssuance")
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('wallet:user:codeIssuance')")
    public AjaxResult codeIssuance(@Validated @RequestBody CodeTransferDto transfer) {

        // 公司账户id
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        SysUser fromUser = userService.selectUserById(accountId);


        SysUser otUser = userService.selectUserByUserName(transfer.getToUsername());

        if(otUser == null){
            return AjaxResult.error(MessageUtils.message("node.user.not.exist"));
        }
        Wallet wallet = walletService.findByUserId(otUser.getUserId());

        //  划转方
        Wallet currentUser = walletService.findByUserId(fromUser.getUserId());
        String currentNodeLevel = currentUser.getNodeLevel();

        //可划转最大数量
        Integer totalRightScardNum = 0;
        if(currentNodeLevel.equals("00")){
            NodeConfig currentNodeConfig= nodeService.findNodeConfigByNodeLevel(currentNodeLevel);
            totalRightScardNum=currentNodeConfig.getTotalRightScardNum();
        }else {
            NodeConfig previousNode = nodeService.findPreviousNode(currentUser.getNodeLevel());
            totalRightScardNum=previousNode.getTotalRightScardNum();
        }
        Integer userNum=wallet.getTotalExchangeSilverActiveCodeNum()+wallet.getSilverActiveCodeNum();
        logger.info("划转接收方持有银卡数量："+userNum);
        if (userNum+transfer.getTotalExchangeSilverActiveCodeNum() > totalRightScardNum){
            // 收款账户可划转数量不足
            //  return AjaxResult.error(MessageUtils.message("node.transfer.insufficient.num"));
        }
        //判断是否是个人用户
        if(wallet.getNodeLevel().equals("00") && currentUser.getNodeLevel().equals("00")){
            logger.info("个人用户向个人用户划转");
        }else {
            // 判断转入节点等级是否小于当前用户的节点等级
            if (!NodeLevel.isHigher(currentUser.getNodeLevel(),wallet.getNodeLevel())){
                return AjaxResult.error(MessageUtils.message("node.transfer.level"));
            }
        }



        // 判断剩余的激活码是否足够
        if (!walletService.checkCardBalance(transfer)){
            return AjaxResult.error(MessageUtils.message("wallet.activitiCode.insufficient.num"));
        }
        //扣款
        VccReqDto source = new VccReqDto();
        source
                .setCodeFromUserId(fromUser.getUserId()) // 扣款id
                .setCodeToUserId(otUser.getUserId()) // 收款id
                .setCardLevel(transfer.getCardLevel()) // 激活码的类型
                .setTxnTypeCode(TxnType.TRANSFER_EXPENDITURE) // 转让支出
                .setActiveCodeNum(transfer.getActiveCodeNum()) //需要处理的激活码数量
                .setActiveCodeAlgorithmType(AlgorithmType.SUB)
                .setUserId(fromUser.getUserId()) // 用户id
                .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                .setWalletAmount(BigDecimal.ZERO); // 预存金额

        // 收款
        VccReqDto target = new VccReqDto();
        target
                .setCodeFromUserId(fromUser.getUserId()) // 扣款id
                .setCodeToUserId(otUser.getUserId()) // 收款id
                .setCardLevel(transfer.getCardLevel()) // 激活码的类型
                .setTxnTypeCode(TxnType.TRANSFER_RECHARGE) // 转让入账
                .setActiveCodeNum(transfer.getActiveCodeNum()) //需要处理的激活码数量
                .setActiveCodeAlgorithmType(AlgorithmType.ADD)
                .setUserId(otUser.getUserId()) // 用户id
                .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                .setWalletAmount(BigDecimal.ZERO); // 预存金额
        AjaxResult ajaxResult = walletService.transfer(source, target);

        if (ajaxResult.isSuccess()) {
            return AjaxResult.success(MessageUtils.message("node.transfer.success"));
        }
        return AjaxResult.error(MessageUtils.message("node.transfer.failed"));
    }
    /**
     * 解除交易受限
     * @return
     */

    @PostMapping("/releaseTradeLimit")
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('wallet:release:trade')")
    public AjaxResult releaseTradeLimit(@RequestParam("userId")Long userId) {
        userService.updateTradeLimitExpired(userId, new Date());
        appCache.releaseLimit("release_trade_limit","?userId="+userId);
        return AjaxResult.success();
    }

    /**
     * 解除交易受限
     * @return
     */

    @PostMapping("/releaseLoginLimit")
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('wallet:release:login')")
    public AjaxResult releaseLoginLimit(@RequestParam("userId")Long userId) {
        SysUser user = userService.selectUserById(userId);
        userService.updateLoginLimitExpired(userId, new Date());
        appCache.releaseLimit("release_login_limit","?username="+user.getUserName());
        return AjaxResult.success();
    }


    /**
     * usd兑换 其他加密货币
     * @param usdExchangeOutDto
     * @return
     */
    @PostMapping("/usdExchangeOut")
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('wallet:user:usdExchangeOut')")
    public AjaxResult usdExchangeOut(@RequestBody @Valid UsdExchangeOutDto usdExchangeOutDto) {
        return walletService.usdExchangeOut(usdExchangeOutDto);
    }
}
