package com.meta.web.controller.app;

import com.meta.common.annotation.RepeatSubmit;
import com.meta.common.core.controller.BaseController;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.domain.entity.SysDictData;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.enums.app.AlgorithmType;
import com.meta.common.enums.app.FreezeType;
import com.meta.common.enums.app.NodeLevel;
import com.meta.common.enums.app.TxnType;
import com.meta.common.utils.DictUtils;
import com.meta.common.utils.MessageUtils;
import com.meta.system.domain.Wallet;
import com.meta.system.domain.app.NodeConfig;
import com.meta.system.dto.UsdExchangeOutDto;
import com.meta.system.dto.VccReqDto;
import com.meta.system.dto.node.CodeTransferDto;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.ISysUserService;
import com.meta.system.service.NodeService;
import com.meta.system.service.WalletService;
import com.meta.system.config.EncryptionProperties;
import com.meta.system.uitls.SimplePrivateKeyEncryptionUtil;
import com.meta.system.vo.WalletVo;
import com.meta.web.dto.TransferDto;
import com.meta.web.utils.AppCache;
import com.meta.web.vo.UserNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/wallet")
public class WalletController extends BaseController {
    @Autowired
    private WalletService walletService;

    @Autowired
    private ISysUserService userService;

    @Resource
    private NodeService nodeService;

    @Autowired
    private AppCache appCache;
    @Autowired
    private ISysConfigService configService;

    @Autowired
    private EncryptionProperties encryptionProperties;

    /**
     * 获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('wallet:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(String nickName,String username,String status,String startDate,String endDate,String nodeLevel,String channelName,String limitFlag) {
        Page<WalletVo> page = walletService.selectUserList(nickName,username,status,startDate,endDate,nodeLevel,channelName,limitFlag);
        return getDataTable(page);
    }

    /**
     * 节点升级/降级
     */
    @PreAuthorize("@ss.hasPermi('wallet:user:upgrade')")
    @PutMapping("/upgrade")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult upgrade(@RequestBody @Validated UserNode userNode) {
        List<SysDictData> nodeType = DictUtils.getDictCache("node_type");
        if (nodeType != null && !nodeType.isEmpty()){
            List<String> nodeLevels = nodeType.stream().map(SysDictData::getDictValue).collect(Collectors.toList());
            // 判断节点等级是否合规
            if(nodeLevels.contains(userNode.getNodeLevel())){
                walletService.upgrade(userNode.getUserId(),userNode.getNodeLevel());
                return AjaxResult.success();
            }
        }
        return AjaxResult.error("节点等级不合规");
    }


    /**
     * 转账
     */
    @PostMapping("/usdTransfer")
    @PreAuthorize("@ss.hasPermi('wallet:user:usdTransfer')")
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    public AjaxResult usdTransfer(@RequestBody @Validated TransferDto transfer){
        SysUser fromUser = userService.selectUserById(transfer.getFromUserId());
        if(fromUser == null){
            return AjaxResult.error("转出用户不存在!");
        }
        String email = fromUser.getEmail();
        if (transfer.getToEmail().equals(email)){
            return AjaxResult.error(MessageUtils.message("wallet.email.can.not.yourself"));
        }

        SysUser user = userService.selectUserByEmail(transfer.getToEmail());
        if (user == null){
            return AjaxResult.error(MessageUtils.message("mail.not.exists"));
        }

        //扣款
        VccReqDto source = new VccReqDto(
                fromUser.getUserId(),
                AlgorithmType.SUB,
                transfer.getAmount(),
                FreezeType.NO,
                TxnType.INTERNAL_TRANSFER_OUT_C,
                fromUser.getUserId(),
                user.getUserId(),
                "",
                BigDecimal.ZERO,null);

        // 收款
        VccReqDto target = new VccReqDto(
                user.getUserId(),
                AlgorithmType.ADD,
                transfer.getAmount(),
                FreezeType.NO,
                TxnType.INTERNAL_TRANSFER_IN_D,
                fromUser.getUserId(),
                user.getUserId(),
                "",
                BigDecimal.ZERO,null
        );

        AjaxResult ajaxResult = walletService.transfer(source,target);
        if (ajaxResult.isSuccess()) {
            return AjaxResult.success(MessageUtils.message("wallet.transfer.success"));
        }
        return AjaxResult.success(MessageUtils.message("wallet.transfer.failed"));
    }


    /**
     * 节点划转
     *
     * @param transfer 节点划转请求对象
     * @return AjaxResult
     */
    @PostMapping("/codeTransfer")
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('wallet:user:codeTransfer')")
    public AjaxResult codeTransfer(@Validated @RequestBody CodeTransferDto transfer) {

        SysUser fromUser = userService.selectUserById(transfer.getFromUserId());
        if(fromUser == null){
            return AjaxResult.error(MessageUtils.message("node.user.not.exist"));
        }

        SysUser otUser = userService.selectUserByUserName(transfer.getToUsername());

        if(otUser == null){
            return AjaxResult.error(MessageUtils.message("node.user.not.exist"));
        }
        Wallet wallet = walletService.findByUserId(otUser.getUserId());

        //  划转方
        Wallet currentUser = walletService.findByUserId(fromUser.getUserId());
        String currentNodeLevel = currentUser.getNodeLevel();

        //可划转最大数量
        Integer totalRightScardNum = 0;
        if(currentNodeLevel.equals("00")){
            NodeConfig currentNodeConfig= nodeService.findNodeConfigByNodeLevel(currentNodeLevel);
            totalRightScardNum=currentNodeConfig.getTotalRightScardNum();
        }else {
            NodeConfig previousNode = nodeService.findPreviousNode(currentUser.getNodeLevel());
            totalRightScardNum=previousNode.getTotalRightScardNum();
        }
        Integer userNum=wallet.getTotalExchangeSilverActiveCodeNum()+wallet.getSilverActiveCodeNum();
        logger.info("划转接收方持有银卡数量："+userNum);
        if (userNum+transfer.getTotalExchangeSilverActiveCodeNum() > totalRightScardNum){
            // 收款账户可划转数量不足
          //  return AjaxResult.error(MessageUtils.message("node.transfer.insufficient.num"));
        }
        //判断是否是个人用户
        if(wallet.getNodeLevel().equals("00") && currentUser.getNodeLevel().equals("00")){
            logger.info("个人用户向个人用户划转");
        }else {
            // 判断转入节点等级是否小于当前用户的节点等级
            if (!NodeLevel.isHigher(currentUser.getNodeLevel(),wallet.getNodeLevel())){
                return AjaxResult.error(MessageUtils.message("node.transfer.level"));
            }
        }



        // 判断剩余的激活码是否足够
        if (!walletService.checkCardBalance(transfer)){
            return AjaxResult.error(MessageUtils.message("wallet.activitiCode.insufficient.num"));
        }
        //扣款
        VccReqDto source = new VccReqDto();
        source
                .setCodeFromUserId(fromUser.getUserId()) // 扣款id
                .setCodeToUserId(otUser.getUserId()) // 收款id
                .setCardLevel(transfer.getCardLevel()) // 激活码的类型
                .setTxnTypeCode(TxnType.TRANSFER_EXPENDITURE) // 转让支出
                .setActiveCodeNum(transfer.getActiveCodeNum()) //需要处理的激活码数量
                .setActiveCodeAlgorithmType(AlgorithmType.SUB)
                .setUserId(fromUser.getUserId()) // 用户id
                .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                .setWalletAmount(BigDecimal.ZERO); // 预存金额

        // 收款
        VccReqDto target = new VccReqDto();
        target
                .setCodeFromUserId(fromUser.getUserId()) // 扣款id
                .setCodeToUserId(otUser.getUserId()) // 收款id
                .setCardLevel(transfer.getCardLevel()) // 激活码的类型
                .setTxnTypeCode(TxnType.TRANSFER_RECHARGE) // 转让入账
                .setActiveCodeNum(transfer.getActiveCodeNum()) //需要处理的激活码数量
                .setActiveCodeAlgorithmType(AlgorithmType.ADD)
                .setUserId(otUser.getUserId()) // 用户id
                .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                .setWalletAmount(BigDecimal.ZERO); // 预存金额
        AjaxResult ajaxResult = walletService.transfer(source, target);

        if (ajaxResult.isSuccess()) {
            return AjaxResult.success(MessageUtils.message("node.transfer.success"));
        }
        return AjaxResult.error(MessageUtils.message("node.transfer.failed"));
    }
    /**
     * 开卡码发放
     *
     * @param transfer 节点划转请求对象
     * @return AjaxResult
     */
    @PostMapping("/codeIssuance")
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('wallet:user:codeIssuance')")
    public AjaxResult codeIssuance(@Validated @RequestBody CodeTransferDto transfer) {

        // 公司账户id
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        SysUser fromUser = userService.selectUserById(accountId);


        SysUser otUser = userService.selectUserByUserName(transfer.getToUsername());

        if(otUser == null){
            return AjaxResult.error(MessageUtils.message("node.user.not.exist"));
        }
        Wallet wallet = walletService.findByUserId(otUser.getUserId());

        //  划转方
        Wallet currentUser = walletService.findByUserId(fromUser.getUserId());
        String currentNodeLevel = currentUser.getNodeLevel();

        //可划转最大数量
        Integer totalRightScardNum = 0;
        if(currentNodeLevel.equals("00")){
            NodeConfig currentNodeConfig= nodeService.findNodeConfigByNodeLevel(currentNodeLevel);
            totalRightScardNum=currentNodeConfig.getTotalRightScardNum();
        }else {
            NodeConfig previousNode = nodeService.findPreviousNode(currentUser.getNodeLevel());
            totalRightScardNum=previousNode.getTotalRightScardNum();
        }
        Integer userNum=wallet.getTotalExchangeSilverActiveCodeNum()+wallet.getSilverActiveCodeNum();
        logger.info("划转接收方持有银卡数量："+userNum);
        if (userNum+transfer.getTotalExchangeSilverActiveCodeNum() > totalRightScardNum){
            // 收款账户可划转数量不足
            //  return AjaxResult.error(MessageUtils.message("node.transfer.insufficient.num"));
        }
        //判断是否是个人用户
        if(wallet.getNodeLevel().equals("00") && currentUser.getNodeLevel().equals("00")){
            logger.info("个人用户向个人用户划转");
        }else {
            // 判断转入节点等级是否小于当前用户的节点等级
            if (!NodeLevel.isHigher(currentUser.getNodeLevel(),wallet.getNodeLevel())){
                return AjaxResult.error(MessageUtils.message("node.transfer.level"));
            }
        }



        // 判断剩余的激活码是否足够
        if (!walletService.checkCardBalance(transfer)){
            return AjaxResult.error(MessageUtils.message("wallet.activitiCode.insufficient.num"));
        }
        //扣款
        VccReqDto source = new VccReqDto();
        source
                .setCodeFromUserId(fromUser.getUserId()) // 扣款id
                .setCodeToUserId(otUser.getUserId()) // 收款id
                .setCardLevel(transfer.getCardLevel()) // 激活码的类型
                .setTxnTypeCode(TxnType.TRANSFER_EXPENDITURE) // 转让支出
                .setActiveCodeNum(transfer.getActiveCodeNum()) //需要处理的激活码数量
                .setActiveCodeAlgorithmType(AlgorithmType.SUB)
                .setUserId(fromUser.getUserId()) // 用户id
                .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                .setWalletAmount(BigDecimal.ZERO); // 预存金额

        // 收款
        VccReqDto target = new VccReqDto();
        target
                .setCodeFromUserId(fromUser.getUserId()) // 扣款id
                .setCodeToUserId(otUser.getUserId()) // 收款id
                .setCardLevel(transfer.getCardLevel()) // 激活码的类型
                .setTxnTypeCode(TxnType.TRANSFER_RECHARGE) // 转让入账
                .setActiveCodeNum(transfer.getActiveCodeNum()) //需要处理的激活码数量
                .setActiveCodeAlgorithmType(AlgorithmType.ADD)
                .setUserId(otUser.getUserId()) // 用户id
                .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                .setWalletAmount(BigDecimal.ZERO); // 预存金额
        AjaxResult ajaxResult = walletService.transfer(source, target);

        if (ajaxResult.isSuccess()) {
            return AjaxResult.success(MessageUtils.message("node.transfer.success"));
        }
        return AjaxResult.error(MessageUtils.message("node.transfer.failed"));
    }
    /**
     * 解除交易受限
     * @return
     */

    @PostMapping("/releaseTradeLimit")
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('wallet:release:trade')")
    public AjaxResult releaseTradeLimit(@RequestParam("userId")Long userId) {
        userService.updateTradeLimitExpired(userId, new Date());
        appCache.releaseLimit("release_trade_limit","?userId="+userId);
        return AjaxResult.success();
    }

    /**
     * 解除交易受限
     * @return
     */

    @PostMapping("/releaseLoginLimit")
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('wallet:release:login')")
    public AjaxResult releaseLoginLimit(@RequestParam("userId")Long userId) {
        SysUser user = userService.selectUserById(userId);
        userService.updateLoginLimitExpired(userId, new Date());
        appCache.releaseLimit("release_login_limit","?username="+user.getUserName());
        return AjaxResult.success();
    }


    /**
     * usd兑换 其他加密货币
     * @param usdExchangeOutDto
     * @return
     */
    @PostMapping("/usdExchangeOut")
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('wallet:user:usdExchangeOut')")
    public AjaxResult usdExchangeOut(@RequestBody @Valid UsdExchangeOutDto usdExchangeOutDto) {
        return walletService.usdExchangeOut(usdExchangeOutDto);
    }

    /**
     * 解密私钥
     * 传入加密后的密文，返回解密后的明文数据
     *
     * @param encryptedData 加密后的密文
     * @return 解密后的明文数据
     */
    @PostMapping("/decrypt")
    public AjaxResult decryptPrivateKey(@RequestParam String encryptedData) {
        try {
            // 参数验证
            if (encryptedData == null || encryptedData.trim().isEmpty()) {
                return AjaxResult.error("加密数据不能为空");
            }

            // 执行解密
            String decryptedData = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encryptedData.trim());

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("encryptedData", encryptedData);
            result.put("decryptedData", decryptedData);
            result.put("algorithm", "AES-GCM");
            result.put("success", true);

            logger.info("私钥解密成功，密文长度: {}", encryptedData.length());
            return AjaxResult.success("解密成功", result);

        } catch (Exception e) {
            logger.error("私钥解密失败，密文: {}", encryptedData, e);

            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("encryptedData", encryptedData);
            errorResult.put("error", e.getMessage());
            errorResult.put("success", false);

            return AjaxResult.error("解密失败: " + e.getMessage(), errorResult);
        }
    }

    /**
     * 智能解密私钥（推荐使用）
     * 自动判断是否需要解密，如果是明文则直接返回
     *
     * @param data 可能是加密或未加密的数据
     * @return 解密后的明文数据
     */
    @PostMapping("/smartDecrypt")
    public AjaxResult smartDecrypt( @RequestParam String data) {
        try {
            // 参数验证
            if (data == null || data.trim().isEmpty()) {
                return AjaxResult.error("数据不能为空");
            }

            String trimmedData = data.trim();

            // 检查是否已加密
            boolean isEncrypted = SimplePrivateKeyEncryptionUtil.isEncrypted(trimmedData);

            // 智能处理
            String resultData = SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(trimmedData);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("originalData", data);
            result.put("isEncrypted", isEncrypted);
            result.put("resultData", resultData);
            result.put("algorithm", "AES-GCM");
            result.put("processed", isEncrypted ? "解密" : "直接返回");
            result.put("success", true);

            logger.info("智能解密处理完成，是否加密: {}, 数据长度: {}", isEncrypted, data.length());
            return AjaxResult.success("处理成功", result);

        } catch (Exception e) {
            logger.error("智能解密失败，数据: {}", data, e);

            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("originalData", data);
            errorResult.put("error", e.getMessage());
            errorResult.put("success", false);

            return AjaxResult.error("处理失败: " + e.getMessage(), errorResult);
        }
    }

    /**
     * 批量解密私钥
     *
     * @param encryptedDataList 加密数据列表
     * @return 解密结果列表
     */
    @PostMapping("/batchDecrypt")
    public AjaxResult batchDecrypt( @RequestBody String[] encryptedDataList) {
        try {
            if (encryptedDataList == null || encryptedDataList.length == 0) {
                return AjaxResult.error("加密数据列表不能为空");
            }

            Map<String, Object> results = new HashMap<>();
            Map<String, String> successResults = new HashMap<>();
            Map<String, String> errorResults = new HashMap<>();

            for (String encryptedData : encryptedDataList) {
                if (encryptedData != null && !encryptedData.trim().isEmpty()) {
                    try {
                        String decrypted = SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(encryptedData.trim());
                        successResults.put(encryptedData, decrypted);
                    } catch (Exception e) {
                        errorResults.put(encryptedData, e.getMessage());
                        logger.warn("批量解密中单个数据失败: {}", encryptedData, e);
                    }
                }
            }

            results.put("success", successResults);
            results.put("errors", errorResults);
            results.put("total", encryptedDataList.length);
            results.put("successCount", successResults.size());
            results.put("errorCount", errorResults.size());
            results.put("algorithm", "AES-GCM");

            logger.info("批量解密完成，总数: {}, 成功: {}, 失败: {}",
                    encryptedDataList.length, successResults.size(), errorResults.size());

            return AjaxResult.success("批量解密完成", results);

        } catch (Exception e) {
            logger.error("批量解密失败", e);
            return AjaxResult.error("批量解密失败: " + e.getMessage());
        }
    }

    /**
     * 获取加密配置状态
     *
     * @return 加密配置信息
     */
    @GetMapping("/encryptionConfig")
    public AjaxResult getEncryptionConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("enabled", encryptionProperties.isEnabled());
            config.put("algorithm", encryptionProperties.getAlgorithm());
            config.put("encoding", encryptionProperties.getEncoding());
            config.put("secretKeyLength", encryptionProperties.getSecretKey().length());
            config.put("configSource", "yml配置文件");
            config.put("version", "1.0.0");

            return AjaxResult.success("获取配置成功", config);

        } catch (Exception e) {
            logger.error("获取加密配置失败", e);
            return AjaxResult.error("获取配置失败: " + e.getMessage());
        }
    }

}
