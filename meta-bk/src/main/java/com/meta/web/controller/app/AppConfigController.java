package com.meta.web.controller.app;

import com.meta.common.annotation.RepeatSubmit;
import com.meta.common.core.controller.BaseController;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.utils.DateUtils;
import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.request.IDelete;
import com.meta.common.utils.request.IInsert;
import com.meta.common.utils.request.IUpdate;
import com.meta.system.domain.app.CardConfig;
import com.meta.system.domain.app.ExchangeRate;
import com.meta.system.domain.app.NodeConfig;
import com.meta.web.dto.CardConfigDto;
import com.meta.web.dto.ExchangeRateDto;
import com.meta.web.dto.NodeConfigDto;
import com.meta.web.service.AppConfigService;
import com.meta.system.uitls.cardConfig.AnnualFee;
import com.meta.system.uitls.cardConfig.Fee;
import com.meta.system.uitls.cardConfig.OpenFee;
import com.meta.system.uitls.cardConfig.Quota;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/config")
public class AppConfigController extends BaseController {

    @Resource
    private AppConfigService configService;

    /**
     * 获取卡配置文件列表(分页)
     */
    @GetMapping("/cardConfigList")
    @PreAuthorize("@ss.hasPermi('card:config:list')")
    public TableDataInfo getCardConfigList(CardConfigDto configDto) {
        return getDataTable(configService.getCardConfigList(configDto));
    }

    /**
     * 获取卡配置详情
     * @param cardCfgId id
     * @return
     */
    @GetMapping("/cardConfig")
    @PreAuthorize("@ss.hasPermi('card:config:detail')")
    public AjaxResult cardConfig(@RequestParam("cardCfgId") Long cardCfgId) {
        return AjaxResult.success(configService.selectCardById(cardCfgId));
    }

    /**
     * 新增卡配置
     */
    @PostMapping("/addCardConfig")
    @PreAuthorize("@ss.hasPermi('card:config:add')")
    @RepeatSubmit
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addCardConfig(@RequestBody @Validated(IInsert.class) CardConfig config) {
        config.setCreateBy(SecurityUtils.getUsername());
        config.setCreateTime(DateUtils.getNowDate());
        config.setUpdateBy(SecurityUtils.getUsername());
        config.setUpdateTime(DateUtils.getNowDate());
        configService.saveCardConfig(config);
        return AjaxResult.success();
    }

    /**
     * 删除卡配置
     */
    @PutMapping("/deleteCardConfig")
    @PreAuthorize("@ss.hasPermi('card:config:delete')")
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    public AjaxResult deleteCardConfig(@RequestBody @Validated(IDelete.class) CardConfig config) {
        configService.deleteCardConfig(config.getCardCfgId());
        return AjaxResult.success();
    }

    /**
     * 修改卡配置(配额:1:单笔限额,2:单日限额,3:单月限额,4:年度限额)
     */
    @PutMapping("/updateQuotaForTheCard")
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("@ss.hasPermi('card:config:update:quota')")
    @RepeatSubmit
    public AjaxResult updateQuotaFeeForTheCard(@RequestBody @Validated(Quota.class) CardConfig config) {
        CardConfig _config = configService.selectCardById(config.getCardCfgId());
        _config.setTxnSingleLimit(config.getTxnSingleLimit()); // 单笔
        _config.setTxnDayLimit(config.getTxnDayLimit()); // 单日
        _config.setTxnMonthLimit(config.getTxnMonthLimit()); // 单月
        _config.setTxnYearLimit(config.getTxnYearLimit()); // 年度
        _config.setUpdateBy(SecurityUtils.getUsername());
        _config.setUpdateTime(DateUtils.getNowDate());
        configService.updateCardConfig(_config);
        return AjaxResult.success();
    }

    /**
     * 年费设置
     */
    @PutMapping("/updateAnnualFeeForTheCard")
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("@ss.hasPermi('card:config:update:annualFee')")
    @RepeatSubmit
    public AjaxResult updateAnnualFeeForTheCard(@RequestBody @Validated(AnnualFee.class) CardConfig config) {
        CardConfig _config = configService.selectCardById(config.getCardCfgId());
        _config.setCardYearFee(config.getCardYearFee()); // 年费
        _config.setCardMonFee(config.getCardMonFee()); // 月费
        _config.setUpdateBy(SecurityUtils.getUsername());
        _config.setUpdateTime(DateUtils.getNowDate());
        configService.updateCardConfig(_config);
        return AjaxResult.success();
    }

    /**
     * 开卡费
     */
    @PutMapping("/updateOpenFeeForTheCard")
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("@ss.hasPermi('card:config:update:openFee')")
    @RepeatSubmit
    public AjaxResult updateOpenFeeForTheCard(@RequestBody @Validated(OpenFee.class) CardConfig config) {
        CardConfig _config = configService.selectCardById(config.getCardCfgId());
        _config.setCardFee(config.getCardFee()); // 开卡费
        _config.setUpdateBy(SecurityUtils.getUsername());
        _config.setUpdateTime(DateUtils.getNowDate());
        configService.updateCardConfig(_config);
        return AjaxResult.success();
    }

    /**
     * 手续费-1:充值,2:提现,3:消费
     */
    @PutMapping("/updateFeeForTheCard")
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("@ss.hasPermi('card:config:update:fee')")
    @RepeatSubmit
    public AjaxResult updateFeeForTheCard(@RequestBody @Validated(Fee.class) CardConfig config) {
        CardConfig _config = configService.selectCardById(config.getCardCfgId());
        _config.setCardRechargeFee(config.getCardRechargeFee()); // 充值手续费
        _config.setCardConsumeFee(config.getCardConsumeFee()); // 提现手续费
        _config.setCardWithdrawalFee(config.getCardWithdrawalFee()); // 消费手续费
        _config.setUpdateBy(SecurityUtils.getUsername());
        _config.setUpdateTime(DateUtils.getNowDate());
        configService.updateCardConfig(_config);
        return AjaxResult.success();
    }




    /**
     * 获取节点配置文件列表(分页)
     */
    @GetMapping("/nodeConfigList")
    @PreAuthorize("@ss.hasPermi('node:config:list')")
    public TableDataInfo getNodeConfigList(NodeConfigDto configDto) {
        return getDataTable(configService.getNodeConfigList(configDto));
    }

    /**
     * 获取节点配置详情
     * @param nodeId id
     * @return
     */
    @GetMapping("/nodeConfig")
    @PreAuthorize("@ss.hasPermi('node:config:detail')")
    public AjaxResult nodeConfig(@RequestParam("nodeId") Integer nodeId) {
        return AjaxResult.success(configService.selectNodeById(nodeId));
    }

    /**
     * 删除节点配置
     */
    @PutMapping("/deleteNodeConfig")
    @PreAuthorize("@ss.hasPermi('node:config:delete')")
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    public AjaxResult deleteNodeConfig(@RequestBody @Validated(IDelete.class) NodeConfig config) {
        configService.deleteNodeConfig(config.getNodeId());
        return AjaxResult.success();
    }


    /**
     * 新增节点配置
     */
    @PostMapping("/addNodeConfig")
    @PreAuthorize("@ss.hasPermi('node:config:add')")
    @RepeatSubmit
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addNodeConfig(@RequestBody @Validated(IInsert.class) NodeConfig config) {
        config.setCreateBy(SecurityUtils.getUsername());
        config.setCreateTime(DateUtils.getNowDate());
        config.setUpdateBy(SecurityUtils.getUsername());
        config.setUpdateTime(DateUtils.getNowDate());
        configService.saveNodeConfig(config);
        return AjaxResult.success();
    }

    /**
     * 修改节点配置
     */
    @PutMapping("/updateNodeConfig")
    @PreAuthorize("@ss.hasPermi('node:config:update')")
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    public AjaxResult updateNodeConfig(@RequestBody @Validated(IUpdate.class) NodeConfig config) {
        config.setUpdateBy(SecurityUtils.getUsername());
        config.setUpdateTime(DateUtils.getNowDate());
        configService.updateNodeConfig(config);
        return AjaxResult.success();
    }


    /**
     * 获取钱包汇率配置文件列表(分页)
     */
    @GetMapping("/exchangeRateList")
    @PreAuthorize("@ss.hasPermi('exchange:rate:list')")
    public TableDataInfo getExchangeRateLis(ExchangeRateDto rateDto) {
        return getDataTable(configService.getExchangeRateLis(rateDto));
    }

    /**
     * 获取钱包汇率配置详情
     * @param exchangeId id
     * @return
     */
    @GetMapping("/exchangeRate")
    @PreAuthorize("@ss.hasPermi('exchange:rate:detail')")
    public AjaxResult exchangeRate(@RequestParam("exchangeId") Integer exchangeId) {
        return AjaxResult.success(configService.selectExchangeById(exchangeId));
    }

    /**
     * 新增钱包汇率配置
     */
    @PostMapping("/addExchangeRate")
    @PreAuthorize("@ss.hasPermi('exchange:rate:add')")
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    public AjaxResult addExchangeRate(@RequestBody @Validated(IInsert.class) ExchangeRate rate) {
        rate.setCreatedBy(SecurityUtils.getUsername());
        rate.setCreateTime(DateUtils.getNowDate());
        rate.setUpdatedBy(SecurityUtils.getUsername());
        rate.setUpdateTime(DateUtils.getNowDate());
        configService.saveExchangeRate(rate);
        return AjaxResult.success();
    }





    /**
     * 删除钱包汇率配置
     */
    @PutMapping("/deleteExchangeRate")
    @PreAuthorize("@ss.hasPermi('exchange:rate:delete')")
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    public AjaxResult deleteExchangeRate(@RequestBody @Validated(IDelete.class) ExchangeRate rate) {
        configService.deleteExchangeRate(rate.getExchangeId());
        return AjaxResult.success();
    }



    /**
     * 修改钱包汇率配置
     */
    @PutMapping("/updateExchangeRate")
    @PreAuthorize("@ss.hasPermi('exchange:rate:update')")
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    public AjaxResult updateExchangeRate(@RequestBody @Validated(IUpdate.class) ExchangeRate rate) {
        rate.setUpdatedBy(SecurityUtils.getUsername());
        rate.setUpdateTime(DateUtils.getNowDate());
        configService.updateExchangeRate(rate);
        return AjaxResult.success();
    }
}

