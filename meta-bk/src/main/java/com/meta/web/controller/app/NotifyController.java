package com.meta.web.controller.app;

import com.meta.common.constant.HttpStatus;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.core.page.TableSupport;
import com.meta.system.dto.NotifyDto;
import com.meta.system.service.MetaPushDataService;
import com.meta.system.uitls.NotifyDataUtils;
import com.meta.web.config.MetaApiConfig;
import com.meta.web.entity.Notify;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/notify")
public class NotifyController {

    @Autowired
    private MetaPushDataService metaPushDataService;

    @Autowired
    private NotifyDataUtils notifyDataUtils;


//    /**
//     * 通知列表
//     */
//    @GetMapping("/notifyPage")
//    @PreAuthorize("@ss.hasPermi('notify:list')")
//    public TableDataInfo couponConfigPage(NotifyDto notifyDto) {
//        return getDataTable(notifyService.getNotifyList(notifyDto));
//    }

    /**
     * 通知列表
     */
    @GetMapping("/notifyPage")
    @PreAuthorize("@ss.hasPermi('notify:list')")
    public TableDataInfo couponConfigPage(NotifyDto notifyDto) {
        return getDataTable(metaPushDataService.getNotifyList(notifyDto));
    }


    @PostMapping("/sendNotify")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult sendNotify(@RequestBody Notify notify) throws Exception {
        boolean deal = notifyDataUtils.deal(notify.getId().longValue(), MetaApiConfig.privateStr, MetaApiConfig.version);
        if (deal) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }

    }

    /**
     * 响应请求分页数据
     */
    protected TableDataInfo getDataTable(Page<?> page) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(page.getContent());
        rspData.setTotal(page.getTotalElements());
        rspData.setSize(pageReq.getPageSize());
        rspData.setPages(page.getTotalPages());
        rspData.setCurrent(pageReq.getPageNo() + 1);
        return rspData;
    }

}
