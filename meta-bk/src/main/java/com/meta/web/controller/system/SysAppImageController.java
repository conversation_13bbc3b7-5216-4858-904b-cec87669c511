package com.meta.web.controller.system;

import com.meta.common.annotation.Log;
import com.meta.common.core.controller.BaseController;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.domain.entity.SysDictData;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.enums.BusinessType;
import com.meta.system.service.ISysDictDataService;
import com.meta.web.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * app广告图片 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/appImage")
public class SysAppImageController extends BaseController {

    @Autowired
    private ISysDictDataService sysDictDataService;


    /**
     * 获取app广告图片列表
     */

    @GetMapping("/list")
    public TableDataInfo list( String dictLabel, String dictType) {
        List<String> list = new ArrayList();
        String label_1="";
        String label_2="";
        if(StringUtil.isEmpty(dictType)){
            label_1="ad_info";
            label_2="node_ad_info";
        }else{
            label_1=dictType;
            label_2="";

        }
        if(StringUtil.isEmpty(dictLabel)){
            list.add(label_1);
            list.add(label_1+"_EN");
            list.add(label_1+"_TW");
            if(!StringUtil.isEmpty(label_2)){
                list.add(label_2);
                list.add(label_2+"_EN");
                list.add(label_2+"_TW");
            }
        }else if("ZW".equals(dictLabel)){
            list.add(label_1);
            if(!StringUtil.isEmpty(label_2)){
                list.add(label_2);
            }
        }else if("EN".equals(dictLabel)){
            list.add(label_1+"_EN");
            if(!StringUtil.isEmpty(label_2)){
                list.add(label_2+"_EN");
            }
        }else if("TW".equals(dictLabel)){
            list.add(label_1+"_TW");
            if(!StringUtil.isEmpty(label_2)){
                list.add(label_2+"_TW");
            }
        }




        Page<SysDictData> page = sysDictDataService.selectAppImageList(list);
        return getDataTable(page);
    }



}
