package com.meta.web.controller.system;

import com.meta.common.constant.Constants;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.domain.entity.SysMenu;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.domain.model.LoginBody;
import com.meta.common.core.domain.model.LoginUser;
import com.meta.common.core.domain.model.loginType.LoginByUsername;
import com.meta.common.core.redis.RedisCache;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.ServletUtils;
import com.meta.common.utils.StringUtils;
import com.meta.framework.security.authticator.GoogleAuthenticator;
import com.meta.framework.web.service.SysLoginService;
import com.meta.framework.web.service.SysPermissionService;
import com.meta.framework.web.service.TokenService;
import com.meta.system.domain.Wallet;
import com.meta.system.dto.RouterVo;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.ISysMenuService;
import com.meta.system.service.ISysUserService;
import com.meta.system.service.WalletService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController {
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private WalletService walletService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 登录方法
     *
     * @param passwd 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@Validated(LoginByUsername.class) @RequestBody LoginBody passwd) {

        //判断图形验证码是否正确
        String verifyKey = Constants.CAPTCHA_CODE_KEY + passwd.getUuid();
        String captcha = redisCache.getCacheObject(verifyKey);
        if(!passwd.getCode().equals(captcha)){
            return  AjaxResult.error(MessageUtils.message("user.jcaptcha.error"));
        }

        String authorization = sysConfigService.selectConfigByKey("google_authorization");
        if (StringUtils.isEmpty(authorization)) {
            // 判断谷歌验证码是否正确
            if (passwd.getGoogleCode() == null) {
                return AjaxResult.error(MessageUtils.message("google.jcaptcha.error2"));
            }
            GoogleAuthenticator g = new GoogleAuthenticator();
            long time = System.currentTimeMillis();
            SysUser sysUser = userService.selectUserByUserName(passwd.getUsername());
            if (sysUser == null) {
                return AjaxResult.error(MessageUtils.message("user.not.exists"));
            }
            String googleSecretKey = sysUser.getGoogleSecretKey();

            boolean result = g.check_code(googleSecretKey, passwd.getGoogleCode(), time);
            if (!result) return AjaxResult.error(MessageUtils.message("google.jcaptcha.error2"));
        }


        AjaxResult ajax = AjaxResult.success();
        passwd.setUserTypes(Arrays.asList("00", "02"));
        // 生成令牌
        String token = loginService.login(passwd);
        ajax.put(Constants.TOKEN, token);
        // 更新登录数据
        loginService.updateLoginInfo(passwd);
        return ajax;
    }


    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();

        HashMap map = new HashMap();
        map.put("usdBalance", "");
        map.put("freezeUsdBalacne", "");
        if("02".equals(user.getUserType())||"01".equals(user.getUserType())){
            Wallet w = walletService.findByUserId(user.getUserId());
            if (w != null) {
                map.put("usdBalance", w.getUsdBalance());
                map.put("freezeUsdBalacne", w.getFreezeUsdBalacne());
            }
        }

        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("wallet", map);
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        // 用户信息
        SysUser user = loginUser.getUser();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(user.getUserId());
        List<RouterVo> routerVos = menuService.buildMenus(menus);
        return AjaxResult.success(routerVos);
    }

    /**
     * 手动解锁账号密码
     */
    @PreAuthorize("@ss.hasPermi('system:user:unlock')")// TODO
    @PostMapping("/unLock")
    public AjaxResult unLock(@RequestParam("userId") Long userId) {
        loginService.unLock(userId);
        return AjaxResult.success();
    }
}
