package com.meta.web.controller.system;

import com.meta.common.annotation.RepeatSubmit;
import com.meta.common.core.domain.AjaxResult;
import com.meta.web.service.ChainService;
import com.meta.web.task.ChainTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @date 2024/08/05/15:46
 */
@RestController
@RequestMapping("/system/chain")
public class SysChain {

    @Autowired
    private ChainService chainService;

//    @PreAuthorize("@ss.hasPermi('system:chain:executeChain')")
    @GetMapping("/executeChain")
    @RepeatSubmit
    public AjaxResult executeChain(@RequestParam("start") Long start,@RequestParam("end") Long end) {
        if (start.compareTo(end)>0){
            return AjaxResult.error("开始高度不能大于结束高度");
        }
        chainService.executeChain(start,end);
        return AjaxResult.success();

    }

}
