package com.meta.web.controller.app;

import com.meta.common.constant.HttpStatus;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.core.page.TableSupport;
import com.meta.common.exception.CustomException;
import com.meta.common.utils.code.BusinessBizCode;
import com.meta.system.service.*;
import com.meta.system.vo.CollectRoportVo;
import com.meta.system.vo.TradeDayTotalVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/11/24/10:24
 */
@RestController
@RequestMapping("/report")
public class ReportController {
    @Autowired
    private BepTransactionsService bepTransactionsService;
    @Autowired
    private ArbTransactionsService arbTransactionsService;
    @Autowired
    private BaseTransactionsService baseTransactionsService;

    @Autowired
    private TrcTransactionsService trcTransactionsService;

    @Autowired
    private TxnService txnService;

    /**
     * 获取钱包归集列表
     */
    @PreAuthorize("@ss.hasPermi('report:collectList')")
    @GetMapping("/collectList")
    public TableDataInfo collectList(String coinNet, String startDate, String endDate) {


        if (coinNet != null && coinNet.equals("TRC20")) {
            Page<CollectRoportVo> page = trcTransactionsService.getReportPage(startDate, endDate);
            return getDataTable(page);
        } else if (coinNet != null && coinNet.equals("ERC20_ARB")) {
            Page<CollectRoportVo> page = arbTransactionsService.getReportPage(startDate, endDate);
            return getDataTable(page);
        } else if (coinNet != null && coinNet.equals("ERC20_BASE")) {
            Page<CollectRoportVo> page = baseTransactionsService.getReportPage(startDate, endDate);
            return getDataTable(page);
        } else if (coinNet != null && coinNet.equals("BEP20")) {
            Page<CollectRoportVo> page = bepTransactionsService.getReportPage(startDate, endDate);
            return getDataTable(page);
        }else{
            throw new CustomException("类型错误");
        }


    }

    /**
     * 客户每日金额统计报表
     */
    @PreAuthorize("@ss.hasPermi('report:tradeReportList')")
    @GetMapping("/tradeReportList")
    public TableDataInfo tradeReportList(String startDate, String endDate) {


        Page<TradeDayTotalVo> page = txnService.tradeReportPage(startDate, endDate);
        return getDataTable(page);


    }


    /**
     * 响应请求分页数据
     */
    protected TableDataInfo getDataTable(Page<?> page) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(page.getContent());
        rspData.setTotal(page.getTotalElements());
        rspData.setSize(pageReq.getPageSize());
        rspData.setPages(page.getTotalPages());
        rspData.setCurrent(pageReq.getPageNo() + 1);
        return rspData;
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows) {
        return rows == BusinessBizCode.OPTION_SUCCESS.getCode() ? AjaxResult.success() : AjaxResult.error();
    }
}
