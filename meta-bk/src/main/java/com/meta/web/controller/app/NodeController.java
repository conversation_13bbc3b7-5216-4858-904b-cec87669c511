package com.meta.web.controller.app;

/**
 * <AUTHOR>
 * @Date 2025/4/22
 */

import com.meta.common.core.controller.BaseController;
import com.meta.common.core.page.TableDataInfo;
import com.meta.system.service.UserCommissionService;
import com.meta.system.vo.AllTeamCommissionVo;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 节点
 */
@RestController
@RequestMapping("/node")
public class NodeController extends BaseController {

    @Resource
    private UserCommissionService userCommissionService;


    /**
     * 节点佣金列表
     *
     * @return
     */
    @GetMapping("/allTeamCommission")
    public TableDataInfo getCommissionTotal(AllTeamCommissionVo vo) {

        Page<AllTeamCommissionVo> page = userCommissionService.allTeamCommission(vo);
        return getDataTable(page);
    }

}
