package com.meta.web.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class TransferDto {

    @NotNull(message = "转出的用户id不能为空")
    private Long fromUserId;


    @NotEmpty(message = "对方邮箱不能为空")
    private String toEmail;


    @NotNull(message = "金额不能为空")
    private BigDecimal amount;
}
