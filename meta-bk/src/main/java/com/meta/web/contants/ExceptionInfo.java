package com.meta.web.contants;

import com.meta.common.utils.MessageUtils;


public class ExceptionInfo {
    private ExceptionInfo(){}

    /**
     * 账号余额不足
     */
    public static final String ACCOUNT_BALANCE_NO_ENOUGH = MessageUtils.message("wallet.account.insufficient.balance");
    /**
     * 銀卡余额不足
     */
    public static final String SILVER_ACTIVITI_CODE_NUM_NO_ENOUGH = MessageUtils.message("wallet.activitiCode.silver.insufficient.num");

    /**
     * 金卡余额不足
     */
    public static final String GOLDEN_ACTIVITI_CODE_NUM_NO_ENOUGH = MessageUtils.message("wallet.activitiCode.golden.insufficient.num");

    /**
     * 黑金卡余额不足
     */
    public static final String BLACKGOLDEN_ACTIVITI_CODE_NUM_NO_ENOUGH = MessageUtils.message("wallet.activitiCode.goldenBlack.insufficient.num");

}
