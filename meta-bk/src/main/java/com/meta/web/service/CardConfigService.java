package com.meta.web.service;

import com.meta.system.domain.app.CardConfig;

import java.util.List;

public interface CardConfigService {
    List<CardConfig> findCardConfigAll();

    CardConfig findCardConfigByCardCfgId(Long cardCfgId);

    CardConfig findCardConfigByCardTypeAndCardLevel(String cardType,String cardLevel);

    CardConfig findCardPartnerConfig(String sysId, String cardBin, String s);
}
