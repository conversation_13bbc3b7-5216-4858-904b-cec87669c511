package com.meta.web.service.impl;

import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableSupport;
import com.meta.common.utils.StringUtils;
import com.meta.system.dao.app.CardConfigDao;
import com.meta.system.dao.app.ExchangeRateDao;
import com.meta.system.dao.app.NodeConfigDao;
import com.meta.system.domain.app.CardConfig;
import com.meta.system.domain.app.ExchangeRate;
import com.meta.system.domain.app.NodeConfig;
import com.meta.web.dto.CardConfigDto;
import com.meta.web.dto.ExchangeRateDto;
import com.meta.web.dto.NodeConfigDto;
import com.meta.web.service.AppConfigService;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class AppConfigServiceImpl implements AppConfigService {
    @Resource
    private NodeConfigDao nodeConfigDao;

    @Resource
    private CardConfigDao cardConfigDao;

    @Resource
    private ExchangeRateDao exchangeRateDao;


    @Override
    public Page<CardConfig> getCardConfigList(CardConfigDto configDto) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        Pageable pageable = PageRequest.of(pageReq.getPageNo(), pageReq.getPageSize());
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("cardLabel", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("cardLevel", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("cardType", ExampleMatcher.GenericPropertyMatchers.exact())
                .withIgnoreNullValues();
        CardConfig config = new CardConfig();
        if (StringUtils.isNotNull(configDto)){
            if (StringUtils.isNotEmpty(configDto.getCardLevel())){
                config.setCardLevel(configDto.getCardLevel());
            }

            if (StringUtils.isNotEmpty(configDto.getCardLabel())){
                config.setCardLabel(configDto.getCardLabel());
            }

            if (StringUtils.isNotEmpty(configDto.getCardType())){
                config.setCardType(configDto.getCardType());
            }
        }

        Example<CardConfig> example = Example.of(config, matcher);
        return cardConfigDao.findAll(example,pageable);
    }

    @Override
    public Page<NodeConfig> getNodeConfigList(NodeConfigDto configDto) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        Pageable pageable = PageRequest.of(pageReq.getPageNo(), pageReq.getPageSize());
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("nodeName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("nodeLevel", ExampleMatcher.GenericPropertyMatchers.exact())
                .withIgnoreNullValues();
        NodeConfig config = new NodeConfig();

        if (StringUtils.isNotNull(configDto)){
            if (StringUtils.isNotEmpty(configDto.getNodeName())){
                config.setNodeName(configDto.getNodeName());
            }

            if (StringUtils.isNotEmpty(configDto.getNodeLevel())){
                config.setNodeLevel(configDto.getNodeLevel());
            }
        }


        Example<NodeConfig> example = Example.of(config, matcher);
        return nodeConfigDao.findAll(example,pageable);
    }

    @Override
    public Page<ExchangeRate> getExchangeRateLis(ExchangeRateDto rateDto) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        Pageable pageable = PageRequest.of(pageReq.getPageNo(), pageReq.getPageSize());
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("fromCurrencyCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("toCurrencyCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withIgnoreNullValues();
        ExchangeRate rate = new ExchangeRate();


        if (StringUtils.isNotNull(rateDto)){
            if (StringUtils.isNotEmpty(rateDto.getFromCurrencyCode())){
                rate.setFromCurrencyCode(rateDto.getFromCurrencyCode());
            }

            if (StringUtils.isNotEmpty(rateDto.getToCurrencyCode())){
                rate.setToCurrencyCode(rateDto.getToCurrencyCode());
            }
        }
        Example<ExchangeRate> example = Example.of(rate, matcher);
        return exchangeRateDao.findAll(example,pageable);
    }

    @Override
    public void saveNodeConfig(NodeConfig config) {
         nodeConfigDao.save(config);
    }

    @Override
    public void saveCardConfig(CardConfig config) {
        cardConfigDao.save(config);
    }

    @Override
    public void saveExchangeRate(ExchangeRate rate) {
        exchangeRateDao.save(rate);
    }

    @Override
    public void deleteNodeConfig(Integer nodeId) {
        nodeConfigDao.deleteById(nodeId);
    }

    @Override
    public void deleteCardConfig(Long cardCfgId) {
        cardConfigDao.deleteById(cardCfgId);
    }

    @Override
    public void deleteExchangeRate(int exchangeId) {
        exchangeRateDao.deleteById(exchangeId);
    }

    @Override
    public void updateNodeConfig(NodeConfig config) {
        nodeConfigDao.save(config);
    }

    @Override
    public void updateCardConfig(CardConfig config) {
        cardConfigDao.save(config);
    }

    @Override
    public void updateExchangeRate(ExchangeRate rate) {
        exchangeRateDao.save(rate);
    }

    @Override
    public CardConfig selectCardById(Long cardCfgId) {
        return cardConfigDao.findById(cardCfgId).orElse(null);
    }

    @Override
    public NodeConfig selectNodeById(Integer nodeId) {
        return nodeConfigDao.findById(nodeId).orElse(null);
    }

    @Override
    public ExchangeRate selectExchangeById(Integer exchangeId) {
        return exchangeRateDao.findById(exchangeId).orElse(null);
    }


}
