package com.meta.web.service;

import com.meta.system.domain.CreditCardApplication;

public interface CreditCardApplicationService {
    /**
     * 卡片申请
     *
     * @param c
     */
    void save(CreditCardApplication c);

    /**
     * 通过cardid查询申请记录
     *
     * @param cardId
     * @return
     */
    CreditCardApplication findByCardId(String cardId);


    CreditCardApplication findByRequestNo(String requestNo);

    void updateCardId(String oldCardId, String cardId, String requestNo);
}
