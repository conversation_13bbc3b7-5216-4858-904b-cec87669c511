package com.meta.web.service.impl;



import com.meta.web.dao.MisttrackLogDao;
import com.meta.web.entity.MisttrackLog;
import com.meta.web.service.MisttrackLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional(readOnly = true)
@Service
public class MisttrackLogServiceImpl implements MisttrackLogService {
    @Autowired
    MisttrackLogDao misttrackLogDao;

    @Override
    @Transactional
    public void save(MisttrackLog m) {
        misttrackLogDao.save(m);
    }
}
