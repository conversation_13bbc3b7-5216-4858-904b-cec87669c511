package com.meta.web.service.impl;

import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableSupport;
import com.meta.common.exception.CustomException;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.code.BusinessBizCode;
import com.meta.system.dao.WalletRiskInfoDao;
import com.meta.system.dao.app.CoinBalanceDao;
import com.meta.system.domain.TrcCstaddress;
import com.meta.system.domain.WalletRiskInfo;
import com.meta.system.domain.app.BepCstaddress;
import com.meta.system.dto.WalletRiskInfoDto;
import com.meta.system.service.*;
import com.meta.web.service.WalletRiskInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/21/16:02
 */
@Slf4j
@Service
public class WalletRiskInfoServiceImpl implements WalletRiskInfoService {

    @Autowired
    private WalletRiskInfoDao walletRiskInfoDao;

    @Autowired
    private CoinBalanceDao coinBalanceDao;

    @Autowired
    private TrcTransactionsService trcTransactionsService;

    @Autowired
    private BepTransactionsService bepTransactionsService;

    @Autowired
    private ArbTransactionsService arbTransactionsService;

    @Autowired
    private BaseTransactionsService baseTransactionsService;

    @Autowired
    private BepCstaddressService bepCstaddressService;

    @Autowired
    private TrcCstaddressService trcCstaddressService;


    @Override
    public Page<WalletRiskInfo> selectList(WalletRiskInfoDto walletRiskInfoDto) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        Sort sort = Sort.by(Sort.Direction.DESC, "createTm");
        Pageable pageable = PageRequest.of(pageReq.getPageNo(), pageReq.getPageSize(), sort);
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("walletAddress", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("riskLevel", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("coin", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("coinNet", ExampleMatcher.GenericPropertyMatchers.exact())
                .withIgnoreNullValues();
        WalletRiskInfo walletRiskInfo = new WalletRiskInfo();
        if (StringUtils.isNotNull(walletRiskInfoDto)) {
            if (StringUtils.isNotEmpty(walletRiskInfoDto.getWalletAddress())) {
                walletRiskInfo.setWalletAddress(walletRiskInfoDto.getWalletAddress());
            }
            if (StringUtils.isNotEmpty(walletRiskInfoDto.getRiskLevel())) {
                walletRiskInfo.setRiskLevel(walletRiskInfoDto.getRiskLevel());
            }
            if (StringUtils.isNotEmpty(walletRiskInfoDto.getCoin())) {
                walletRiskInfo.setCoin(walletRiskInfoDto.getCoin());
            }
            if (StringUtils.isNotEmpty(walletRiskInfoDto.getCoinNet())) {
                walletRiskInfo.setCoinNet(walletRiskInfoDto.getCoinNet());
            }

        }

        Example<WalletRiskInfo> example = Example.of(walletRiskInfo, matcher);

        return walletRiskInfoDao.findAll(example, pageable);

    }

    @Override
    public WalletRiskInfo save(WalletRiskInfo walletRiskInfo) {
        return walletRiskInfoDao.save(walletRiskInfo);
    }

    @Override
    public WalletRiskInfo update(WalletRiskInfo walletRiskInfo) {
        WalletRiskInfo riskInfo = walletRiskInfoDao.findById(walletRiskInfo.getId()).orElse(null);
        if (riskInfo == null) {
            return null;
        }
        riskInfo.update(walletRiskInfo);
        return walletRiskInfoDao.save(riskInfo);
    }

    @Override
    public boolean existData(String existData, String coin, Integer id) {

        List<Integer> ids = walletRiskInfoDao.existData(existData, coin);
        if (ids.isEmpty()) {
            return false;
        } else if (ids.size() == 1 && ids.contains(id)) {
            return false;
        }
        return true;

    }

    @Override
    public int delete(Integer id) {
        walletRiskInfoDao.deleteById(id);
        return BusinessBizCode.OPTION_SUCCESS.getCode();
    }

    @Override
    public WalletRiskInfo findData(String fromAddress) {
        return walletRiskInfoDao.findData(fromAddress);
    }

    @Override
    public List<WalletRiskInfo> addressForlist(String address, String coinCode, String coinNet) {


        List<WalletRiskInfo> riskList;

        if ("TRC20".equals(coinNet)) {
            TrcCstaddress trcCstaddress = trcCstaddressService.findByAddress(address);
            riskList = walletRiskInfoDao.addressForlist("TRC", trcCstaddress.getCstAddress(), 0);
        } else if ("BEP20".equals(coinNet)) {
            BepCstaddress bepCstaddress = bepCstaddressService.findByAddress(address);
            riskList = walletRiskInfoDao.addressForlist("BEP", bepCstaddress.getCstAdress(), 0);
        } else if ("ERC20_ARB".equals(coinNet)) {
            BepCstaddress arbCstaddress = bepCstaddressService.findByAddress(address);
            log.info("测试打印参数,address:{}", arbCstaddress.getCstAdress());
            riskList = walletRiskInfoDao.addressForlist("ARB", arbCstaddress.getCstAdress(), 0);
        } else if ("ERC20_BASE".equals(coinNet)) {
            BepCstaddress baseCstaddress = bepCstaddressService.findByAddress(address);
            riskList = walletRiskInfoDao.addressForlist("BASE", baseCstaddress.getCstAdress(), 0);
        } else {
            throw new CustomException("不支持的链类型");
        }

        return riskList;
    }


}
