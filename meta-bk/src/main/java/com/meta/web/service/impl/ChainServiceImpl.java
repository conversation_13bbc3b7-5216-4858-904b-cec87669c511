package com.meta.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meta.common.enums.BizError;
import com.meta.common.exception.CustomException;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.StringUtils;
import com.meta.system.domain.MetaMainAddress;
import com.meta.system.domain.app.BepCstaddress;
import com.meta.system.domain.app.BepTransactions;
import com.meta.system.domain.partner.MetaPartnerAssetPool;
import com.meta.system.domain.partner.MetaPushData;
import com.meta.system.enums.CoinType;
import com.meta.system.service.*;
import com.meta.web.contants.Constants;
import com.meta.web.redis.BizReidsLock;
import com.meta.web.service.ChainService;
import com.meta.web.task.ChainTask;
import com.meta.web.utils.CoinDataUtil;
import com.meta.web.utils.MQutils;
import com.meta.web.utils.QuikNodeApi;
import com.meta.web.utils.WeiToCoinUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/08/05/16:23
 */
@Service
public class ChainServiceImpl implements ChainService {

    @Autowired
    private ChainTask chainTask;
    private Logger logger = LoggerFactory.getLogger(this.getClass());

//    @Autowired
//    Web3j web3j;

    @Autowired
    private BizReidsLock bizRedisLock;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    BepCstaddressService bepCstaddressService;

//    @Autowired
//    private Web3jUtils web3jUtils;


    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private BepTransactionsService bepTransactionsService;

    @Autowired
    private QuikNodeApi quikNodeApi;


    @Autowired
    private CoinDataUtil coinDataUtil;

    @Autowired
    private MetaMainAddressService metaMainAddressService;

    @Autowired
    private MetaPushDataService metaPushDataService;

    @Autowired
    private MetaPartnerAssetPoolService metaPartnerAssetPoolService;

    @Autowired
    private MQutils mQutils;


    @Override
    public void executeChain(Long start, Long end) {

        String url = configService.selectConfigByKey("quicknode_url");


        String blockKeyStart = Constants.BK_START_BLOCKNUMBER + "blockNumber";
        redisTemplate.opsForValue().set(blockKeyStart, String.valueOf(start));
        logger.info("开始的桶startBlock：" + start);

        String blockKeyEnd = Constants.BK_END_BLOCKNUMBER + "blockNumber";
        redisTemplate.opsForValue().set(blockKeyEnd, String.valueOf(end));
        logger.info("结束的桶startBlock：" + end);

        bepCstaddressService.refreshWallet();

        chainTask.startTask();

    }

    @Override
    public void eventWeb3j() {

        String url = configService.selectConfigByKey("quicknode_url");

        BigInteger beginBlock = null;
        BigInteger endBlock = null;

        //判断是否存在blocknumber的key
        String blockKeyStart = Constants.BK_START_BLOCKNUMBER + "blockNumber";
        BigInteger startBlock = new BigInteger(redisTemplate.opsForValue().get(blockKeyStart));
        logger.info("开始的桶startBlock：" + startBlock);

        String blockKeyEnd = Constants.BK_END_BLOCKNUMBER + "blockNumber";
        BigInteger latestBlockNum = new BigInteger(redisTemplate.opsForValue().get(blockKeyEnd));
        logger.info("结束的桶latestBlockNum：" + latestBlockNum);
        endBlock = latestBlockNum;

        //添加合约
        List<String> tokenList = new ArrayList<>();
        String dco_contracta_ddress = "";//configService.selectConfigByKey("DCO_CONTRACTA_DDRESS");
        if (StringUtils.isNotEmpty(dco_contracta_ddress)) {
            tokenList.add(dco_contracta_ddress);
        }
        String usdt_contracta_ddress = configService.selectConfigByKey("USDT_CONTRACTA_DDRESS");
        if (StringUtils.isNotEmpty(usdt_contracta_ddress)) {
            tokenList.add(usdt_contracta_ddress);
        }

        String fromBlock = "0x" + startBlock.toString(16).toUpperCase();//开始的桶16进制
        String toBlock = "0x" + latestBlockNum.toString(16).toUpperCase();//结束的桶16进制


        //判断开始的桶和结束的桶的差距是否大于20000
        BigInteger dif = latestBlockNum.subtract(startBlock);
        int rs = dif.compareTo(BigInteger.valueOf(5));
        if (rs > 0) {
            latestBlockNum = startBlock.add(BigInteger.valueOf(5));
            toBlock = "0x" + latestBlockNum.toString(16).toUpperCase();//开始的桶16进制
            logger.info("两个相差大于5，结束的桶latestBlockNum：" + latestBlockNum);
        }
        beginBlock = latestBlockNum;
        redisTemplate.opsForValue().set(blockKeyStart, String.valueOf(latestBlockNum));
        String filterId = quikNodeApi.ethNewFilter(fromBlock, toBlock, tokenList, url);
        logger.info("filterId：" + filterId);
        if (StringUtils.isNull(filterId)) {
            logger.info("开始桶：" + fromBlock + "结束的桶：" + latestBlockNum + "，获取过滤器失败");
            return;
        }
        String filterLogs = quikNodeApi.ethGetFilterLogs(filterId, url);
        if (StringUtils.isNull(filterLogs)) {
            logger.info("没获取到数据");
        }

        List<MetaMainAddress> bep20Addresses = metaMainAddressService.getList("BEP20");
        //kazepay和H5的数据
//        Map<String, List<String>> map = new HashMap<>();
        List<String> allList = new ArrayList<>();
        for (MetaMainAddress metaMainAddress : bep20Addresses) {

            String sysId = metaMainAddress.getSysId();

            String keyBep20 = Constants.BEP_ADDRESS + sysId;
            boolean hskey20 = redisTemplate.hasKey(keyBep20);
            if (!hskey20) {
                bepCstaddressService.refreshAddress(metaMainAddress.getMainaddress().toLowerCase(), sysId);
            }
            List<String> addressList = redisTemplate.opsForList().range(keyBep20, 0, -1);
            String mainaddress = metaMainAddress.getMainaddress().toLowerCase();
            if (!addressList.contains(mainaddress)) {
                addressList.add(mainaddress);
            }
            if (addressList.size() > 0) {
//                map.put(sysId, addressList);
                allList.addAll(addressList);
            }


        }


        JSONArray jsonArray = JSONArray.parseArray(filterLogs);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);


            JSONArray topics = jsonObject.getJSONArray("topics");

            String from = "0x" + topics.get(1).toString().substring(26);
            String to = "0x" + topics.get(2).toString().substring(26);
            //判断是否存在数据
            String txid = jsonObject.getString("transactionHash");
            String blockNumber16 = jsonObject.getString("blockNumber");
            String blockNumber = String.valueOf(Integer.parseInt(blockNumber16.substring(2), 16));
            if (allList.size() > 0 && allList.contains(to)) {
                logger.info("交易充值/钱包归集");

                try {
                    //限制多次请求同一个Txid
                    logger.info("txid为：" + txid + "，桶为：" + blockNumber);
                    if (!bizRedisLock.locks(txid, Constants.redisLock.TXID)) {
                        throw new CustomException(MessageUtils.message(BizError.SYS_RECHARGE_BUSY.value()));
                    }
                    BepTransactions bepTransactions = new BepTransactions();
                    String sysId = "";
                    //判断交易是否已存在
                    List<BepTransactions> bcList = bepTransactionsService.findByTxid(txid);
                    if (bcList.size() < 1) {

                        MetaMainAddress metaMainAddress = null;
                        //判断是否是钱包归集
                        MetaMainAddress maddress = metaMainAddressService.findAddress(to);
                        if (maddress != null) {
                            sysId = maddress.getSysId();
                            metaMainAddress = maddress;
                        } else {
                            BepCstaddress bepCstaddress = bepCstaddressService.findByAddress(to);
                            sysId = bepCstaddress.getSysId();
                            metaMainAddress = metaMainAddressService.getData("BEP20", sysId);
                        }


                        String mainaddress = metaMainAddress.getMainaddress();//主钱包地址
                        BigDecimal rechargeAmount = metaMainAddress.getMinimumRecharge();
                        if (metaMainAddress.getMinimumRecharge() == null) {
                            rechargeAmount = new BigDecimal("10");
                        }

                        String token = jsonObject.getString("address"); //这是Token合约地址

                        //金额
                        String value = jsonObject.getString("data");
                        System.out.println(value);
                        BigInteger big = new BigInteger(value.substring(2), 16);
                        System.out.println(big);
                        BigDecimal amount = WeiToCoinUtils.convertWeiToCoin(new BigDecimal(big));


                        //交易费用wei
                        BigDecimal use = null;
                        //时间戳
                        String timestamp = "";
                        Date time = null;

                        //得到交易桶信息
                        String blockStr = quikNodeApi.ethGetBlockByNumber(blockNumber16, url);
                        JSONObject jsonObjectBlcok = JSONObject.parseObject(blockStr);
                        timestamp = String.valueOf(Long.parseLong(jsonObjectBlcok.getString("timestamp").substring(2), 16));
                        Instant instant = Instant.ofEpochSecond(Long.valueOf(timestamp));
                        LocalDateTime transactionTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
                        logger.info("Transaction Time: " + transactionTime);
                        time = Date.from(transactionTime.atZone(ZoneId.systemDefault()).toInstant());

                        //交易费
                        //单位Gwei
                        //转换为对应的金额
                        // 计算交易费用
                        // 获取交易对象
                        String transactionByHashStr = quikNodeApi.ethGetTransactionByHash(txid, url);
                        JSONObject transactionByHash = JSONObject.parseObject(transactionByHashStr);
                        // 获取交易的 gas 价格和 gas 限制
                        BigInteger gasPrice = new BigInteger(String.valueOf(Long.parseLong(transactionByHash.getString("gasPrice").substring(2), 16)));
                        // 获取实际使用的 gas 数量
                        String receiptStr = quikNodeApi.ethGetTransactionReceipt(txid, url);
                        JSONObject receipt = JSONObject.parseObject(receiptStr);
                        BigInteger gasUsed = new BigInteger(String.valueOf(Long.parseLong(receipt.getString("gasUsed").substring(2), 16)));
                        BigInteger transactionFee = gasPrice.multiply(gasUsed);
                        use = WeiToCoinUtils.convertWeiToCoin(new BigDecimal(String.valueOf(transactionFee)));
                        bepTransactions.setFee(use);


                        bepTransactions.setAddress(to);
                        bepTransactions.setFromaddress(from);
                        bepTransactions.setAmount(amount);
                        bepTransactions.setContract(token);
                        bepTransactions.setBlockHeight(Long.valueOf(String.valueOf(blockNumber)));
                        bepTransactions.setTxid(txid);
                        if (timestamp != null && timestamp != "") {

                            bepTransactions.setTimestamp(Long.valueOf(timestamp));
                        }

                        bepTransactions.setIsSync(0);

                        JSONObject data = new JSONObject();
                        data.put("txid", txid);
                        data.put("netWork", "BEP20");

                        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
                        String kazePaySysId = String.valueOf(accountId);

                        if (to.equals(mainaddress)) {
                            if (allList.contains(from)) {
                                bepTransactions.setType("collect");
                            } else {
                                bepTransactions.setType("receive");
                                bepTransactions.setIsSync(2);
                                bepTransactionsService.save(bepTransactions);
                                if (!sysId.equals(kazePaySysId)) {
                                    data.put("bepTransactions", (JSONObject) JSONObject.toJSON(bepTransactions));
                                    data.put("isRecorded", false);//是否入账
                                    pushData("H5_LISTENING_DATA", sysId, data);
                                }
                                return;
                            }

                        } else {
                            bepTransactions.setType("receive");

                        }
                        bepTransactionsService.save(bepTransactions);
                        //充值小于10的情况下不对数据做操作
                        if (bepTransactions.getType().equals("receive")) {
                            if (amount.compareTo(rechargeAmount) < 0) {
                                logger.info("sysId:" + sysId + "充值小于" + rechargeAmount + "的情况下不对数据做操作");

                                if (!sysId.equals(kazePaySysId)) {
                                    data.put("isRecorded", false);//是否入账
                                    pushData("H5_LISTENING_DATA", sysId, data);
                                }
                                return;
                            }
                        }

                        CoinType coinType = CoinType.USDT;//币种
                        if (!sysId.equals(kazePaySysId)) {
                            data.put("isRecorded", true);//是否入账
                            data.put("from", from);
                            data.put("to", to);
                            data.put("amount", amount);
                            data.put("use", use);
                            data.put("time", time);
                            data.put("coinType", coinType);
                            data.put("mainAddress", mainaddress);
                            logger.info("推送的数据:" + data);
                            pushData("H5_LISTENING_DATA", sysId, data);

                        } else {
//                            coinDataUtil.doData(from, to, txid, amount, use, time, bepTransactions, coinType, mainaddress,);
                        }
                    }


                } finally {
                    bizRedisLock.unlocks(txid, Constants.redisLock.TXID);
                }

            }
        }

        if (beginBlock.compareTo(endBlock) == 0) {
            // TODO 开始结束的高度一样的时候停止

            chainTask.stopTask();
            logger.info("结束监控链");
        }

    }

    /**
     * 推送数据
     *
     * @param type
     * @param sysId
     * @param json
     */
    @Transactional
    public void pushData(String type, String sysId, JSONObject json) {
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartnerBySysId(sysId);
        String apiUrl = metaPartnerAssetPool.getApiUrl();
        MetaPushData metaPushData = metaPushDataService.creatPashData(sysId, "", "", apiUrl, JSON.toJSONString(json), type);
        metaPushDataService.save(metaPushData);
        if (StringUtils.isNotEmpty(apiUrl)) {
            // 发送消息（确保在事务提交后发送）
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    String routingKey = "";
                    if ("H5_LISTENING_DATA".equals(type)) {
                        routingKey = com.meta.common.constant.Constants.routing_key_h5_listening;
                    }
                    mQutils.send(metaPushData, routingKey, 3);
                }
            });
        }
    }
}
