package com.meta.web.service;

import com.meta.system.domain.WalletRiskInfo;
import com.meta.system.dto.WalletRiskInfoDto;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/21/16:02
 */
public interface WalletRiskInfoService {


    Page<WalletRiskInfo> selectList(WalletRiskInfoDto walletRiskInfoDto);

    WalletRiskInfo save(WalletRiskInfo walletRiskInfo);

    WalletRiskInfo update(WalletRiskInfo walletRiskInfo);

    boolean existData(String walletAddress, String coin, Integer id);

    int delete(Integer id);

    WalletRiskInfo findData(String fromAddress);

    List<WalletRiskInfo> addressForlist(String s, String address,String coinNet);
}
