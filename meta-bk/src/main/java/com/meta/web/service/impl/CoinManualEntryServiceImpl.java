package com.meta.web.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.exception.CustomException;
import com.meta.system.dao.MetaPushDataDao;
import com.meta.system.domain.MetaMainAddress;
import com.meta.system.domain.TrcCstaddress;
import com.meta.system.domain.TrcTransactions;
import com.meta.system.domain.app.ArbTransactions;
import com.meta.system.domain.app.BaseTransactions;
import com.meta.system.domain.app.BepCstaddress;
import com.meta.system.domain.app.BepTransactions;
import com.meta.system.dto.RechargeDto;
import com.meta.system.enums.CoinType;
import com.meta.system.service.*;
import com.meta.web.service.CoinManualEntryService;
import com.meta.web.utils.CoinDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/08/20/18:11
 */
@Slf4j
@Service
public class CoinManualEntryServiceImpl implements CoinManualEntryService {
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private BepTransactionsService bepTransactionsService;
    @Autowired
    private ArbTransactionsService arbTransactionsService;
    @Autowired
    private BaseTransactionsService baseTransactionsService;

    @Autowired
    private TrcTransactionsService trcTransactionsService;

    @Autowired
    private CoinDataUtil coinDataUtil;

    @Autowired
    private ISysConfigService configService;


    @Autowired
    BepCstaddressService bepCstaddressService;

    @Autowired
    private MetaMainAddressService metaMainAddressService;

    @Autowired
    private TrcCstaddressService trcCstaddressService;

    @Autowired
    private MetaPushDataDao metaPushDataDao;
    private RedisTemplate<Object, Object> redisTemplate;

    @Override
    public AjaxResult manualEntry(RechargeDto dto) {
        if ("TRC20".equals(dto.getCoinNet())) {
            TrcTransactions trcTransactions = trcTransactionsService.findId(dto.getId());
            TrcCstaddress trcCstaddress = trcCstaddressService.findByAddress(trcTransactions.getAddress());
            if (!trcCstaddress.getSysId().equals("99") && trcTransactions.getIsSync() == 0) {
                int push = metaPushDataDao.pushByTrc(trcTransactions.getTxid());
                if (push > 0) {
                    trcTransactions.setIsSync(1);
                    trcTransactionsService.updateTransactions(trcTransactions);
                    return AjaxResult.success("SUCCESS");
                }
                return AjaxResult.error("FAIL");
            }

            if (trcTransactions.getIsSync() == 0) {

                if (trcTransactions.getAmount().compareTo(new BigDecimal("1")) >= 0) {
                    Date date = new Date(trcTransactions.getTimestamp() * 1000L);
                    System.out.println(date);
                    Boolean flag = coinDataUtil.doDataTrc(trcTransactions.getFromaddress(), trcTransactions.getAddress(), trcTransactions.getTxid(), trcTransactions.getAmount(),
                            trcTransactions.getFee(), date, trcTransactions, "USDT");
                    if (!flag) {
                        return AjaxResult.error("FAIL");
                    } else {
                        return AjaxResult.success("SUCCESS");
                    }
                }
            }
        } else if ("BEP20".equals(dto.getCoinNet())) {
//            bepCstaddressService.refreshWallet();
            BepTransactions bepTransactions = bepTransactionsService.findId(dto.getId());
            BepCstaddress bepCstaddress = bepCstaddressService.findByAddress(bepTransactions.getAddress());
            if (!bepCstaddress.getSysId().equals("99") && bepTransactions.getIsSync() == 0) {
                int push = metaPushDataDao.pushByBep(bepTransactions.getTxid());
                if (push > 0) {
                    bepTransactions.setIsSync(1);
                    bepTransactionsService.updateBepTransactions(bepTransactions);
                    return AjaxResult.success("SUCCESS");
                }
                return AjaxResult.error("FAIL");
            }

            if (bepTransactions.getIsSync() == 0) {

                if (bepTransactions.getAmount().compareTo(new BigDecimal("1")) >= 0) {
                    Date date = new Date(bepTransactions.getTimestamp() * 1000L);
                    System.out.println(date);
                    Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
                    String kazePaySysId = String.valueOf(accountId);
                    MetaMainAddress bep20 = metaMainAddressService.getData("BEP20", kazePaySysId);

                    CoinType coinType;

                    if (bepTransactions.getContract().equalsIgnoreCase(configService.selectConfigByKey("USDT_CONTRACTA_DDRESS"))) {
                        coinType = CoinType.USDT;
                    } else if (bepTransactions.getContract().equalsIgnoreCase(configService.selectConfigByKey("USDC_CONTRACTA_DDRESS"))) {
                        coinType = CoinType.USDC;
                    } else {
                        throw new CustomException("合约地址出错");
                    }

                    ArrayList<String> addressList = new ArrayList<>();
                    addressList.add(bepCstaddress.getCstAdress());

                    Boolean flag = coinDataUtil.doData(bepTransactions.getFromaddress(), bepTransactions.getAddress(), bepTransactions.getTxid(), bepTransactions.getAmount(),
                            bepTransactions.getFee(), date, bepTransactions, coinType, bep20.getMainaddress(), addressList, "BEP");
                    if (!flag) {
                        return AjaxResult.error("FAIL");
                    } else {
                        return AjaxResult.success("SUCCESS");
                    }
                }
            }


        } else if ("ERC20_ARB".equals(dto.getCoinNet())) {
//            bepCstaddressService.refreshWallet();
            ArbTransactions bepTransactions = arbTransactionsService.findId(dto.getId());
            BepCstaddress bepCstaddress = bepCstaddressService.findByAddress(bepTransactions.getAddress());
            if (!bepCstaddress.getSysId().equals("99") && bepTransactions.getIsSync() == 0) {
                int push = metaPushDataDao.pushByBep(bepTransactions.getTxid());
                if (push > 0) {
                    bepTransactions.setIsSync(1);
                    arbTransactionsService.updateTransactions(bepTransactions);
                    return AjaxResult.success("SUCCESS");
                }
                return AjaxResult.error("FAIL");
            }

            if (bepTransactions.getIsSync() == 0) {

                if (bepTransactions.getAmount().compareTo(new BigDecimal("1")) >= 0) {
                    Date date = new Date(bepTransactions.getTimestamp() * 1000L);
                    System.out.println(date);
                    Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
                    String kazePaySysId = String.valueOf(accountId);
                    MetaMainAddress bep20 = metaMainAddressService.getData("BEP20", kazePaySysId);

                    CoinType coinType;
                    if (bepTransactions.getContract().equalsIgnoreCase(configService.selectConfigByKey("USDT_CONTRACT_ADDRESS_FOR_ARB"))) {
                        coinType = CoinType.USDT;
                    } else if (bepTransactions.getContract().equalsIgnoreCase(configService.selectConfigByKey("USDC_CONTRACT_ADDRESS_FOR_ARB"))) {
                        coinType = CoinType.USDC;
                    } else {
                        throw new CustomException("合约地址出错");
                    }
                    ArrayList<String> addressList = new ArrayList<>();
                    addressList.add(bepCstaddress.getCstAdress());
                    Boolean flag = coinDataUtil.doData(bepTransactions.getFromaddress(), bepTransactions.getAddress(), bepTransactions.getTxid(), bepTransactions.getAmount(),
                            bepTransactions.getFee(), date, bepTransactions, coinType, bep20.getMainaddress(), addressList, "ARB");
                    if (!flag) {
                        return AjaxResult.error("FAIL");
                    } else {
                        return AjaxResult.success("SUCCESS");
                    }
                }
            }


        } else if ("ERC20_BASE".equals(dto.getCoinNet())) {
//            bepCstaddressService.refreshWallet();
            BaseTransactions bepTransactions = baseTransactionsService.findId(dto.getId());
            BepCstaddress bepCstaddress = bepCstaddressService.findByAddress(bepTransactions.getAddress());
            if (!bepCstaddress.getSysId().equals("99") && bepTransactions.getIsSync() == 0) {
                int push = metaPushDataDao.pushByBep(bepTransactions.getTxid());
                if (push > 0) {
                    bepTransactions.setIsSync(1);
                    baseTransactionsService.updateTransactions(bepTransactions);
                    return AjaxResult.success("SUCCESS");
                }
                return AjaxResult.error("FAIL");
            }

            if (bepTransactions.getIsSync() == 0) {

                if (bepTransactions.getAmount().compareTo(new BigDecimal("1")) >= 0) {
                    Date date = new Date(bepTransactions.getTimestamp() * 1000L);
                    System.out.println(date);
                    Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
                    String kazePaySysId = String.valueOf(accountId);
                    MetaMainAddress bep20 = metaMainAddressService.getData("BEP20", kazePaySysId);
                    CoinType coinType;
                    if (bepTransactions.getContract().equalsIgnoreCase(configService.selectConfigByKey("USDT_CONTRACT_ADDRESS_FOR_BASE"))) {
                        coinType = CoinType.USDT;
                    } else if (bepTransactions.getContract().equalsIgnoreCase(configService.selectConfigByKey("USDC_CONTRACT_ADDRESS_FOR_BASE"))) {
                        coinType = CoinType.USDC;
                    } else {
                        throw new CustomException("合约地址出错");
                    }
                    ArrayList<String> addressList = new ArrayList<>();
                    addressList.add(bepCstaddress.getCstAdress());

                    Boolean flag = coinDataUtil.doData(bepTransactions.getFromaddress(), bepTransactions.getAddress(), bepTransactions.getTxid(), bepTransactions.getAmount(),
                            bepTransactions.getFee(), date, bepTransactions, coinType, bep20.getMainaddress(), addressList, "BASE");
                    if (!flag) {
                        return AjaxResult.error("FAIL");
                    } else {
                        return AjaxResult.success("SUCCESS");
                    }
                }
            }
        }
        /*
         * 请求solana接口：/sol/solanaTransactions/doData/{transactionId}
         */
        else if ("SOL".equals(dto.getCoinNet())) {
            try {
                // 创建请求头
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                // 创建HTTP实体对象
                HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);
                // 创建RestTemplate并发送请求
                RestTemplate restTemplate = new RestTemplate();
                String solUri = sysConfigService.selectConfigByKey("SOL_URI");
                ResponseEntity<String> response = restTemplate.postForEntity(
                        solUri + "/sol/solanaTransactions/doData/" + dto.getId(),
                        requestEntity,
                        String.class
                );

                // 处理响应
                if (response.getStatusCode().is2xxSuccessful()) {
                    String responseBody = response.getBody();
                    JSONObject jsonResponse = JSONObject.parseObject(responseBody);

                    // 假设返回的JSON中包含address字段
                    if (jsonResponse.containsKey("code") && jsonResponse.getIntValue("code") == 200) {
                        return AjaxResult.success();
                    } else {
                        return AjaxResult.error(jsonResponse.getString("msg"));
                    }
                }
                log.error("solana归集接口请求失败: {}", response.getStatusCodeValue());
            } catch (Exception e) {
                log.error("solana归集接口请求异常", e);
            }

        } else {
            return AjaxResult.error("暂未开放 " + dto.getCoinNet());
        }
        return null;
    }
}
