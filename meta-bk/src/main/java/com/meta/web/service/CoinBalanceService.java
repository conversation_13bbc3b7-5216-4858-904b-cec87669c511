package com.meta.web.service;

import com.meta.common.core.domain.AjaxResult;
import com.meta.system.domain.app.CoinBalance;
import com.meta.system.domain.app.CoinBalancePK;
import com.meta.system.dto.CoinBalanceDto;
import com.meta.system.vo.CoinBalanceVo;
import org.springframework.data.domain.Page;

import java.util.List;

public interface CoinBalanceService {

    Page<CoinBalanceVo> selectList(CoinBalanceDto coinBalanceDto);


    AjaxResult collect(CoinBalanceVo coinBalanceVo);

    List<CoinBalance> getCoinlist(Long userId);

    CoinBalance findByUserIdAndCoinCode(Long userId,String coinCode);

    void updateCoinBalance(CoinBalance cb);

    /**
     * 查询
     * @return
     */
    CoinBalance findById(CoinBalancePK pk);

    /**
     * 更新 用户数字货币资产余额
     *
     * @param cb
     */
    void editCoinBalanceByEBP20(CoinBalance cb);

    void editCoinBalanceByTRC20(CoinBalance cb);
//    void editCoinBalanceBySol(CoinBalance cb);

    void editCoinBalanceByErc20Base(CoinBalance cb);

    void editCoinBalanceByErc20Arb(CoinBalance cb);

}
