package com.meta.web.utils;

import com.meta.common.constant.Constants;
import com.meta.system.domain.partner.MetaPushData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024/08/24/14:21
 */
@Slf4j
@Component
public class MQutils {


    @Autowired
    private RabbitTemplate rabbitTemplate;


    public void send(MetaPushData metaPushData, String routingKey, int retryCount) {

        Long id = metaPushData.getId();
        Message message = MessageBuilder.withBody(String.valueOf(id).getBytes(StandardCharsets.UTF_8))
                .setDeliveryMode(MessageDeliveryMode.PERSISTENT)// 设置消息为持久化
                .setContentType(MessageProperties.CONTENT_TYPE_JSON)  // 确保内容类型为JSON
                .build();

        CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
        // 2.2.准备ConfirmCallback
        String finalRoutingKey = routingKey;
        correlationData.getFuture().addCallback(result -> {
            log.info("1");
            // 判断结果
            if (result.isAck()) {
                // ACK
                log.debug("消息成功投递到队列!！消息ID: {}", correlationData.getId());
            } else {
                // NACK
                log.error("消息投递到队列失败！消息ID: {}，重试次数: {}", correlationData.getId(), retryCount);
                retrySendMessage(metaPushData,finalRoutingKey, retryCount);

            }
        }, ex -> {
            log.info("2");
            // 记录日志
            log.error("消息发送失败！消息ID: {}，重试次数: {}", correlationData.getId(), retryCount, ex);
            retrySendMessage(metaPushData,finalRoutingKey, retryCount);
        });
        log.info("开始发送");
        rabbitTemplate.convertAndSend(Constants.exchange_name_notify,routingKey, message);

    }


    public void retrySendMessage(MetaPushData message,String routingKey, int retryCount) {
        if (retryCount > 0) {
            try {
                log.info("等待一分钟后重试...");
                Thread.sleep(60000);
                log.info("正在重发消息... 剩余重试次数: {}", retryCount);
                send(message,routingKey, retryCount - 1);
            }catch (Exception e){
                e.printStackTrace();
            }
        } else {
            log.error("消息重发次数已达上限，发送失败。");
        }
    }

}
