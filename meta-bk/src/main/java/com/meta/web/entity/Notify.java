package com.meta.web.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.*;
import java.math.BigInteger;
import java.util.Date;
@Data
@Entity
@Table(name = "meta_push_data")
public class Notify {
    /**
     * 主键
     */
    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private BigInteger id;
    /**
     * 请求号
     */
    @Column(name = "request_no")
    private String requestNo;
    /**
     * B端id
     */
    @Column(name = "sys_id")
    private String sysId;
    /**
     * 卡id
     */
    @Column(name = "card_id")
    private String cardId;
    /**
     * 用户邮箱
     */
    @Column(name = "user_email")
    private String userEmail;
    /**
     * 请求地址
     */
    @Column(name = "api_url")
    private String apiUrl;
    /**
     * 请求类型
     */
    @Column(name = "api_code")
    private String apiCode;
    /**
     * 请求体
     */
    @Column(name = "request")
    private String request;
    /**
     * 返回体
     */
    @Column(name = "response")
    private String response;
    /**
     * 状态(0:失败，1：成功，2：处理中)
     */
    @Column(name = "statue")
    private String statue;
    /**
     * 发送次数
     */
    @Column(name = "num")
    private Integer num;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
