package com.meta.web.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "meta_misttrack_log")
public class MisttrackLog {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "log_id")
    private Long logId; // 自增长流水id


    @Column(name = "from_address")
    private String fromAddress; //交易付款人

    @Column(name = "to_address")
    private String toAddress; //交易收款人

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time")
    private Date createTime; // 创建时间

    @Column(name = "amount")
    private BigDecimal amount; //金额


    @Column(name = "address_label")
    private String addressLabel; //地址标签

    @Column(name = "score")
    private String score; //评分

    @Column(name = "level")
    private String level; //等级

    @Column(name = "coin")
    private String coin; //MistTrack的coin

    @Column(name = "txid")
    private String txid; //链上交易号

    @Column(name = "user_id")
    private Long userId; //用户id


}
