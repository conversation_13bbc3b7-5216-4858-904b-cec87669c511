package com.meta.web.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

@Entity
@Table(name = "meta_user_info")
@Data
@AllArgsConstructor
@NoArgsConstructor
@DynamicInsert
@DynamicUpdate
@Accessors(chain = true)
public class Wallet {
    @Id
    @Column(name = "user_id")
    private Long userId;

    @Column(name = "usd_balacne")
    private BigDecimal usdBalance;

    @Column(name = "freeze_usd_balacne")
    private BigDecimal freezeUsdBalacne;

    /**
     * {@link NodeLevel}
     */
    @Column(name = "node_level")
    private String nodeLevel;

    @JsonIgnore
    @Column(name = "trade_passwd")
    private String tradePasswd;

    @Column(name = "silver_active_code_num")
    private Integer silverActiveCodeNum;

    @Column(name = "silver_active_code_used")
    private Integer silverActiveCodeUsed;

    @Column(name = "golden_active_code_num")
    private Integer goldenActiveCodeNum;

    @Column(name = "golden_active_code_used")
    private Integer goldenActiveCodeUsed;

    @Column(name = "goldenblack_active_code_num")
    private Integer goldenblackActiveCodeNum;

    @Column(name = "goldenblack_active_code_used")
    private Integer goldenblackActiveCodeUsed;

    @Column(name = "channel_user_id") //渠道用户ID
    private Long channelUserId;

    @Column(name = "referrer_id") //推荐人
    private Long referrerId;
//
//    @Column(name = "lv1_parent_id") //A级父节点
//    private Long lv1_parent_id;
//    @Column(name = "lv2_parent_id") //B级父节点
//    private Long lv2ParentId;
//    @Column(name = "lv3_parent_id") //C级父节点
//    private Long lv3ParentId;
//    @Column(name = "lv4_parent_id") //D级父节点
//    private Long lv4ParentId;

}
