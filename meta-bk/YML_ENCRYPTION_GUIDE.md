# 钱包解密接口 - YML配置使用指南

## 概述

钱包解密接口现在支持从yml配置文件中读取加密密钥和相关设置，提供更安全和灵活的配置管理。

## 配置步骤

### 1. 添加yml配置

在您的 `application.yml` 或 `application-dev.yml` 中添加以下配置：

```yaml
# 私钥加密配置
encryption:
  # 是否启用加密
  enabled: true
  
  # 加密密钥（必须是16、24或32字节）
  secret-key: "MetaWallet123456"
  
  # 加密算法
  algorithm: "AES"
  
  # 编码方式
  encoding: "BASE64"
```

### 2. 环境变量配置（推荐生产环境）

```yaml
encryption:
  enabled: true
  secret-key: ${ENCRYPTION_SECRET_KEY:MetaWallet123456}
  algorithm: "AES"
  encoding: "BASE64"
```

然后设置环境变量：
```bash
export ENCRYPTION_SECRET_KEY="YourStrongSecretKey123456"
```

### 3. 不同环境配置

#### 开发环境 (application-dev.yml)
```yaml
encryption:
  enabled: false  # 开发环境禁用加密，便于调试
  secret-key: "DevWallet1234567"
  algorithm: "AES"
  encoding: "BASE64"
```

#### 生产环境 (application-prod.yml)
```yaml
encryption:
  enabled: true   # 生产环境必须启用加密
  secret-key: ${ENCRYPTION_SECRET_KEY}  # 从环境变量读取
  algorithm: "AES"
  encoding: "BASE64"
```

## 接口使用

### 1. 解密接口

**接口地址**: `POST /wallet/decrypt`

**请求示例**:
```bash
curl -X POST "http://localhost:8080/wallet/decrypt" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "encryptedData=您的加密数据"
```

### 2. 智能解密接口（推荐）

**接口地址**: `POST /wallet/smartDecrypt`

**请求示例**:
```bash
curl -X POST "http://localhost:8080/wallet/smartDecrypt" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "data=加密或未加密的数据"
```

### 3. 批量解密接口

**接口地址**: `POST /wallet/batchDecrypt`

**请求示例**:
```bash
curl -X POST "http://localhost:8080/wallet/batchDecrypt" \
  -H "Content-Type: application/json" \
  -d '["data1", "data2", "data3"]'
```

### 4. 配置状态查询接口

**接口地址**: `GET /wallet/encryptionConfig`

**请求示例**:
```bash
curl -X GET "http://localhost:8080/wallet/encryptionConfig"
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "获取配置成功",
  "data": {
    "enabled": true,
    "algorithm": "AES",
    "encoding": "BASE64",
    "secretKeyLength": 16,
    "configSource": "yml配置文件",
    "version": "1.0.0"
  }
}
```

## 代码使用示例

### 1. 直接调用工具类

```java
// 工具类会自动读取yml配置
String encrypted = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(originalKey);
String decrypted = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encrypted);

// 智能处理（推荐）
String result = SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(someKey);
```

### 2. 在Service中使用

```java
@Service
public class WalletService {
    
    public String processPrivateKey(String privateKey) {
        // 自动处理加密/解密
        return SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(privateKey);
    }
}
```

### 3. 检查配置状态

```java
@Autowired
private EncryptionProperties encryptionProperties;

public void checkEncryptionConfig() {
    System.out.println("加密启用: " + encryptionProperties.isEnabled());
    System.out.println("算法: " + encryptionProperties.getAlgorithm());
    System.out.println("密钥长度: " + encryptionProperties.getSecretKey().length());
}
```

## 配置验证

### 启动时自动验证

应用启动时会自动验证配置：

```
=== 私钥加密工具初始化 ===
配置信息: EncryptionProperties{enabled=true, algorithm='AES', encoding='BASE64', secretKeyLength=16}
========================
```

### 手动验证

```java
@Component
public class ConfigValidator {
    
    @Autowired
    private EncryptionProperties encryptionProperties;
    
    @PostConstruct
    public void validate() {
        String secretKey = encryptionProperties.getSecretKey();
        
        // 验证密钥长度
        if (secretKey.length() != 16 && secretKey.length() != 24 && secretKey.length() != 32) {
            throw new IllegalArgumentException("AES密钥长度必须是16、24或32字节");
        }
        
        System.out.println("✅ 配置验证通过");
    }
}
```

## 安全最佳实践

### 1. 密钥管理

```yaml
# ❌ 不安全 - 弱密钥
encryption:
  secret-key: "123456"

# ❌ 不安全 - 默认密钥
encryption:
  secret-key: "MetaWallet123456"

# ✅ 安全 - 强密钥
encryption:
  secret-key: "MyStr0ng@Encrypt!0nK3y#2025"

# ✅ 最安全 - 环境变量
encryption:
  secret-key: ${ENCRYPTION_SECRET_KEY}
```

### 2. 环境配置

```bash
# Docker 部署
ENV ENCRYPTION_SECRET_KEY="YourStrongSecretKey123456"

# Kubernetes 部署
apiVersion: v1
kind: Secret
metadata:
  name: encryption-secret
data:
  secret-key: WW91clN0cm9uZ1NlY3JldEtleTEyMzQ1Ng==
```

### 3. 配置分离

```yaml
# 主配置文件
spring:
  config:
    import: "optional:file:./config/encryption.yml"

# 外部配置文件 config/encryption.yml
encryption:
  enabled: true
  secret-key: ${ENCRYPTION_SECRET_KEY}
  algorithm: "AES"
  encoding: "BASE64"
```

## 迁移指南

### 从硬编码到yml配置

1. **添加yml配置**
```yaml
encryption:
  enabled: true
  secret-key: "YourCurrentKey"
  algorithm: "AES"
  encoding: "BASE64"
```

2. **验证功能**
```bash
# 测试配置接口
curl -X GET "http://localhost:8080/wallet/encryptionConfig"

# 测试解密接口
curl -X POST "http://localhost:8080/wallet/smartDecrypt" \
  -d "data=test_data"
```

3. **逐步部署**
- 先在测试环境验证
- 确认新旧数据都能正确处理
- 再部署到生产环境

## 故障排除

### 常见问题

1. **"加密工具未正确初始化"**
   - 确保Spring容器已启动
   - 检查配置类是否被扫描到

2. **"密钥长度无效"**
   - 检查secret-key长度是否为16、24或32字节

3. **"配置未生效"**
   - 检查yml文件格式是否正确
   - 确认配置文件路径正确

### 调试技巧

```yaml
# 启用调试日志
logging:
  level:
    com.meta.system.config.EncryptionProperties: DEBUG
    com.meta.system.uitls.SimplePrivateKeyEncryptionUtil: DEBUG
```

## 总结

现在钱包解密接口支持：

- ✅ **yml配置驱动** - 从配置文件读取所有设置
- ✅ **环境变量支持** - 支持从环境变量读取敏感配置
- ✅ **多环境配置** - 不同环境使用不同配置
- ✅ **配置验证** - 启动时自动验证配置正确性
- ✅ **配置查询接口** - 可以查询当前配置状态
- ✅ **向后兼容** - 兼容现有的加密数据

这样的配置方式更加安全、灵活，符合现代应用的配置管理最佳实践。

## 接口列表总结

| 接口 | 方法 | 路径 | 功能 |
|------|------|------|------|
| 基础解密 | POST | `/wallet/decrypt` | 解密加密数据 |
| 智能解密 | POST | `/wallet/smartDecrypt` | 智能处理加密/明文数据 |
| 批量解密 | POST | `/wallet/batchDecrypt` | 批量处理多个数据 |
| 配置查询 | GET | `/wallet/encryptionConfig` | 查询当前配置状态 |

推荐使用智能解密接口，它能自动处理各种情况，提供最佳的兼容性。
