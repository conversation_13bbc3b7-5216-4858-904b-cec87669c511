package com.meta;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 * @date 2023/12/18/14:26
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableAsync
public class MetaMoonbankApplication {
    public static void main(String[] args) {

        SpringApplication.run(MetaMoonbankApplication.class, args);
        System.out.println("启动moonbank成功!");
    }
}
