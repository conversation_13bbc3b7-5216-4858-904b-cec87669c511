package com.meta.api.moonbank.enums;

import com.meta.common.enums.BaseEnum;

import java.util.stream.Stream;

/**
 *  moonbank接口的apicode
 */
public enum ApiMoonbankCode implements BaseEnum<ApiMoonbankCode> {



    //*********** 通知接口 ******************************
    transactionVerificationCode("TRANSACTION_VERIFICATION_CODE"),
    cardStatusChange("CARD_STATUS_CHANGE"),
    transactionCreated("TRANSACTION_CREATED"),
    cardRechargeResult("CARD_RECHARGE_RESULT"),
    coinRechargeResult("COIN_RECHARGE_RESULT"),
    card3dsAuthResult("CARD_3DS_AUTH_RESULT"),
    userKycStatusChange("USER_KYC_STATUS_CHANGE"),
    activationCode("ACTIVATION_CODE"),//激活U卡
    cardCloseResult("CARD_CLOSE_RESULT"),//卡关闭结果
    ;

    String name;

    ApiMoonbankCode(String name) {
        this.name = name;
    }

    public String getName(){
        return this.name;
    }
    @Override
    public String getOrdinal() {
        return String.valueOf(this.ordinal());
    }

    public static ApiMoonbankCode toType(String name) {
        return Stream.of(ApiMoonbankCode.values())
                .filter(p -> p.name.equals(name))
                .findAny()
                .orElse(null);
    }
}
