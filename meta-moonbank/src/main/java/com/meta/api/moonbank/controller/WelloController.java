package com.meta.api.moonbank.controller;

import com.meta.api.moonbank.service.WelloService;
import com.meta.system.dto.wello.WelloOrderDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/2/25
 */


@RestController
@RequestMapping("/wello")
public class WelloController {

    @Autowired
    private WelloService welloService;

    /**
     * 监控订单
     * @param welloOrderDto
     */
    @PostMapping("/monitorOrder")
    public void monitorOrder(@RequestBody WelloOrderDto welloOrderDto, @RequestHeader Map<String, String> headers) {
        welloService.monitorOrder(welloOrderDto, headers);
    }

}
