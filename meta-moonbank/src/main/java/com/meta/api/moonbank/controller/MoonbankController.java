package com.meta.api.moonbank.controller;

import com.meta.api.moonbank.enums.ApiMoonbankCode;
import com.meta.api.moonbank.enums.NotifyRespCode;
import com.meta.api.moonbank.service.DealNotifyService;
import com.meta.common.utils.JsonUtils;
import com.meta.common.utils.StringUtils;
import com.meta.system.config.MoonbankConfig;
import com.meta.system.domain.es.notify.RequestMoonbankNofity;
import com.meta.system.domain.moonbank.MetaMoonbankLog;
import com.meta.system.moonbank.models.ApiResponse;
import com.meta.system.moonbank.util.MoonbankEncryptUtil;
import com.meta.system.service.MetaMoonbankLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/12/18/14:52
 */
@RestController
@RequestMapping("/api/mcard")
public class MoonbankController {

    private Logger logger = LoggerFactory.getLogger(MoonbankController.class);



    @Autowired
    private MetaMoonbankLogService metaMoonbankLogService;

    @Autowired
    private DealNotifyService dealNotifyService;



    //    @PostMapping(value = "/notify", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
//    @Transactional(rollbackFor = Exception.class)
//    public ApiResponse notify(@RequestBody RequestMoonbankNofity request) {
//        ApiResponse response = response(request);
//        return response;
//    }
    @PostMapping(value = "/notify", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @Transactional(rollbackFor = Exception.class)
    public String notify(@ModelAttribute RequestMoonbankNofity request) {
        ApiResponse response = response(request);
        return response.getMessage();
    }

    private ApiResponse response(RequestMoonbankNofity request) {
        ApiResponse apiResponse = new ApiResponse();

        ApiMoonbankCode apiCode = ApiMoonbankCode.toType(request.getType());
        String appId = request.getAppId();
        String uniqueNo = request.getUniqueNo();

        //3.解密
        String result = request.getResult();
        String resultDesc = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, result);
        logger.info("====resultDesc====   "+resultDesc);
        System.out.println(resultDesc);

        //1.判断appId是否正确
        if (!StringUtils.equals(appId, MoonbankConfig.appId)) {
            logger.info("appId不存在");
            apiResponse.setSuccess(false);
            apiResponse.setMessage(NotifyRespCode.APP_ID_ERROR.getValue());
            apiResponse.setCode(NotifyRespCode.APP_ID_ERROR.getCode());

            dealNotifyService.save(resultDesc, JsonUtils.obj2String(apiResponse)
                    , apiCode.getName(), uniqueNo, NotifyRespCode.APP_ID_ERROR.getValue(), NotifyRespCode.APP_ID_ERROR.getName());
            return apiResponse;
        }
        //2.判断请求号是否已经存在
        List<MetaMoonbankLog> logList = metaMoonbankLogService.find(uniqueNo);
        if (logList.size()>0) {
            logger.info("请求号重复");
            apiResponse.setSuccess(false);
            apiResponse.setMessage(NotifyRespCode.REQUEST_NO_NOT_UNIQUE.getValue());
            apiResponse.setCode(NotifyRespCode.REQUEST_NO_NOT_UNIQUE.getCode());
            dealNotifyService.save(resultDesc, JsonUtils.obj2String(apiResponse)
                    , apiCode.getName(), uniqueNo, NotifyRespCode.REQUEST_NO_NOT_UNIQUE.getValue(), NotifyRespCode.REQUEST_NO_NOT_UNIQUE.getName());
            return apiResponse;
        }

        //4.处理
        switch (apiCode) {
            case transactionVerificationCode:
                //卡验证码
                apiResponse = dealNotifyService.dealVerificationCode(resultDesc, apiCode.getName(), uniqueNo);
                break;
            case cardStatusChange:
                //卡状态改变
                apiResponse = dealNotifyService.cardStatusChange(resultDesc, apiCode.getName(), uniqueNo);
                break;
            case transactionCreated:
                //卡交易
                apiResponse = dealNotifyService.dealTransactionCreated(resultDesc, apiCode.getName(), uniqueNo);
                break;
            case cardRechargeResult:
                //卡充值结果
                apiResponse = dealNotifyService.cardRechargeResult(resultDesc, apiCode.getName(), uniqueNo);
                break;
            case coinRechargeResult:
                //币充值结果
                apiResponse = dealNotifyService.coinRechargeResult(resultDesc, apiCode.getName(), uniqueNo);
                break;
            case userKycStatusChange:
                //kyc状态改变
                apiResponse = dealNotifyService.userKycStatusChange(resultDesc, apiCode.getName(), uniqueNo);
                break;
            case card3dsAuthResult:
                // 3DS通知
                apiResponse = dealNotifyService.card3dsAuthResult(resultDesc, apiCode.getName(), uniqueNo);
                break;
            case activationCode:
                // u卡激活获取激活码
                apiResponse = dealNotifyService.activationCode(resultDesc, apiCode.getName(), uniqueNo);
                break;
            case cardCloseResult:
                // 销卡结果通知
                apiResponse = dealNotifyService.cardCloseResult(resultDesc, apiCode.getName(), uniqueNo);
                break;
            default:
                break;
        }

        return apiResponse;

    }

    @GetMapping(value = "/getdata")
    public String getdata() {

        return "OK";
    }

}
