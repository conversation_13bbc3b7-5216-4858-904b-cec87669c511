package com.meta.api.moonbank.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meta.api.moonbank.service.ForDealService;
import com.meta.api.moonbank.util.MQutils;
import com.meta.common.constant.Constants;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.enums.app.TxnStatus;
import com.meta.common.utils.DateUtils;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.system.constant.ChannelType;
import com.meta.system.constant.KzOrderStatus;
import com.meta.system.constant.ResultType;
import com.meta.system.domain.CreditCard;
import com.meta.system.domain.CreditCardLog;
import com.meta.system.domain.MetaCreditCardLogSub;
import com.meta.system.domain.app.MetaOrder;
import com.meta.system.domain.groupkey.MetaPartnerAssetPoolKey;
import com.meta.system.domain.partner.MetaPushData;
import com.meta.system.email.SendEmail;
import com.meta.system.fornax.constants.FornaxOpType;
import com.meta.system.fornax.constants.FornaxTradeStatus;
import com.meta.system.fornax.service.FornaxService;
import com.meta.system.models.CardRechargeResult;
import com.meta.system.service.*;
import com.meta.system.uitls.SendEmailDataUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/05/09/10:19
 */
@Slf4j
@Service
public class ForDealServiceImpl implements ForDealService {

    @Autowired
    private MetaOrderService metaOrderService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private CreditCardService creditCardService;

    @Autowired
    private SendEmail sendEmail;

    @Autowired
    private FornaxService fornaxService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private MetaPartnerAssetPoolService metaPartnerAssetPoolService;

    @Autowired
    private SendEmailDataUtils sendEmailDataUtils;

    @Autowired
    private MetaPushDataService metaPushDataService;


    @Autowired
    private MQutils mQutils;


    @Autowired
    private CreditCardLogService creditCardLogService;

    @Autowired
    private MetaCreditCardLogSubService metaCreditCardLogSubService;

    /**
     * 交易结果通知
     *
     * @param json
     * @return
     */
    @Override
    public String cardTransaction(JSONObject json) {


        String recordNo = json.getString("recordNo");//记录编号
        String originRecordNo = json.getString("originRecordNo");//原记录编号


        String bankcardId = json.getString("cardId");
        String transactionTime = json.getString("transactionTime");
        String transCurrency = json.getString("transCurrency");
        BigDecimal transCurrencyAmt = json.getBigDecimal("transCurrencyAmt");
        String localCurrency = json.getString("localCurrency");
        BigDecimal localCurrencyAmt = json.getBigDecimal("localCurrencyAmt");
        String merchantName = json.getString("merchantName");
        String transStatus = json.getString("transStatus");
        String transType = json.getString("transType");
        BigDecimal fee = json.getBigDecimal("fee");
        String respCode = json.getString("respCode");
        String approvalCode = json.getString("approvalCode");

        CreditCard creditCard = creditCardService.findByBankCardId(ChannelType.FORNAX.getValue(), bankcardId);
        if (creditCard == null) {
            log.info("卡片不存在");
            return "FAIL";
        }
        CreditCardLog log = null;
        if (StringUtils.isNotEmpty(originRecordNo)) {
            //原始订单
            log = creditCardLogService.findByTransactionId(originRecordNo);
        } else {
            //新的订单
            log = creditCardLogService.findByTransactionId(recordNo);
        }

        if (log == null) {
            log = new CreditCardLog();
        }
        log.setCardId(creditCard.getCardId());
        log.setTransactionId(recordNo);//交易id
        log.setTransactionDate(transactionTime);
        log.setCardTransactionAmount(localCurrencyAmt);//卡交易金额
        log.setCardCurrency(localCurrency);//卡币种:货币三位代码
        log.setTransactionCurrency(transCurrency);//交易币种:货币三位代码
        log.setTransactionAmount(transCurrencyAmt);//交易金额

        if ("1".equals(transStatus)) {
            log.setTransactionStatus(TxnStatus.APPROVED.getName());//交易状态
        }
        if ("2".equals(transStatus)) {
            log.setTransactionStatus(TxnStatus.DECLINED.getName());//交易状态
        }
        String tradeType = fornaxService.findTradeType(transType);
        log.setTransactionType(tradeType);//交易类型：
        log.setFee(fee);
        log.setFeeCurrency(creditCard.getCurrency());
        log.setMerchantName(merchantName);
        log.setDescription(respCode + " " + approvalCode);//描述

        if (StringUtils.isNotEmpty(originRecordNo)) {
            //原始订单
            saveLogSUb(originRecordNo, recordNo, log);
            saveLogSUb(recordNo, recordNo, log);
        } else {
            //新的订单
            saveLogSUb(recordNo, recordNo, log);
        }
        if (log.getCardAvailableBalance() == null) {
            CreditCard cardInfo = creditCardService.getCardInfo(creditCard);
            log.setCardAvailableBalance(cardInfo.getBalance());//卡可用余额
            log.setCardAvailableBalanceCurrency(cardInfo.getCurrency());//卡可用余额币种
        }


        creditCardLogService.save(log);
        if (StringUtils.isNotEmpty(creditCard.getSysId())) {
            pushTransactionData(creditCard, log);

        }

        return ResultType.SUCCESS.getValue();
    }

    private void saveLogSUb(String originRecordNo, String recordNo, CreditCardLog log) {
        MetaCreditCardLogSub sub = metaCreditCardLogSubService.findByNo(recordNo);
        if (sub == null) {
            sub = new MetaCreditCardLogSub();
        }
        sub.setCardId(log.getCardId());
        sub.setTransactionId(originRecordNo);
        sub.setTransactionIdSub(recordNo);
        sub.setTransactionDate(log.getTransactionDate());
        sub.setTransactionCurrency(log.getTransactionCurrency());
        sub.setTransactionAmount(log.getTransactionAmount());
        sub.setTransactionType(log.getTransactionType());
        sub.setCardCurrency(log.getCardCurrency());
        sub.setCardTransactionAmount(log.getCardTransactionAmount());
        sub.setFee(log.getFee());
        sub.setFeeCurrency(log.getFeeCurrency());
        sub.setTransactionStatus(log.getTransactionStatus());
        metaCreditCardLogSubService.save(sub);
    }

    /**
     * 订单通知
     *
     * @param json
     * @return
     */
    @Override
    public String cardOperate(JSONObject json) {

        String orderId = json.getString("orderId");
        int transactionType = json.getIntValue("transactionType");
        BigDecimal amount = json.getBigDecimal("amount");
        int transactionStatus = json.getIntValue("transactionStatus");
        String userReqNo = json.getString("userReqNo");
        MetaOrder metaOrder = metaOrderService.findRequestNo(userReqNo);
        String cardId = metaOrder.getCardId();
        CreditCard card = creditCardService.findByCardId(cardId);
        boolean isB = false;
        if (StringUtils.isNotEmpty(card.getSysId())) {
            isB = true;
        }
        log.info("订单id" + metaOrder.getId() + ",订单状态：" + metaOrder.getStatus());
        if (transactionType == FornaxOpType.apply.getValue()) {
            //开卡
            if (transactionStatus == FornaxTradeStatus.success.getValue() && KzOrderStatus.process.getValue().equals(metaOrder.getStatus())) {
                //保存订单状态为成功
                metaOrder.setStatus(KzOrderStatus.success.getValue());
                metaOrderService.save(metaOrder);
                //成功
                String authLimitAmount = sysConfigService.selectConfigByKey("authLimitAmount");
                if (amount.compareTo(BigDecimal.ZERO) > 0) {
                    log.info("开卡金额：" + authLimitAmount + "实际金额：" + amount);
                    if (amount.compareTo(new BigDecimal(authLimitAmount)) != 0) {
                        log.error("开卡的金额不对：" + userReqNo);
                        sendEmail.remindMail("开卡金额：" + authLimitAmount + "实际金额：" + amount + ",订单id：" + userReqNo, "fornax开卡金额不对");
                        //冻结卡片
                        fornaxService.updateCard(card, "INACTIVE");
                        return null;
                    }
                    JSONObject jsonObject = fornaxService.queryOrderRechargeCard(metaOrder.getRequestNo());
                    BigDecimal orderAmt = jsonObject.getBigDecimal("amount");
                    if (orderAmt != null) {
                        log.info("订单金额：" + orderAmt + "实际金额：" + amount);
                    }
                    if (amount.compareTo(orderAmt) != 0) {
                        log.error("开卡的订单金额不对：" + userReqNo);
                        sendEmail.remindMail("开卡的订单金额：" + orderAmt + "，实际金额：" + amount + ",订单id：" + userReqNo, "fornax开卡金额不对");
                        //冻结卡片
                        fornaxService.updateCard(card, "INACTIVE");
                        return null;
                    }
                    String status = jsonObject.getString("status");
                    if (!status.equals(String.valueOf(transactionStatus))) {
                        log.error("订单状态不对：" + userReqNo);
                        sendEmail.remindMail("开卡的状态：" + transactionStatus + "，实际状态：" + status + ",订单id：" + userReqNo, "fornax订单状态不对");
                        //冻结卡片
                        fornaxService.updateCard(card, "INACTIVE");
                        return null;
                    }


                }

                creditCardService.openCardOrderDeal(metaOrder, card, isB);


            } else if (transactionStatus == FornaxTradeStatus.fail.getValue()) {
                //失败
                //保存订单状态为失败
                metaOrder.setStatus(KzOrderStatus.fail.getValue());
                metaOrderService.save(metaOrder);
                creditCardService.openCardOrderDeal(metaOrder, card, isB);

            }
        } else if (transactionType == FornaxOpType.recharge.getValue()) {
            //充值
            if (transactionStatus == FornaxTradeStatus.success.getValue() && KzOrderStatus.process.getValue().equals(metaOrder.getStatus())) {
                //保存订单状态为成功
                metaOrder.setStatus(KzOrderStatus.success.getValue());
                metaOrderService.save(metaOrder);
                //成功
                BigDecimal orderAmount = metaOrder.getOrderAmount();
                log.info("充值金额：" + orderAmount + "，实际金额：" + amount);

                if (amount.compareTo(orderAmount) != 0) {
                    log.error("开卡的金额不对：" + userReqNo);
                    sendEmail.remindMail("充值金额：" + orderAmount + "实际金额：" + amount + ",订单id：" + userReqNo, "fornax充值金额不对");
                    //冻结卡片
                    fornaxService.updateCard(card, "INACTIVE");
                }


                JSONObject jsonObject = fornaxService.queryOrderRechargeCard(metaOrder.getRequestNo());
                BigDecimal orderAmt = jsonObject.getBigDecimal("amount");
                if (orderAmt != null) {
                    log.info("订单金额：" + orderAmt + "实际金额：" + amount);
                }
                if (amount.compareTo(orderAmt) != 0) {
                    log.error("充值的订单金额不对：" + userReqNo);
                    sendEmail.remindMail("充值的订单金额：" + orderAmt + "实际金额：" + amount + ",订单id：" + userReqNo, "fornax充值金额不对");
                    //冻结卡片
                    fornaxService.updateCard(card, "INACTIVE");
                }
                String status = jsonObject.getString("status");
                if (!status.equals(String.valueOf(transactionStatus))) {
                    log.error("订单状态不对：" + userReqNo);
                    sendEmail.remindMail("充值的状态：" + transactionStatus + "，实际状态：" + status + ",订单id：" + userReqNo, "fornax订单状态不对");
                    //冻结卡片
                    fornaxService.updateCard(card, "INACTIVE");
                }

                CardRechargeResult cardRechargeResult = new CardRechargeResult();
                cardRechargeResult.setStatus(TxnStatus.APPROVED.getName());
                creditCardService.orderRechageDeal(metaOrder, cardRechargeResult, card);
            } else if (transactionStatus == FornaxTradeStatus.fail.getValue()) {
                //失败
                //保存订单状态为失败
                metaOrder.setStatus(KzOrderStatus.fail.getValue());
                metaOrderService.save(metaOrder);
                CardRechargeResult cardRechargeResult = new CardRechargeResult();
                cardRechargeResult.setStatus(TxnStatus.DECLINED.getName());
                creditCardService.orderRechageDeal(metaOrder, cardRechargeResult, card);

            }

        } else if (transactionType == FornaxOpType.close.getValue()) {
            //销卡


        } else if (transactionType == FornaxOpType.withdraw.getValue()) {
            //提现


        }
        if (KzOrderStatus.success.getValue().equals(metaOrder.getStatus())) {
            log.info("订单成功：" + metaOrder.getId());
            return ResultType.SUCCESS.getValue();
        } else if (KzOrderStatus.fail.getValue().equals(metaOrder.getStatus())) {
            log.info("订单失败：" + metaOrder.getId());
            return ResultType.SUCCESS.getValue();

        }

        return "";
    }

    /**
     * 交易手续费
     *
     * @param json
     * @return
     */
    @Override
    public String tradeFee(JSONObject json) {
        return ResultType.SUCCESS.getValue();
    }

    /**
     * 3ds 通知
     *
     * @param json
     * @return
     */
    @Override
    public String card3dsOtp(JSONObject json) {

        String code = json.getString("otp");
        String cardId = json.getString("cardId");
        log.info("cardId:" + cardId);
        CreditCard creditCard = creditCardService.findByBankCardId(ChannelType.FORNAX.getValue(), cardId);
        if (creditCard == null) {
            log.error("卡片不存在");
            return null;
        }
        String formattedDateTime = DateUtils.getTime();
        //判断是否是B端用户
        if (StringUtils.isNotEmpty(creditCard.getSysId())) {
            MetaPartnerAssetPoolKey metaPartnerAssetPool = metaPartnerAssetPoolService.selectBySysId(creditCard.getSysId());

            JSONObject jsonData = new JSONObject();
            jsonData.put("cardID", creditCard.getCardId());
            jsonData.put("time", formattedDateTime);
            jsonData.put("code", code);
            pushData("TRANSACTION_VERIFICATION_CODE", creditCard.getSysId(), creditCard.getCardId(), creditCard.getCardHolder(), metaPartnerAssetPool.getApiUrl(), jsonData);

        } else {
            SysUser sysUser = iSysUserService.selectUserById(creditCard.getUserId());
            String cardNo = AESUtils.aesDecrypt(Constants.card_key, creditCard.getCardNo());
            sendEmailDataUtils.sendVerificationCode(sysUser.getLanguage(), sysUser.getEmail(), cardNo, code, formattedDateTime);
        }

        return ResultType.SUCCESS.getValue();

    }

    /**
     * 数字货币收单通知
     *
     * @param json
     * @return
     */
    @Override
    public String dptPaymentBill(JSONObject json) {
        return ResultType.SUCCESS.getValue();
    }

    /**
     * 推送交易数据
     *
     * @param creditCard
     * @param log
     */
    public void pushTransactionData(CreditCard creditCard, CreditCardLog log) {
        MetaPartnerAssetPoolKey metaPartnerAssetPool = metaPartnerAssetPoolService.selectBySysId(creditCard.getSysId());

        JSONObject jsonData = new JSONObject();

        jsonData.put("cardID", log.getCardId());
        jsonData.put("transactionId", log.getTransactionId());
        jsonData.put("transactionTime", log.getTransactionDate());
        jsonData.put("cardCurrency", log.getCardCurrency());
        jsonData.put("cardCurrencyAmt", log.getCardTransactionAmount());
        jsonData.put("transCurrency", log.getTransactionCurrency());
        jsonData.put("transCurrencyAmt", log.getTransactionAmount());
        jsonData.put("transStatus", log.getTransactionStatus());
        jsonData.put("transType", log.getTransactionType());
        jsonData.put("merchantName", log.getMerchantName());
        jsonData.put("respCode", log.getRespCode());
        jsonData.put("respCodeDesc", log.getDescription());
        jsonData.put("fee", log.getFee());
        jsonData.put("feeCurrency", log.getFeeCurrency());

        List<MetaCreditCardLogSub> subList = metaCreditCardLogSubService.findList(log.getTransactionId());
        List<JSONObject> jsList = new ArrayList<>();

        for (MetaCreditCardLogSub sub : subList) {
            JSONObject jData = new JSONObject();
            jData.put("cardId", sub.getCardId());
            jData.put("transactionId", sub.getTransactionId());
            jData.put("transactionIdSub", sub.getTransactionIdSub());
            jData.put("transactionDate", sub.getTransactionDate());
            jData.put("transactionCurrency", sub.getTransactionCurrency());
            jData.put("transactionAmount", sub.getTransactionAmount());
            jData.put("cardCurrency", sub.getCardCurrency());
            jData.put("cardTransactionAmount", sub.getCardTransactionAmount());
            jData.put("fee", sub.getFee());
            jData.put("feeCurrency", sub.getFeeCurrency());
            jData.put("transactionType", sub.getTransactionType());
            jData.put("transactionStatus", sub.getTransactionStatus());
            jsList.add(jData);
        }
        jsonData.put("subList", jsList);
        pushData("TRANSACTION_CREATED", creditCard.getSysId(), creditCard.getCardId(), creditCard.getCardHolder(), metaPartnerAssetPool.getApiUrl(), jsonData);
    }

    @Transactional
    public void pushData(String type, String sysId, String cardId, String userEmail, String apiUrl, JSONObject json) {

        MetaPushData metaPushData = metaPushDataService.creatPashData(sysId, cardId, userEmail, apiUrl, JSON.toJSONString(json), type);
        metaPushDataService.save(metaPushData);
        if (StringUtils.isNotEmpty(apiUrl)) {
            // 发送消息（确保在事务提交后发送）
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    String routingKey = "";
                    if ("TRANSACTION_VERIFICATION_CODE".equals(type)) {
                        routingKey = Constants.routing_key_transaction_code;
                    } else if ("CARD_RECHARGE_RESULT".equals(type)) {
                        routingKey = Constants.routing_key_card_recharge;
                    } else if ("CARD_3DS_AUTH_RESULT".equals(type)) {
                        routingKey = Constants.routing_key_card_3ds;
                    } else if ("USER_KYC_STATUS_CHANGE".equals(type)) {
                        routingKey = Constants.routing_key_card_kyc;
                    } else if ("CARD_STATUS_CHANGE".equals(type)) {
                        routingKey = Constants.routing_key_card_status;
                    } else if ("TRANSACTION_CREATED".equals(type)) {
                        routingKey = Constants.routing_transaction_created;
                    } else if ("CARD_ACTIVATION_CODE".equals(type)) {
                        routingKey = Constants.routing_card_activation_code;
                    } else if ("CARD_CLOSE_RESULT".equals(type)) {
                        routingKey = Constants.routing_key_card_close;
                    }


                    mQutils.send(metaPushData, routingKey, 3);
                }
            });
        }

    }
}
