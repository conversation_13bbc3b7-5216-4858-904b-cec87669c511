package com.meta.api.moonbank.config;

import com.meta.common.constant.Constants;
import com.meta.system.email.Mail;
import com.meta.system.email.SendEmail;
import com.meta.system.service.impl.SysConfigServiceImpl;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

/**
 * 支持 Confirm + Return + 本地重试 + 死信投递 + 告警
 */
@Slf4j
@Configuration
public class CommonConfig implements ApplicationContextAware {

    @Autowired
    private SendEmail sendEmail;
    @Autowired
    private SysConfigServiceImpl sysConfigService;

    private static final String HEADER_RETRY_COUNT = "x-retry-count";
    private static final int MAX_RETRY_COUNT = 3;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        RabbitTemplate rabbitTemplate = applicationContext.getBean(RabbitTemplate.class);

        // ========== Publisher Confirm ==========
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {

            if (ack) {
                log.info("消息成功送达交换机");
            } else {
                log.error("消息未送达交换机");
                // 重试发送
                if (correlationData instanceof CustomCorrelationData) {
                    CustomCorrelationData customData = (CustomCorrelationData) correlationData;
                    retryOrSendToDLX(rabbitTemplate, customData.getMessage(), customData.getExchange(), customData.getRoutingKey());
                }
            }
        });

        // ========== Publisher Return ==========
        rabbitTemplate.setReturnCallback((message, replyCode, replyText, exchange, routingKey) -> {
            // 延迟消息可跳过
            Integer delay = message.getMessageProperties().getReceivedDelay();
            if (delay != null && delay > 0) return;

            log.error("消息未路由到队列");

            retryOrSendToDLX(rabbitTemplate, message, exchange, routingKey);
        });

        rabbitTemplate.setMandatory(true);
    }

    /**
     * 本地重试或投递到死信交换机
     */
    private void retryOrSendToDLX(RabbitTemplate rabbitTemplate, Message message, String exchange, String routingKey) {
        // 判断是否超过最大重发次数
        Integer currentRetry = (Integer) message.getMessageProperties().getHeaders().getOrDefault(HEADER_RETRY_COUNT, 0);
        if (currentRetry < MAX_RETRY_COUNT) {
            try {
                currentRetry++;
                message.getMessageProperties().setHeader(HEADER_RETRY_COUNT, currentRetry);
                log.warn("正在第 {} 次本地重试...", currentRetry);
                Thread.sleep(1000);
                rabbitTemplate.send(exchange, routingKey, message, new CustomCorrelationData(message.getMessageProperties().getMessageId(), message, exchange, routingKey));
            } catch (Throwable e) {
                log.error("本地重试异常，消息ID: {}, 错误: {}", message.getMessageProperties().getMessageId(), e.getMessage());
            }
        } else {
            log.error("消息重试已达最大次数，投递至死信交换机");
            try {
                rabbitTemplate.send(Constants.exchange_name_dlx, Constants.routing_key_dlx_amount_alert, message);
                log.warn("已发送到死信交换机！");
                // 发送告警邮件
                String email = sysConfigService.selectConfigByKey("mq_abnormal_admin_email");
                Mail mail = new Mail();
                mail.setEmail(email);
                mail.setSubject("MQ消息投递失败");
                mail.setContent("消息投递失败，已发送到死信交换机:" + Constants.exchange_name_dlx
                        + "，队列：" + Constants.queue_dlx_amount_alert
                        + "，消息ID: " + message.getMessageProperties().getMessageId()
                        + "，消息内容: " + rabbitTemplate.getMessageConverter().fromMessage(message));
                sendEmail.send(mail);
            } catch (Throwable e) {
                log.error("死信交换机投递失败！消息ID: {}, 错误: {}", message.getMessageProperties().getMessageId(), e.getMessage());
            }
        }
    }

    /**
     * 自定义 CorrelationData
     */
    @Getter
    public static class CustomCorrelationData extends CorrelationData {
        private final Message message;
        private final String exchange;
        private final String routingKey;

        public CustomCorrelationData(String id, Message message, String exchange, String routingKey) {
            super(id);
            this.message = message;
            this.exchange = exchange;
            this.routingKey = routingKey;
        }

    }
}

