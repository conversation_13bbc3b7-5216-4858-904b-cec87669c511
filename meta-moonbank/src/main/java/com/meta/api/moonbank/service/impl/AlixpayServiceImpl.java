package com.meta.api.moonbank.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.meta.api.moonbank.service.AlixpayService;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.utils.StringUtils;
import com.meta.system.config.AlixpayConfig;
import com.meta.system.constant.Constants;
import com.meta.system.dao.MetaAlixpayLogDao;
import com.meta.system.dao.MetaExchangeOrderInfoAlixpayDao;
import com.meta.system.dao.app.CoinBalanceDao;
import com.meta.system.dao.app.CoinTxnDtlDao;
import com.meta.system.domain.app.*;
import com.meta.system.dto.alixpay.AlixPayOrderDto;
import com.meta.system.email.Mail;
import com.meta.system.email.SendEmail;
import com.meta.system.service.impl.SysConfigServiceImpl;
import com.meta.system.uitls.AlixpayUtil;
import com.meta.system.uitls.RedisConstants;
import com.meta.system.uitls.RestTemplateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/3/7
 */
@Slf4j
@Service
public class AlixpayServiceImpl implements AlixpayService {

    private final AlixpayServiceImpl self;

    @Autowired
    public AlixpayServiceImpl(@Lazy AlixpayServiceImpl self) {
        this.self = self;
    }

    @Autowired
    private MetaExchangeOrderInfoAlixpayDao metaExchangeOrderInfoAlixpayDao;
    @Autowired
    private MetaAlixpayLogDao metaAlixpayLogDao;
    @Autowired
    private CoinBalanceDao coinBalanceDao;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private CoinTxnDtlDao coinTxnDtlDao;
    @Autowired
    private AlixpayUtil alixpayUtil;


    /**
     * 接收到webhook消息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void monitorOrder(AlixPayOrderDto dto) {
        log.info("------接收到alixpay的消息，dto：{}------", dto);

        MetaAlixpayLog metaAlixpayLog = dto.convertDtoToEntity();
        self.recordAlixpayOrder(metaAlixpayLog);

        String data = dto.getExternalOrderId() + "|" + dto.getType() + "|" + dto.getFiatAmount() + "|" + dto.getStatus() + "|" + AlixpayConfig.secretKey;
        boolean verified = AlixpayUtil.verifySignature(data, dto.getSignature());
        if (!verified){
            log.error("------签名验证失败------");
            alixpayUtil.sendEmail("------签名验证失败------", null, "AlixPayOrderDto：" + dto);
            return;
        }
        log.info("------签名验证成功------");

        String orderId = redisTemplate.opsForValue().get(Constants.ALIXPAY_READ_TIMEOUT_KEY + dto.getExternalOrderId());
        if (StringUtils.isNotBlank(orderId)) return;

        MetaExchangeOrderInfoAlixpay order = metaExchangeOrderInfoAlixpayDao.getOneForUpdate(dto.getExternalOrderId());
        if (order.getStatus().equals("SUCCESS")){
            log.warn("------该订单已经处理过，订单为{}------", order);
            return;
        }
        order.setStatus(dto.getStatus());
        order.setDescriptions(dto.getDescriptions());
        order.setDealTime(dto.getStatus().equals("SUCCESS") ? String.valueOf(System.currentTimeMillis()) : null);

        CoinTxnDtl txn = coinTxnDtlDao.findByTxnProduct(order.getMerchantOrderId());
        CoinBalance coinBalance = coinBalanceDao.findOneForUpdate(order.getUserId(), order.getCurrency());

        if (dto.getStatus().equals("SUCCESS")) {
            log.info("------该订单状态为SUCCESS，正在处理------");
            String ccAmount = redisTemplate.opsForValue().get(Constants.ALIXPAY_CC_AMOUNT_KEY + order.getUserId() + ":" + order.getMerchantOrderId());
            if (ccAmount == null) {
                alixpayUtil.sendEmail("用户冻结金额在redis不存在，无法入账", order ,null);
                throw new RuntimeException("------alixpay-用户冻结金额在redis不存在，无法入账，用户Id为：" + order.getUserId() + "------");
            }
            // 冻结金额
            BigDecimal c = new BigDecimal(ccAmount);
            BigDecimal freezeBalance = coinBalance.getFreezeBalance();
            if (freezeBalance.compareTo(c) < 0) {
                alixpayUtil.sendEmail("------alixpay-用户冻结金额不足，无法入账，本应保留冻结金额：" + c +
                        "，实际剩余冻结金额：" + freezeBalance +
                        "，userId为：" + order.getUserId() + "------", order, null);
                throw new RuntimeException("------alixpay-用户冻结金额不足，无法入账，本应保留冻结金额：" + c +
                        "，实际剩余冻结金额：" + freezeBalance +
                        "，userId为：" + order.getUserId() + "------");
            }
            // 入账
            coinBalance.setFreezeBalance(coinBalance.getFreezeBalance().subtract(c));

        } else if (dto.getStatus().equals("FAILED")) {
            log.info("------该订单状态为FAILED，正在处理------");
            // 失败，返还用户冻结金额
            String ccAmount = redisTemplate.opsForValue().get(Constants.ALIXPAY_CC_AMOUNT_KEY + order.getUserId() + ":" + order.getMerchantOrderId());
            if (ccAmount == null) {
                alixpayUtil.sendEmail("用户冻结金额在redis不存在，无法入账", order, null);
                throw new RuntimeException("------alixpay-用户冻结金额在redis不存在，无法入账，用户Id为：" + order.getUserId() + "------");
            }
            BigDecimal c = new BigDecimal(ccAmount);
            BigDecimal freezeBalance = coinBalance.getFreezeBalance();
            if (freezeBalance.compareTo(c) < 0) {
                alixpayUtil.sendEmail("------alixpay-用户冻结金额不足，无法入账，本应保留冻结金额：" + c +
                        "，实际剩余冻结金额：" + freezeBalance +
                        "，userId为：" + order.getUserId() + "------", order, null);
                throw new RuntimeException("------alixpay-用户冻结金额不足，无法入账，本应保留冻结金额：" + c +
                        "，实际剩余冻结金额：" + freezeBalance +
                        "，userId为：" + order.getUserId() + "------");
            }
            coinBalance.setCoinBalance(coinBalance.getCoinBalance().add(c));
            coinBalance.setFreezeBalance(coinBalance.getFreezeBalance().subtract(c));
            order.setRefundAmount(c.toString());
            String userDayLimit = redisTemplate.opsForValue().get(Constants.ALIXPAY_DAY_LIMIT_KEY + order.getUserId());
            String[] split = userDayLimit.split(":");
            BigDecimal balance = new BigDecimal(split[1]);
            redisTemplate.opsForValue().set(Constants.ALIXPAY_DAY_LIMIT_KEY + order.getUserId(), LocalDate.now() + ":" + balance.add(new BigDecimal(ccAmount)));
        }

        txn.setTxnStatus(dto.getStatus().equals("SUCCESS") ? 1 : 0);
        coinTxnDtlDao.save(txn);
        coinBalanceDao.save(coinBalance);
        metaExchangeOrderInfoAlixpayDao.save(order);
        redisTemplate.delete(Constants.ALIXPAY_CC_AMOUNT_KEY + order.getUserId() + ":" + order.getMerchantOrderId());

        if (dto.getStatus().equals("SUCCESS")) {
            alixpayUtil.sendSseMessage(order);
        }
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void recordAlixpayOrder(MetaAlixpayLog metaAlixpayLog) {
        log.info("------正在记录订单到 meta_alixpay_log :{}------", metaAlixpayLog);
        metaAlixpayLogDao.save(metaAlixpayLog);
    }
}
