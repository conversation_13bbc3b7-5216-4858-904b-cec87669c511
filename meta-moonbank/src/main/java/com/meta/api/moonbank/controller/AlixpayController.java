package com.meta.api.moonbank.controller;

import cn.hutool.json.JSONObject;
import com.meta.api.moonbank.service.AlixpayService;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.exception.CustomException;
import com.meta.system.config.AlixpayConfig;
import com.meta.system.domain.app.MetaExchangeOrderInfoAlixpay;
import com.meta.system.dto.alixpay.AlixPayOrderDto;
import com.meta.system.uitls.AlixpayUtil;
import com.meta.system.uitls.RedisConstants;
import com.meta.system.uitls.RestTemplateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @Date 2025/3/7
 */
@RestController
@RequestMapping("/alixpay")
public class AlixpayController {

    @Autowired
    private AlixpayService alixpayService;


    /**
     * 监控订单
     * 接收到webhook消息，验证签名，处理，通知前端
     */
    @PostMapping("/monitorOrder")
    public void monitorOrder(@RequestBody AlixPayOrderDto dto) {
        alixpayService.monitorOrder(dto);
    }
}
