# 项目相关配置
meta:
  # 名称
  name: metabank
  # 版本
  version: 3.2.1
  # 版权年份
  copyrightYear: 2022
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/meta/uploadPath，Linux配置 /home/<USER>/uploadPath）
  #  profile: ./Users/<USER>/meta/file
  profile: D:/home/<USER>/file
  # 获取ip地址开关
  addressEnabled: false
  # 登录风控规则
  loginRuleEnabled: false
  # 交易风控规则
  tradeRuleEnabled: true
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
#  resetRedisIp: **************

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8893
  servlet:
    # 应用的访问路径
    context-path: /meta
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

# 日志配置
logging:
  level:
    com.meta: debug
    org.springframework: warn
  file:
    path: ./polito/meta/logs
#    path: G:/home/<USER>/logs

# Spring配置
spring:
  config:
    additional-location: classpath:/config/
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  jpa:
    database: mysql
    generate-ddl: false
    hibernate:
      ddlAuto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
        jdbc:
          #为spring data jpa saveAll方法提供批量插入操作 此处可以随时更改大小 建议500
          batch_size: 500
          batch_versioned_data: true
        order_inserts: true
        order_updates: true
    show-sql: true
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: *********************************************************************************************************************************************
        username: root
        password: 123456
      #        url: ***************************************************************************************************************************
      #        username: metabank
      #        password: 555666
      #        url: jdbc:mysql://************:3306/meta?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
      #        username: metabank
      #        password: Z;J1!q2}AGU:5
      #        url: **************************************************************************************************************************************************
      #        username: metabank
      #        password: 555666
      #        url: *********************************************************************************************************************************************
      #        username: root
      #        password: root
      #        url: *****************************************************************************************************************************************************************************************
      #        username: metabank
      #        password: TC]n}(E$43Tr2
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size:  10MB
      # 设置总上传的文件大小
      max-request-size:  20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址 **************
    host: localhost
    # 端口
    port: 6379
    # 密码
#    password: Meta@Bank
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
    database: 10
  boot:
    filters:
      # 这里假设你的Filter类的包名为com.meta.system.filter
      # Filter的名称可以根据你的需要进行修改
      filter-name:
        order: -1
        class: com.meta.system.filter.IPBlacklistFilter
  #  #rabbitmq 配置
  #  rabbitmq:
  #    host: 127.0.0.1
  #    username: guest
  #    password: guest
  #    #虚拟主机
  #    virtual-host: /
  #    #端口
  #    port: 5672
  #    listener:
  #      simple:
  #        #消费者最小数量
  #        concurrency: 10
  #        #消费者最大数量
  #        max-concurrency: 10
  #        #限制消费者，每次只能处理一条消息，处理完才能继续下一条消息
  #        prefetch: 1
  #        #启动时是否默认启动容器，默认为 true
  #        auto-startup: true
  #        #被拒绝时重新进入队列的
  #        default-requeue-rejected: true
  #    publisher-confirm-type: correlated
  #    publisher-returns: true
  #    template:
  #      mandatory: true
  #      retry:
  #        #启用消息重试机制，默认为 false
  #        enabled: true
  #        #初始重试间隔时间
  #        initial-interval: 60000ms
  #        #重试最大次数，默认为 3 次
  #        max-attempts: 5
  #        #重试最大时间间隔，默认 10000ms
  #        max-interval: 120000ms
  #        #重试的间隔乘数，
  #        #配置 2 的话，第一次等 1s，第二次等 2s，第三次等 4s
  #        multiplier: 1
  #
  #        #在 RabbitMQ 中，initial-interval 和 max-interval 是用于指定消息重试机制的两个参数，
  #        #它们的区别如下：
  #        #1. initial-interval（初始间隔时间）：表示第一次重试的时间间隔，也就是在消息第一次处
  #        #理失败后，等待多长时间再尝试重新发送消息。这个参数的默认值是 1 秒。
  #        #2.max-interval（最大间隔时间）：表示重试过程中的最大时间间隔，也就是每次重试时，
  #        #最长等待多长时间再尝试重新发送消息。这个参数的默认值是 10 秒。

  #  rabbitmq:
  #    host: 127.0.0.1 # rabbitMQ的ip地址
  #    port: 5672 # 端口
  #    username: guest
  #    password: guest
  #    virtual-host: /
  #    publisher-confirm-type: correlated
  #    publisher-returns: true
  #    template:
  #      mandatory: true
  rabbitmq:
    host: b-3a6e11d3-116c-43cf-b645-37b01f200194.mq.ap-southeast-1.amazonaws.com # rabbitMQ的ip地址
    port: 5671 # 端口
    username: mqpaytest
    password: 3nmK7gbiKMqr
    virtual-host: /
    publisher-confirm-type: correlated
    publisher-returns: true
    template:
      mandatory: true
    ssl:
      enabled: true

#vcc 配置
vcc:
  url: https://test-open.whalet.com/api
  version: 1.0
  partnerId: 202305061716298892600001
  #test服务器私钥
  #  privateStr: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC9E7fupXdmwZx5fGQZgj+FczgCEI9u6LyFxY/RFrkYXHojY7qPgyjaznqCrN3xfGolWO7iq9EbpoRetR5/ovLG8eWlMfWVkYfKZeQ6pfv1eVl+9p4O0rlBpVGVNzbDtLTuwX7Xw0RxFFJyZVR2EQzo0cb8XyZG3Z7XxDPMnOzqGIPVcHp0nL8vE8IPFyrg1z1AYoWUy90B7Kx05h/nNX7jtjNrVnpxHNpmK8kx5DQXrEjZZPVrunXzHRXg2vGkpb6DkRPHUCfuMpFTTL5Oge8O9sjM71tJeGPSmb++k8KL/6j97S4/a69PUH3ZldIMvgsKuzMYCY0O7Pwwrf5B6iTrAgMBAAECggEALlM7eHwQAhwjs1w3xkw0NgUhztex3NGnBvt9nhP8K6zUvAD+P5U6GEoImCW0hysdcqMUfHLuW+Dzg6TKoSkSZI313wCblBbA92T5gykRz3X46HOSDD2y6BOSJoYNo+uNfQXphwGvrij1flO3WuoYiJ6FK2ZAoZJBDcpjiplULpKBOih/MhvQ6CyN3fIyjvaKWJnFNnb5k+lG9i8ff95tuJVBey1OPFNuJ/TvDysAJkzUV9pl8MItlVdYjLLbWrBnVLojpdOI2/OcKjp/TJk+Y+3pw4R1kAH29LvH5nSO8kaaPu186BbSQOLl9/Dw43FwtPudWoePbucZCyQI/TTmqQKBgQDhyQ+e6gnCIvRSwgqBFUduZ5Pk/PWRA7NTKxTF8gRA9vsPju5vll3H3NXJdEwHA7MNlLXgWDsur/dwa9LUkdWPkrzPZiPZK8p5LcudVmpLgvoDzg9paSflA9tKVFyS2BRWoT91BZ+0k8lYkiuZ/iOfC4IF7UZocIaEpEBvQKOl/QKBgQDWYRrcfP2JYSwkDplSdKty1iB3fN/ISb/Q1s1azYrx5DOnWoVCfHauveY0eqXh1lZksZyr1SQ0kY6N6IihgUWGZy8bAezsKKy+b8oHVLa0UPoMUZGRF/KjZu0RBEyfoHGec1J7tSttCLTff4U+06fsYX37X8ZZJOuoSHoOVuh3BwKBgQDH9B6QVqWTtw72p39T97tNzA7O0TLMXSGXeuSntIAN5GxMyADi86BT2n++K+8UmzMbyOIVLy4iV5XjiqmotQoTXxk09ziyIDTsgiD7UsdJ3lF5wygk6wp4p6Sxu+pL5W6FlcGz6eoYqnS6qqBQfR+gvzlD9HDRFy4aE7g4jl8fJQKBgHxaAVnKSsroidE0gq04rcbD/DszPR+R3+kE4EN+nM0pIOk/cbMaPUGpN6JTTmuMh7qK6CUoUoMHt5gLPU3pjWmj6sgQutxVz7X7ZEYSVpSLsC97FSQDryQnbPYE92lUiPNvU3Ycpd7uQPSvpJvH7E0KVCh+6rAE4YlQ2TP+J4P7AoGBANd0ZmRORBn7tXJZpRwJPrliKwyFj4dGIyWCgaUiVYwWSYg3TkHm4LU+zMLcUfF1Cu7qBPhFM/zdhFEnaffqAv4WH9P/BKVba9BhL6z6nhWEI7be2waFUSDNhLkJhxmot0NUpdlsSau+x60HGuaOY1weEZf6bLarn4TI1Le+vxtI
  #我本地公钥
  #  publicStr: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt7Bct5InySYB0lyYeMkwJtCnF/q0eHNQ2S7UiquPb4VA8SHvu52RQO37RFViPpqmWp7qtke3QSN3VizZlT/oBKifWW4p1mDTK7uRVSNWOnBkEdWijpGXcD1m6dvCAKoiyPSM+C696viETdTrfx7RanLcfWoKbe/TQliIMzUkdVG2o/gXboI/fw83cG9vAhh7bjB3ONuAd/Dr1LYoJNkhroBWj40yDC49Sw8xVlZGBO8dtDrnm+1EEyvju1iIIQr1me8ucZs5uPFQheeGhlgjeV12s1eDIgp3kdAQN/mu9sGADeYkiDgTRMqCnAl2GFUOdaJsacjWwflxCSzOvwPeSQIDAQAB
  privateStr: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC3sFy3kifJJgHSXJh4yTAm0KcX+rR4c1DZLtSKq49vhUDxIe+7nZFA7ftEVWI+mqZanuq2R7dBI3dWLNmVP+gEqJ9ZbinWYNMru5FVI1Y6cGQR1aKOkZdwPWbp28IAqiLI9Iz4Lr3q+IRN1Ot/HtFqctx9agpt79NCWIgzNSR1Ubaj+Bdugj9/Dzdwb28CGHtuMHc424B38OvUtigk2SGugFaPjTIMLj1LDzFWVkYE7x20Oueb7UQTK+O7WIghCvWZ7y5xmzm48VCF54aGWCN5XXazV4MiCneR0BA3+a72wYAN5iSIOBNEyoKcCXYYVQ51omxpyNbB+XEJLM6/A95JAgMBAAECggEAB9ehvbyhsjbLMR3+7Hk6cw3JdH6hodragss7C8iTUSGRSjo2r/3kK699YMd8cEvN+mR6hNm++yr2d1lm2LZnw3sngnvvkVWE7oMNi2L81XXi79f9HrUckBAvCqD1QW4CGi5GrnngZd3CWEhU3ZKFQlQxEbGrTFJG5Lg+6GDGc7O83Ybt3DpRdBu3xyKqstio3Bnyi726xgoFvyapVxMA80NqHygp3I0xQM7+xcqcpToA76BnzEwxrcJA46fVz6ARpeoZJZm2LEpLjwtUUPP4nPGWaFbpvJWBVrYtrIUsbdvU/k872MJrGU2HKM2Y3pKE9ftf3r9BsUmjhg4Qqz3EAQKBgQDivK++jRWQg76C5tjomhTtdHP3UyY5NNepfxWU/fXGI2pXGxI8JFvMXTmA2mcOw3smPsXJTzRVh5rsLFZcAI1cNkgr7St0Cq83sbes2qgWLUU/9hwD/44zcK2AbGCUA/l1XjshHdzBUjYN/Te90HSE6POhdrRus9Mk7H6L2wyWiQKBgQDPZWFKIrwhIImf9vlaiKT6DT3+61sErPnOB6MXee7iMqtfSdFrry6vU+tR/YvQP5BQm5RHaFqx+wMXlv5g4HSzAlN4Y5pt2KslVRyx77uesFqDyKPK8AzVZuXGQwL35zqyeR3qWMQAiyJpbYa5i9JOlOL1NWa19VMOG2/6rHsZwQKBgAUSZ3QBv+u9prNS6qM5XyJ4qmdr6rz426Ik/5yXmbnW7PgJ2PyTa6JEq5agTBHeeZC/crkwFmGbaPHDhCMGuCLJ7A9ffMtZudWrGgq50Wy9koD9xl6ohsvLx3XJ9tcYx6nCc9wnyNpiNmdVtLuAQDsA4wJHn32idCStTZ9fRQbRAoGBAImA4scfTnH8O6LmQR6oOBFdVDw+WMM52AmkgJohCqPICl0L+SAXLrGpxvw+SUNR0WHQNLg/VNpp3Pv37UHXrye8JBFOoGWx4I7I5lSG6HLm46w6C3aSP0ABj9gqN76a/Qy1RoNIRPNDTBZwOoGsVUwqZGtE84syUtWolU6yqo9BAoGAAedNwM57PufFJHSA11rFM1nIOyTeG83bQInAjacLsdQDe12kFZeC0jTZKC/8R6S+BpsDGO2NLUhOJ5QstID7qX+ba+bqkcUzVTzrJJM2/U/wmeSIRKBvb//XGcMKy+A+pnlwV6ODQHzEE5h0BSRW1hgwWICYPGBBGIKGEsScz7c=
  publicStr: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvw2IzvWHwCAWbVYbh/lOv1fUGR5AtHvwLQLZuyrY0GiNSovSuGbhsWQ/ImTqqn5UtSPHt+2ANGg6wibHOIYG87uqp2q/HLLgs5IYh39tHcyX+saRBAADpFShgeM+VZEfOKa97HvNfJskqIj0JsCjZsNRardxbvJtQpn6hMuo0l7w0cY9qvzjKn0kcslKFYafz9h24wQTyjJrd+G/VjPt/EDn3zbY8OzGv6nI8j0uQSRN5SkIzV09Yk2Ymzivb/gHNOr6qU9r2pGsjn+ObmL1avVXA4niL6yoqe5YQ0QOMJ5hIey3WBF9PRgBljz64hoyPB8r1mvz6uTmnCQlcOntOwIDAQAB
wallet:
  userAddress:
    url: http://************:8290/user/address
    key: ja88@uP660088#Meta
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 300000
  # 是否允许账户多终端登录
  soloLogin: false


# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

arcFace:
  # 应用ID
  appId: BuQo8C4eBakpMMhTEtDJp1xEKcDBrDGca2PtDW1oK9Tw
  # 应用密钥
  sdkKeyForWindows: A8CdeNmhc1LjfYm6T1X8aJTMA5N4i9T2rBNzGq5xpLoj
  sdkKeyForLinux: A8CdeNmhc1LjfYm6T1X8aJTM1xnwytPEH8A6fLhXP2J5
  isAbsolutePath: false
  ## SDK路径
  sdkPath: /sdk/


mail:
  username: <EMAIL>
  password: TGGLTiJnN2KrrcZ
  authorizationCode: FFTPBUWAVPNIMZDC
  serverHost: smtp.163.com
  serverPort: 465
  reconnectSecond: 600



#moonbank 配置
moonbank:
  gateway: https://test.asinx.io/api-web
  appId: app_35940
  appSecret: b635dd5c87f7bf73387929203321b1e1
  notifyTimeout: 20000
  notifyConnectTimeout: 20000
  useProxy: false
  proxyAddress:
  proxyPort:

#kun 配置 + 配置文件
kun:
  environment: test
  ip: *************

wello:
  clientId: Kaze-Pay
  merchantCode: Kaze-Pay
  apiPrivateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDP79RdR+eUCgdLNPhpn9MHdgamcgL90S4hm+DrlyCIAX9tM86B2INYyNjl7dwVQZFJZMM0vAc5AhoSuJrywsX8llos5FVgBSx4oCLjNpLwPa7V/0o8PrxLCuHVvMZz62R3pj7mo8yzSeoFbg2xsGFeOn85GpsoUIXpBBsrRhYk9Q2C0N/uudbsHq5ok7qox8T9KexHX1hEMHxbZMR9n87iL3fkwbdx9L0n187AqqrdtH+6+NzmFNM4vsH7SRSqvJQhfYeCEll/d4WIaMhrrVFM9udeBgA6eulwQDkWPfqhLAGfUY2N4LYW9LfeYQzuJ9XLB7bt3WM6IQCH32T4JHolAgMBAAECggEAELaui2vo64+NzAf/MqA/2mDsgmr2Hhg2/MkWgGFGol/6cdjg77NrRVLpiju+/a9mQe+5OA8oBndKiLgxbjNlLhsYtvSIfz4TN8PpYfmEQI64s6dAPIzvXhnLEr7Idc9VJT/l3OyMGtolVxfkPmTWwrfxe3HNyGUGincDxwvsYj5yHO7Pw5FIIveSCw0aC8yomvKaUFAt26nPsQntWQVEH84kp7ef+kdgCiZLtlyMEdy+6WTwhXvT4E+m4sp1ePhwxzUdU/AjtCCJYLimHtfaudDLw29S+M+IQzAEOg4iVegN1nHGNVjQa+mfPjEs+6C9JPp2tzlWT60pf0/B5TXiuQKBgQDcPbNl5bIY2FFQoT9IjcaC19P6K53zLAhaAiiMIazY9YTSR3Hyv4fQxbZYstJ8+5fDfAUpM5VWkbe2KUEt05EsaJdFyhehO99B8qrIje+Rkq9FmPIMcOTuXg0IsN9xoe9xW6QTBn1oJnnbcS7lmNl2SkmgZ1+DAkwoTznYD5ctWQKBgQDxsrTWJU/TvWK36LvR+zAWOHkbBP/+EB4niBC+MxKs+dwoXGh1ZwutHsUxc2/w0yQYwbw+eK+z2kKa6B0mfqEC7HRd1G9SpUngNvBqPygEMYNRbUIAx99iRQHg+Mv+kJXQNOw/RTWDMPqd14TuX836JF2KF+36BdNnDQe1H93drQKBgC+do2CfZX7bndD52627ATUepnTK405EsdWL6+pICY+qnTm9OpSJ9job1M2ec8WTu38HHDKJNXtO69IVX0MiH5vjTPlItQ0l3aHrbiMIFMh7VyuL4qDv837ZlKXRX624ngT7FCEM4SYYU3YvJT18a1+YtgxY7Rxs7bw1TMM6s6uZAoGBAK+BqgjafTXvDtEm4cPfl5J7HY2+OrldMNhZ2HaUlZofsXtpZrzhv7vNXTnEbmO8njiP7cabUlLSSY08wXYje32o//LmgJPNzHBQ1JECHWe2IHovUT9J4+7y1yNq5o6KpQ7UbzyeX/ZMyZ92UWPbE7HxWC8fo0Hrmw/4iUE/y5bdAoGBANb8sPOzz2ZsooVyMCWS7i3ps3ouGGgWqt3BGMdPpXRo7qtFh4ocnTdv2O3r58mHv4zRHCXVDxMv1vvylIuucvtaVgxoeilnAEf8jtz9HHgvLj9DsRPysiw73ZIVt5ITLXYHzxRS7lKNGs2/F8/jLJGSBZGN5rrmg0I5lww56ny0
  webhookPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzqc85e3543ZFMGGE/zFzCYTaZDMSrFeOVnlly2iFNeF3fjTvKRYpKpOX2XQi/dfna0ru5OKe9AYfC+dfY5fGHxrVleerZTNwS1AX1HjaDV1bJ20ipS7KBzXGdHIPw0GSnvF8QXEKCysrtZnpXZLx5ndjt0X1sA4Fo1D0g/FeRbgRGkn1fR6IDc5REgnsasVV0tNbqxTNykmOaASvgA5n+F9AhWpHjF3UPV7g7mxfi2Je/QeLsIym6yq+jspcHgfEBpGUdSfy8EWqw72qobA+4CoRlfmBXirwm1VFPjYlViuD0F7nu+qhjjJzlqKpcoUTPan4N+Cid9x0w/7Y/RLeywIDAQAB
  walletAddress: "******************************************"

alixpay:
  secretKey: sdYQWuKg41C4NeBPSe7vQ8TVgLZqBrxarNer
  partnerCode: kazepay
  apiPrivateKey: -----BEGIN RSA PRIVATE KEY----- MIICXQIBAAKBgQDBGj8pH/eIjo4NaovibOw+ky/hpOV3iRcOP9+BAsVnJI9hjrj51rksi7S1CiCRGCaGOtb9urbH7ie7PPHOUQubRpw/rZaQHNI83gnro8qS+wGRQrRZVVGSySbLy+5O6QTh7PR3Bm8N3iaXMpLSr7jXcXg7lSEtUdzyIhkUmV0BLQIDAQABAoGATs2gQu8pvE+4xt7ZUUD0NCEq5CHAB0Up5IXKd0KjPpFb9gshj8vB69zjlRIZ8jxx1EuE2yzsVLDrA+0+HN1UJ4bRYrLpBqP2uwzdzh0h4YWfquFsNGsGpHeloAyosj4xUInNJQ8PRdY2fDkiD80fOMzim7Sm62zf42Yy8uyzrN0CQQDxuV+yu2Bkr0y1tWXYIzwiO9PJApQgSnIQJTK1bQ/5Mash3PA3wsjuROT1hAyt+3eTSpJl73/yjKRpUGwToOFXAkEAzIHDYsff6LXRintMbBj9UO0r5U9uKvUoHAHg5LlUFQkCtiu5f4Auo7SbxuZQUZkFboS4l7QVdX0d1TV/vW2LGwJBAOUuFIWpEiNj8R729bCO95ydjJawUzCqZKraGdHZraNebBqXF0pG66q8tzfwEvWBzkVgFp1/DZgxsFAJZ+7UQdECQQCLSfKnLBWgbX4LwRD7K0YVwYQ9Sg1yKson0Uu43VWcgiebpkpQwiCCFbhoD2QzP3MIpFMJ2HkpLY+NMUQQ5DRzAkAY2PCa99v7FjvbL4h4jJ93XyyISORz9DmJEQYmXxUSM2CEBiwLogB8SZO3TWJHo09qa8Nu9yISQsL8K/wGRgoi -----END RSA PRIVATE KEY-----
  apiPublicKey: -----BEGIN PUBLIC KEY----- MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqBHpZy4CkGAEmTl/WvpN44j7oUmuShzsZgA72OVI7gMjc+kzOM6+WUaXB/IUDEG3p+UcwXlEtP3f5WOrBneInkFCcEynKy7SJy4g3zSCMPNjq8GGhn0UCogwFq8+sNIai71+Fv4viaNn5yv75xmkUx1B9jz4E7WG+AefWq215mpwUZabzNfrxieLdxw8S0vetCuwpwz+C46IE9i0TW3iPpEOyPiYeDCRein+pU2V56ylJgKXq0Q5uZMEEjyMsxarHai/NraK+mXICe777x7UPujVeGxIiWcrF24AIg89NXMUK025duU43kF4eyKK2s55JmzatKiJxY5vaoMsJf1xIQIDAQAB -----END PUBLIC KEY-----
  webhookSecretKey: 5397e9b5-7008-4bc0-a533-58daa889baca


fornax:
  userNo: 2025534874059126850
  version: 1.0.0
  dataType: json
  # fornax的公钥
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQD6WIZEnVtdRxhxnjZ9m1kD3k+21fCc2UBxCy80lIY0tgTmZ5pSCq18cYkE2CBlb0QUXwdd8uBycMVW2JTStVpBp70UAlvvK2C5VVajzoz6hKbHgDH1GYGSbbevuPnMpP1qNhlC0mItMt1n3dz6NsoSt5t2RO0RPOZ6eN7hcna8zwIDAQAB
  # kazepay的私钥
  privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAN08oQ3nVFUsn3A83rsefGVVRbPoBOwaS02gawc5b1wuzopbK0V9BrLN8Zplye0QCFe7Fw062PMYyrkafIwqBwa+si2P4MolH26D6dlcqNUUoZSl0hdd42X1eU/xX4cICqkXcjZdG3xp7VCSxbwyZp4mDpOLPKlWAm0nmuS3lZpJAgMBAAECgYEAnS2y+q7MQosmZ1ZHo8df95pioBOKU7fGoksDXymWDXcPtEpbbs4J0UTxjpEcqfkHBFVeZj7V3bhSZroDnI1LWVPalkyQ6QXAu9tON2mg5fGqmuUJpe6iR8wMkuV6nMBGWnhV7Pr/XniEYr4Hl44HcpNwMyW2g/HuLOZZ6+/JowECQQD5wgR4Aeqxxf/BeWKXGlw69vKJIhcDmuiNUz+oVmyPh5Tqz9b5i3lXkfUh91MavfFKp8I/UG8ris11i+bVv93hAkEA4sQhYOP7oAG7zLFngRBvPIJu3wL5bc5UTP3AWkqfrk40UX3lY/1OsxdJ2vDltHMzxvBOkp1QXtpmNZeUvgy5aQJAJ4fq9yeugwbIzVGuJiZ49KhDiSsJI3vwVPYZPEa5lhoqmuPcrdDCmvayKvcfgzsPJvbYivbrMaQdkv5awfhFYQJAYeaXGoOS8OOOtrKoovn7bD5Lq5BXK2b20j6DOvgYtGIjCEO4M1D7FyqowhNauGs8IVlCMt4kAq97jQuiNeK8cQJAZzxL/GGpyfoDzCvqbDzfIVskxAOKCOLdGh2/ty8f2w3hDbjvPq5JKNz8nTegJa/SrhdzKhEBLdKwD1vWFNLGew==
  # kazepay的公钥: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDdPKEN51RVLJ9wPN67HnxlVUWz6ATsGktNoGsHOW9cLs6KWytFfQayzfGaZcntEAhXuxcNOtjzGMq5GnyMKgcGvrItj+DKJR9ug+nZXKjVFKGUpdIXXeNl9XlP8V+HCAqpF3I2XRt8ae1QksW8MmaeJg6TizypVgJtJ5rkt5WaSQIDAQAB
  url: https://api.wewallex.com
