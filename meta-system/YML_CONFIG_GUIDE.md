# 私钥加密工具 - YML配置指南

## 概述

私钥加密工具现在支持从yml配置文件中读取密钥和相关设置，提供更灵活和安全的配置管理。

## 配置文件设置

### 1. 在 application.yml 中添加配置

```yaml
# 私钥加密配置
encryption:
  # 是否启用加密
  enabled: true
  
  # 加密密钥（16、24或32字节）
  secret-key: "MetaWallet123456"
  
  # 加密算法
  algorithm: "AES"
  
  # 编码方式
  encoding: "BASE64"
```

### 2. 不同环境的配置

#### 开发环境 (application-dev.yml)
```yaml
encryption:
  enabled: false  # 开发环境禁用加密，便于调试
  secret-key: "DevWallet1234567"
  algorithm: "AES"
  encoding: "BASE64"
```

#### 测试环境 (application-test.yml)
```yaml
encryption:
  enabled: true   # 测试环境启用加密
  secret-key: "TestWallet123456"
  algorithm: "AES"
  encoding: "BASE64"
```

#### 生产环境 (application-prod.yml)
```yaml
encryption:
  enabled: true   # 生产环境必须启用加密
  secret-key: "ProdWallet@#$%^&*()123456789"  # 使用强密钥
  algorithm: "AES"
  encoding: "BASE64"
```

## 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | boolean | true | 是否启用加密功能 |
| `secret-key` | String | MetaWallet123456 | AES加密密钥，长度必须是16、24或32字节 |
| `algorithm` | String | AES | 加密算法（目前只支持AES） |
| `encoding` | String | BASE64 | 编码方式（目前只支持BASE64） |

## 密钥管理最佳实践

### 1. 密钥强度要求

```yaml
# ❌ 弱密钥 - 不要使用
secret-key: "123456"

# ❌ 默认密钥 - 生产环境不要使用
secret-key: "MetaWallet123456"

# ✅ 强密钥 - 推荐使用
secret-key: "MyStr0ng@Encrypt!0nK3y#2025"
```

### 2. 环境变量配置（推荐）

```yaml
encryption:
  enabled: true
  secret-key: ${ENCRYPTION_SECRET_KEY:MetaWallet123456}
  algorithm: "AES"
  encoding: "BASE64"
```

然后在环境变量中设置：
```bash
export ENCRYPTION_SECRET_KEY="YourStrongSecretKey123456"
```

### 3. 外部配置文件

```yaml
# application.yml
spring:
  config:
    import: "optional:file:./config/encryption.yml"
```

创建 `config/encryption.yml`：
```yaml
encryption:
  enabled: true
  secret-key: "ExternalConfigKey123456"
  algorithm: "AES"
  encoding: "BASE64"
```

## 代码使用示例

### 1. 自动配置（推荐）

工具类会自动读取yml配置，无需手动设置：

```java
// 直接使用，会自动读取yml配置
String encrypted = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(originalKey);
String decrypted = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encrypted);
```

### 2. 检查配置状态

```java
@Autowired
private EncryptionProperties encryptionProperties;

public void checkConfig() {
    System.out.println("加密启用: " + encryptionProperties.isEnabled());
    System.out.println("算法: " + encryptionProperties.getAlgorithm());
    System.out.println("密钥长度: " + encryptionProperties.getSecretKey().length());
}
```

### 3. 动态修改配置（仅用于测试）

```java
// 仅用于测试环境
SimplePrivateKeyEncryptionUtil.setEncryptionEnabled(false);
```

## 配置验证

### 1. 启动时验证

应用启动时会自动验证配置并输出信息：

```
=== 私钥加密工具初始化 ===
配置信息: EncryptionProperties{enabled=true, algorithm='AES', encoding='BASE64', secretKeyLength=16}
========================
```

### 2. 手动验证配置

```java
@Component
public class EncryptionConfigValidator {
    
    @Autowired
    private EncryptionProperties encryptionProperties;
    
    @PostConstruct
    public void validateConfig() {
        String secretKey = encryptionProperties.getSecretKey();
        
        // 验证密钥长度
        if (secretKey.length() != 16 && secretKey.length() != 24 && secretKey.length() != 32) {
            throw new IllegalArgumentException("AES密钥长度必须是16、24或32字节");
        }
        
        // 验证是否使用默认密钥
        if ("MetaWallet123456".equals(secretKey) && encryptionProperties.isEnabled()) {
            System.err.println("⚠️  警告：生产环境不应使用默认密钥！");
        }
        
        System.out.println("✅ 加密配置验证通过");
    }
}
```

## 安全建议

### 1. 密钥管理

- ✅ 使用强随机密钥
- ✅ 不同环境使用不同密钥
- ✅ 定期轮换密钥
- ✅ 密钥不要提交到代码仓库
- ✅ 使用环境变量或外部配置文件

### 2. 配置安全

```yaml
# ✅ 好的配置
encryption:
  enabled: true
  secret-key: ${ENCRYPTION_SECRET_KEY}  # 从环境变量读取
  algorithm: "AES"
  encoding: "BASE64"

# ❌ 不好的配置
encryption:
  enabled: true
  secret-key: "123456"  # 弱密钥
  algorithm: "AES"
  encoding: "BASE64"
```

### 3. 部署建议

#### Docker 部署
```dockerfile
ENV ENCRYPTION_SECRET_KEY="YourStrongSecretKey123456"
```

#### Kubernetes 部署
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: encryption-secret
data:
  secret-key: WW91clN0cm9uZ1NlY3JldEtleTEyMzQ1Ng==  # Base64编码
---
apiVersion: apps/v1
kind: Deployment
spec:
  template:
    spec:
      containers:
      - name: app
        env:
        - name: ENCRYPTION_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: encryption-secret
              key: secret-key
```

## 故障排除

### 1. 常见错误

**错误**: `加密工具未正确初始化，请确保Spring容器已启动`
**解决**: 确保在Spring容器启动后调用加密方法

**错误**: `AES密钥长度必须是16、24或32字节`
**解决**: 检查yml配置中的secret-key长度

### 2. 调试技巧

```yaml
# 启用调试日志
logging:
  level:
    com.meta.system.uitls.SimplePrivateKeyEncryptionUtil: DEBUG
    com.meta.system.config.EncryptionProperties: DEBUG
```

## 迁移指南

### 从硬编码密钥迁移到yml配置

1. **添加yml配置**
```yaml
encryption:
  enabled: true
  secret-key: "YourCurrentHardcodedKey"
  algorithm: "AES"
  encoding: "BASE64"
```

2. **验证功能正常**
```java
// 测试加密解密功能
String test = "test_data";
String encrypted = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(test);
String decrypted = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encrypted);
assert test.equals(decrypted);
```

3. **逐步替换密钥**
- 先在测试环境验证
- 再在生产环境部署
- 确保新旧数据都能正确处理

## 总结

现在私钥加密工具支持：

- ✅ **yml配置驱动** - 从配置文件读取所有设置
- ✅ **环境变量支持** - 支持从环境变量读取敏感配置
- ✅ **多环境配置** - 不同环境使用不同配置
- ✅ **配置验证** - 启动时自动验证配置正确性
- ✅ **安全最佳实践** - 提供完整的安全配置指南

这样的配置方式更加灵活、安全，符合现代应用的配置管理最佳实践。
