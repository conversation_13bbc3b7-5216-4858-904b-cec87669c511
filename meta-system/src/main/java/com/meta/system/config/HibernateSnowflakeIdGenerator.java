package com.meta.system.config;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.hibernate.HibernateException;
import org.hibernate.MappingException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.Configurable;
import org.hibernate.id.IdentifierGenerator;
import org.hibernate.service.ServiceRegistry;
import org.hibernate.type.Type;

import java.io.Serializable;
import java.util.Properties;

/**
 * 符合Hibernate标准的雪花算法ID生成器
 */
public class HibernateSnowflakeIdGenerator implements IdentifierGenerator, Configurable {

    private Snowflake snowflake;

    private long workerId = 1;
    private long datacenterId = 1;

    @Override
    public void configure(Type type, Properties params, ServiceRegistry serviceRegistry) throws MappingException {
        // 从属性中获取workerId和datacenterId配置
        String workerIdParam = params.getProperty("worker_id");
        if (workerIdParam != null && !workerIdParam.isEmpty()) {
            workerId = Long.parseLong(workerIdParam);
        }

        String datacenterIdParam = params.getProperty("datacenter_id");
        if (datacenterIdParam != null && !datacenterIdParam.isEmpty()) {
            datacenterId = Long.parseLong(datacenterIdParam);
        }

        // 初始化雪花算法
        snowflake = IdUtil.getSnowflake(workerId, datacenterId);
    }

    @Override
    public Serializable generate(SharedSessionContractImplementor session, Object object) throws HibernateException {
        return snowflake.nextId();
    }
}
