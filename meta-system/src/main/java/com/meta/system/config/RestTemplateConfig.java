package com.meta.system.config;


import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/04/08/21:34
 */
@Configuration
public class RestTemplateConfig {
    /**
     * 专用RestTemplate（针对图片上传方法定制化配置）
     */
    @Bean("uploadRestTemplate")
    public RestTemplate uploadRestTemplate() {
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager();
        connManager.setMaxTotal(5);
        connManager.setDefaultMaxPerRoute(2);
        connManager.setValidateAfterInactivity(3_000); // 增加连接有效性检查

        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connManager)
                .evictIdleConnections(10, TimeUnit.SECONDS) // 10秒清理空闲连接
                .evictExpiredConnections()  // 主动清理过期连接
                .disableContentCompression() // 禁用压缩减少CPU开销
                .disableCookieManagement()                // 禁用Cookie
                .build();

        // 配置连接存活时间（防止僵尸连接）
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
        factory.setBufferRequestBody(false);             // 避免内存双缓冲
        factory.setConnectTimeout(2_000);                // 2秒连接超时
        factory.setReadTimeout(15_000);                   // 15秒读取超时
        factory.setConnectionRequestTimeout(1_000);      // 1秒获取连接超时
        return new RestTemplate(factory);
    }


}
