package com.meta.system.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 *  Vcc 配置
 *
 */
@Component
@ConfigurationProperties(prefix = "vcc")
public class VccConfig {
    /**
     * 地址
     */
    public static String url;
    /**
     * 公钥（易鲸）
     */
    public static String publicKey;

    /**
     * 版本
     */
    public static String version;

    /**
     *  合作方id，yj提供
     */
    public static String partnerId;
    /**
     *  私钥（合作方）  todo merchant private key fill it
     */
    public static String privateStr;




    @Value("${vcc.url}")
    public void setUrl(String url) {
        VccConfig.url = url;
    }

    @Value("${vcc.version}")
    public void setVersion(String version) {
        VccConfig.version = version;
    }
    @Value("${vcc.privateStr}")
    public void setPrivateStr(String privateStr) {
        VccConfig.privateStr = privateStr;
    }

    @Value("${vcc.partnerId}")
    public void setPartnerId(String partnerId) {
        VccConfig.partnerId = partnerId;
    }
    @Value("${vcc.publicStr}")
    public void setPublicKey(String publicKey) {
        VccConfig.publicKey = publicKey;
    }

}
