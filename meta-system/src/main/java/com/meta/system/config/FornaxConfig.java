package com.meta.system.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/05/07/14:27
 */
@Component
@ConfigurationProperties(prefix = "fornax")
public class FornaxConfig {

    public static String userNo;
    public static String version;
    public static String dataType;
    public static String publicKey;
    public static String privateKey;
    public static String url;

    @Value("${fornax.userNo:}")
    public void setUserNo(String userNo) {
        FornaxConfig.userNo = userNo;
    }

    @Value("${fornax.version:}")
    public void setVersion(String version) {
        FornaxConfig.version = version;
    }

    @Value("${fornax.dataType:}")
    public void setDataType(String dataType) {
        FornaxConfig.dataType = dataType;
    }

    @Value("${fornax.publicKey:}")
    public void setPublicKey(String publicKey) {
        FornaxConfig.publicKey = publicKey;
    }

    @Value("${fornax.privateKey:}")
    public void setPrivateKey(String privateKey) {
        FornaxConfig.privateKey = privateKey;
    }

    @Value("${fornax.url:}")
    public void setUrl(String url) {
        FornaxConfig.url = url;
    }


}
