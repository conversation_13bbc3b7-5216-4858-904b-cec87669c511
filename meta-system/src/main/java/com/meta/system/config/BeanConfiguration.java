package com.meta.system.config;


import com.meta.system.fornax.http.MySocketFactoryRegistry;
import com.meta.system.fornax.http.PooledHttpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class BeanConfiguration {

    @Bean
    protected PooledHttpService initHttpService() {
        try {
            PoolingHttpClientConnectionManager httpClientConnectionManager =
                    new PoolingHttpClientConnectionManager(new MySocketFactoryRegistry().createRegistry());
            httpClientConnectionManager.setMaxTotal(50);
            HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
            httpClientBuilder.setConnectionManager(httpClientConnectionManager);
            CloseableHttpClient httpClient = httpClientBuilder.build();
            // 配置信息
            RequestConfig requestConfig = RequestConfig.custom()
                    // 设置连接超时时间(单位毫秒)
                    .setConnectTimeout(20000)
                    // 设置请求超时时间(单位毫秒)
                    .setConnectionRequestTimeout(20000)
                    // socket读写超时时间(单位毫秒)
                    .setSocketTimeout(20000)
                    // 设置是否允许重定向(默认为true)
                    .setRedirectsEnabled(true).build();

            PooledHttpService pooledHttpService = new PooledHttpService();
            pooledHttpService.setCloseableHttpClient(httpClient);
            pooledHttpService.setRequestConfig(requestConfig);
            return pooledHttpService;
        } catch (Exception e) {
            log.error("http init fail!", e);
            return null;
        }

    }
}
