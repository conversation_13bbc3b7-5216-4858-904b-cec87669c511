package com.meta.system.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 私钥加密配置属性类
 * 从yml配置文件中读取加密相关配置
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Component
@ConfigurationProperties(prefix = "encryption")
public class EncryptionProperties {

    /**
     * 是否启用加密
     */
    private boolean enabled = true;

    /**
     * 加密密钥
     */
    private String secretKey = "MetaWallet123456";

    /**
     * 加密算法
     */
    private String algorithm = "AES";

    /**
     * 编码方式
     */
    private String encoding = "BASE64";

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getAlgorithm() {
        return algorithm;
    }

    public void setAlgorithm(String algorithm) {
        this.algorithm = algorithm;
    }

    public String getEncoding() {
        return encoding;
    }

    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }

    @Override
    public String toString() {
        return "EncryptionProperties{" +
                "enabled=" + enabled +
                ", algorithm='" + algorithm + '\'' +
                ", encoding='" + encoding + '\'' +
                ", secretKeyLength=" + (secretKey != null ? secretKey.length() : 0) +
                '}';
    }
}
