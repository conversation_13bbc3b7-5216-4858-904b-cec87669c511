package com.meta.system.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025/3/7
 */
@Component
@ConfigurationProperties(prefix = "alixpay")
public class AlixpayConfig {

    public static String secretKey;
    public static String partnerCode;
    public static String apiPrivateKey;
    public static String apiPublicKey;
//    public static String webhookPublicKey;
    public static String webhookSecretKey;

    @Value("${alixpay.secretKey:''}")
    public void setClientId(String secretKey) {
        AlixpayConfig.secretKey = secretKey;
    }

    @Value("${alixpay.partnerCode:''}")
    public void setMerchantCode(String partnerCode) {
        AlixpayConfig.partnerCode = partnerCode;
    }

    @Value("${alixpay.apiPrivateKey:''}")
    public void setApiPrivateKey(String apiPrivateKey) {
        AlixpayConfig.apiPrivateKey = apiPrivateKey;
    }

    @Value("${alixpay.apiPublicKey:''}")
    public void setApiPublicKey(String apiPublicKey) {
        AlixpayConfig.apiPublicKey = apiPublicKey;
    }

//    @Value("${alixpay.webhookPublicKey}")
//    public void setWebhookPublicKey(String webhookPublicKey) {
//        AlixpayConfig.webhookPublicKey = webhookPublicKey;
//    }

    @Value("${alixpay.webhookSecretKey:''}")
    public void setWebhookSecretKey(String webhookSecretKey) {
        AlixpayConfig.webhookSecretKey = webhookSecretKey;
    }
}

