package com.meta.system.config;
/**
 * <AUTHOR>
 * @Date 2025/2/14
 */


import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * wello 配置
 */
@Component
@ConfigurationProperties(prefix = "wello")
public class WelloConfig {

    public static String clientId;
    public static String merchantCode;
    public static String apiPrivateKey;
    public static String webhookPublicKey;
    public static String walletAddress;

    @Value("${wello.clientId}")
    public void setClientId(String clientId) {
        WelloConfig.clientId = clientId;
    }

    @Value("${wello.merchantCode}")
    public void setMerchantCode(String merchantCode) {
        WelloConfig.merchantCode = merchantCode;
    }

    @Value("${wello.apiPrivateKey}")
    public void setApiPrivateKey(String apiPrivateKey) {
        WelloConfig.apiPrivateKey = apiPrivateKey;
    }

    @Value("${wello.webhookPublicKey}")
    public void setWebhookPublicKey(String webhookPublicKey) {
        WelloConfig.webhookPublicKey = webhookPublicKey;
    }

    @Value("${wello.walletAddress}")
    public void setWalletAddress(String walletAddress) {
        WelloConfig.walletAddress = walletAddress;
    }
}
