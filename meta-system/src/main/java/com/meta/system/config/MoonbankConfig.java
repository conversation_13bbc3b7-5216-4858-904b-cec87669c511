package com.meta.system.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/12/13/14:24
 */
@Component
@ConfigurationProperties(prefix = "moonbank")
public class MoonbankConfig {
    public static String gateway;
    public static String appId;
    public static String appSecret;

    public static int notifyTimeout;
    public static int notifyConnectTimeout;
    public static boolean useProxy;
    public static String proxyAddress;
    public static int proxyPort;


    @Value("${moonbank.gateway}")
    public  void setGateway(String gateway) {
        MoonbankConfig.gateway = gateway;
    }

    @Value("${moonbank.appId}")
    public  void setAppId(String appId) {
        MoonbankConfig.appId = appId;
    }

    @Value("${moonbank.appSecret}")
    public  void setAppSecret(String appSecret) {
        MoonbankConfig.appSecret = appSecret;
    }

    @Value("${moonbank.notifyTimeout}")
    public  void setNotifyTimeout(int notifyTimeout) {
        MoonbankConfig.notifyTimeout = notifyTimeout;
    }

    @Value("${moonbank.notifyConnectTimeout}")
    public  void setNotifyConnectTimeout(int notifyConnectTimeout) {
        MoonbankConfig.notifyConnectTimeout = notifyConnectTimeout;
    }

    @Value("${moonbank.useProxy}")
    public static void setUseProxy(boolean useProxy) {
        MoonbankConfig.useProxy = useProxy;
    }

    @Value("${moonbank.proxyAddress}")
    public static void setProxyAddress(String proxyAddress) {
        MoonbankConfig.proxyAddress = proxyAddress;
    }

    @Value("${moonbank.proxyPort}")
    public static void setProxyPort(int proxyPort) {
        MoonbankConfig.proxyPort = proxyPort;
    }
}
