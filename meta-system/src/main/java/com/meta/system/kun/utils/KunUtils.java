package com.meta.system.kun.utils;

import com.alibaba.fastjson.JSONObject;
import com.meta.common.utils.StringUtils;
import com.meta.system.domain.kun.MetaKunLog;
import com.meta.system.service.MetaKunLogService;
import global.kun.shade.yop.sdk.service.common.YopClient;
import global.kun.shade.yop.sdk.service.common.request.YopRequest;
import global.kun.shade.yop.sdk.service.common.response.YopResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/10/26/10:57
 */
@Slf4j
@Component
public class KunUtils {

    @Autowired
    private MetaKunLogService metaKunLogService;

    @PostConstruct
    public void init() {
        // 设置 DNS 缓存 TTL
        System.setProperty("sun.net.inetaddr.ttl", "3600");
        System.setProperty("sun.net.inetaddr.negative.ttl", "10");
    }

    /**
     * 发送请求
     *
     * @param environment 调用环境
     * @param content     请求内容
     * @param apiUri      请求路径
     * @param httpMethod  请求方式
     * @param cardId      卡id
     * @return
     */
    public YopResponse sendRequest(String environment, String content, String requestNo, String apiUri, String httpMethod, String cardId) {
        YopClient client = YopClientSingleton.getClient();
        // 指定要请求的API地址和请求方式
        YopRequest request = new YopRequest(apiUri, httpMethod);
        if ("test".equals(environment)) {
            request.getRequestConfig().setServerRoot("https://apiqa.kun.global/yop-center");
        }

        log.info("apiUri: " + apiUri);
        log.info("httpMethod: " + httpMethod);
        log.info("请求内容：" + content);
        request.setContent(content);

        YopResponse response = null;

        try {
            response = client.request(request);
            log.info("result:{}", response.getResult());
        } catch (Exception ex) {
            log.error("Exception when calling, ex:", ex);
        } finally {
            //todo 保存请求日志
            MetaKunLog kunLog = new MetaKunLog();
            kunLog.setApicode(apiUri);
            kunLog.setRequest(content);
            if (response != null) {
                kunLog.setResponse(response.getStringResult());
                if (StringUtils.isNotEmpty(response.getStringResult())) {
                    JSONObject jsonObject = JSONObject.parseObject(response.getStringResult());
                    kunLog.setCode(jsonObject.getString("code"));
                    kunLog.setDetail(jsonObject.getString("message"));

                }
            }

            kunLog.setCreatedAt(LocalDateTime.now());
            kunLog.setRequestNo(requestNo);
            kunLog.setCardId(cardId);
            metaKunLogService.save(kunLog);
        }
        return response;
    }

    /**
     * 获取返回结果
     *
     * @param environment
     * @param content
     * @param requestNo
     * @param apiUri
     * @param httpMethod
     * @param cardId
     * @return
     */
    public JSONObject result(String environment, String content, String requestNo, String apiUri, String httpMethod, String cardId) {
        YopResponse yopResponse = sendRequest(environment, content, requestNo, apiUri, httpMethod, cardId);
        String res = yopResponse.getStringResult();
        JSONObject result = null;
        if (StringUtils.isNotEmpty(res)) {
            result = JSONObject.parseObject(res);
        }
        return result;
    }


}
