package com.meta.system.kun.constants;

import com.meta.common.enums.BaseEnum;

/**
 * kun订单状态
 * <AUTHOR>
 * @date 2024/10/26/15:04
 */
public enum KunOrderStatus implements BaseEnum<KunOrderStatus> {


    PENDING("PENDING", "等待处理"),
    CLEARED("CLEARED", "已确认"),
    CLEAR("CLEAR", "已清算"),
    DECLINED("DECLINED", "已被拒绝"),
    VOID("VOID", "作废"),
    CANCEL("CANCEL","取消"),
    PROCESS("PROCESS", "处理中"),
    PROCESSING("PROCESSING", "处理中"),
    FAIL("FAIL", "失败"),
    SUCCESS("SUCCESS", "成功"),
    RECHARGING("RECHARGING", "处理中"),
    FAILED("FAILED", "失败"),
    REQUEST("REQUEST", "待授权"),
    CONFIRM("CONFIRM", "授权已确认"),
    ERROR("ERROR", "交易异常"),
    PART_CLEAR("PART_CLEAR", "部分清算"),
    REVERSAL("REVERSAL", "已撤销"),
    PART_REVERSAL("PART_REVERSAL", "部分撤销"),
    REFUND("REFUND", "已退款"),
    PART_REFUND("PART_REFUND", "部分退款"),
    ;

    String value;
    String name;

    KunOrderStatus(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public String getValue() {
        return this.value;
    }

    @Override
    public String getOrdinal() {
        return String.valueOf(this.ordinal());
    }

}
