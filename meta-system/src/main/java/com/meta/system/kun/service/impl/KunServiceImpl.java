package com.meta.system.kun.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meta.common.constant.Constants;
import com.meta.common.enums.app.PartnerTxnType;
import com.meta.common.enums.app.TxnStatus;
import com.meta.common.enums.app.TxnType;
import com.meta.common.utils.DateUtils;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.uuid.UniqueIDGenerator;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.system.config.KunConfig;
import com.meta.system.constant.KzOrderStatus;
import com.meta.system.domain.CreditCard;
import com.meta.system.domain.CreditCardLog;
import com.meta.system.domain.MetaCreditCardLogSub;
import com.meta.system.domain.app.CardConfig;
import com.meta.system.domain.app.MetaOrder;
import com.meta.system.domain.groupkey.MetaPartnerAssetPoolKey;
import com.meta.system.domain.partner.MetaPartnerAssetPool;
import com.meta.system.domain.partner.MetaPartnerTxnDtl;
import com.meta.system.domain.partner.MetaPartnerUser;
import com.meta.system.domain.partner.MetaPushData;
import com.meta.system.kun.constants.*;
import com.meta.system.kun.service.KunService;
import com.meta.system.kun.utils.AesTool;
import com.meta.system.kun.utils.KunUtils;
import com.meta.system.models.CardInfo;
import com.meta.system.models.CardRechargeResult;
import com.meta.system.models.CardResult;
import com.meta.system.service.*;
import com.meta.system.vo.CardVo;
import com.meta.system.vo.CreditCardDtl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/10/26/10:56
 */
@Service
public class KunServiceImpl implements KunService {

    Logger logger = LoggerFactory.getLogger(KunServiceImpl.class);

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private KunUtils kunUtils;

    @Autowired
    private MetaPartnerTxnDtlService metaPartnerTxnDtlService;

    @Autowired
    @Lazy
    private CreditCardService creditCardService;

    @Autowired
    private CreditCardLogService creditCardLogService;


    @Autowired
    private TxnService txnService;

    @Autowired
    private ExchangeRateService exchangeRateService;

    @Autowired
    private MetaPushDataService metaPushDataService;

    @Autowired
    private MetaPartnerAssetPoolService metaPartnerAssetPoolService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private MetaCreditCardUoldService metaCreditCardUoldService;

    @Autowired
    private MetaCreditCardLogSubService metaCreditCardLogSubService;

    @Autowired
    private MetaOrderService metaOrderService;


    /**
     * 卡信息解密密钥
     *
     * @return
     */
    @Override
    public String getAesStr() {
        JSONObject json = new JSONObject();
        String requestNo = UniqueIDGenerator.generateID();
        String kun_environment = KunConfig.environment;
        String aesKey = "";
        json.put("requestNo", requestNo);
        //卡信息解密密钥查询
        JSONObject result = kunUtils.result(kun_environment, json.toJSONString(), requestNo, KunMethod.aes_card, "POST", null);

        if (result != null) {
            if ("********".equals(result.getString("code"))) {
                aesKey = result.getString("aesKey");
            }
        }
        return aesKey;
    }

    /**
     * K信息解密密钥
     *
     * @return
     */
    @Override
    public String getKcardAesStr() {
        JSONObject json = new JSONObject();
        String requestNo = UniqueIDGenerator.generateID();
        String kun_environment = KunConfig.environment;
        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String aesKey = "";
        json.put("customerId", kun_customerId);
        json.put("requestNo", requestNo);
        //卡信息解密密钥查询
        JSONObject result = kunUtils.result(kun_environment, json.toJSONString(), requestNo, KunMethod.aes_kcard, "POST", null);

        if (result != null) {
            if ("000000".equals(result.getString("code"))) {
                aesKey = result.getString("aeskey");
            }
        }
        return aesKey;
    }

    /**
     * 將kun卡的变成kazepay的卡状态
     *
     * @param cardStatus
     * @return
     */
    @Override
    public String getCardStatus(String cardStatus) {
        if (KunCardStatus.OPENING.getValue().equals(cardStatus)) {
            return "AUDITING";
        } else if (KunCardStatus.ACTIVE.getValue().equals(cardStatus)) {
            return "ACTIVE";
        } else if (KunCardStatus.FROZEN.getValue().equals(cardStatus)) {
            return "INACTIVE";
        } else if (KunCardStatus.EXPIRED.getValue().equals(cardStatus)) {
            return "EXPIRED";
        } else if (KunCardStatus.CANCELED.getValue().equals(cardStatus)) {
            return "CLOSE";
        } else if (KunCardStatus.DESTROY.getValue().equals(cardStatus)) {
            return "CLOSE";
        } else if (KunCardStatus.DESTROYING.getValue().equals(cardStatus)) {
            return "CLOSE_PROCESSING";
        } else if (KunCardStatus.FAIL.getValue().equals(cardStatus)) {
            return "AUDIT_NOT_PASS";
        } else if (KunCardStatus.NORMAL.getValue().equals(cardStatus)) {
            return "ACTIVE";
        } else if (KunCardStatus.FREEZE.getValue().equals(cardStatus)) {
            return "INACTIVE";
        } else if (KunCardStatus.LOCKING.getValue().equals(cardStatus)) {
            return "INACTIVE";
        } else if (KunCardStatus.CANCEL.getValue().equals(cardStatus)) {
            return "CLOSE";
        } else if (KunCardStatus.PENDING.getValue().equals(cardStatus)) {
            return "AUDITING";
        }

        return cardStatus;
    }

    /**
     * kun卡交易类型
     *
     * @param type
     * @return
     */
    @Override
    public String getCardTransactionType(String type) {
        if (KunTransactionType.AUTHORIZATION.getValue().equals(type)) {
            return "AUTH";
        }
        if (KunTransactionType.AUTHORIZED.getValue().equals(type)) {
            return "AUTH";
        } else if (KunTransactionType.OTHER.getValue().equals(type)) {
            return "other";
        } else if (KunTransactionType.CLEARED.getValue().equals(type)) {
            return "CLEARING";
        } else if (KunTransactionType.REVERSAL.getValue().equals(type)) {
            return "REVERSAL";
        } else if (KunTransactionType.REFUND.getValue().equals(type)) {
            return "REFUND";
        }
        //其他为新增
        return type;
    }

    /**
     * 订单状态(返回给B端接口)
     *
     * @param type
     * @return
     */
    @Override
    public String getOrderTransactionType(String type) {


        if (KunOrderStatus.PROCESSING.getValue().equals(type)) {
            return TxnStatus.PENDING.getName();
        } else if (KunOrderStatus.FAIL.getValue().equals(type)) {
            return TxnStatus.DECLINED.getName();
        } else if (KunOrderStatus.SUCCESS.getValue().equals(type)) {
            return TxnStatus.APPROVED.getName();
        } else if (KunOrderStatus.PENDING.getValue().equals(type)) {
            return TxnStatus.PENDING.getName();
        } else if (KunOrderStatus.CLEARED.getValue().equals(type)) {
            return TxnStatus.APPROVED.getName();
        } else if (KunOrderStatus.DECLINED.getValue().equals(type)) {
            return TxnStatus.DECLINED.getName();
        } else if (KunOrderStatus.VOID.getValue().equals(type)) {
            return TxnStatus.CANCEL.getName();
        } else if (KunOrderStatus.CANCEL.getValue().equals(type)) {
            return TxnStatus.CANCEL.getName();
        } else if (KunOrderStatus.RECHARGING.getValue().equals(type)) {
            return TxnStatus.PENDING.getName();
        } else if (KunOrderStatus.FAILED.getValue().equals(type)) {
            return TxnStatus.DECLINED.getName();
        } else if (KunOrderStatus.REQUEST.getValue().equals(type)) {
            return TxnStatus.PENDING.getName();
        } else if (KunOrderStatus.CONFIRM.getValue().equals(type)) {
            return TxnStatus.CONFIRM.getName();
        } else if (KunOrderStatus.ERROR.getValue().equals(type)) {
            return TxnStatus.DECLINED.getName();
        } else if (KunOrderStatus.CLEAR.getValue().equals(type)) {
            return TxnStatus.APPROVED.getName();
        } else if (KunOrderStatus.PART_CLEAR.getValue().equals(type)) {
            return TxnStatus.APPROVED.getName();
        } else if (KunOrderStatus.REVERSAL.getValue().equals(type)) {
            return TxnStatus.APPROVED.getName();
        } else if (KunOrderStatus.PART_REVERSAL.getValue().equals(type)) {
            return TxnStatus.APPROVED.getName();
        } else if (KunOrderStatus.REFUND.getValue().equals(type)) {
            return TxnStatus.APPROVED.getName();
        } else if (KunOrderStatus.PART_REFUND.getValue().equals(type)) {
            return TxnStatus.APPROVED.getName();
        }

        //交易的状态：PENDING（等待处理）、CLEARED（已确认）、DECLINED（已被拒绝）、VOID（交易作废）
        //REQUEST-待授权、SUCCESS-授权成功、FAIL-授权失败、CONFIRM-授权已确认、ERROR-交易异常

        return type;
    }

    /**
     * 订单状态(在kazepay的状态)
     *
     * @param status
     * @return
     */
    @Override
    public Character getOrderStatus(String status) {
        if (KunOrderStatus.PROCESSING.getValue().equals(status)) {
            return TxnStatus.PENDING.getValue();
        } else if (KunOrderStatus.FAIL.getValue().equals(status)) {
            return TxnStatus.DECLINED.getValue();
        } else if (KunOrderStatus.SUCCESS.getValue().equals(status)) {
            return TxnStatus.APPROVED.getValue();
        } else if (KunOrderStatus.PENDING.getValue().equals(status)) {
            return TxnStatus.PENDING.getValue();
        } else if (KunOrderStatus.CLEARED.getValue().equals(status)) {
            return TxnStatus.APPROVED.getValue();
        } else if (KunOrderStatus.DECLINED.getValue().equals(status)) {
            return TxnStatus.DECLINED.getValue();
        } else if (KunOrderStatus.VOID.getValue().equals(status)) {
            return TxnStatus.CANCEL.getValue();
        } else if (KunOrderStatus.CANCEL.getValue().equals(status)) {
            return TxnStatus.CANCEL.getValue();
        } else if (KunOrderStatus.RECHARGING.getValue().equals(status)) {
            return TxnStatus.PENDING.getValue();
        } else if (KunOrderStatus.FAILED.getValue().equals(status)) {
            return TxnStatus.DECLINED.getValue();
        }
        return null;
    }

    @Override
    public JSONObject updateCardBalance(JSONObject bodyJson, CreditCard c) {
        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String kun_environment = KunConfig.environment;

        JSONObject jsonKun = new JSONObject();
        String requestNoKun = UniqueIDGenerator.generateID();

        jsonKun.put("customerId", kun_customerId);
        jsonKun.put("requestNo", requestNoKun);
        JSONObject result = null;
        if ("87".equals(c.getCardType())) {
            jsonKun.put("cardId", c.getBankCardId());
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.card_info, "POST", c.getBankCardId());
        } else {
            jsonKun.put("cardId", c.getBankCardId());
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.kcard_info, "POST", c.getBankCardId());
        }

        if (result != null) {
            if (("87".equals(c.getCardType()) && "********".equals(result.getString("code")))
                    || (!"87".equals(c.getCardType()) && "000000".equals(result.getString("code")))) {


                String balance = result.getString("balance");
                BigDecimal avaBalance = BigDecimal.ZERO;
                if (StringUtils.isNotEmpty(balance)) {
                    if (!"0.****************0000".equals(balance)) {
                        avaBalance = new BigDecimal(balance);

                    }

                }
                String cardStatus = "";
                if ("87".equals(c.getCardType())) {
                    cardStatus = result.getString("cardStatus");
                } else {
                    cardStatus = result.getString("status");
                }

                String status = c.getCardStatus();
                String newstatus = getCardStatus(cardStatus);
                if (StringUtils.isNotEmpty(newstatus) && !newstatus.equals(status)) {
                    c.setStatusUpdateTime(new Date());
                }
                c.setCardStatus(newstatus);
                c.setBalance(avaBalance);


                bodyJson.put("cardID", c.getCardId());
                bodyJson.put("currency", c.getCurrency());
                bodyJson.put("avaBalance", avaBalance);
                bodyJson.put("blockBalance", BigDecimal.ZERO);
                creditCardService.save(c);
                return bodyJson;

            } else {
                //返回错误
                return returnError(bodyJson, result);
            }

        }
        return null;
    }


    /**
     * 返回错误信息
     *
     * @param json
     * @param result
     * @return
     */
    @Override
    public JSONObject returnError(JSONObject json, JSONObject result) {
        String message = result.getString("message");
        json.put("error_message", message);
        logger.info("======调用错误===== " + message);
        return json;
    }


    @Override
    @Transactional
    public void queryCardOpenResultTaskKZ() {
        List<CreditCardDtl> list = creditCardService.findOpening("kun",
                getCardStatus(KunCardStatus.OPENING.getValue()),
                String.valueOf(TxnStatus.PADDING.getValue()),
                TxnType.COIN_OPEN_CARD_OUT.getValue(),
                TxnType.CARD_ACTIVATION_FEE_C.getValue(),
                TxnType.CARD_ACTIVATION.getValue());

        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String kun_environment = KunConfig.environment;

        for (CreditCardDtl cardDtl : list) {

            JSONObject jsonKun = new JSONObject();
            String requestNoKun = cardDtl.getRequestNo();

            jsonKun.put("customerId", kun_customerId);
            jsonKun.put("requestNo", requestNoKun);


            CreditCard card = creditCardService.findByCardId(cardDtl.getCardId());
            JSONObject result = null;
            if ("87".equals(card.getCardType())) {
                result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.open_card_query, "POST", cardDtl.getCardId());
            } else {
                result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.open_kcard_query, "POST", null);
            }

            if (result != null) {
                if ("87".equals(card.getCardType()) && "********".equals(result.getString("code"))) {
                    String status = result.getString("status");
                    String cardId = result.getString("cardId");
                    //查询流水
                    String orderTransactionType = getOrderTransactionType(status);
                    logger.info("开卡订单状态: " + status);
                    Character orderStatus = getOrderStatus(status);
                    if (TxnStatus.PENDING.getValue() != orderStatus) {
                        logger.info("开卡流水: " + cardDtl.getId());
                        creditCardService.openCardDeal(cardDtl.getCardId(), orderTransactionType, cardDtl.getType(), requestNoKun);
                        MetaOrder metaOrder = metaOrderService.findRequestNo(requestNoKun);
                        if (metaOrder != null) {
                            if (TxnStatus.APPROVED.getName().equals(orderTransactionType)) {
                                metaOrder.setStatus(KzOrderStatus.success.getValue());
                            } else if (TxnStatus.DECLINED.getName().equals(orderTransactionType)) {
                                metaOrder.setStatus(KzOrderStatus.fail.getValue());
                            }
                            metaOrderService.save(metaOrder);
                        }

                    }
                } else if (!"87".equals(card.getCardType()) && "000000".equals(result.getString("code"))) {
                    String status = result.getString("status");
                    String cardId = result.getString("cardId");

//                    creditCardService.save(card);

                    Character orderStatus = getOpenKcardOrderStatus(status);
                    String orderTransactionType = getOpenKcardOrderType(status);
                    if (TxnStatus.PENDING.getValue() != orderStatus) {
                        logger.info("开卡流水: " + cardDtl.getId());
                        creditCardService.openCardDeal(cardDtl.getCardId(), orderTransactionType, cardDtl.getType(), requestNoKun);
                        MetaOrder metaOrder = metaOrderService.findRequestNo(requestNoKun);
                        if (metaOrder != null) {
                            if (TxnStatus.APPROVED.getName().equals(orderTransactionType)) {
                                metaOrder.setStatus(KzOrderStatus.success.getValue());
                            } else if (TxnStatus.DECLINED.getName().equals(orderTransactionType)) {
                                metaOrder.setStatus(KzOrderStatus.fail.getValue());
                            }
                            metaOrderService.save(metaOrder);
                        }
                    }

                }

            }
        }

    }

    /**
     * k卡开卡的订单状态
     *
     * @param status
     */
    @Override
    public Character getOpenKcardOrderStatus(String status) {

        if (KunCardStatus.NORMAL.getValue().equals(status)) {
            return TxnStatus.SUCCESS.getValue();
        } else if (KunCardStatus.FAIL.getValue().equals(status)) {
            return TxnStatus.FAIL.getValue();
        } else {
            return TxnStatus.PENDING.getValue();
        }
    }

    /**
     * k卡开卡的订单名称
     *
     * @param status
     */
    @Override
    public String getOpenKcardOrderType(String status) {

        if (KunCardStatus.NORMAL.getValue().equals(status)) {
            return TxnStatus.APPROVED.getName();
        } else if (KunCardStatus.FAIL.getValue().equals(status)) {
            return TxnStatus.DECLINED.getName();
        } else {
            return TxnStatus.PENDING.getName();
        }
    }

    @Override
    @Transactional
    public void queryCardRechargeResultTaskKZ() {
        List<CreditCardDtl> list = creditCardService.findRecharge("kun",
                getCardStatus(KunCardStatus.OPENING.getValue()),
                String.valueOf(TxnStatus.PADDING.getValue()),
                TxnType.COIN_CARD_RECHARGE_OUT.getValue(),
                TxnType.CARD_RECHARGE_FEE_C.getValue());


        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String kun_environment = KunConfig.environment;

        for (CreditCardDtl cardDtl : list) {

            JSONObject jsonKun = new JSONObject();
            String requestNoKun = cardDtl.getRequestNo();

            jsonKun.put("customerId", kun_customerId);
            jsonKun.put("requestNo", requestNoKun);

            CreditCard card = creditCardService.findByCardId(cardDtl.getCardId());
            JSONObject result = null;
            if ("87".equals(card.getCardType())) {
                result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.card_recharge_query, "POST", card.getBankCardId());
            } else {
                result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.kcard_recharge_query, "POST", card.getBankCardId());
            }

            if (result != null) {
                if (("87".equals(card.getCardType()) && "********".equals(result.getString("code")))
                        || (!"87".equals(card.getCardType()) && "000000".equals(result.getString("code")))) {
                    String status = result.getString("status");
                    String orderTransactionType = getOrderTransactionType(status);
                    logger.info("充值订单状态: " + status);
                    Character orderStatus = getOrderStatus(status);

                    if (TxnStatus.PENDING.getValue() != orderStatus) {
                        logger.info("充值流水: " + cardDtl.getRequestNo());
                        creditCardService.dealRecharge(cardDtl.getCardId(), cardDtl.getRequestNo(), orderTransactionType, cardDtl.getType());
                        CreditCard c = creditCardService.findByCardId(cardDtl.getCardId());

                    }
                }

            }
        }

    }


    /**
     * 更新卡交易数据
     *
     * @param c
     */
    @Override
    public List<CreditCardLog> updateCardOrder(CreditCard c) {
        logger.info("查询卡id的数据："+c.getCardId());
        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String kun_environment = KunConfig.environment;
        String kun_start_day = configService.selectConfigByKey("kun_start_day");
        JSONObject jsonKun = new JSONObject();
        String requestNoKun = UniqueIDGenerator.generateID();
        jsonKun.put("customerId", kun_customerId);
        jsonKun.put("requestNo", requestNoKun);
        // 获取当前时间（UTC）
        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);
        // 获取当前时间（UTC）
        // 定义 ISO 8601 格式
        DateTimeFormatter formatter = DateTimeFormatter.ISO_INSTANT;

        ZonedDateTime daysAgo = getZonedDateTime(kun_start_day, now);
        logger.info("当前时间: " + now.format(formatter));
        logger.info(kun_start_day + "天前的时间: " + daysAgo.format(formatter));
        DateTimeFormatter fm = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter fm2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 转换为字符串
        String transactionStartTime = daysAgo.format(fm);


        jsonKun.put("pageNo", "1");
        jsonKun.put("pageSize", "100");
        JSONObject result = null;
        if ("87".equals(c.getCardType())) {
            jsonKun.put("transactionStartTime", daysAgo.format(formatter));
            jsonKun.put("transactionEndTime", now.format(formatter));
            jsonKun.put("cardId", c.getBankCardId());
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.card_order_history, "POST", c.getBankCardId());
        } else {
            jsonKun.put("transactionStartTime", daysAgo.format(fm2));
            jsonKun.put("transactionEndTime", now.format(fm2));
            jsonKun.put("cardId", c.getBankCardId());
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.kcard_order_history, "POST", c.getBankCardId());
        }

        String balance = "";

        if (result != null) {
            if ("87".equals(c.getCardType()) && "********".equals(result.getString("code"))) {
                String totalPage = result.getString("totalPage");
                logger.info("总页数：" + totalPage);
                if (!"0".equals(totalPage)) {

                    JSONArray dataArray = result.getJSONArray("data");
                    if (dataArray.size() > 0) {
                        List<String> list = creditCardLogService.findByCardId(c.getCardId());
                        List<String> pendingList = creditCardLogService.findPending(c.getCardId(), transactionStartTime, "PENDING");
                        List<CreditCardLog> unsaveList = new ArrayList<>();
                        // 遍历数据
                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject dataObject = dataArray.getJSONObject(i);
                            boolean exists = false;

                            boolean pend = false;//是不是已存在的pending数据

                            //由于kun卡的PENDING数据需要更新
                            for (String txnId : pendingList) {
                                if (dataObject.getString("orderId").equals(txnId)) {
                                    pend = true;
                                    break;
                                }
                            }
                            List<CreditCardLog> logList = null;
                            if (pend) {
                                logList = creditCardLogService.findData(dataObject.getString("orderId"));
                            } else {
                                //不是pending的数据才判断是否存在
                                for (String txnId : list) {
                                    if (dataObject.getString("orderId").equals(txnId)) {
                                        exists = true;
                                        break;
                                    }
                                }

                            }
                            if (!exists) {
                                String transactionId = dataObject.getString("transactionId");//交易时生成的订单号
                                String orderId = dataObject.getString("orderId");//客户服务系统中唯一的订单标识
                                // String orderType = dataObject.getString("orderType");//交易类型：PAY表示消费支付，REFUND表示退款
                                String customerId = dataObject.getString("customerId");//客户的唯一识别ID

                                String cardNo = dataObject.getString("cardNo");//银行卡或信用卡的卡号
                                String transactionAmount = dataObject.getString("transactionAmount");//实际收单金额，保留两位小数，单位根据transactionCurrency确定
                                String transactionCurrency = getCoin(dataObject.getString("transactionCurrency"));//进行交易时使用的货币种类，例如CNY（人民币）、USD（美元）等
                                String billAmount = dataObject.getString("billAmount");//授权给商户可收取的最大金额
                                String billReversalAmount = dataObject.getString("billReversalAmount");//如果交易被撤销，此字段记录应退还给客户的金额
                                String clearAmount = dataObject.getString("clearAmount");//已经结算到商户账户的金额
                                // String feeAmount = dataObject.getString("feeAmount");//银行或支付机构向商户收取的服务费用

                                String feeCurrency = "";
                                if (!"87".equals(c.getCardType())) {
                                    feeCurrency = getCoin(dataObject.getString("feeAmountCurrency"));
                                }
                                String cardCurrency = getCoin(dataObject.getString("cardCurrency"));//卡片默认的货币种类

                                //交易的状态：PENDING（等待处理）、CLEARED（已确认）、DECLINED（已被拒绝）、VOID（交易作废）
                                //REQUEST-待授权、SUCCESS-授权成功、FAIL-授权失败、CONFIRM-授权已确认、ERROR-交易异常
                                String status = dataObject.getString("status");
                                String createTransactionDate = dataObject.getString("createTransactionDate");//创建该笔交易的时间戳
                                String tradeDate = dataObject.getString("tradeDate");//交易真正发生的时间
                                String remark = dataObject.getString("remark");//若交易失败，此处填写具体的失败原因
                                String merchantNameLocation = dataObject.getString("merchantNameLocation");//执行收款操作的商户名称及地点
                                String merchantCategoryCode = dataObject.getString("merchantCategoryCode");//商户所属行业类别代码，由4位数字组成，如餐馆可能是5812.

                                if (logList != null && logList.size() > 0) {
                                    if (getOrderTransactionType(status).equals(logList.get(0).getTransactionStatus())) {

                                        break;
                                    }
                                }

                                //查询详情
                                JSONObject orderDetail = getOrderDetail(orderId, createTransactionDate, c);
                                if (orderDetail != null) {
                                    JSONArray detailArray = orderDetail.getJSONArray("data");
                                    for (int j = 0; j < detailArray.size(); j++) {
                                        JSONObject detailObject = detailArray.getJSONObject(j);
//                                        String subOrderId = detailObject.getString("subOrderId");//唯一订单号
////                                        String transactionAmount = detailObject.getString("transactionAmount");//交易金额
////                                        String transactionCurrency = getCoin(detailObject.getString("transactionCurrency"));//交易币种
//                                        String billingAmount = detailObject.getString("billingAmount");//订单清算金额
//                                        String billingCurrency = getCoin(detailObject.getString("billingCurrency"));//订单清算币种
//                                        String movingAmount = detailObject.getString("movingAmount");//总动账金额
//                                        String movingCurrency = getCoin(detailObject.getString("movingCurrency"));//动账币种
                                        String orderType = detailObject.getString("orderType");//订单类型（3-授权、4-授权撤销、1-清算、2-退款、5-授权失败，实际处理按type进行排序）
//
//                                        String tradeTime = detailObject.getString("tradeTime");//交易时间
                                        String feeAmount = detailObject.getString("feeAmount");//交易手续费
                                        if ("87".equals(c.getCardType())) {
                                            feeCurrency = getCoin(detailObject.getString("feeCurrency"));//手续费币种
                                        }

//                                        String accountStatus = detailObject.getString("accountStatus");//动账状态（INIT-初始化、PROCESS-扣款中、WAIT-待处理、SUCC-成功）
//                                        String affectMerBalance = detailObject.getString("affectMerBalance");//是否影响商户资金账户余额
//                                        String channelCode = detailObject.getString("channelCode");//卡通道
//                                        String channelNotifyType = detailObject.getString("channelNotifyType");//授权通知类型（授权先到、清算先到）
                                        String merchantName = detailObject.getString("merchantName");//通道返回商户名
                                        String merchantCity = detailObject.getString("merchantCity");//通道返回城市
//                                        String merchantCountry = detailObject.getString("merchantCountry");//通道返回商家国家
//                                        String mccCode = detailObject.getString("mccCode");//商家mcc
//                                        String mccCategory = detailObject.getString("mccCategory");//类别


                                        //保存creditCardLog
                                        CreditCardLog log = null;
                                        if (logList != null) {
                                            for (CreditCardLog log1 : logList) {
                                                System.out.println(getCardTransactionType(orderType));
                                                System.out.println(log1.getTransactionType());
                                                if (!getCardTransactionType(orderType).equals(log1.getTransactionType())) {

                                                    continue;
                                                } else {
                                                    log = log1;
                                                    break;
                                                }
                                            }
                                            if (log == null) {
                                                continue;
                                            }
                                        } else {
                                            log = new CreditCardLog();
                                            //只保存一条数据
                                            if (j > 0) {
                                                break;
                                            }
                                        }


                                        log.setCardId(c.getCardId());//卡号
                                        log.setTransactionId(orderId);//交易id
                                        log.setTransactionCurrency(transactionCurrency);//交易币种:货币三位代码

                                        log.setTransactionAmount(new BigDecimal(transactionAmount));//交易金额
                                        log.setCardCurrency(cardCurrency);//卡币种:货币三位代码
                                        log.setCardTransactionAmount(new BigDecimal(billAmount));//卡交易金额

                                        log.setTransactionType(getCardTransactionType(orderType));//交易类型：
                                        // todo
                                        log.setTransactionStatus(getOrderTransactionType(status));//交易状态
                                        if ("87".equals(c.getCardType())) {
                                            String formattedDateTime = getTimeString(tradeDate);
                                            log.setTransactionDate(formattedDateTime);//交易时间
                                        } else {
                                            log.setTransactionDate(tradeDate);//交易时间
                                        }


                                        if (StringUtils.isNotEmpty(feeAmount)) {
                                            log.setFee(new BigDecimal(feeAmount));//手续费金额
                                        }

                                        log.setFeeCurrency(feeCurrency);//手续费卡币种
                                        log.setMerchantName(merchantName);//商户名称
                                        log.setMerchantCity(merchantCity);//商户所在城市

                                        if (StringUtils.isNotEmpty(remark)) {
                                            log.setDescription(remark);//描述
                                        }
                                        log.setTransactionDetail(null);//交易详细信息
                                        log.setRespCode(null);

                                        //查询卡余额
                                        if (i < 1) {
                                            JSONObject balanceObject = updateCardBalance(new JSONObject(), c);
                                            balance = balanceObject.getString("avaBalance");
                                        }
                                        if (StringUtils.isNotEmpty(balance)) {
                                            log.setCardAvailableBalance(new BigDecimal(balance));//卡可用余额
                                        }
                                        log.setCardAvailableBalanceCurrency(c.getCurrency());//卡可用余额币种

                                        unsaveList.add(log);


                                    }


                                }


                            }


                        }
                        if (!unsaveList.isEmpty()) {
                            creditCardLogService.saveAll(unsaveList);
                            return unsaveList;
                        }

                    }

                }
            } else if (!"87".equals(c.getCardType()) && "000000".equals(result.getString("code"))) {
                String totalPage = result.getString("totalPage");
                logger.info("总页数：" + totalPage);
                if (!"0".equals(totalPage)) {

                    JSONArray dataArray = result.getJSONArray("data");
                    if (dataArray.size() > 0) {

                        List<CreditCardLog> pendingList = creditCardLogService.findConfirm(c.getCardId(), transactionStartTime, "CONFIRM");
                        List<CreditCardLog> unsaveList = new ArrayList<>();
                        // 遍历数据
                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject dataObject = dataArray.getJSONObject(i);

                            //订单id
                            String orderId = dataObject.getString("orderId");//客户服务系统中唯一的订单标识

                            CreditCardLog log = null;
                            for (CreditCardLog logv : pendingList) {
                                if (Objects.equals(logv.getTransactionId(), orderId)) {
                                    log = logv;
                                    break;
                                }
                            }

                            // String orderType = dataObject.getString("orderType");//交易类型：PAY表示消费支付，REFUND表示退款
                            String customerId = dataObject.getString("customerId");//客户的唯一识别ID

                            String cardNo = dataObject.getString("cardNo");//银行卡或信用卡的卡号
                            BigDecimal transactionAmount = new BigDecimal(dataObject.getString("transactionAmount"));//交易金额
                            String transactionCurrency = getCoin(dataObject.getString("transactionCurrency"));//交易币种
                            BigDecimal billAmount = new BigDecimal(dataObject.getString("billAmount"));//账单金额
                            String billingCurrency = getCoin(dataObject.getString("billingCurrency"));//账单币种

                            BigDecimal feeAmount = new BigDecimal(dataObject.getString("feeAmount"));//银行或支付机构向商户收取的服务费用
                            String feeAmountCurrency = dataObject.getString("feeAmountCurrency");//银行或支付机构向商户收取的服务费用


                            String cardCurrency = getCoin(dataObject.getString("cardCurrency"));//卡片默认的货币种类
                            //交易的状态：PENDING（等待处理）、CLEARED（已确认）、DECLINED（已被拒绝）、VOID（交易作废）
                            //REQUEST-待授权、SUCCESS-授权成功、FAIL-授权失败、CONFIRM-授权已确认、ERROR-交易异常
                            String status = getOrderTransactionType(dataObject.getString("status"));
                            String createTransactionDate = dataObject.getString("createTransactionDate");//创建该笔交易的时间戳
                            String tradeDate = dataObject.getString("tradeDate");//交易真正发生的时间
                            String remark = dataObject.getString("remark");//若交易失败，此处填写具体的失败原因
                            String merchantNameLocation = dataObject.getString("merchantNameLocation");//执行收款操作的商户名称及地点
                            String merchantCategoryCode = dataObject.getString("merchantCategoryCode");//商户所属行业类别代码，由4位数字组成，如餐馆可能是5812.


                            BigDecimal billReversalAmount = new BigDecimal(dataObject.getString("billReversalAmount"));//如果交易被撤销，此字段记录应退还给客户的金额
                            BigDecimal clearAmount = new BigDecimal(dataObject.getString("clearAmount"));//已经结算到商户账户的金额


                            if (log == null) {
                                log = new CreditCardLog();
                                //数据新增
                                log.setCardId(c.getCardId());
                                log.setTransactionId(orderId); //交易id
                                log.setTransactionCurrency(transactionCurrency);//交易币种
                                log.setTransactionAmount(transactionAmount);//交易金额
                                log.setCardCurrency(billingCurrency);//卡币种
                                log.setCardTransactionAmount(billAmount);//卡金额

                                log.setTransactionType("AUTH");//交易类型
                                log.setTransactionStatus(status);//交易状态

                                log.setTransactionDate(tradeDate);//交易日期

                                log.setFeeCurrency(feeAmountCurrency);//手续费币种
                                log.setMerchantName(merchantNameLocation);//商户名称

                                //todo
                                getCardAvailableBalance(log, c);//卡有效金额

                                //log.setDescription(); 交易失败的原因

                            }
//                            else {
//                                //判断金额是否正确
//                                if (clearAmount.compareTo(BigDecimal.ZERO) > 0) {
//                                    //清算
//                                    //判断是否是部分清算
//
//
//                                } else if (billReversalAmount.compareTo(billAmount) == 0) {//退款
//
//
//                                }
//                            }
                            log.setFee(feeAmount);//手续费
                            //子订单处理
                            boolean b = detailSubOrder(c, orderId, log, createTransactionDate);
                            if (!b) {
                                continue;
                            }
                            //判断
                            if (clearAmount.compareTo(billAmount) == 0) {
                                //清算完成
                                log.setTransactionStatus(TxnStatus.APPROVED.getName());
                            } else if (billReversalAmount.compareTo(billAmount) == 0) {
                                //撤销完成
                                log.setTransactionStatus(TxnStatus.APPROVED.getName());
                            }
                            if (clearAmount.compareTo(BigDecimal.ZERO) > 0) {
                                //部分清算
                                log.setTransactionStatus(TxnStatus.APPROVED.getName());
                            }
                            unsaveList.add(log);

                        }
                        if (!unsaveList.isEmpty()) {
                            creditCardLogService.saveAll(unsaveList);
                            return unsaveList;
                        }

                    }

                }
            }
        }
        return null;

    }

    /**
     * 子订单处理
     *
     * @param c
     * @param orderId
     * @param log
     * @param createTransactionDate
     */
    private boolean detailSubOrder(CreditCard c, String orderId, CreditCardLog log, String createTransactionDate) {

        boolean flag = false;//是否有新增数据
        //查询详情
        JSONObject orderDetail = getOrderDetail(orderId, createTransactionDate, c);

        //获取订单详情
        List<MetaCreditCardLogSub> subList = metaCreditCardLogSubService.findList(orderId);
        if (orderDetail != null) {
            JSONArray detailArray = orderDetail.getJSONArray("data");
            if (subList.size() == detailArray.size()) {
                return false; //无新增数据
            }
            for (int j = 0; j < detailArray.size(); j++) {
                JSONObject detailObject = detailArray.getJSONObject(j);


                String orderTypeSub = getCardTransactionType(detailObject.getString("orderType"));//订单类型（3-授权、4-授权撤销、1-清算、2-退款、5-授权失败，实际处理按type进行排序）

                String transactionCurrencySub = getCoin(detailObject.getString("transactionCurrency"));
                BigDecimal transactionAmountSub = new BigDecimal(detailObject.getString("transactionAmount"));
                BigDecimal billAmountSub = new BigDecimal(detailObject.getString("billAmount"));
                String billingCurrencySub = getCoin(detailObject.getString("billingCurrency"));
                BigDecimal feeAmountSub = new BigDecimal(detailObject.getString("feeAmount"));
                String feeAmountCurrency = detailObject.getString("feeAmountCurrency");
                String merchantCity = detailObject.getString("merchantCity");
                String tradeDateSub = detailObject.getString("tradeDate");
                String merchantName = detailObject.getString("merchantName");
                String subOrderId = detailObject.getString("subOrderId");
                String statusSub = getOrderTransactionType(detailObject.getString("status"));

                if (StringUtils.isEmpty(log.getMerchantCity())) {
                    log.setMerchantCity(merchantCity);
                }

                MetaCreditCardLogSub sub = null;
                for (MetaCreditCardLogSub metaCreditCardLogSub : subList) {
                    if (subOrderId.equals(metaCreditCardLogSub.getTransactionIdSub())) {
                        sub = metaCreditCardLogSub;
                    }
                }

                if (sub == null) {
                    sub = new MetaCreditCardLogSub();
                }

                sub.setCardId(c.getCardId());
                sub.setTransactionId(orderId);
                sub.setTransactionIdSub(subOrderId);
                sub.setTransactionDate(tradeDateSub);
                sub.setTransactionCurrency(transactionCurrencySub);
                sub.setTransactionAmount(transactionAmountSub);
                sub.setTransactionType(orderTypeSub);

                sub.setCardCurrency(billingCurrencySub);
                sub.setCardTransactionAmount(billAmountSub);
                sub.setFee(feeAmountSub);
                sub.setFeeCurrency(feeAmountCurrency);
                sub.setTransactionStatus(statusSub);
                metaCreditCardLogSubService.save(sub);

            }
            flag = true;

        }
        return flag;
    }

    /**
     * 交易后剩余有效金额处理
     *
     * @param log
     * @param c
     */
    private void getCardAvailableBalance(CreditCardLog log, CreditCard c) {
        if (log.getCardAvailableBalance() == null) {
            //查询卡余额

            JSONObject balanceObject = updateCardBalance(new JSONObject(), c);
            String balance = balanceObject.getString("avaBalance");
            if (StringUtils.isNotEmpty(balance)) {
                log.setCardAvailableBalance(new BigDecimal(balance));//卡余额
            }
            log.setCardAvailableBalanceCurrency(c.getCurrency());//卡币种
        }

    }

    /**
     * 币种转换，如果是数字就转为英文编码
     *
     * @param code
     * @return
     */
    public String getCoin(String code) {
        if (StringUtils.isNotEmpty(code)) {
            if (isNumeric(code)) {
                int data = Integer.parseInt(code);
                String s = stringRedisTemplate.opsForValue().get(Constants.TRADE_COIN + data);
                return s;
            } else {
                return code;
            }
        }

        return code;

    }

    // 判断字符串是否是数字
    public static boolean isNumeric(String str) {
        return str != null && str.matches("\\d+");
    }
//    /**
//     * 更新卡交易数据(kun卡一个记录有两个详情，这里改成两条消费记录)
//     *
//     * @param c
//     */
//    @Override
//    public List<CreditCardLog> updateCardOrder(CreditCard c) {
//        String kun_customerId = configService.selectConfigByKey("kun_customerId");
//        String kun_environment = KunConfig.environment;
//        String kun_start_day = configService.selectConfigByKey("kun_start_day");
//        JSONObject jsonKun = new JSONObject();
//        String requestNoKun = UniqueIDGenerator.generateID();
//        jsonKun.put("customerId", kun_customerId);
//        jsonKun.put("requestNo", requestNoKun);
//        // 获取当前时间（UTC）
//        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);
//        // 获取当前时间（UTC）
//        // 定义 ISO 8601 格式
//        DateTimeFormatter formatter = DateTimeFormatter.ISO_INSTANT;
//
//        ZonedDateTime daysAgo = getZonedDateTime(kun_start_day, now);
//        logger.info("当前时间: " + now.format(formatter));
//        logger.info(kun_start_day + "天前的时间: " + daysAgo.format(formatter));
//        DateTimeFormatter fm = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//        // 转换为字符串
//        String transactionStartTime = daysAgo.format(fm);
//
//        jsonKun.put("transactionStartTime", daysAgo.format(formatter));
//        jsonKun.put("transactionEndTime", now.format(formatter));
//        jsonKun.put("cardId", c.getCardId());
//        jsonKun.put("pageNo", "1");
//        jsonKun.put("pageSize", "100");
//        JSONObject result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.card_order_history, "POST", c.getCardId());
//
//        String balance = "";
//
//        if (result != null) {
//            if ("********".equals(result.getString("code"))) {
//                String totalPage = result.getString("totalPage");
//                logger.info("总页数：" + totalPage);
//                if (!"0".equals(totalPage)) {
//
//                    JSONArray dataArray = result.getJSONArray("data");
//                    if (dataArray.size() > 0) {
//                        List<String> list = creditCardLogService.findByCardId(c.getCardId());
//                        List<String> pendingList = creditCardLogService.findPending(c.getCardId(), transactionStartTime, "PENDING");
//                        List<CreditCardLog> unsaveList = new ArrayList<>();
//
//                        // 遍历数据
//                        for (int i = 0; i < dataArray.size(); i++) {
//                            JSONObject dataObject = dataArray.getJSONObject(i);
//                            boolean exists = false;
//
//                            boolean pend = false;//是不是已存在的pending数据
//
//                            //由于kun卡的PENDING数据需要更新
//                            for (String txnId : pendingList) {
//                                if (dataObject.getString("orderId").equals(txnId)) {
//                                    pend = true;
//                                    break;
//                                }
//                            }
//                            List<CreditCardLog> logList = null;
//                            if (pend) {
//                                logList = creditCardLogService.findData(dataObject.getString("orderId"));
//                            } else {
//                                //不是pending的数据才判断是否存在
//                                for (String txnId : list) {
//                                    if (dataObject.getString("orderId").equals(txnId)) {
//                                        exists = true;
//                                        break;
//                                    }
//                                }
//
//                            }
//
//                            if (!exists) {
//
//
//                                String transactionId = dataObject.getString("transactionId");//交易时生成的订单号
//                                String orderId = dataObject.getString("orderId");//客户服务系统中唯一的订单标识
//                                // String orderType = dataObject.getString("orderType");//交易类型：PAY表示消费支付，REFUND表示退款
//                                String customerId = dataObject.getString("customerId");//客户的唯一识别ID
//                                String cardId = dataObject.getString("cardId");//用于交易的卡片ID
//                                String cardNo = dataObject.getString("cardNo");//银行卡或信用卡的卡号
//                                // String transactionAmount = dataObject.getString("transactionAmount");//实际收单金额，保留两位小数，单位根据transactionCurrency确定
//                                // String transactionCurrency = dataObject.getString("transactionCurrency");//进行交易时使用的货币种类，例如CNY（人民币）、USD（美元）等
//                                String billAmount = dataObject.getString("billAmount");//授权给商户可收取的最大金额
//                                String billReversalAmount = dataObject.getString("billReversalAmount");//如果交易被撤销，此字段记录应退还给客户的金额
//                                String clearAmount = dataObject.getString("clearAmount");//已经结算到商户账户的金额
//                                // String feeAmount = dataObject.getString("feeAmount");//银行或支付机构向商户收取的服务费用
//                                String cardCurrency = dataObject.getString("cardCurrency");//卡片默认的货币种类
//                                String status = dataObject.getString("status");//交易的状态：PENDING（等待处理）、CLEARED（已确认）、DECLINED（已被拒绝）、VOID（交易作废）
//                                String createTransactionDate = dataObject.getString("createTransactionDate");//创建该笔交易的时间戳
//                                String tradeDate = dataObject.getString("tradeDate");//交易真正发生的时间
//                                String remark = dataObject.getString("remark");//若交易失败，此处填写具体的失败原因
//                                String merchantNameLocation = dataObject.getString("merchantNameLocation");//执行收款操作的商户名称及地点
//                                String merchantCategoryCode = dataObject.getString("merchantCategoryCode");//商户所属行业类别代码，由4位数字组成，如餐馆可能是5812.
//
//
//                                //查询详情
//                                JSONObject orderDetail = getOrderDetail(orderId, createTransactionDate);
//                                if (orderDetail != null) {
//                                    JSONArray detailArray = orderDetail.getJSONArray("data");
//                                    for (int j = 0; j < detailArray.size(); j++) {
//
//
//                                        JSONObject detailObject = detailArray.getJSONObject(j);
//                                        String subOrderId = detailObject.getString("subOrderId");//唯一订单号
//                                        String transactionAmount = detailObject.getString("transactionAmount");//交易金额
//                                        String transactionCurrency = detailObject.getString("transactionCurrency");//交易币种
//                                        String billingAmount = detailObject.getString("billingAmount");//订单清算金额
//                                        String billingCurrency = detailObject.getString("billingCurrency");//订单清算币种
//                                        String movingAmount = detailObject.getString("movingAmount");//总动账金额
//                                        String movingCurrency = detailObject.getString("movingCurrency");//动账币种
//                                        String orderType = detailObject.getString("orderType");//订单类型（3-授权、4-授权撤销、1-清算、2-退款、5-授权失败，实际处理按type进行排序）
//
//                                        String tradeTime = detailObject.getString("tradeTime");//交易时间
//                                        String feeAmount = detailObject.getString("feeAmount");//交易手续费
//                                        String feeCurrency = detailObject.getString("feeCurrency");//手续费币种
//                                        String accountStatus = detailObject.getString("accountStatus");//动账状态（INIT-初始化、PROCESS-扣款中、WAIT-待处理、SUCC-成功）
//                                        String affectMerBalance = detailObject.getString("affectMerBalance");//是否影响商户资金账户余额
//                                        String channelCode = detailObject.getString("channelCode");//卡通道
//                                        String channelNotifyType = detailObject.getString("channelNotifyType");//授权通知类型（授权先到、清算先到）
//                                        String merchantName = detailObject.getString("merchantName");//通道返回商户名
//                                        String merchantCity = detailObject.getString("merchantCity");//通道返回城市
//                                        String merchantCountry = detailObject.getString("merchantCountry");//通道返回商家国家
//                                        String mccCode = detailObject.getString("mccCode");//商家mcc
//                                        String mccCategory = detailObject.getString("mccCategory");//类别
//
//
//                                        //保存creditCardLog
//                                        CreditCardLog log = null;
//                                        if (logList != null) {
//                                            for (CreditCardLog log1 : logList) {
//                                                if (getCardTransactionType(orderType).equals(log1.getTransactionType())) {
//                                                    log = log1;
//                                                    break;
//                                                }
//                                            }
//                                        }
//                                        if (log == null) {
//                                            log = new CreditCardLog();
//                                        }
//
//
//                                        log.setCardId(c.getCardId());//卡号
//                                        log.setTransactionId(orderId);//交易id
//                                        log.setTransactionCurrency(transactionCurrency);//交易币种:货币三位代码
//
//                                        log.setTransactionAmount(new BigDecimal(transactionAmount));//交易金额
//                                        log.setCardCurrency(cardCurrency);//卡币种:货币三位代码
//                                        if (StringUtils.isNotEmpty(billingAmount)) {
//                                            log.setCardTransactionAmount(new BigDecimal(billingAmount));//卡交易金额
//                                        } else {
//                                            log.setCardTransactionAmount(new BigDecimal(billAmount));//卡交易金额
//                                        }
//
//                                        log.setTransactionType(getCardTransactionType(orderType));//交易类型：
//                                        // todo
//                                        log.setTransactionStatus(getOrderTransactionType(status));//交易状态
//                                        String formattedDateTime = getTimeString(tradeDate);
//
//                                        log.setTransactionDate(formattedDateTime);//交易时间
//                                        if (StringUtils.isNotEmpty(feeAmount)) {
//                                            log.setFee(new BigDecimal(feeAmount));//手续费金额
//                                        }
//
//                                        log.setFeeCurrency(feeCurrency);//手续费卡币种
//                                        log.setMerchantName(merchantName);//商户名称
//                                        log.setMerchantCity(merchantCity);//商户所在城市
//
//                                        if (StringUtils.isNotEmpty(remark)) {
//                                            log.setDescription(remark);//描述
//                                        }
//                                        log.setTransactionDetail(null);//交易详细信息
//                                        log.setRespCode(null);
//
//                                        //查询卡余额
//                                        if (i < 1) {
//                                            JSONObject balanceObject = updateCardBalance(new JSONObject(), c);
//                                            balance = balanceObject.getString("avaBalance");
//                                        }
//                                        if (StringUtils.isNotEmpty(balance)) {
//                                            log.setCardAvailableBalance(new BigDecimal(billAmount));//卡可用余额
//                                        }
//                                        log.setCardAvailableBalanceCurrency(c.getCurrency());//卡可用余额币种
//
//                                        unsaveList.add(log);
//
//
//                                    }
//
//
//                                }
//
//
//                            }
//
//
//                        }
//                        if (!unsaveList.isEmpty()) {
//                            creditCardLogService.saveAll(unsaveList);
//                            return unsaveList;
//                        }
//
//                    }
//
//                }
//            }
//        }
//        return null;
//
//    }

    /**
     * 查询交易详情
     *
     * @param orderId               订单号
     * @param createTransactionDate 创建时间
     * @param c
     * @return
     */
    public JSONObject getOrderDetail(String orderId, String createTransactionDate, CreditCard c) {
        JSONObject json = new JSONObject();
        String requestNo = UniqueIDGenerator.generateID();
        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String kun_environment = KunConfig.environment;
        json.put("customerId", kun_customerId);
        json.put("requestNo", requestNo);
        json.put("orderId", orderId);

        JSONObject result = null;
        if ("87".equals(c.getCardType())) {
            json.put("createTransactionDate", createTransactionDate);
            result = kunUtils.result(kun_environment, json.toJSONString(), requestNo, KunMethod.card_order_detail, "POST", c.getCardId());
        } else {
            result = kunUtils.result(kun_environment, json.toJSONString(), requestNo, KunMethod.kcard_order_detail, "POST", c.getCardId());
        }

        if (result != null) {
            if (("87".equals(c.getCardType()) && "********".equals(result.getString("code")))
                    || (!"87".equals(c.getCardType()) && "000000".equals(result.getString("code")))) {
                return result;
            }
        }
        return null;
    }


    @Override
    public JSONObject getCardInfo(JSONObject bodyJson, CreditCard c, MetaPartnerUser partnerUser) {

        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String kun_environment = KunConfig.environment;

        JSONObject jsonKun = new JSONObject();
        String requestNoKun = UniqueIDGenerator.generateID();

        jsonKun.put("customerId", kun_customerId);

        jsonKun.put("requestNo", requestNoKun);
        JSONObject result = null;
        if ("87".equals(c.getCardType())) {
            jsonKun.put("cardId", c.getBankCardId());
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.card_info, "POST", c.getBankCardId());
        } else {
            jsonKun.put("cardId", c.getBankCardId());
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.kcard_info, "POST", c.getBankCardId());
        }

        if (result != null) {
            if (("87".equals(c.getCardType()) && "********".equals(result.getString("code")))
                    || (!"87".equals(c.getCardType()) && "000000".equals(result.getString("code")))) {

                String cardNo = "";
                String cvv = "";
                //判断cvv2是否为空
                if (StringUtils.isEmpty(c.getCvv2())) {
                    String aesKey = "";
                    if ("87".equals(c.getCardType())) {
                        aesKey = getAesStr();
                    } else {
                        aesKey = getKcardAesStr();
                    }

                    if (StringUtils.isEmpty(aesKey)) {
                        String message = "Failed to obtain card information";
                        bodyJson.put("error_message", message);

                        return bodyJson;
                    }

                    String cardNoAes = result.getString("cardNo");
                    String cvvAes = result.getString("cvv");
                    String expiryDate = "";
                    if ("87".equals(c.getCardType())) {
                        cardNo = AesTool.decode(aesKey, cardNoAes).replaceAll(" ","");
                        cvv = AesTool.decode(aesKey, cvvAes);
                        expiryDate = result.getString("expiryDate");
                        c.setExpirationTime(expiryDate.substring(3, 5) + expiryDate.substring(0, 2));
                    } else {
                        cardNo = AesTool.decodeKcard(aesKey, cardNoAes).replaceAll(" ","");
                        cvv = AesTool.decodeKcard(aesKey, cvvAes);
                        expiryDate = AesTool.decodeKcard(aesKey, result.getString("expiryDate"));
                        if (expiryDate.length() > 5) {
                            c.setExpirationTime(expiryDate.substring(2, 4) + expiryDate.substring(5, 7));
                        } else {
                            c.setExpirationTime(expiryDate.substring(3, 5) + expiryDate.substring(0, 2));
                        }

                    }

                    if (StringUtils.isNotEmpty(cardNo)) {
                        cardNo = cardNo.replaceAll(" ", "");

                        c.setCardNo(AESUtils.aesEncrypt(Constants.card_key, cardNo));
                    }
                    if (StringUtils.isNotEmpty(cvv)) {
                        cvv = cvv.replaceAll(" ", "");

                        c.setCvv2(AESUtils.aesEncrypt(Constants.card_key, cvv));
                    }

                } else {
                    cardNo = AESUtils.aesDecrypt(Constants.card_key, c.getCardNo());
                    cvv = AESUtils.aesDecrypt(Constants.card_key, c.getCvv2());
                }

                bodyJson.put("cardID", c.getCardId());
                bodyJson.put("cardNo", cardNo);
                bodyJson.put("cardCvv", cvv);

                bodyJson.put("expiryDate", c.getExpirationTime());
                bodyJson.put("currency", c.getCurrency());


                String balance = result.getString("balance");
                BigDecimal avaBalance = BigDecimal.ZERO;
                if (StringUtils.isNotEmpty(balance)) {
                    if (!"0.****************0000".equals(balance)) {
                        avaBalance = new BigDecimal(balance);
                    }

                }
                c.setBalance(avaBalance);

                String cardStatus = "";
                if ("87".equals(c.getCardType())) {
                    cardStatus = result.getString("cardStatus");
                } else {
                    cardStatus = result.getString("status");
                }

                String status = c.getCardStatus();
                String newstatus = getCardStatus(cardStatus);
                if (StringUtils.isNotEmpty(newstatus) && !newstatus.equals(status)) {
                    c.setStatusUpdateTime(new Date());
                }
                c.setCardStatus(newstatus);
                bodyJson.put("userBankCardStatus", newstatus);
                bodyJson.put("countryCode", c.getBillingCountry());
                bodyJson.put("billingState", c.getProvince());
                bodyJson.put("billingCity", c.getCity());
                bodyJson.put("billingAddress", c.getAddressLine1());
                bodyJson.put("billingZipCode", c.getPostalCode());
                if (partnerUser != null) {
                    bodyJson.put("cardHolderEmail", partnerUser.getEmail());
                    bodyJson.put("cardHolderMobileNumber", partnerUser.getMobileNumber());
                    bodyJson.put("cardHolderMobilePrefix", partnerUser.getMobilePrefix());
                }

                creditCardService.save(c);
                return bodyJson;
            } else {
                //返回错误
                return returnError(bodyJson, result);
            }

        }

        return null;
    }

    /**
     * (2024-10-29T09:15:20.000Z)时间格式转为yyyy-mm-dd
     *
     * @param tradeDate
     * @return
     */
    private String getTimeString(String tradeDate) {
        // 解析字符串为OffsetDateTime
        OffsetDateTime offsetDateTime = OffsetDateTime.parse(tradeDate);

        // 定义目标格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 格式化为目标格式
        return offsetDateTime.format(formatter);
    }

    @Override
    @Transactional
    public void upCardInfo(String cardId) {
        CreditCard card = creditCardService.findByCardId(cardId);
        if (card != null && !getCardStatus(KunCardStatus.OPENING.getValue()).equals(card.getCardStatus())) {
            getCardInfo(new JSONObject(), card, null);
        }
    }

    /**
     * 几天之前的时间
     *
     * @param kun_start_day
     * @param now
     * @return
     */
    private ZonedDateTime getZonedDateTime(String kun_start_day, ZonedDateTime now) {
        return now.minusDays(Integer.valueOf(kun_start_day));
    }

    /**
     * 关闭卡
     *
     * @param bodyJson
     * @param c
     * @return
     */
    @Override
    public JSONObject closeCard(JSONObject bodyJson, CreditCard c, CardConfig cardConfig) {

        JSONObject result2 = updateCardBalance(new JSONObject(), c);
        logger.info("result2:" + result2);
        String avaBalance = result2.getString("avaBalance");
        BigDecimal balance = BigDecimal.ZERO;
        if (StringUtils.isNotEmpty(avaBalance)) {
            balance = new BigDecimal(avaBalance);
        }

        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String kun_environment = KunConfig.environment;

        JSONObject jsonKun = new JSONObject();
        String requestNoKun = UniqueIDGenerator.generateID();

        jsonKun.put("customerId", kun_customerId);

        jsonKun.put("requestNo", requestNoKun);

        JSONObject result = null;
        if ("87".equals(c.getCardType())) {
            jsonKun.put("cardId", c.getBankCardId());
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.close_card, "POST", c.getBankCardId());
        } else {
            jsonKun.put("cardId", c.getBankCardId());
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.close_kcard, "POST", c.getBankCardId());
        }

        if (result != null) {

            if (("87".equals(c.getCardType()) && "********".equals(result.getString("code")))
                    || (!"87".equals(c.getCardType()) && "000000".equals(result.getString("code")))) {
                String status = result.getString("status");
                String txnId = result.getString("orderId");

                bodyJson.put("cardID", c.getCardId());
                bodyJson.put("status", getOrderTransactionType(status));
                bodyJson.put("refundAmt", balance);
                bodyJson.put("txnId", requestNoKun);

                bodyJson.put("currency", c.getCurrency());
                return bodyJson;
            } else {
                //返回错误
                return returnError(bodyJson, result);
            }

        }

        return null;
    }

    /**
     * 卡片解锁
     *
     * @param c
     * @return
     */
    @Override
    public JSONObject cardUnlock(CreditCard c, JSONObject bodyJson) {
        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String kun_environment = KunConfig.environment;

        JSONObject jsonKun = new JSONObject();
        String requestNoKun = UniqueIDGenerator.generateID();

        jsonKun.put("customerId", kun_customerId);
        jsonKun.put("cardId", c.getBankCardId());
        jsonKun.put("requestNo", requestNoKun);

        JSONObject result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.kcard_unlock, "POST", c.getBankCardId());
        if (result != null) {

            if ("000000".equals(result.getString("code"))) {
                String status = result.getString("status");


                bodyJson.put("cardID", c.getCardId());
                bodyJson.put("status", status);

                return bodyJson;
            } else {
                //返回错误
                return returnError(bodyJson, result);
            }

        }
        return null;
    }


    /**
     * 卡片冻结/解冻
     *
     * @param c        卡片
     * @param enable
     * @param bodyJson
     * @return
     */
    @Override
    public JSONObject cardfreeze(CreditCard c, boolean enable, JSONObject bodyJson) {
        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String kun_environment = KunConfig.environment;

        JSONObject jsonKun = new JSONObject();
        String requestNoKun = UniqueIDGenerator.generateID();

        jsonKun.put("customerId", kun_customerId);
        jsonKun.put("cardId", c.getBankCardId());
        jsonKun.put("requestNo", requestNoKun);
        jsonKun.put("ip", KunConfig.ip);
        String operationType = "";
        if (enable) {
            operationType = KunOperationType.THAWED.getValue();
        } else {
            operationType = KunOperationType.FREEZE.getValue();
        }
        jsonKun.put("operationType", operationType);


        JSONObject result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.kcard_freeze, "POST", c.getBankCardId());
        if (result != null) {

            if ("000000".equals(result.getString("code"))) {
                String status = result.getString("status"); //SUCCESS/FAIL


                bodyJson.put("cardID", c.getCardId());
                bodyJson.put("status", status);

                return bodyJson;
            } else {
                //返回错误
                return returnError(bodyJson, result);
            }

        }
        return null;
    }

    /**
     * 定时任务查询开卡结果
     */
    @Override
    @Transactional
    public void queryCardOpenResultTask() {


        List<MetaPartnerTxnDtl> list = metaPartnerTxnDtlService.findPaddingKunData(PartnerTxnType.CardApplication.getValue(), String.valueOf(TxnStatus.PADDING.getValue()));
        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String kun_environment = KunConfig.environment;
        for (MetaPartnerTxnDtl dtl : list) {

            JSONObject jsonKun = new JSONObject();
            String requestNoKun = dtl.getTxnId();
            jsonKun.put("customerId", kun_customerId);
            jsonKun.put("requestNo", requestNoKun);


            CreditCard card = creditCardService.findByCardId(dtl.getCardId());
            JSONObject result = null;
            if ("87".equals(card.getCardType())) {
                result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.open_card_query, "POST", card.getBankCardId());
            } else {

                result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.open_kcard_query, "POST", null);
            }
            CreditCard creditCard = creditCardService.findByCardId(card.getCardId());
            if (result != null) {
                if ("87".equals(card.getCardType()) && "********".equals(result.getString("code"))) {
                    String status = result.getString("status");
                    String cardId = result.getString("cardId");
                    //查询流水
                    String orderTransactionType = getOrderTransactionType(status);
                    logger.info("开卡订单状态: " + status);
                    Character orderStatus = getOrderStatus(status);
                    if (TxnStatus.PENDING.getValue() != orderStatus) {
                        logger.info("开卡流水: " + dtl.getId());
                        metaPartnerTxnDtlService.updateDtl(dtl, orderTransactionType);
                        MetaOrder metaOrder = metaOrderService.findRequestNo(requestNoKun);
                        if (metaOrder != null) {
                            if (TxnStatus.APPROVED.getName().equals(orderTransactionType)) {
                                metaOrder.setStatus(KzOrderStatus.success.getValue());
                            } else if (TxnStatus.DECLINED.getName().equals(orderTransactionType)) {
                                metaOrder.setStatus(KzOrderStatus.fail.getValue());
                            }
                            metaOrderService.save(metaOrder);
                        }
                        //新增推送数据

                        if (card != null) {
                            MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartnerBySysId(card.getSysId());
                            JSONObject jsonData = new JSONObject();
                            jsonData.put("cardID", creditCard.getCardId());
                            jsonData.put("reason", "");
                            jsonData.put("status", creditCard.getCardStatus());
                            pushData("CARD_STATUS_CHANGE", metaPartnerAssetPool.getSysId(), card.getCardId(), card.getCardHolder(), metaPartnerAssetPool.getApiUrl(), jsonData);
                        }
                    }
                } else if (!"87".equals(card.getCardType()) && "000000".equals(result.getString("code"))) {
                    String status = result.getString("status");
                    String cardId = result.getString("cardId");


                    logger.info("开卡订单状态: " + status);
                    Character orderStatus = getOpenKcardOrderStatus(status);
                    logger.info("orderStatus: " + orderStatus);
                    String orderTransactionType = getOpenKcardOrderType(status);
                    logger.info("orderTransactionType: " + orderTransactionType);

                    if (TxnStatus.PENDING.getValue() != orderStatus) {
                        logger.info("开卡流水: " + dtl.getId());
                        metaPartnerTxnDtlService.updateDtl(dtl, orderTransactionType);
                        MetaOrder metaOrder = metaOrderService.findRequestNo(requestNoKun);
                        if (metaOrder != null) {
                            if (TxnStatus.APPROVED.getName().equals(orderTransactionType)) {
                                metaOrder.setStatus(KzOrderStatus.success.getValue());
                            } else if (TxnStatus.DECLINED.getName().equals(orderTransactionType)) {
                                metaOrder.setStatus(KzOrderStatus.fail.getValue());
                            }
                            metaOrderService.save(metaOrder);
                        }
                        //新增推送数据
                        if (card != null) {
                            MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartnerBySysId(card.getSysId());
                            JSONObject jsonData = new JSONObject();
                            jsonData.put("cardID", card.getCardId());
                            jsonData.put("reason", "");
                            if (TxnStatus.APPROVED.getName().equals(orderTransactionType)) {
                                jsonData.put("status", getCardStatus(KunCardStatus.NORMAL.getValue()));
                            } else if (TxnStatus.DECLINED.getName().equals(orderTransactionType)) {
                                jsonData.put("status", getCardStatus(KunCardStatus.FAIL.getValue()));
                            }
                            pushData("CARD_STATUS_CHANGE", metaPartnerAssetPool.getSysId(), card.getCardId(), card.getCardHolder(), metaPartnerAssetPool.getApiUrl(), jsonData);
                        }

                    }

                }

            }
        }

    }

    /**
     * 定时任务查询卡充值结果
     */
    @Override
    @Transactional
    public void queryCardRechargeResultTask() {
        List<MetaPartnerTxnDtl> list = metaPartnerTxnDtlService.findPaddingKunData(PartnerTxnType.CardTopup.getValue(), String.valueOf(TxnStatus.PADDING.getValue()));
        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String kun_environment = KunConfig.environment;
        for (MetaPartnerTxnDtl dtl : list) {


            JSONObject jsonKun = new JSONObject();
            String requestNoKun = dtl.getTxnId();
            jsonKun.put("customerId", kun_customerId);
            jsonKun.put("requestNo", requestNoKun);
            CreditCard card = creditCardService.findByCardId(dtl.getCardId());
            JSONObject result = null;
            if ("87".equals(card.getCardType())) {
                result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.card_recharge_query, "POST", card.getBankCardId());
            } else {
                result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.kcard_recharge_query, "POST", card.getBankCardId());
            }

            if (result != null) {
                if (("87".equals(card.getCardType()) && "********".equals(result.getString("code")))
                        || (!"87".equals(card.getCardType()) && "000000".equals(result.getString("code")))) {
                    String status = result.getString("status");

                    //查询流水
                    String orderTransactionType = getOrderTransactionType(status);
                    logger.info("充值订单状态: " + status);
                    Character orderStatus = getOrderStatus(status);
                    if (TxnStatus.PENDING.getValue() != orderStatus) {
                        logger.info("充值流水: " + dtl.getId());
                        metaPartnerTxnDtlService.updateDtl(dtl, orderTransactionType);
                        CreditCard c = creditCardService.findByCardId(dtl.getCardId());
                        logger.info("更新卡片信息");
                        updateCardBalance(new JSONObject(), c);
                        logger.info("更新卡片余额为: " + c.getBalance());

                        //新增推送数据
                        if (c != null && StringUtils.isNotEmpty(c.getSysId())) {
                            MetaPartnerAssetPoolKey metaPartnerAssetPool = metaPartnerAssetPoolService.selectBySysId(c.getSysId());
                            CreditCardLog creditCardLog = creditCardLogService.findByTransactionId(dtl.getTxnId());

                            JSONObject jsonData = new JSONObject();
                            jsonData.put("cardID", c.getCardId());

                            Date currentDate = new Date();

                            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                            // 将日期时间格式化为指定格式的字符串
                            String formattedDate = formatter.format(currentDate);
                            jsonData.put("time", formattedDate);
                            jsonData.put("currency", dtl.getCurrency());
                            jsonData.put("amount", dtl.getTxnAmount().abs());//扣减的资产
                            jsonData.put("sendAmount", creditCardLog.getCardTransactionAmount());//充值到卡的金额
                            jsonData.put("sendCurrency", c.getCurrency());
                            if (TxnStatus.APPROVED.getValue().equals(orderStatus)) {
                                jsonData.put("status", "SUCCESS");
                                jsonData.put("transactionId", requestNoKun);
                                pushData("CARD_RECHARGE_RESULT", metaPartnerAssetPool.getSysId(), c.getCardId(), c.getCardHolder(), metaPartnerAssetPool.getApiUrl(), jsonData);

                            } else if (TxnStatus.FAIL.getValue().equals(orderStatus)) {
                                jsonData.put("status", "FAIL");
                                jsonData.put("transactionId", requestNoKun);
                                pushData("CARD_RECHARGE_RESULT", metaPartnerAssetPool.getSysId(), c.getCardId(), c.getCardHolder(), metaPartnerAssetPool.getApiUrl(), jsonData);

                            }


                        }


                    }
                }

            }

        }

    }

    /**
     * 定时任务查询卡交易历史
     */
    @Override
    public void queryCardOrderResultTask() {
        List<CreditCard> list = creditCardService.findBySource("kun");
        for (CreditCard c : list) {
            orderDeal(c);
        }
    }

    @Override
    public void queryCardOrderResultTask2() {
        List<CreditCard> list = creditCardService.findBySource2("kun","*********");
        for (CreditCard c : list) {
            orderDeal(c);
        }
    }

    public void orderDeal(CreditCard c) {
        List<CreditCardLog> unsaveList = updateCardOrder(c);
        if (unsaveList == null) {
            return;
        }
        logger.info("c.getSysId()-->" + c.getSysId());
        if (StringUtils.isNotEmpty(c.getSysId())) {
            for (CreditCardLog log : unsaveList) {
                MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPool(c.getSysId());

                JSONObject jsonData = new JSONObject();

                jsonData.put("cardID", log.getCardId());
                jsonData.put("transactionId", log.getTransactionId());
                jsonData.put("transactionTime", log.getTransactionDate());
                jsonData.put("cardCurrency", log.getCardCurrency());
                jsonData.put("cardCurrencyAmt", log.getCardTransactionAmount());
                jsonData.put("transCurrency", log.getTransactionCurrency());
                jsonData.put("transCurrencyAmt", log.getTransactionAmount());
                jsonData.put("transStatus", log.getTransactionStatus());
                jsonData.put("transType", log.getTransactionType());
                jsonData.put("merchantName", log.getMerchantName());
                jsonData.put("respCode", log.getRespCode());
                jsonData.put("respCodeDesc", log.getDescription());
                jsonData.put("fee", log.getFee());
                jsonData.put("feeCurrency", log.getFeeCurrency());

                if ("89".equals(c.getCardType())) {


                    List<MetaCreditCardLogSub> subList = metaCreditCardLogSubService.findList(log.getTransactionId());
                    List<JSONObject> jsList = new ArrayList<>();
                    BigDecimal refundAmount = BigDecimal.ZERO;
                    for (MetaCreditCardLogSub sub : subList) {

                        if ("CLEARING".equals(sub.getTransactionType()) && log.getCardTransactionAmount().compareTo(sub.getCardTransactionAmount()) != 0) {
                            BigDecimal amount = sub.getCardTransactionAmount();
                            if (amount != null) {
                                refundAmount = log.getCardTransactionAmount().subtract(amount);
                            }
                        } else if ("REVERSAL".equals(sub.getTransactionType())) {
                            refundAmount = sub.getCardTransactionAmount();
                        } else if ("REFUND".equals(sub.getTransactionType())) {
                            refundAmount = sub.getCardTransactionAmount();
                        }

                        JSONObject jData = new JSONObject();
                        jData.put("cardId", sub.getCardId());
                        jData.put("transactionId", sub.getTransactionId());
                        jData.put("transactionIdSub", sub.getTransactionIdSub());
                        jData.put("transactionDate", sub.getTransactionDate());
                        jData.put("transactionCurrency", sub.getTransactionCurrency());
                        jData.put("transactionAmount", sub.getTransactionAmount());
                        jData.put("cardCurrency", sub.getCardCurrency());
                        jData.put("cardTransactionAmount", sub.getCardTransactionAmount());
                        jData.put("fee", sub.getFee());
                        jData.put("feeCurrency", sub.getFeeCurrency());
                        jData.put("transactionType", sub.getTransactionType());
                        jData.put("transactionStatus", sub.getTransactionStatus());
                        jsList.add(jData);

                    }


                    jsonData.put("refundAmount", refundAmount);
                    jsonData.put("refundCurrency", c.getCurrency());
                    jsonData.put("subList", jsList);
                }


                pushData("TRANSACTION_CREATED", c.getSysId(), c.getCardId(), c.getCardHolder(), metaPartnerAssetPool.getApiUrl(), jsonData);
            }
        }
    }





    @Transactional
    public void pushData(String type, String sysId, String cardId, String userEmail, String apiUrl, JSONObject json) {

        MetaPushData metaPushData = metaPushDataService.creatPashData(sysId, cardId, userEmail, apiUrl, JSON.toJSONString(json), type);
        if (StringUtils.isNotEmpty(apiUrl)) {
            metaPushData.setStatue("3");//表示还未推送到mq
        } else {
            metaPushData.setStatue("0");//推送失败
        }
        metaPushDataService.save(metaPushData);

    }

    /**
     * 询价
     *
     * @param from   由什么币
     * @param to     转为什么币
     * @param side   方向
     * @param amount 金额
     * @return
     */
    @Override
    public BigDecimal getPrice(String from, String to, String side, BigDecimal amount) {

        amount = amount.setScale(2, RoundingMode.CEILING);
        String key = Constants.KUN_RATE + "_" + from + "_" + side + "_" + to;
        String s = stringRedisTemplate.opsForValue().get(key);
        logger.info("获取redis上的值" + from + "_" + to + ":" + s);
        if (StringUtils.isNotEmpty(s)) {
            logger.info("返回价格：" + s);
            return new BigDecimal(s);
        }
        logger.info("获取redis上的值不存在，接口获取");
        String kun_rate_adjust = configService.selectConfigByKey("kun_rate_adjust");
        String kun_rate_Amount = configService.selectConfigByKey("kun_rate_Amount").replaceAll(" ", "");
        if (amount.compareTo(new BigDecimal(kun_rate_Amount)) < 0) {
            amount = new BigDecimal(kun_rate_Amount);
        }
        BigDecimal price = BigDecimal.ZERO;
        String kun_environment = KunConfig.environment;


        JSONObject jsonKun = new JSONObject();
        String requestNoKun = UniqueIDGenerator.generateID();

        jsonKun.put("symbol", from + "_" + to);
        jsonKun.put("side", side);
        jsonKun.put("amount", String.valueOf(amount));

        JSONObject result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.exchange_ask_price, "POST", "");
        if (result != null) {

            if (result.getBoolean("success")) {
                String amt = result.getString("price");
                if (StringUtils.isNotEmpty(amt)) {
                    price = new BigDecimal(amt);
                    if (side.equals(KunPriceSide.BUY.getValue())) {
                        BigDecimal decimal = new BigDecimal(kun_rate_adjust);
                        price = price.add(decimal.abs());
                    } else {
                        price = price.add(new BigDecimal(kun_rate_adjust));
                    }
                    stringRedisTemplate.opsForValue().set(key, String.valueOf(price), 20, TimeUnit.SECONDS);
                }
            }
        }
        logger.info("返回价格：" + price);
        return price;
    }

    /**
     * 询价获取的HKD(比如：7.771)转为USDT
     * 得到1HKD等于多少USDT
     *
     * @param price
     * @return
     */
    @Override
    public BigDecimal exchangeUSDT(BigDecimal price) {
        logger.info("获取的金额：" + price);
        if (price.compareTo(new BigDecimal("1")) > 0) {
            price = new BigDecimal("1").divide(price, 6, BigDecimal.ROUND_DOWN);
        }
        logger.info("转换后的金额：" + price);
        return price;

    }

    /**
     * kun的旧卡换新
     */
    @Override
    @Transactional
    public void queryCardExchangeResultTask() {
        List<CardVo> list = creditCardService.findExchangeList();
        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String kun_environment = KunConfig.environment;
        for (CardVo vo : list) {


            JSONObject jsonKun = new JSONObject();
            String requestNoKun = vo.getRequestNo();

            jsonKun.put("customerId", kun_customerId);
            jsonKun.put("requestNo", requestNoKun);

            JSONObject result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.open_kcard_query, "POST", null);
            if (result != null && "000000".equals(result.getString("code"))) {

                CreditCard card = creditCardService.findByCardId(vo.getCardId());
                String status = result.getString("status");
                String orderTransactionType = getOpenKcardOrderType(status);

                if (TxnStatus.APPROVED.getName().equals(orderTransactionType)) {
                    //开卡成功
                    logger.info("开卡成功");
                    getCardInfo(new JSONObject(), card, null);
                } else if (TxnStatus.DECLINED.getName().equals(orderTransactionType)) {
                    //开卡失败
                    logger.info("开卡失败");
                    creditCardService.updateStatus(card.getCardId(), getCardStatus(KunCardStatus.FAIL.getValue()));
                }

            }

        }
    }

    /**
     * 卡片信息修改
     *
     * @param cardId      卡id
     * @param bodyJson
     * @param email       邮箱
     * @param phoneNumber 手机号码
     * @param areaCode    区号
     * @return
     */
    @Override
    public JSONObject cardSetting(String cardId, JSONObject bodyJson, String email, String phoneNumber, String areaCode) {
        String kun_environment = KunConfig.environment;

        JSONObject jsonKun = new JSONObject();
        String requestNoKun = UniqueIDGenerator.generateID();
        CreditCard card = creditCardService.findByCardId(cardId);
        jsonKun.put("cardId", card.getBankCardId());
        if (StringUtils.isNotEmpty(email)) {
            jsonKun.put("email", email);
        }

        if (StringUtils.isNotEmpty(phoneNumber)) {
            jsonKun.put("phoneNumber", phoneNumber);
            jsonKun.put("areaCode", areaCode);
        }


        JSONObject result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.card_cardsetting, "POST", cardId);
        if (result != null) {

            if ("000000".equals(result.getString("code"))) {
                String status = result.getString("status");


                bodyJson.put("cardID", cardId);
                bodyJson.put("status", status);

                return bodyJson;
            } else {
                //返回错误
                return returnError(bodyJson, result);
            }

        }
        return null;
    }

    /**
     * 开卡
     *
     * @param card
     * @param cardConfig
     * @param metaOrder
     * @return
     */
    @Override
    public CardResult openCard(CreditCard card, CardConfig cardConfig, MetaOrder metaOrder) {
        CardResult cardResult = null;
        logger.info("申请kun卡");
        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String kun_open_currency = configService.selectConfigByKey("kun_open_currency");
        String kun_open_amout = configService.selectConfigByKey("kun_open_amout");
        String kun_kcard_open_currency = configService.selectConfigByKey("kun_kcard_open_currency");
        String kun_kcard_open_amout = configService.selectConfigByKey("kun_kcard_open_amout");
        String send_kun_ip = KunConfig.ip;
        String kun_environment = KunConfig.environment;
        JSONObject jsonKun = new JSONObject();

        String requestNoKun = metaOrder.getRequestNo();
        jsonKun.put("customerId", kun_customerId);
        jsonKun.put("requestNo", requestNoKun);
        jsonKun.put("outUserId", card.getUserId());
        jsonKun.put("outUserName", metaOrder.getOpenEmail());

        if ("87".equals(cardConfig.getCardType())) {
            jsonKun.put("openAmount", kun_open_amout);
            jsonKun.put("openCurrency", kun_open_currency);
        } else {
            jsonKun.put("openAmount", kun_kcard_open_amout);
            jsonKun.put("openCurrency", kun_kcard_open_currency);
        }

        jsonKun.put("ip", send_kun_ip);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("dialCode", metaOrder.getOpenMobilePrefix());
        jsonObject.put("phone", metaOrder.getOpenMobileNumber());
        jsonObject.put("email", metaOrder.getOpenEmail());
        jsonKun.put("meta", jsonObject);

        if (!"87".equals(cardConfig.getCardType())) {
            jsonKun.put("cardType", true);
            jsonObject.put("cardName", "KazePay");
            jsonObject.put("expiryDate", DateUtils.futureDate(3));
        }

        jsonKun.put("meta", jsonObject);
        JSONObject result = null;
        if ("87".equals(cardConfig.getCardType())) {
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.open_card, "POST", null);
        } else {
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.open_kcard, "POST", null);
        }


        if (result != null) {

            if (("87".equals(cardConfig.getCardType()) && "********".equals(result.getString("code")))
                    || (!"87".equals(cardConfig.getCardType()) && "000000".equals(result.getString("code")))) {
                String status = result.getString("status");
                if (StringUtils.isEmpty(status)) {
                    status = KunOrderStatus.PROCESSING.getValue();
                }
                String cardId = result.getString("cardId");
                String cardStatus = "";
                Character orderStatus = getOrderStatus(status);
                if (TxnStatus.PENDING.getValue() == orderStatus) {
                    //处理中
                    cardStatus = getCardStatus(KunCardStatus.OPENING.getValue());

                } else if (TxnStatus.SUCCESS.getValue() == orderStatus) {
                    // 成功
                    cardStatus = getCardStatus(KunCardStatus.ACTIVE.getValue());

                } else if (TxnStatus.FAIL.getValue() == orderStatus) {
                    //失败
                    cardStatus = getCardStatus(KunCardStatus.FAIL.getValue());
                }
                cardResult = new CardResult();
                cardResult.setCardId(cardId);
                cardResult.setCardStatus(cardStatus);

            } else {
                String cardStatus = "";
                cardResult = new CardResult();
                cardStatus = getCardStatus(KunCardStatus.FAIL.getValue());
                cardResult.setCardStatus(cardStatus);
            }

        }

        return cardResult;
    }

    /**
     * 卡片充值
     *
     * @param
     * @param metaOrder
     */
    @Override
    public CardRechargeResult rechargeCard(CreditCard c, MetaOrder metaOrder) {
        logger.info("充值调用");
        CardRechargeResult cardRechargeResult = null;

        BigDecimal rechargeAmount = metaOrder.getOrderAmount();


        String coin = "HKD";
        String from = "USDT";
        BigDecimal price = getPrice(from, coin, KunPriceSide.SELL.getValue(), rechargeAmount);
        if (price == null || price.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }

        BigDecimal recharge = rechargeAmount.multiply(price).setScale(2, RoundingMode.CEILING);
        logger.info("充值金额:" + recharge + "HKD," + rechargeAmount + "USD,exchange" + price);


        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String send_kun_ip = KunConfig.ip;
        String requestNoKun = metaOrder.getRequestNo();
        logger.info("充值流水：" + requestNoKun);
        String kun_environment = KunConfig.environment;


        JSONObject jsonKun = new JSONObject();

        jsonKun.put("customerId", kun_customerId);

        jsonKun.put("amount", recharge);
        jsonKun.put("currency", coin);
        jsonKun.put("ip", send_kun_ip);
        jsonKun.put("requestNo", requestNoKun);

        CreditCardLog cardLog = creditCardLogService.findByTransactionId(requestNoKun);
        if (cardLog == null) {
            logger.info("充值日志不存在:" + requestNoKun);
            return null;
        }

        cardLog.setCardCurrency(c.getCurrency());
        cardLog.setCardTransactionAmount(recharge);
        creditCardLogService.save(cardLog);

        JSONObject result = null;
        if ("87".equals(c.getCardType())) {
            jsonKun.put("cardId", c.getBankCardId());
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.card_recharge, "POST", c.getBankCardId());
        } else {
            jsonKun.put("cardId", c.getBankCardId());
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.kcard_recharge, "POST", c.getBankCardId());
        }


        if (result != null) {

            if (("87".equals(c.getCardType()) && "********".equals(result.getString("code")))
                    || (!"87".equals(c.getCardType()) && "000000".equals(result.getString("code")))) {
                String status = result.getString("status");
                if (StringUtils.isEmpty(status)) {
                    status = KunOrderStatus.PROCESSING.getValue();
                }
                Character orderStatus = getOrderStatus(status);
                cardRechargeResult = new CardRechargeResult();
                if (TxnStatus.PENDING.getValue() == orderStatus) {
                    cardRechargeResult.setStatus(TxnStatus.PENDING.getName());

                } else if (TxnStatus.APPROVED.getValue() == orderStatus) {
                    cardRechargeResult.setStatus(TxnStatus.APPROVED.getName());

                } else if (TxnStatus.DECLINED.getValue() == orderStatus) {
                    cardRechargeResult.setStatus(TxnStatus.DECLINED.getName());

                }

            }

        }
        return cardRechargeResult;
    }

    /**
     * 获取卡片信息
     *
     * @param c 卡片
     * @return
     */
    @Override
    public CardInfo getCardInfo(CreditCard c) {
        CardInfo cardInfo = null;
        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String kun_environment = KunConfig.environment;

        JSONObject jsonKun = new JSONObject();
        String requestNoKun = UniqueIDGenerator.generateID();

        jsonKun.put("customerId", kun_customerId);

        jsonKun.put("requestNo", requestNoKun);
        JSONObject result = null;
        if ("87".equals(c.getCardType())) {
            jsonKun.put("cardId", c.getBankCardId());
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.card_info, "POST", c.getBankCardId());
        } else {
            jsonKun.put("cardId", c.getBankCardId());
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.kcard_info, "POST", c.getBankCardId());
        }

        if (result != null) {
            if (("87".equals(c.getCardType()) && "********".equals(result.getString("code")))
                    || (!"87".equals(c.getCardType()) && "000000".equals(result.getString("code")))) {
                cardInfo = new CardInfo();
                String cardNo = "";
                String cvv = "";
                //判断cvv2是否为空

                String aesKey = "";
                if ("87".equals(c.getCardType())) {
                    aesKey = getAesStr();
                } else {
                    aesKey = getKcardAesStr();
                }

                if (StringUtils.isEmpty(aesKey)) {
                    return null;
                }


                if (StringUtils.isEmpty(c.getCvv2())) {
                    String cardNoAes = result.getString("cardNo");
                    String cvvAes = result.getString("cvv");

                    if ("87".equals(c.getCardType())) {
                        cardNo = AesTool.decode(aesKey, cardNoAes).replaceAll(" ","");;
                        cvv = AesTool.decode(aesKey, cvvAes);
                        String expiryDate = result.getString("expiryDate");
                        expiryDate = expiryDate.substring(3, 5) + expiryDate.substring(0, 2);

                        cardInfo.setCardNo(cardNo);
                        cardInfo.setCvv2(cvv);
                        cardInfo.setExpirationTime(expiryDate);


                    } else {
                        cardNo = AesTool.decodeKcard(aesKey, cardNoAes).replaceAll(" ","");
                        cvv = AesTool.decodeKcard(aesKey, cvvAes);
                        String expiryDate = AesTool.decodeKcard(aesKey, result.getString("expiryDate"));
                        if (expiryDate.length() > 5) {
                            expiryDate = expiryDate.substring(2, 4) + expiryDate.substring(5, 7);
                        } else {
                            expiryDate = expiryDate.substring(3, 5) + expiryDate.substring(0, 2);
                        }


                        cardInfo.setCardNo(cardNo);
                        cardInfo.setCvv2(cvv);
                        cardInfo.setExpirationTime(expiryDate);
                    }
                } else {
                    cardNo = AESUtils.aesDecrypt(Constants.card_key, c.getCardNo());
                    cvv = AESUtils.aesDecrypt(Constants.card_key, c.getCvv2());
                    cardInfo.setCardNo(cardNo);
                    cardInfo.setCvv2(cvv);
                    cardInfo.setExpirationTime(c.getExpirationTime());
                }
                String balance = result.getString("balance");
                BigDecimal avaBalance = BigDecimal.ZERO;
                if (StringUtils.isNotEmpty(balance)) {
                    if (!"0.****************0000".equals(balance)) {
                        avaBalance = new BigDecimal(balance);
                    }

                }
                cardInfo.setBalance(avaBalance);

                String cardStatus = "";
                if ("87".equals(c.getCardType())) {
                    cardStatus = result.getString("cardStatus");
                } else {
                    cardStatus = result.getString("status");
                }


                String newstatus = getCardStatus(cardStatus);
                cardInfo.setCardStatus(newstatus);
            }

        }

        return cardInfo;
    }


    /**
     * 查询充值订单
     *
     * @param transactionId
     * @param c
     * @return
     */
    public JSONObject queryCharge(String transactionId, CreditCard c) {
        logger.info("查询kun卡充值情况");

        JSONObject jsonObject = null;
        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String kun_environment = KunConfig.environment;

        JSONObject jsonKun = new JSONObject();
        String requestNoKun = transactionId;
        jsonKun.put("customerId", kun_customerId);
        jsonKun.put("requestNo", requestNoKun);


        JSONObject result = null;
        if ("87".equals(c.getCardType())) {
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.card_recharge_query, "POST", c.getCardId());
        } else {
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.kcard_recharge_query, "POST", c.getCardId());
        }

        if (result != null) {
            if (("87".equals(c.getCardType()) && "********".equals(result.getString("code")))
                    || (!"87".equals(c.getCardType()) && "000000".equals(result.getString("code")))) {
                String status = result.getString("status");


                //查询流水
                String orderTransactionType = getOrderTransactionType(status);

                logger.info("开卡订单状态: " + status);
                Character orderStatus = getOrderStatus(status);

                jsonObject = new JSONObject();
                jsonObject.put("cardID", c.getCardId());
                jsonObject.put("transactionId", transactionId);
                jsonObject.put("transactionStatus", orderTransactionType);

            }

        }
        return jsonObject;
    }


    @Override
    public JSONObject queryOpenCard(CreditCard c, String requestNoKun) {
        logger.info("查询kun卡开卡情况");
        JSONObject bodyJson = null;
        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String kun_environment = KunConfig.environment;
        JSONObject jsonKun = new JSONObject();

        jsonKun.put("customerId", kun_customerId);
        jsonKun.put("requestNo", requestNoKun);

        JSONObject result = null;
        if ("87".equals(c.getCardType())) {
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.open_card_query, "POST", c.getBankCardId());
        } else {
            result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.open_kcard_query, "POST", null);
        }


        if (result != null) {
            if ("87".equals(c.getCardType()) && "********".equals(result.getString("code"))) {
                String status = result.getString("status");
                //String cardId = result.getString("cardId");

                //查询流水
                String orderTransactionType = getOrderTransactionType(status);
                logger.info("开卡订单状态: " + status);

                bodyJson = new JSONObject();
                bodyJson.put("cardID", c.getCardId());
                bodyJson.put("transactionStatus", orderTransactionType);

            } else if (!"87".equals(c.getCardType()) && "000000".equals(result.getString("code"))) {

                String status = result.getString("status");
                String orderTransactionType = getOpenKcardOrderType(status);
                bodyJson = new JSONObject();
                bodyJson.put("cardID", c.getCardId());
                bodyJson.put("transactionStatus", orderTransactionType);

            }

        }
        return bodyJson;
    }
}

