package com.meta.system.kun.service;

import com.alibaba.fastjson.JSONObject;
import com.meta.system.domain.CreditCard;
import com.meta.system.domain.CreditCardLog;
import com.meta.system.domain.app.CardConfig;
import com.meta.system.domain.app.MetaOrder;
import com.meta.system.domain.partner.MetaPartnerUser;
import com.meta.system.models.CardInfo;
import com.meta.system.models.CardRechargeResult;
import com.meta.system.models.CardResult;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/26/10:56
 */
public interface KunService {
    String getAesStr();
    String getKcardAesStr();

    String getCardStatus(String cardStatus);

    Character getOrderStatus(String status);

    String getCardTransactionType(String type);

    String getOrderTransactionType(String type);

    JSONObject updateCardBalance(JSONObject bodyJson, CreditCard c);

    JSONObject returnError(JSONObject bodyJson, JSONObject result);

    void queryCardOpenResultTask();

    void queryCardRechargeResultTask();

    void queryCardOrderResultTask();

    void queryCardOrderResultTask2();

     void orderDeal(CreditCard c);

    void queryCardOpenResultTaskKZ();

    void queryCardRechargeResultTaskKZ();

    JSONObject getCardInfo(JSONObject bodyJson, CreditCard c, MetaPartnerUser partnerUser);

    void upCardInfo(String cardId);

    List<CreditCardLog> updateCardOrder(CreditCard c);

    JSONObject closeCard(JSONObject bodyJson, CreditCard c, CardConfig cardConfig);

    JSONObject cardUnlock(CreditCard c,JSONObject bodyJson);

    JSONObject cardfreeze(CreditCard c, boolean enable, JSONObject bodyJson);

    Character getOpenKcardOrderStatus(String status);

    String getOpenKcardOrderType(String status);

    /**
     * 询价
     * @param from 由什么币
     * @param to 转为什么币
     * @param side 方向
     * @param amount 金额
     * @return
     */
    BigDecimal getPrice(String from, String to, String side, BigDecimal amount);

    BigDecimal exchangeUSDT(BigDecimal price);

    /**
     * kun的旧卡换新
     */
    void queryCardExchangeResultTask();

    JSONObject cardSetting(String cardId, JSONObject bodyJson, String email, String phoneNumber, String areaCode);

    CardResult openCard(CreditCard card, CardConfig cardConfig, MetaOrder metaOrder);

    /**
     * 卡片充值
     */
    CardRechargeResult rechargeCard(CreditCard card, MetaOrder metaOrder);

    /**
     * 获取卡片信息
     * @param c 卡片
     * @return
     */
    CardInfo getCardInfo(CreditCard c);

    JSONObject queryCharge(String transactionId, CreditCard c);

    JSONObject queryOpenCard(CreditCard c,String requestNo);


}
