package com.meta.system.kun.constants;

import com.meta.common.enums.BaseEnum;

/**
 * kun-交易类型
 * <AUTHOR>
 * @date 2024/10/26/15:03
 */
public enum KunTransactionType implements BaseEnum<KunTransactionType> {
    CLEARING("CLEARING", "清算"),
    CLEARED("CLEARED", "清算"),
    REFUND("REFUND", "退款"),
    AUTHORIZATION("AUTHORIZATION", "授权"),
    AUTHORIZED("AUTHORIZED", "授权"),
    AUTH_REVERSAL("AUTH_REVERSAL", "授权交易撤销"),
    REVERSAL("REVERSAL", "授权交易撤销"),
    AUTH_FAILURE("AUTH_FAILURE", "失败交易通知"),
    OTHER("OTHER", "其他");

    String value;
    String name;

    KunTransactionType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public String getValue() {
        return this.value;
    }

    @Override
    public String getOrdinal() {
        return String.valueOf(this.ordinal());
    }
}
