package com.meta.system.kun.constants;

import com.meta.common.enums.BaseEnum;

/**
 * kun卡状态
 * <AUTHOR>
 * @date 2024/10/26/13:49
 */
public enum KunCardStatus implements BaseEnum<KunCardStatus> {

    OPENING("OPENING", "开卡中"),
    ACTIVE("ACTIVE", "正常"),
    FROZEN("FROZEN", "已冻结"),
    EXPIRED("EXPIRED", "已过期"),
    CANCELED("CANCELED", "已注销"),
    DESTROYING("DESTROYING", "注销中"),
    DESTROY("DESTROY", "已注销"),
    FAIL("FAIL", "开卡失败"),

    NORMAL("NORMAL", "正常"),
    FREEZE ("FREEZE", "冻结"),
    LOCKING("LOCKING", "锁定"),
    CANCEL("CANCEL", "注销"),
    PENDING("PENDING", "开卡中"),

    ;

    String value;
    String name;

    KunCardStatus(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public String getValue() {
        return this.value;
    }

    @Override
    public String getOrdinal() {
        return String.valueOf(this.ordinal());
    }

}
