package com.meta.system.dao;

import com.meta.system.domain.app.UserCommission;
import com.meta.system.vo.*;
import org.springframework.data.domain.Page;

import java.util.List;

public interface UserCommissionCustom{
    /**
     * 根据Userid 获取 佣金情况
     * @param userId 用户id
     * @return 佣金情况
     */
    CommissionStat commissionStat(Long userId);

    Page<UserCommissionVo> getCommissionList3(UserCommissionVo userCommission);

    Page<UserTeamCommissionVo> teamCommissionDetail(Long userId, TeamCommissionVo teamCommissionVo);

    UserTeamCommissionVo getCommissionTotal(Long userId);

    List<CommissionVo> totalCommissin(Long userId);

    CommissionVo totalNum(Long userId);

    Page<AllTeamCommissionVo> allTeamCommission(AllTeamCommissionVo vo);

    Page<UserCommissionVo> commissionDataList(UserCommissionVo userCommissionVo);
}
