package com.meta.system.dao.impl;

import com.meta.common.utils.DateUtils;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.data.TransactionService;
import com.meta.system.dao.WalletCustom;
import com.meta.system.vo.WalletVo;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;

@Repository
public class WalletCustomImpl implements WalletCustom {


    @Resource
    private TransactionService transactionService;

    /**
     * 账号管理列表
     *
     * @param username    用户名
     * @param status      状态
     * @param startDate   注册开始时间
     * @param endDate     注册结束日期
     * @param nodeLevel
     * @param channelName
     * @param limitFlag
     * @return
     */
    @Override
    public Page<WalletVo> selectUserList(String nickName,String username, String status, String startDate, String endDate, String nodeLevel, String channelName, String limitFlag) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sqlHead = new StringBuilder();
        StringBuilder sqlWhere = new StringBuilder();
        sqlHead.append(" select ");
        sqlHead.append(" t1.user_id as userId, ");
        sqlHead.append(" t1.user_name as username, ");
        sqlHead.append(" t1.Nick_name as nickname, ");
        sqlHead.append(" t1.Phonenumber as phonenumber, ");
        sqlHead.append(" t2.usd_balacne as usdBalacne, ");
        sqlHead.append(" t2.freeze_usd_balacne as freezeUsdBalacne, ");
        sqlHead.append(" t2.silver_active_code_num as silverActiveCodeNum, ");
        sqlHead.append(" t2.golden_active_code_num as goldenActiveCodeNum, ");
        sqlHead.append(" t2.goldenblack_active_code_num as goldenblackActiveCodeNum, ");
        sqlHead.append(" t1.Status as status, ");
        sqlHead.append(" t1.create_time as createTime, ");
        sqlHead.append(" t1.login_limit_expired as loginLimitExpired, ");
        sqlHead.append(" t1.trade_limit_expired as tradeLimitExpired, ");
        sqlHead.append(" coalesce(t2.node_level,'00') as nodeLevel, ");
        sqlHead.append(" t3.user_name as channelName ");
        sqlHead.append(" from meta_user_info t2 ");
        sqlHead.append(" inner join sys_user t1 on t2.user_id = t1.user_id ") ;
        sqlHead.append(" left join sys_user t3 on t3.user_id = t2.channel_user_id");
        sqlWhere.append(" where t1.user_type != '00' and t1.del_flag<>'2' ");
        if (StringUtils.isNotEmpty(username)){
            sqlWhere.append(" and t1.user_name like ? ");
            params.add("%"+username.trim()+"%");
        }
        if (StringUtils.isNotEmpty(nickName)){
            sqlWhere.append(" and t1.nick_name = ? ");
            params.add(nickName.trim());
        }
        if (StringUtils.isNotEmpty(status)){
            sqlWhere.append(" and t1.status = ? ");
            params.add(status.trim());
        }
        if (StringUtils.isNotEmpty(startDate)){
            sqlWhere.append(" and t1.create_time >= ? ");
            params.add(DateUtils.getStartOfDay(DateUtils.parseDate(startDate.trim())));
        }
        if (StringUtils.isNotEmpty(endDate)){
            sqlWhere.append(" and t1.create_time <= ? ");
            params.add(DateUtils.getEndOfDay(DateUtils.parseDate(endDate.trim())));
        }

        if (StringUtils.isNotEmpty(nodeLevel)){
            sqlWhere.append(" and t2.node_level = ? ");
            params.add(nodeLevel);
        }

        if (StringUtils.isNotEmpty(channelName)){
            sqlWhere.append(" and t3.user_name like ? ");
            params.add("%"+channelName.trim()+"%");
        }
        if (StringUtils.isNotBlank(limitFlag) && "1".equals(limitFlag)){
            sqlWhere.append(" and (t1.login_limit_expired > now() or t1.trade_limit_expired > now()) ");
        }

        sqlWhere.append(" order by t1.create_time desc ");
        return transactionService.page(sqlHead.toString()+sqlWhere, params, WalletVo.class);
    }
}
