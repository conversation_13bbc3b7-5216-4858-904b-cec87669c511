package com.meta.system.dao.app.impl;

import com.meta.common.utils.data.TransactionService;
import com.meta.system.dao.app.MetaCardScenarioDetailCustom;
import com.meta.system.vo.MetaCardScenarioDetailVo;
import com.meta.system.vo.MetaGatePrePayVo;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/09/04/17:54
 */
public class MetaCardScenarioDetailCustomImpl implements MetaCardScenarioDetailCustom {

    @Resource
    private TransactionService transactionService;


    @Override
    public List<MetaCardScenarioDetailVo> findByCardType(String cardType) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT ");
        sql.append(" t1.id AS id, ");
        sql.append("         t1.type AS type, ");
        sql.append(" t1.valid_flag AS validFlag, ");
        sql.append("         t1.card_type AS cardType, ");
        sql.append(" t2.NAME AS name, ");
        sql.append("         t2.pic_name AS picName ");
        sql.append(" FROM ");
        sql.append(" meta_card_secnario_detail t1 ");
        sql.append(" LEFT JOIN meta_card_scenario t2 ON t1.secnairo_id = t2.id ");
        sql.append(" WHERE t1.card_type = ? ");
        params.add(cardType);
        return transactionService.list(sql.toString(), params, MetaCardScenarioDetailVo.class);
    }
}
