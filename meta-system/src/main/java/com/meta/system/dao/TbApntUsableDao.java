package com.meta.system.dao;

import com.meta.system.domain.apnt.TbApntUsable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/02/02/15:48
 */
public interface TbApntUsableDao extends JpaRepository<TbApntUsable, Long>, JpaSpecificationExecutor<TbApntUsable>,TbApntUsableCustom {
    @Query(value = " select * from tb_apnt_usable where cst_id=? ", nativeQuery = true)
    TbApntUsable findByUserId(Long userId);

    @Query(value = "update tb_apnt_usable set value = ?2 where id = ?1", nativeQuery = true)
    @Modifying
    void updateValue(Long id, BigInteger value);

    @Query(value = "SELECT  sum(`value`) FROM tb_apnt_usable WHERE cst_id=?1 and  deleted=0 and  expire_time >= NOW() ", nativeQuery = true)
    BigInteger selectIntegralByCstId(Long userId);

    @Query(value = "SELECT  * FROM tb_apnt_usable WHERE cst_id=?1 and  deleted=0 and  expire_time >= NOW() order by expire_time ASC ", nativeQuery = true)
    List<TbApntUsable> selectIntegralList(Long userId);

    @Query(value = "update tb_apnt_usable set value = ?2 ,deleted =?3 where id = ?1", nativeQuery = true)
    @Modifying
    void updateTbApntUsable(Long id, BigInteger value, Integer deleted);


}
