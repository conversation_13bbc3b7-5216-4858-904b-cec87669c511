package com.meta.system.dao.app.impl;

import com.meta.common.utils.StringUtils;
import com.meta.common.utils.data.TransactionService;
import com.meta.system.dao.app.MetaGatePrePayCustom;
import com.meta.system.vo.MetaGatePrePayVo;
import org.springframework.data.domain.Page;

import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2024/04/07/11:25
 */
public class MetaGatePrePayCustomImpl implements MetaGatePrePayCustom {
    @Resource
    private TransactionService transactionService;

    @Override
    public Page<MetaGatePrePayVo> tradeList(String email) {

        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT t1.order_amount as orderAmount, ");
        sql.append(" t1.create_time as createTime, ");
        sql.append(" t1.statue as statue ");
        sql.append(" FROM meta_gate_prepay t1 ");
        sql.append(" LEFT JOIN sys_user t2 ");
        sql.append(" ON t1.user_id = t2.user_id ");
        sql.append(" WHERE t1.statue = 'PAY_SUCCESS' ");
        sql.append(" AND t2.user_name = ? ");
        params.add(email);
        sql.append(" order by t1.create_time desc");

        return transactionService.page(sql.toString(), params, MetaGatePrePayVo.class);
    }
}
