package com.meta.system.dao;

import com.meta.system.domain.MetaUserInvites;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 钱包 数据层
 *
 * <AUTHOR>
 */
@Repository
public interface MetaUserInvitesDao extends JpaRepository<MetaUserInvites, Long>, JpaSpecificationExecutor<MetaUserInvites>, MetaUserInvitesCustom {

    /**
     * 更新开卡状态
     */
    @Modifying
    @Query(value = "update meta_user_invites set is_card_success = ?2 where invitee_user_id = ?1", nativeQuery = true)
    int updateCardStatus(Long inviteeUserId, Boolean cardSuccess);

    /**
     * 更新激活状态
     */
    @Modifying
    @Query(value = "update meta_user_invites set is_active_success = ?2 where invitee_user_id = ?1", nativeQuery = true)
    int updateActiveStatus(Long inviteeUserId,Boolean activeSuccess);

    /**
     * 查找该用户所有邀请记录
     */
    @Query("select u from MetaUserInvites u where u.inviterUserId = ?1")
    List<MetaUserInvites> findByInviterUserId(Long inviterUserId);

    /**
     * 统计该用户所有邀请且激活数量
     */
    @Query("select count(u) from MetaUserInvites u where u.inviterUserId = ?1 and (u.activeSuccess = true or u.cardSuccess=true)")
    int countActiveByInviterUserId(Long inviterUserId);

    /**
     * 通过该用户被邀请记录
     */
    @Query("select u from MetaUserInvites u where u.inviteeUserId = ?1")
    MetaUserInvites findByInviteeUserId(Long inviteeUserId);

}
