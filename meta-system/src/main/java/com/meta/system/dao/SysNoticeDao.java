package com.meta.system.dao;


import com.meta.system.domain.SysNotice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 通知公告表 数据层
 *
 * <AUTHOR>
 * @since 1.0  2020-12-11
 */
@Repository
public interface SysNoticeDao extends JpaRepository<SysNotice, Long>, JpaSpecificationExecutor<SysNotice>, SysMenuDaoCustom {

    void deleteByNoticeIdIn(List<Long> ids);

}
