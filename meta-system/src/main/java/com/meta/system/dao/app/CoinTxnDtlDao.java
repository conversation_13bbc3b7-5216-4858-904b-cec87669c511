package com.meta.system.dao.app;

import com.meta.system.domain.app.CoinTxnDtl;
import com.meta.system.domain.log.TxnDtlUSD;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 *  客户数字资产交易明细  数据层
 *
 * <AUTHOR>
 */
@Repository
public interface CoinTxnDtlDao extends JpaRepository<CoinTxnDtl, Long>, JpaSpecificationExecutor<CoinTxnDtl>, CoinTxnDtlCustom {

    CoinTxnDtl findByReceiptNo(String receiptNo);

    @Query(value = "select * from meta_coin_txn_dtl where txn_id =?1",nativeQuery = true)
    CoinTxnDtl findByTxnId(Long txnId);

    @Query(value = "update meta_coin_txn_dtl t set t.txn_product=?4, t.txn_status = ?2 , t.user_balance =  t.user_balance + ?3  where t.txn_id = ?1", nativeQuery = true)
    @Modifying
    void updateTxnStatusAndUsdBalanceById(Long id, Character txnStatus, BigDecimal txnAmount,String cardId);


    @Query(value = "update meta_coin_txn_dtl t set t.txn_status = ?2 where t.txn_id = ?1", nativeQuery = true)
    @Modifying
    void updateTxnStatusById(Long id,Character txnStatus);

    @Query(value = "select * from meta_coin_txn_dtl t where t.txn_product = ?1 and t.txn_status = ?2 and t.txn_code in (?3) ", nativeQuery = true)
    List<CoinTxnDtl> findAllByCardIdAndStatusAndTxnType(String cardId, String stauts, String[] txnTypes);

    @Query(value = "update meta_coin_txn_dtl t  set t.txn_product = ?2 where t.txn_product = ?1", nativeQuery = true)
    @Modifying
    void updateTxnProduct(String cardId, String userBankId);


    @Query(value = "select * from meta_coin_txn_dtl t where t.txn_product = ?1 and t.txn_status = ?2 and t.receipt_no = ?3 ", nativeQuery = true)
    List<CoinTxnDtl> findAllByCardIdAndStatusAndTxnId(String cardId, String stauts, String txnId);

    @Query(value = "select * from meta_coin_txn_dtl t where t.txn_product = ?1 and t.txn_status = ?2 and t.receipt_no = ?3 and  t.txn_code in (?4) ", nativeQuery = true)
    List<CoinTxnDtl> findAllByCardIdAndStatusAndTxnId(String cardId, String stauts, String txnId, String[] txnTypes);

    @Query(value = "select * from meta_coin_txn_dtl where txn_product = ?1 for update ", nativeQuery = true)
    CoinTxnDtl findByTxnProduct(String txnProduct);

}
