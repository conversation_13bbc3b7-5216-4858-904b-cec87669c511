package com.meta.system.dao;

import com.meta.system.domain.app.CoinConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CoinConfigDao extends JpaRepository<CoinConfig,Integer> {

    @Query(value = " select coin_id from meta_coin_config where symbol = ?1",nativeQuery = true)
    List<Integer> findBySymbol(String symbol);
}
