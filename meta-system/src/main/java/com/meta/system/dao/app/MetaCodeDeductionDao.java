package com.meta.system.dao.app;

import com.meta.system.domain.app.MetaCodeDeduction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/04/15/18:08
 */
@Repository
public interface MetaCodeDeductionDao extends JpaRepository<MetaCodeDeduction, Long>, JpaSpecificationExecutor<MetaCodeDeduction> {
    @Query(value = "select * from  meta_code_deduction where user_id = ?1 and  statue in ('0','1') ", nativeQuery = true)
    List<MetaCodeDeduction> findByUserId(long userId);

    @Query(value = "select * from  meta_code_deduction where txn_id = ?1 and  statue='0' ", nativeQuery = true)
    MetaCodeDeduction findByTxnId(Long txnId);

    @Modifying
    @Query(value = "update meta_code_deduction set statue = ?1 where id = ?2", nativeQuery = true)
    void updateStatue(String statue,Long id);

}
