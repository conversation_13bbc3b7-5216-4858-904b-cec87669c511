package com.meta.system.dao;

import com.meta.system.domain.vcc.MetaVccLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 卡日志 数据层
 *
 * <AUTHOR>
 */
@Repository
public interface MetaVccLogDao extends JpaRepository<MetaVccLog, Long>, JpaSpecificationExecutor<MetaVccLog> {


}
