package com.meta.system.dao.app;

import com.meta.system.domain.app.CardConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CardConfigDao extends JpaRepository<CardConfig, Long>, CardConfigCustom {
    CardConfig findByCardTypeAndCardLevel(String cardType, String cardLevel);

//    @Query(value = "select distinct card_type from meta_card_config c where c.valid_flag = 'Y' and  card_level !='99'",nativeQuery = true)
//    List<String> findValidCard();

    @Query(value = "select distinct card_type from meta_card_config c where c.valid_flag = 'Y' and  card_level ='99' and is_provide_b='N'", nativeQuery = true)
    List<String> findPhysicalCard();

    //CardConfig findCardConfigByCardTypeAndCardLevelAndChannel(String cardType, String cardLevel, String channel);

    @Query(value = "select distinct card_fee from meta_card_config c where c.valid_flag = 'Y' and  card_level =?1 and is_provide_b='N'", nativeQuery = true)
    List<String> findFeeBylevel(String level);

    @Query(value = "select * from meta_card_config c where c.valid_flag = 'Y' and  card_type =?1 and card_level=?2 and channel=?3 and is_provide_b='N'", nativeQuery = true)
    CardConfig findCardConfigByCardTypeAndCardLevelAndChannel(String cardType, String cardLevel, String channel);

    @Query(value = "select * from meta_card_config c where c.valid_flag = 'Y' and channel='fiat24' and is_provide_b='N' ", nativeQuery = true)
    List<CardConfig> getFiatCardConfig();

    @Query(value = "select * from meta_card_config where sys_id=?1 and card_bin=?2 and card_level=?3 and valid_flag = 'Y' and is_provide_b='Y'", nativeQuery = true)
    CardConfig findCardPartnerConfig(String sysId, String cardBin, String cardLevel);

    @Query(value = "select * from meta_card_config c where card_type = '01' ", nativeQuery = true)
    List<CardConfig> findCardEquConvertNum();

    @Query(value = "select * from meta_card_config c where c.valid_flag = 'Y' and  card_type =?1 and card_level=?2 and is_provide_b='N'", nativeQuery = true)
    CardConfig findCardConfigByCardTypeAndCardLevel(String cardType, String cardLevel);

    @Query(value = "select * from meta_card_config where sys_id=?1 and card_type=?2 and card_level=?3 and valid_flag = 'Y' and is_provide_b='Y'", nativeQuery = true)
    CardConfig findCardConfig(String sysId, String cardType, String cardLevel);

    @Query(value = "select * from meta_card_config c where c.valid_flag = 'Y' and  card_bin =?1  and is_provide_b=?2 ", nativeQuery = true)
    CardConfig findByCardBin(String no,String isB);
}
