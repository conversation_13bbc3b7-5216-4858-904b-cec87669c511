package com.meta.system.dao.app;

import com.meta.system.domain.app.node.UserNodeMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface UserNodeMappingDao extends JpaRepository<UserNodeMapping, Long> {

    /**
     * 2.将新注册用户的信息维护至推荐人关系中，找到referrer_id如111，
     * 通过查询sql获取关系数据后将level_no关系+1后，新增mapping；如新注册用户没有推荐人，则这步可忽略。
     *
     * @param userId     注册人id
     * @param nodeLevel  注册人节点等级
     * @param referrerId 推荐人id
     */

    @Query(value = " REPLACE INTO meta.meta_user_node_mapping (user_id, node_level, child_id, child_node_level, level_no, record_dt) "+
            " SELECT " +
            " user_id, "+
            " node_level, "+
            " ?1 AS child_id, "+
            " ?2 AS child_node_level, "+
            " level_no + 1, "+
            " CURRENT_TIMESTAMP() AS record_dt "+
            " FROM meta.meta_user_node_mapping "+
            " WHERE child_id = ?3 "
            ,nativeQuery = true)
    @Modifying
    void maintenance(Long userId,String nodeLevel, Long referrerId);

    @Modifying
    @Query(value = "update meta.meta_user_node_mapping set node_level = ?2 where user_id = ?1",nativeQuery = true)
    void upgradeDown(Long userId, String nodeLevel);

    @Modifying
    @Query(value = "update meta.meta_user_node_mapping set node_level = ?2 where child_id = ?1",nativeQuery = true)
    void upgradeUp(Long userId, String nodeLevel);
}
