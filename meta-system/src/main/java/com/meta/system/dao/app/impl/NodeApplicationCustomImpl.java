package com.meta.system.dao.app.impl;

import com.meta.common.utils.StringUtils;
import com.meta.common.utils.data.TransactionService;
import com.meta.system.dao.app.NodeApplicationCustom;
import com.meta.system.dto.node.NodeApplicationDto;
import com.meta.system.vo.NodeApplicationVo;
import com.meta.system.vo.NodeCenter;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;


@Repository
public class NodeApplicationCustomImpl implements NodeApplicationCustom {

    @Resource
    private TransactionService transactionService;



    /**
     * 节点申请分页查询
     *
     * @param apply 查询条件
     */
    @Override
    public Page<NodeApplicationVo> list(NodeApplicationDto apply) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sqlHead = new StringBuilder();
        StringBuilder sqlWhere = new StringBuilder();
        sqlHead.append(" select ");
        sqlHead.append(" node.id as id, ");
        sqlHead.append(" su.user_id as userId, ");
        sqlHead.append(" nick_name as nickname, ");
        sqlHead.append(" email as email, ");
        sqlHead.append(" node.node_level as nodeLevel, ");
        sqlHead.append(" node.payment_method as paymentMethod, ");
        sqlHead.append(" node.deposit_amount as depositAmount, ");
        sqlHead.append(" node.approval_status as approvalStatus, ");
        sqlHead.append(" node.create_time as createTime, ");
        sqlHead.append(" node.update_time as updateTime ");
        sqlHead.append(" from sys_user su ");
        sqlWhere.append(" inner join meta_node_application node on node.user_id = su.user_id ");
        if (apply != null) {
            sqlWhere.append(" where 1=1 ");
            if (StringUtils.isNotEmpty(apply.getNickname())) {
                sqlWhere.append(" and node.nick_name like ? ");
                params.add("%" + apply.getNickname() + "%");
            }
            if (StringUtils.isNotEmpty(apply.getEmail())) {
                sqlWhere.append(" and node.email like ? ");
                params.add("%" + apply.getEmail() + "%");
            }
            if (apply.getPaymentMethod() != null) {
                sqlWhere.append(" and node.payment_method = ? ");
                params.add(apply.getPaymentMethod().getOrdinal());
            }
            if (apply.getApprovalStatus() != null) {
                sqlWhere.append(" and node.approval_status = ? ");
                params.add(apply.getApprovalStatus().getOrdinal());
            }
            if (apply.getNodeLevel() != null) {
                sqlWhere.append(" and node.node_level = ? ");
                params.add(apply.getNodeLevel().getOrdinal());
            }
        }
        sqlWhere.append(" order by node.create_time desc ");
        return transactionService.page(sqlHead.toString()+sqlWhere, params, NodeApplicationVo.class);
    }

    /**
     * 统计节点数据
     *
     * @param userId 用户id
     * @return 节点中心内容
     */
    @Override
    public NodeCenter findNodeCenter(Long userId) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ");
        sql.append(" mui.user_id AS userId, ");
        sql.append(" mui.silver_active_code_num AS silverActiveCodeNum, ");
        sql.append(" mui.golden_active_code_num AS goldenActiveCodeNum, ");
        sql.append(" mui.goldenblack_active_code_num AS goldenblackActiveCodeNum, ");

        sql.append(" mui.node_level AS nodeLevel, ");
        sql.append(" IFNULL(muc.totalCommisionAmount, 0) AS totalCommisionAmount, ");
        sql.append(" ifNull(muni.card_recharge_rebate,mnc.card_recharge_rebate) AS cardRechargeRebate, ");
        sql.append(" count(distinct munm.user_id) AS totalRecommendNum, ");
        sql.append(" ifNull(muni.card_open_rebate, mnc.card_open_rebate) AS cardOpenRebate, ");
        sql.append(" COUNT(DISTINCT c.user_id) AS totalOpenNum, ");
        sql.append(" mui.white_active_code_num AS whiteActiveCodeNum ");
        sql.append(" FROM ");
        sql.append(" meta_user_info mui ");
        sql.append(" LEFT JOIN meta_user_node_info muni ON muni.user_id = mui.user_id and muni.is_valid = 'Y' ");
        sql.append(" LEFT JOIN meta_node_config mnc ON mnc.node_level = mui.node_level ");
        sql.append(" LEFT JOIN ( ");
        sql.append(" SELECT ");
        sql.append(" user_id, ");
        sql.append(" SUM(IFNULL(commision_amount, 0)) AS totalCommisionAmount ");
        sql.append(" FROM ");
        sql.append(" meta_user_commission ");
        sql.append(" GROUP BY ");
        sql.append(" user_id ) ");
        sql.append(" muc ON muc.user_id = mui.user_id ");
        sql.append(" LEFT JOIN meta_user_info munm on munm.referrer_id = mui.user_id and  munm.user_id != mui.user_id ");
        sql.append(" LEFT JOIN meta_credit_card c ON c.user_id = munm.user_id ");
        sql.append(" WHERE ");
        sql.append(" mui.user_id = ? ");
        params.add(userId);
        sql.append(" GROUP BY  1, 2, 3, 4, 5, 6, 7, 9 ,11");
        return transactionService.single(sql.toString(),params,NodeCenter.class);
    }
}
