package com.meta.system.dao.app.impl;

import com.meta.common.enums.app.TxnType;
import com.meta.common.utils.DateUtils;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.data.TransactionService;
import com.meta.system.dao.app.AllTransactionCustom;
import com.meta.system.domain.app.AllTransaction;
import com.meta.system.dto.AllTxnDto;
import org.springframework.data.domain.Page;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/13/14:42
 */
public class AllTransactionCustomImpl implements AllTransactionCustom {

    @Resource
    private TransactionService transactionService;

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Page<AllTransaction> page(AllTxnDto txnDto) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();

        sql.append(" select type , ");
        sql.append(" id, ");
        sql.append(" user_id as userId, ");
        sql.append(" receipt_no as receiptNo, ");
        sql.append(" txn_code  as txnCode, ");
        sql.append(" txn_amount as txnAmount, ");
        sql.append(" txn_status as txnStatus, ");
        sql.append(" txn_time as txnTime, ");
        sql.append(" txn_fee as txnFee, ");
        sql.append(" txn_currency as txnCurrency, ");
        sql.append(" product as product, ");
        sql.append(" from_address as fromAddress, ");
        sql.append(" to_address as toAddress ");
        sql.append(" from all_transaction ");
        sql.append(" where user_id =? and txn_status !='DELETE'");
        params.add(txnDto.getUserId().toString());
        if (txnDto.getTxnTypes() != null && txnDto.getTxnTypes().size() > 0) {
            sql.append(" and txn_code in ( ");

            for (TxnType type : txnDto.getTxnTypes()) {
                sql.append("?,");
                params.add(type.getValue());
            }

            sql.deleteCharAt(sql.length() - 1);
            sql.append(" ) ");
        }

        if (StringUtils.isNotEmpty(txnDto.getTxnStatus())) {
            sql.append(" and txn_status=? ");
            params.add(txnDto.getTxnStatus());
        }

        if (StringUtils.isNotEmpty(txnDto.getCardId())) {
            sql.append(" and type='card' and product=? ");
            params.add(txnDto.getCardId());
        }

        if (StringUtils.isNotEmpty(txnDto.getTxnCurrency())) {
            sql.append(" and txn_currency=? ");
            params.add(txnDto.getTxnCurrency());
        }
        if (StringUtils.isNotEmpty(txnDto.getType())) {
            sql.append(" and type=? ");
            params.add(txnDto.getType());
        }
        if (StringUtils.isNotEmpty(txnDto.getStartDate())) {
            sql.append(" and txn_time >= ? ");
            params.add(DateUtils.getEndOfDay(DateUtils.parseDate(txnDto.getStartDate().trim())));
        }
        if (StringUtils.isNotEmpty(txnDto.getEndDate())) {
            sql.append(" and txn_time <= ? ");
            params.add(DateUtils.getEndOfDay(DateUtils.parseDate(txnDto.getEndDate().trim())));
        }
        sql.append(" order by txn_time desc ");
        System.out.println(sql.toString());
        return transactionService.page(sql.toString(), params, AllTransaction.class);
    }

    @Override
    public List<String> recentAddress(Long userId, String type) {
        StringBuilder sql = new StringBuilder();
        ArrayList<Object> params = new ArrayList<>();
//        if ("COIN".equals(type)) {


        sql.append(" SELECT ");
        sql.append("         to_address as toAddress ");
        sql.append(" FROM ");
        sql.append("         ( SELECT to_address, max( txn_id ) AS txn_id FROM meta_coin_txn_dtl WHERE user_id = ?1 AND txn_code IN ( 't1020', 'c1010' ) GROUP BY 1 ) AS t ");
        params.add(userId);
        sql.append(" ORDER BY ");
        sql.append(" txn_id DESC ");
        sql.append(" LIMIT 5 ");
//        } else if ("CODE".equals(type)) {
//            sql.append(" SELECT ");
//            sql.append(" t.to_userid as toAddress ");
//            sql.append("         FROM ");
//            sql.append(" ( SELECT to_userid, max(id) AS id FROM meta_txn_dtl_code WHERE user_id = ?1 AND txn_type ='31' GROUP BY 1 ) AS t ");
//            sql.append("  ");
//            sql.append(" ORDER BY ");
//            sql.append(" id DESC ");
//            sql.append(" LIMIT 5 ");
//            params.add(userId);
//        } else {
//            sql.append(" SELECT ");
//            sql.append(" t.to_userid AS to_address ");
//            sql.append("         FROM ");
//            sql.append(" ( SELECT to_userid, max( id ) AS id FROM meta_txn_dtl_usd WHERE user_id = ?1 AND txn_type = 'c3020' GROUP BY 1 ) AS t ");
//            sql.append(" ORDER BY ");
//            sql.append(" id DESC ");
//            sql.append(" LIMIT 5 ");
//            params.add(userId);
//        }

        Query contentQuery = entityManager.createNativeQuery(sql.toString());
        for (int i = 0; i < params.size(); i++) {
            contentQuery.setParameter(i + 1, params.get(i));
        }
        List<String> results = contentQuery.getResultList();
        return results;

    }
}
