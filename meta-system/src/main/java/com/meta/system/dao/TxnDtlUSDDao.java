package com.meta.system.dao;

import com.meta.system.domain.log.TxnDtlUSD;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface TxnDtlUSDDao extends JpaRepository<TxnDtlUSD, Long>, TxnDtlUSDCustom {
    @Query(value = "update meta_txn_dtl_usd t set t.txn_status = ?2 where t.id = ?1", nativeQuery = true)
    @Modifying
    void updateTxnStatusById(Long id,Character txnStatus);

    @Query(value = "update meta_txn_dtl_usd t set t.txn_status = ?2,t.txn_product = ?3 where t.id = ?1", nativeQuery = true)
    @Modifying
    void updateTxnStatusAndTxnProductById(Long id,Character txnStatus,String txnProduct);

    @Query(value = "update meta_txn_dtl_usd t set t.txn_product=?4, t.txn_status = ?2 , t.usd_balance =  t.usd_balance + ?3  where t.id = ?1", nativeQuery = true)
    @Modifying
    void updateTxnStatusAndUsdBalanceById(Long id, Character txnStatus, BigDecimal txnAmount,String cardId);

    @Query(value = "update meta_txn_dtl_usd t set t.txn_product = ?2 where t.id = ?1", nativeQuery = true)
    @Modifying
    void updateTxnProductById(Long id,String txnProduct);

    @Query(value = "select * from meta_txn_dtl_usd t where t.id = ?1", nativeQuery = true)
    TxnDtlUSD findById(String id);


    @Query(value = "select * from meta_txn_dtl_usd t where t.txn_product = ?1 and t.txn_type = ?2 and t.rela_transactionid = ?3 limit 1", nativeQuery = true)
    TxnDtlUSD findByCardIdAndTxnTypeAndRelaTransactionid(String cardId,String txnType,String txnId);

    @Query(value = "select * from meta_txn_dtl_usd t where t.txn_product = ?1 and t.txn_status = ?2", nativeQuery = true)
    List<TxnDtlUSD> findAllByCardIdAndStatus(String cardId, String stauts);

    @Query(value = "select sum(txn_amount) from meta_txn_dtl_usd t where t.txn_product = ?1 and t.txn_status = ?2 and t.txn_type = ?3", nativeQuery = true)
    BigDecimal findRechargeAmount(String cardId, String stauts,String txnType);

    @Query(value = "select * from meta_txn_dtl_usd t where t.txn_product = ?1 and t.txn_status = ?2 and t.rela_transactionid = ?3", nativeQuery = true)
    List<TxnDtlUSD> findAllByCardIdAndStatusAndTransactionId(String cardId, String stauts,String transactionId);

    @Query(value = "select * from meta_txn_dtl_usd t where t.txn_product = ?1 and t.txn_status = ?2 and t.rela_transactionid = ?3  and t.txn_type in (?4)", nativeQuery = true)
    List<TxnDtlUSD> findAllByCardIdAndStatusAndTransactionId(String cardId, String stauts,String transactionId, String[] txnTypes);

    @Query(value = "update meta_txn_dtl_usd t set t.txn_status = ?2,t.merchant_name=?3,t.merchant_city=?4 where t.id = ?1", nativeQuery = true)
    @Modifying
    void updateTxnStatusForCard(Long id,Character txnStatus,String merchantName,String merchantCity);

    @Query(value = "select * from meta_txn_dtl_usd t where t.txn_product = ?1 and t.txn_status = ?2 and t.txn_type in (?3) ", nativeQuery = true)
    List<TxnDtlUSD> findAllByCardIdAndStatusAndTxnType(String cardId, String stauts, String[] txnTypes);



    @Query(value = "select * from meta_txn_dtl_usd t where  t.transaction_id in (?1) ", nativeQuery = true)
    List<TxnDtlUSD> findAllByTransactionIn(String[] transactionId);

    @Query(value = "select * from meta_txn_dtl_usd t where  t.txn_product = ?1 and t.rela_transactionid =?2 and  t.txn_type in (?3)", nativeQuery = true)
    List<TxnDtlUSD> findCardIdAndTransactionId(String userBankcardId, String recordNo,String[] txnTypes);

    @Query(value = "update meta_txn_dtl_usd t set t.txn_product = ?2 where t.txn_product = ?1", nativeQuery = true)
    @Modifying
    void updateTxnProduct(String cardId, String userBankId);
}
