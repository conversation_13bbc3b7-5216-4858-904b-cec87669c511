package com.meta.system.dao;

import com.meta.system.domain.apnt.TbApntType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2024/02/02/13:42
 */
@Repository
public interface TbApntTypeDao extends JpaRepository<TbApntType, Long>, JpaSpecificationExecutor<TbApntType> {
    @Query(value = " select * from tb_apnt_type where type=? ", nativeQuery = true)
    TbApntType findByType(String type);
}
