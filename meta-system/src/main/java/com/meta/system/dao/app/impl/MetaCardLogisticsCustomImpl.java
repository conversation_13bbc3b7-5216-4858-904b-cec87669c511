package com.meta.system.dao.app.impl;

import com.meta.common.utils.StringUtils;
import com.meta.common.utils.data.TransactionService;
import com.meta.system.dao.app.MetaCardLogisticsCustom;
import com.meta.system.vo.CardLogisticsVo;
import org.springframework.data.domain.Page;

import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2024/03/08/17:22
 */
public class MetaCardLogisticsCustomImpl implements MetaCardLogisticsCustom {
    @Resource
    private TransactionService transactionService;
    @Override
    public Page<CardLogisticsVo> page(String cstName, String cardHolder) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT ");
        sql.append(" t1.id as id , ");
        sql.append(" t2.user_name as cstName, ");
        sql.append(" t3.card_holder as cardHolder, ");
        sql.append(" t3.card_label as cardLabel, ");
        sql.append(" t3.card_no as cardNo, ");
        sql.append(" t1.tel as tel, ");
        sql.append(" t1.area as area, ");
        sql.append(" t1.address as address, ");
        sql.append(" t1.fee as fee, ");
        sql.append(" t1.type as type, ");
        sql.append(" t1.tracking_number as trackingNumber, ");
        sql.append(" t1.post_code as postCode, ");
        sql.append(" t1.create_time as createTime ");
        sql.append("   FROM ");
        sql.append(" meta_card_logistics t1 ");
        sql.append(" LEFT JOIN sys_user t2 ON t1.user_id = t2.user_id ");
        sql.append(" LEFT JOIN meta_credit_card t3 ON t1.card_id = t3.card_id ");
        sql.append(" where 1=1 ");


        if (StringUtils.isNotEmpty(cstName)){
            sql.append(" and t2.user_name = ? ");
            params.add(cstName);
        }

        if (StringUtils.isNotEmpty(cardHolder)){
            sql.append(" and t3.card_holder like ? ");
            params.add(""+cardHolder+"%");
        }

        sql.append(" order by t1.create_time desc");


        return transactionService.page(sql.toString(),params, CardLogisticsVo.class);
    }
}
