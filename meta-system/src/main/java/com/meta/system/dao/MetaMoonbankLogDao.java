package com.meta.system.dao;

import com.meta.system.domain.moonbank.MetaMoonbankLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/15/16:21
 */
@Repository
public interface MetaMoonbankLogDao extends JpaRepository<MetaMoonbankLog, Long>, JpaSpecificationExecutor<MetaMoonbankLog> {
    @Query(value = "select * from meta_moonbank_log where request_no =?1 and code='success' ",nativeQuery = true)
    List<MetaMoonbankLog> find(String uniqueNo);
}
