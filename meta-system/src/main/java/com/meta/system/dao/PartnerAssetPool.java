package com.meta.system.dao;

import com.meta.system.domain.groupkey.MetaPartnerAssetPoolKey;
import com.meta.system.domain.partner.MetaPartnerAssetPool;


import java.util.List;
import java.util.Optional;

public interface PartnerAssetPool {
    MetaPartnerAssetPool getData(Long userId, String coin);
    List<String> getCard(Long loginId);

    MetaPartnerAssetPoolKey selectBySysId(String sysId);

    List<String> findType(Long userId);

    MetaPartnerAssetPoolKey selectByPartnerId(Long userId);

    void updateAuto(Character auto, Long userId);
}
