package com.meta.system.dao.impl;

import com.meta.common.utils.data.TransactionService;
import com.meta.system.dao.PartnerAssetPool;
import com.meta.system.domain.groupkey.MetaPartnerAssetPoolKey;
import com.meta.system.domain.partner.MetaPartnerAssetPool;
import org.hibernate.sql.Update;


import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;

public class PartnerAssetPoolImpl implements PartnerAssetPool {

    @Resource
    private TransactionService transactionService;

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public MetaPartnerAssetPool getData(Long userId, String coin) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT  ");
        sql.append(" t1.alarm_insufficient_amt AS alarmInsufficientAmt,  ");
        sql.append(" t1.usdt_balacne AS usdtBalacne, ");
        sql.append(" t1.partner_id AS partnerId, ");
        sql.append(" t1.coin_code AS coinCode, ");
        sql.append(" t1.txn_day_limit AS txnDayLimit, ");
        sql.append(" t1.txn_single_limit AS txnSingleLimit, ");
        sql.append(" t1.txn_month_limit AS txnMonthLimit, ");
        sql.append(" t1.sys_id AS sysId, ");
        sql.append(" t1.txn_year_limit AS txnYearLimit ");
        sql.append(" FROM ");
        sql.append(" meta_partner_asset_pool t1 ");
        sql.append(" LEFT JOIN meta_user_node_mapping t2 ON t1.partner_id = t2.user_id ");
        sql.append(" LEFT JOIN meta_coin_balance t3 ON t1.partner_id = t3.user_id ");
        sql.append(" AND t1.coin_code = t3.coin_code ");
        sql.append(" WHERE ");
        sql.append(" t1.`status` = '00' ");
        sql.append(" AND t1.coin_code =? ");
        params.add(coin);
        sql.append(" AND t2.child_id = ? ");
        params.add(userId);
        sql.append(" AND t2.node_level != '00' ");
        sql.append(" ORDER BY ");
        sql.append(" t2.node_level ASC LIMIT 1 ");
        return transactionService.single(sql.toString(), params, MetaPartnerAssetPool.class);
    }

    @Override
    public List<String> getCard(Long loginId) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ");
        //  sql.append("  t1.partner_id AS partnerId , ");
        sql.append("  t1.coin_code ");
        sql.append(" FROM meta_partner_asset_pool t1 ");
        sql.append(" LEFT JOIN meta_user_node_mapping t2  ");
        sql.append(" ON t1.partner_id = t2.user_id ");
        sql.append(" WHERE t1.`status`='00' ");
        sql.append(" AND t2.child_id = ? ");
        params.add(loginId);
        Query contentQuery = entityManager.createNativeQuery(sql.toString());
        for (int i = 0; i < params.size(); i++) {
            contentQuery.setParameter(i + 1, params.get(i));
        }
        List<String> results = contentQuery.getResultList();
        return results;

    }

    @Override
    public MetaPartnerAssetPoolKey selectBySysId(String sysId) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT  ");
        sql.append(" t1.partner_id AS partnerId, ");
        sql.append(" t1.usdt_pool_limit AS usdtPoolLimit, ");
        sql.append(" t1.sys_id as sysId ,");
        sql.append(" t1.alarm_insufficient_amt AS alarmInsufficientAmt, ");
        sql.append(" t1.alarm_insufficient_ratio AS alarmInsufficientRatio, ");
        sql.append(" t1.min_deposit_amt AS minDepositAmt, ");
        sql.append(" t1.coin_code AS coinCode, ");
        sql.append(" t1.api_url AS apiUrl, ");
        sql.append(" t1.usdt_balacne AS usdtBalacne, ");
        sql.append(" t1.freeze_usdt_balacne AS freezeUstdBalacne ");
        sql.append(" FROM ");
        sql.append(" meta_partner_asset_pool t1 ");
        sql.append(" WHERE ");
        sql.append(" t1.`status` = '00' ");
        sql.append(" AND t1.sys_id =? ");
        params.add(sysId);

        return transactionService.single(sql.toString(), params, MetaPartnerAssetPoolKey.class);
    }

    @Override
    public List<String> findType(Long userId) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ");
        sql.append("t1.type as type");
        sql.append("        FROM ");
        sql.append("meta_partner_asset_pool t1 ");
        sql.append("LEFT JOIN meta_user_node_mapping t2 ON t1.partner_id = t2.user_id ");
        sql.append("where  t1.`status` = '00' and t2.level_no>0 and  t2.child_id = ? ");
        params.add(userId);
        Query contentQuery = entityManager.createNativeQuery(sql.toString());
        for (int i = 0; i < params.size(); i++) {
            contentQuery.setParameter(i + 1, params.get(i));
        }
        List<String> results = contentQuery.getResultList();
        return results;
    }

    @Override
    public MetaPartnerAssetPoolKey selectByPartnerId(Long userId) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT  ");
        sql.append(" t1.auto_amt AS autoAmt, ");
        sql.append(" t1.auto AS auto, ");
        sql.append(" t1.sys_id AS sysId, ");
        sql.append(" t1.partner_id AS partnerId,");
        sql.append(" t1.coin_code AS coinCode, ");
        sql.append(" t1.api_url AS apiUrl, ");
        sql.append(" t1.usdt_balacne AS usdtBalacne, ");
        sql.append(" t1.freeze_usdt_balacne AS freezeUstdBalacne ");
        sql.append(" FROM ");
        sql.append(" meta_partner_asset_pool t1 ");
        sql.append(" WHERE ");
        sql.append(" t1.`status` = '00' ");
        sql.append(" AND t1.partner_id =? ");
        params.add(userId);

        return transactionService.single(sql.toString(), params, MetaPartnerAssetPoolKey.class);
    }

    @Override
    public void updateAuto(Character auto, Long userId) {


        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" update ");
        sql.append(" meta_partner_asset_pool t1 ");
        sql.append(" set");
        sql.append(" auto = ?");
        params.add(auto);
        sql.append(" WHERE ");
        sql.append("  t1.partner_id =? ");
        params.add(userId);
        Query  contentQuery = entityManager.createNativeQuery(sql.toString());
        for (int i = 0; i < params.size(); i++) {
            contentQuery.setParameter(i + 1, params.get(i));
        }
        contentQuery.executeUpdate();
    }
}
