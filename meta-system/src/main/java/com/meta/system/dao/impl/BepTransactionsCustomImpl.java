package com.meta.system.dao.impl;

import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableSupport;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.data.TransactionService;
import com.meta.system.dao.BepTransactionsCustom;
import com.meta.system.dto.RechargeDto;
import com.meta.system.service.ISysUserService;
import com.meta.system.uitls.SolHttpManager;
import com.meta.system.vo.CollectRoportVo;
import com.meta.system.vo.RechargeDataVo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/24/11:47
 */
public class BepTransactionsCustomImpl implements BepTransactionsCustom {
    @Resource
    private ISysUserService sysUserService;

    @Resource
    private TransactionService transactionService;

    @Resource
    private SolHttpManager solHttpManager;

    @Override
    public Page<CollectRoportVo> getReportPage(long startStamp, long endStamp) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT ");

        sql.append(" DATE_FORMAT( FROM_UNIXTIME( t.TIMESTAMP ), '%Y%m%d' ) AS date, ");
        sql.append(" count( 1 ) AS count, ");
        sql.append(" sum( t.amount ) AS amount, ");
        sql.append(" sum( t.fee ) AS bnbFee ");
        sql.append(" FROM ");
        sql.append(" meta_bep20_transactions t ");
        sql.append(" WHERE ");
        sql.append(" t.type = 'collect' ");
        if (startStamp > 0) {
            sql.append(" and t.TIMESTAMP >= ? ");
            params.add(startStamp);
        }

        if (endStamp > 0) {
            sql.append(" and t.TIMESTAMP < ? ");
            params.add(endStamp);
        }
        sql.append(" GROUP BY ");
        sql.append(" 1 ");
        sql.append(" ORDER BY ");
        sql.append(" t.TIMESTAMP DESC ");

        return transactionService.page(sql.toString(), params, CollectRoportVo.class);
    }


    @Override
    public Page<RechargeDataVo> page(RechargeDto dto) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        StringBuilder trcSql = new StringBuilder();
        StringBuilder bepSql = new StringBuilder();
        StringBuilder erc20_arbSql = new StringBuilder();
        StringBuilder erc20_baseSql = new StringBuilder();
        if ("TRC20".equals(dto.getCoinNet())) {
            createTrc(dto, trcSql, params);
            sql = trcSql;
        } else if ("BEP20".equals(dto.getCoinNet())) {
            createBep(dto, bepSql, params);
            sql = bepSql;
        } else if ("ERC20_ARB".equals(dto.getCoinNet())) {
            createERC20_ARB(dto, erc20_arbSql, params);
            sql = erc20_arbSql;
        } else if ("ERC20_BASE".equals(dto.getCoinNet())) {
            createERC20_BASE(dto, erc20_baseSql, params);
            sql = erc20_baseSql;
        }
        //solana需要使用http请求
        else if ("SOL".equals(dto.getCoinNet())) {
            List<RechargeDataVo> list = solHttpManager.queryRechargeData(dto);
            list.forEach(item -> {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getUserName())) {
                    SysUser sysUser = sysUserService.selectUserById(Long.parseLong(item.getUserName()));
                    if (sysUser != null) {
                        item.setUserName(sysUser.getEmail());
                    }
                }
            });
            PageDomain pageReq = TableSupport.buildPageRequest();
            Pageable pageable = PageRequest.of(pageReq.getPageNo(), pageReq.getPageSize());
            return new PageImpl<>(list, pageable, pageReq.getPageSize());
        }
        return transactionService.page(sql.toString(), params, RechargeDataVo.class);
    }

    private void createERC20_BASE(RechargeDto dto, StringBuilder erc20BaseSql, ArrayList<Object> params) {
        erc20BaseSql.append("  SELECT ");
        erc20BaseSql.append("  coinNet, ");
        erc20BaseSql.append("  txnCoin, ");
        erc20BaseSql.append("  id, ");
        erc20BaseSql.append("          txid, ");
        erc20BaseSql.append("          blockheight, ");
        erc20BaseSql.append("          address, ");
        erc20BaseSql.append("          fromaddress, ");
        erc20BaseSql.append("          contract, ");
        erc20BaseSql.append("          amount, ");
        erc20BaseSql.append("          fee, ");
        erc20BaseSql.append("          date, ");
        erc20BaseSql.append("  type, ");
        erc20BaseSql.append("          issync, ");
        erc20BaseSql.append("          userName , ");
        erc20BaseSql.append("          nickName, ");
        erc20BaseSql.append("          sysId, ");
        erc20BaseSql.append("          remark ");
        erc20BaseSql.append("  FROM ");
        erc20BaseSql.append("  v_meta_base_transactions_pending ");
//        erc20BaseSql.append("  meta_base_transactions t1 ");
//        erc20BaseSql.append("  LEFT JOIN meta_bep20_cstaddressinfo t2 ON t1.address = t2.cst_address ");
//        erc20BaseSql.append("  LEFT JOIN sys_user t3 ON t2.cst_id = t3.user_id" );
//        erc20BaseSql.append("  LEFT JOIN meta_partner_asset_pool t4 ON t2.sys_id = t4.sys_id" );
        erc20BaseSql.append("  WHERE");
        erc20BaseSql.append("  type = 'receive' and issync = '0'");
        if (StringUtils.isNotEmpty(dto.getAmountFilter())) {
            if (dto.getAmountFilter().equals("<")) {
                erc20BaseSql.append(" and amount < ?");
            } else {
                erc20BaseSql.append(" and amount >= ?");
            }
            params.add("10");
        }
        if (StringUtils.isNotEmpty(dto.getTxid())) {
            erc20BaseSql.append(" and txid = ?");
            params.add(dto.getTxid());
        }

        if (StringUtils.isNotEmpty(dto.getNickName())) {
            erc20BaseSql.append(" and  userName = ?");
            params.add(dto.getNickName());
        }

        if (StringUtils.isNotEmpty(dto.getUserName())) {
            erc20BaseSql.append(" and userName = ?");
            params.add(dto.getUserName());
        }

        if (StringUtils.isNotEmpty(dto.getTxnStatus())) {
            erc20BaseSql.append(" and  issync = ?");
            params.add(dto.getTxnStatus());
        }

        if (StringUtils.isNotEmpty(dto.getSysId())) {
            erc20BaseSql.append(" and sysId = ?");
            params.add(dto.getSysId());
        }


        if (StringUtils.isNotEmpty(dto.getStartDate())) {
            erc20BaseSql.append("  and date >= ?");
            params.add(dto.getStartDate());
        }

        if (StringUtils.isNotEmpty(dto.getEndDate())) {
            erc20BaseSql.append(" and date <= ?");
            params.add(dto.getEndDate());
        }
        erc20BaseSql.append("   order by date desc");
    }

    private void createERC20_ARB(RechargeDto dto, StringBuilder erc20_arbSql, ArrayList<Object> params) {
        erc20_arbSql.append("  SELECT ");
        erc20_arbSql.append("  coinNet, ");
        erc20_arbSql.append("  txnCoin, ");
        erc20_arbSql.append("  id, ");
        erc20_arbSql.append("          txid, ");
        erc20_arbSql.append("          blockheight, ");
        erc20_arbSql.append("          address, ");
        erc20_arbSql.append("          fromaddress, ");
        erc20_arbSql.append("          contract, ");
        erc20_arbSql.append("          amount, ");
        erc20_arbSql.append("          fee, ");
        erc20_arbSql.append("          date, ");
        erc20_arbSql.append("  type, ");
        erc20_arbSql.append("          issync, ");
        erc20_arbSql.append("          userName , ");
        erc20_arbSql.append("          nickName, ");
        erc20_arbSql.append("          sysId, ");
        erc20_arbSql.append("          remark ");
        erc20_arbSql.append("  FROM ");
        erc20_arbSql.append("  v_meta_arb_transactions_pending ");
//        erc20_arbSql.append("  meta_arb_transactions t1 ");
//        erc20_arbSql.append("  LEFT JOIN meta_bep20_cstaddressinfo t2 ON t1.address = t2.cst_address ");
//        erc20_arbSql.append("  LEFT JOIN sys_user t3 ON t2.cst_id = t3.user_id" );
//        erc20_arbSql.append("  LEFT JOIN meta_partner_asset_pool t4 ON t2.sys_id = t4.sys_id" );
        erc20_arbSql.append("  WHERE");
        erc20_arbSql.append("  type = 'receive' and issync = '0'");
        if (StringUtils.isNotEmpty(dto.getAmountFilter())) {
            if (dto.getAmountFilter().equals("<")) {
                erc20_arbSql.append(" and amount < ?");
            } else {
                erc20_arbSql.append(" and amount >= ?");
            }
            params.add("10");
        }
        if (StringUtils.isNotEmpty(dto.getTxid())) {
            erc20_arbSql.append(" and txid = ?");
            params.add(dto.getTxid());
        }

        if (StringUtils.isNotEmpty(dto.getNickName())) {
            erc20_arbSql.append(" and  userName = ?");
            params.add(dto.getNickName());
        }

        if (StringUtils.isNotEmpty(dto.getUserName())) {
            erc20_arbSql.append(" and userName = ?");
            params.add(dto.getUserName());
        }

        if (StringUtils.isNotEmpty(dto.getTxnStatus())) {
            erc20_arbSql.append(" and  issync = ?");
            params.add(dto.getTxnStatus());
        }

        if (StringUtils.isNotEmpty(dto.getSysId())) {
            erc20_arbSql.append(" and sysId = ?");
            params.add(dto.getSysId());
        }


        if (StringUtils.isNotEmpty(dto.getStartDate())) {
            erc20_arbSql.append("  and date >= ?");
            params.add(dto.getStartDate());
        }

        if (StringUtils.isNotEmpty(dto.getEndDate())) {
            erc20_arbSql.append(" and date <= ?");
            params.add(dto.getEndDate());
        }
        erc20_arbSql.append("   order by date desc");
    }

    private void createBep(RechargeDto dto, StringBuilder bepSql, ArrayList<Object> params) {

        bepSql.append("  SELECT ");
        bepSql.append("  coinNet, ");
        bepSql.append("  txnCoin, ");
        bepSql.append("  id, ");
        bepSql.append("          txid, ");
        bepSql.append("          blockheight, ");
        bepSql.append("          address, ");
        bepSql.append("          fromaddress, ");
        bepSql.append("          contract, ");
        bepSql.append("          amount, ");
        bepSql.append("          fee, ");
        bepSql.append("          date, ");
        bepSql.append("  type, ");
        bepSql.append("          issync, ");
        bepSql.append("          userName , ");
        bepSql.append("          nickName, ");
        bepSql.append("          sysId, ");
        bepSql.append("          remark ");
        bepSql.append("  FROM ");
        bepSql.append("  v_meta_bep20_transactions_pending ");
//        bepSql.append("  meta_bep20_transactions t1 ");
//        bepSql.append("  LEFT JOIN meta_bep20_cstaddressinfo t2 ON t1.address = t2.cst_address ");
//        bepSql.append("  LEFT JOIN sys_user t3 ON t2.cst_id = t3.user_id ");
//        bepSql.append("  LEFT JOIN meta_partner_asset_pool t4 ON t2.sys_id = t4.sys_id" );
        bepSql.append("  WHERE");
        bepSql.append("  type = 'receive' and issync = '0'");
        if (StringUtils.isNotEmpty(dto.getAmountFilter())) {
            if (dto.getAmountFilter().equals("<")) {
                bepSql.append(" and amount < ?");
            } else {
                bepSql.append(" and amount >= ?");
            }
            params.add("10");
        }
        if (StringUtils.isNotEmpty(dto.getTxid())) {
            bepSql.append(" and txid = ?");
            params.add(dto.getTxid());
        }

        if (StringUtils.isNotEmpty(dto.getNickName())) {
            bepSql.append(" and  userName = ?");
            params.add(dto.getNickName());
        }

        if (StringUtils.isNotEmpty(dto.getUserName())) {
            bepSql.append(" and userName = ?");
            params.add(dto.getUserName());
        }

        if (StringUtils.isNotEmpty(dto.getTxnStatus())) {
            bepSql.append(" and  issync = ?");
            params.add(dto.getTxnStatus());
        }

        if (StringUtils.isNotEmpty(dto.getSysId())) {
            bepSql.append(" and sysId = ?");
            params.add(dto.getSysId());
        }


        if (StringUtils.isNotEmpty(dto.getStartDate())) {
            bepSql.append(" and date >= ?");
            params.add(dto.getStartDate());
        }

        if (StringUtils.isNotEmpty(dto.getEndDate())) {
            bepSql.append(" and date <= ?");
            params.add(dto.getEndDate());
        }

        bepSql.append("   order by   date desc");

    }

    private void createTrc(RechargeDto dto, StringBuilder trcSql, ArrayList<Object> params) {
        trcSql.append("  SELECT ");
        trcSql.append("  coinNet, ");
        trcSql.append("  txnCoin, ");
        trcSql.append("  id, ");
        trcSql.append("          txid, ");
        trcSql.append("          blockheight, ");
        trcSql.append("          address, ");
        trcSql.append("          fromaddress, ");
        trcSql.append("          contract, ");
        trcSql.append("          amount, ");
        trcSql.append("          fee, ");
        trcSql.append("          date, ");
        trcSql.append("  type, ");
        trcSql.append("          issync, ");
        trcSql.append("          userName , ");
        trcSql.append("          nickName, ");
        trcSql.append("          sysId, ");
        trcSql.append("          remark ");
        trcSql.append("  FROM ");
        trcSql.append("  v_meta_trc20_transactions_pending ");
//        trcSql.append("  meta_trc20_transactions t1 ");
//        trcSql.append("  LEFT JOIN meta_trc20_cstaddressinfo t2 ON t1.address = t2.cst_address ");
//        trcSql.append("  LEFT JOIN sys_user t3 ON t2.cst_id = t3.user_id" );
//        trcSql.append("  LEFT JOIN meta_partner_asset_pool t4 ON t2.sys_id = t4.sys_id" );
        trcSql.append("  WHERE");
        trcSql.append("  type = 'receive' and issync = '0'");
        if (StringUtils.isNotEmpty(dto.getAmountFilter())) {
            if (dto.getAmountFilter().equals("<")) {
                trcSql.append(" and amount < ?");
            } else {
                trcSql.append(" and amount >= ?");
            }
            params.add("10");
        }
        if (StringUtils.isNotEmpty(dto.getTxid())) {
            trcSql.append(" and txid = ?");
            params.add(dto.getTxid());
        }

        if (StringUtils.isNotEmpty(dto.getNickName())) {
            trcSql.append(" and  userName = ?");
            params.add(dto.getNickName());
        }

        if (StringUtils.isNotEmpty(dto.getUserName())) {
            trcSql.append(" and userName = ?");
            params.add(dto.getUserName());
        }

        if (StringUtils.isNotEmpty(dto.getTxnStatus())) {
            trcSql.append(" and  issync = ?");
            params.add(dto.getTxnStatus());
        }

        if (StringUtils.isNotEmpty(dto.getSysId())) {
            trcSql.append(" and sysId = ?");
            params.add(dto.getSysId());
        }


        if (StringUtils.isNotEmpty(dto.getStartDate())) {
            trcSql.append("  and date >= ?");
            params.add(dto.getStartDate());
        }

        if (StringUtils.isNotEmpty(dto.getEndDate())) {
            trcSql.append(" and date <= ?");
            params.add(dto.getEndDate());
        }
        trcSql.append("   order by date desc");
    }
}

