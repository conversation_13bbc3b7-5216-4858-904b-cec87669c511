package com.meta.system.dao.app;

import com.meta.system.domain.app.MetaCardCity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/08/16:42
 */
@Repository
public interface MetaCardCityDao extends JpaRepository<MetaCardCity, Long>, JpaSpecificationExecutor<MetaCardCity> {

    @Query(value = "select * from meta_card_city where country_id = ?1 ", nativeQuery = true)
    List<MetaCardCity> getListByCountry(String id);

    @Query(value = "select * from meta_card_city where country_id in (select id from  meta_card_country where  open='Y' ) ", nativeQuery = true)
    List<MetaCardCity> findOpen();

    @Query(value = "select * from meta_card_city where name = ?1 ", nativeQuery = true)
    MetaCardCity selectByname(String wlState);
}
