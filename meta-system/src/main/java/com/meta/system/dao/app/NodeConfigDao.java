package com.meta.system.dao.app;

import com.meta.system.domain.app.NodeConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface NodeConfigDao extends JpaRepository<NodeConfig, Integer>{
    NodeConfig findByNodeLevel(String nodeLevel);

    /**
     * 上一节点信息
     * @param nodeLevel
     * @return
     */
    @Query(value = "select * from meta_node_config  where CAST(node_level AS SIGNED)<CAST(?1 AS SIGNED) order by node_level desc limit 1 ",nativeQuery = true)
    NodeConfig findPreviousNode(String nodeLevel);


}
