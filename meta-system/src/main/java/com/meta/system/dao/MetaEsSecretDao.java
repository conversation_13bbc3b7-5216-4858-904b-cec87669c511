package com.meta.system.dao;

import com.meta.system.domain.es.MetaEsSecret;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * es接口的secret 数据层
 *
 * <AUTHOR>
 */
@Repository
public interface MetaEsSecretDao extends JpaRepository<MetaEsSecret, Long>, JpaSpecificationExecutor<MetaEsSecret> {
    @Query(value="select * from meta_es_secret where secret=?1 and sys_id = ?2 order by create_time desc limit 1",nativeQuery = true)
    MetaEsSecret findBySecretAndSysId(String secret, String sysId);

}
