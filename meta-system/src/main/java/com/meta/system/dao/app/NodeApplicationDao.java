package com.meta.system.dao.app;

import com.meta.system.domain.app.node.NodeApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 节点申请
 */
@Repository
public interface NodeApplicationDao extends JpaRepository<NodeApplication, Long>, NodeApplicationCustom {

    /**
     *
     * @param userId userId
     * @param approvalStatus 审批状态
     */
    @Query(value = "select count(1) from meta_node_application where user_id = ?1 and approval_status = ?2", nativeQuery = true)
    Integer checkByUserIdAndApprovalStatus(Long userId, String approvalStatus);


    /**
     *
     * @param userId userId
     * @param approvalStatus 审批状态
     */
    @Query(value = "select * from meta_node_application where user_id = ?1 and approval_status = ?2", nativeQuery = true)
    List<NodeApplication> findByUserIdAndApprovalStatus(Long userId, String approvalStatus);
//
//    @Query(value = "CALL p_rel_node_apply_benefit(:txnId, @flag)", nativeQuery = true)
//    void relNodeApplyBenefit(@Param("txnId")Long txnId);
}
