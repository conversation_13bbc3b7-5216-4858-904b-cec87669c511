package com.meta.system.dao.app;

import com.meta.system.domain.app.CoinBalance;
import com.meta.system.domain.app.CoinBalancePK;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 *  客户数字货币余额 数据层
 *
 * <AUTHOR>
 */
@Repository
public interface CoinBalanceDao extends JpaRepository<CoinBalance, CoinBalancePK>, JpaSpecificationExecutor<CoinBalance>,CoinBalanceCustom{

//    CoinBalance findByWalletAddressAndCoinCode(String walletAddress,String coidCode);

    @Modifying
    @Query(value="update meta_coin_balance set coin_balance=?3 where user_id=?1 and coin_code=?2 ",nativeQuery = true)
    void updateCoinBalance(Long userId,String coinCode,BigDecimal coinBalance);

    @Modifying
    @Query(value = "update meta_coin_balance set unclt_coin_balance=?5,freeze_balance=?4,coin_balance=?3 where user_id=?1 and coin_code=?2 ", nativeQuery = true)
    void updateCoinBalanceData(Long userId, String coinCode, BigDecimal coinBalance, BigDecimal freezeBalance, BigDecimal uncltCoinBalance);

    @Query(value = "select * from meta_coin_balance where user_id = ?1 and  coin_code=?2 for update", nativeQuery = true)
    CoinBalance findByUserIdAndCoinCode(Long userId,String coinCode);

    List<CoinBalance> findAllByUserId(Long userId);

    @Query(value = "select * from meta_coin_balance where user_id = ?1 and  coin_code=?2 for update", nativeQuery = true)
    CoinBalance findOneForUpdate(Long userId,String coinCode);

    @Modifying
    @Query(value="update meta_coin_balance set coin_balance=?3,freeze_balance=?4 where user_id=?1 and coin_code=?2 ",nativeQuery = true)
    void update(Long userId, String fromCoin, BigDecimal coinBalance, BigDecimal freezeBalance);

    @Modifying
    @Query(value = "update meta_coin_balance set unclt_bep20_coin_balance=?5,freeze_balance=?4,coin_balance=?3 where user_id=?1 and coin_code=?2 ", nativeQuery = true)
    void editCoinBalanceByEBP20(Long userId, String coinCode, BigDecimal coinBalance, BigDecimal freezeBalance, BigDecimal uncltBep20CoinBalance);

    @Modifying
    @Query(value = "update meta_coin_balance set unclt_coin_balance=?5,freeze_balance=?4,coin_balance=?3 where user_id=?1 and coin_code=?2 ", nativeQuery = true)
    void editCoinBalanceByTRC20(Long userId, String coinCode, BigDecimal coinBalance, BigDecimal freezeBalance, BigDecimal uncltTRC20CoinBalance);


    @Modifying
    @Query(value = "update meta_coin_balance set unclt_sol_coin_balance=?5,freeze_balance=?4,coin_balance=?3 where user_id=?1 and coin_code=?2 ", nativeQuery = true)
    void editCoinBalanceBySol(Long userId, String coinCode, BigDecimal coinBalance, BigDecimal freezeBalance, BigDecimal uncltSolCoinBalance);


    @Modifying
    @Query(value = "update meta_coin_balance set unclt_erc20_base_coin_balance=?5,freeze_balance=?4,coin_balance=?3 where user_id=?1 and coin_code=?2 ", nativeQuery = true)
    void editCoinBalanceByErc20Base(Long userId, String coinCode, BigDecimal coinBalance, BigDecimal freezeBalance, BigDecimal uncltBep20CoinBalance);

    @Modifying
    @Query(value = "update meta_coin_balance set unclt_erc20_arb_coin_balance=?5,freeze_balance=?4,coin_balance=?3 where user_id=?1 and coin_code=?2 ", nativeQuery = true)
    void editCoinBalanceByErc20Arb(Long userId, String coinCode, BigDecimal coinBalance, BigDecimal freezeBalance, BigDecimal uncltBep20CoinBalance);

}
