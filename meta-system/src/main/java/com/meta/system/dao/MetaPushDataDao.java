package com.meta.system.dao;

import com.meta.system.domain.partner.MetaPushData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/08/22/14:40
 */
@Repository
public interface MetaPushDataDao extends JpaRepository<MetaPushData, Long>, JpaSpecificationExecutor<MetaPushData>, MetaPushDataDaoCustom {

    @Query(value = "select * from meta_push_data where statue='3' ", nativeQuery = true)
    List<MetaPushData> findNoPush();

    @Modifying
    @Query(value = "update meta_push_data set statue=?2 where id =?1 and num =0  ", nativeQuery = true)
    void updateStatue(Long  id, String statue);

    @Modifying
    @Query(value = "update meta_push_data set statue=?2,num=num+1 where id =?1  ", nativeQuery = true)
    void updateFailStatue(long id, String s);

    @Modifying
    @Transactional
    @Query(
            value = "INSERT INTO meta_push_data " +
                    "(id, request_no, sys_id, card_id, user_email, api_url, api_code, request, response, statue, num, create_time) " +
                    "SELECT 0, CAST(UNIX_TIMESTAMP(NOW(6)) * 100000000 AS DECIMAL(20,0)), t2.sys_id, '', '', t4.api_url, 'H5_LISTENING_DATA', " +
                    "CONCAT(" +
                    "'{\"coinType\":\"USDT\",'," +
                    "'\"amount\":', t1.amount, ',', " +
                    "'\"use\":', t1.fee, ',', " +
                    "'\"txid\":\"', t1.txid, '\",'," +
                    "'\"trcTransactions\":{'," +
                    "'\"amount\":', t1.amount, ',', " +
                    "'\"address\":\"', t1.address, '\",'," +
                    "'\"blockHeight\":', t1.blockheight, ',', " +
                    "'\"contract\":\"', t1.contract, '\",'," +
                    "'\"fee\":', t1.fee, ',', " +
                    "'\"txid\":\"', t1.txid, '\",'," +
                    "'\"type\":\"', t1.type, '\",'," +
                    "'\"fromaddress\":\"', t1.fromaddress, '\",'," +
                    "'\"isSync\":', t1.issync, ',', " +
                    "'\"timestamp\":', t1.timestamp, " +
                    "'},'," +
                    "'\"from\":\"', t1.fromaddress, '\",'," +
                    "'\"to\":\"', t1.address, '\",'," +
                    "'\"time\":', t1.timestamp * 1000, ','," +
                    "'\"netWork\":\"', t3.type, '\",'," +
                    "'\"mainAddress\":\"', t3.mainaddress, '\",'," +
                    "'\"isRecorded\":true}'" +
                    ") AS request, " +
                    "'true', '3', 0, NOW() " +
                    "FROM meta_trc20_transactions t1 " +
                    "LEFT JOIN meta_trc20_cstaddressinfo t2 ON t1.address = t2.cst_address " +
                    "LEFT JOIN meta_main_address t3 ON t2.sys_id = t3.sys_id AND t3.type = 'TRC20' " +
                    "LEFT JOIN meta_partner_asset_pool t4 ON t2.sys_id = t4.sys_id " +
                    "WHERE t1.txid = :txid AND t1.issync = '0'",
            nativeQuery = true
    )
    int pushByTrc(@Param("txid") String txid);


    @Modifying
    @Transactional
    @Query(
            value = "INSERT INTO meta_push_data " +
                    "(id, request_no, sys_id, card_id, user_email, api_url, api_code, request, response, statue, num, create_time) " +
                    "SELECT 0, CAST(UNIX_TIMESTAMP(NOW(6)) * 100000000 AS DECIMAL(20,0)), t2.sys_id, '', '', t4.api_url, 'H5_LISTENING_DATA', " +
                    "CONCAT(" +
                    "'{\"coinType\":\"USDT\",'," +
                    "'\"amount\":', t1.amount, ',', " +
                    "'\"bepTransactions\":{'," +
                    "'\"amount\":', t1.amount, ',', " +
                    "'\"address\":\"', t1.address, '\",'," +
                    "'\"blockHeight\":', t1.blockheight, ',', " +
                    "'\"contract\":\"', t1.contract, '\",'," +
                    "'\"fee\":', t1.fee, ',', " +
                    "'\"txid\":\"', t1.txid, '\",'," +
                    "'\"type\":\"', t1.type, '\",'," +
                    "'\"fromaddress\":\"', t1.fromaddress, '\",'," +
                    "'\"isSync\":', t1.issync, ',', " +
                    "'\"timestamp\":', t1.timestamp, " +
                    "'},'," +
                    "'\"use\":', t1.fee, ',', " +
                    "'\"txid\":\"', t1.txid, '\",'," +
                    "'\"from\":\"', t1.fromaddress, '\",'," +
                    "'\"to\":\"', t1.address, '\",'," +
                    "'\"time\":\"', t1.timestamp * 1000, '\",'," +
                    "'\"netWork\":\"', t3.type, '\",'," +
                    "'\"mainAddress\":\"', t3.mainaddress, '\",'," +
                    "'\"isRecorded\":true}'" +
                    ") AS request, " +
                    "'true', '3', 0, NOW() " +
                    "FROM meta_bep20_transactions t1 " +
                    "LEFT JOIN meta_bep20_cstaddressinfo t2 ON t1.address = t2.cst_address " +
                    "LEFT JOIN meta_main_address t3 ON t2.sys_id = t3.sys_id AND t3.type = 'BEP20' " +
                    "LEFT JOIN meta_partner_asset_pool t4 ON t2.sys_id = t4.sys_id " +
                    "WHERE t1.txid = :txid AND t1.issync = '0'",
            nativeQuery = true
    )
    int pushByBep(@Param("txid") String txid);
}
