package com.meta.system.dao.app;

import com.meta.system.domain.app.FileSource;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface FileSourceDao extends JpaRepository<FileSource,Long>, JpaSpecificationExecutor<FileSource> {

    @Modifying
    @Query(value = "Update oa_file_source set del_flag = 1 where id = ?1",nativeQuery = true)
    void UpdateDelFlag(Long id);

    @Modifying
    @Query(value = "Update oa_file_source set del_flag = 1 where id in (?1)",nativeQuery = true)
    void UpdateDelFlag(List<Long> id);

    List<FileSource> findByFileIdAndDelFlag(String fileId, String delFlag);

    FileSource findByIdAndDelFlag(Long id, String delFlag);

    @Query(value = "select * from oa_file_source where file_id in (?1) and del_flag = 0",nativeQuery = true)
    List<FileSource> findByFileIds(List<String> fileIds);

    @Query(value = " SELECT t2.* FROM oa_file_source t2 " +
            " WHERE t2.id IN (SELECT t1.id FROM (SELECT id, ROW_NUMBER() over ( PARTITION BY file_id, file_type ORDER BY file_date DESC ) AS rn FROM oa_file_source ) t1 " +
            " WHERE rn = 1) AND t2.file_id = ?1 and t2.del_flag = '0'",nativeQuery = true)
    List<FileSource> findByFileIdAndNew(String fileId);


    @Query(value = " SELECT t2.* FROM oa_file_source t2 " +
            " WHERE t2.id IN (SELECT t1.id FROM (SELECT id, ROW_NUMBER() over ( PARTITION BY file_id, file_type ORDER BY file_date DESC ) AS rn FROM oa_file_source ) t1 " +
            " WHERE rn = 1) AND t2.file_id = ?1 and t2.del_flag = '0' and file_type in (?2) ",nativeQuery = true)
    List<FileSource> findByFileIdAndFileTypeInAndNew(String fileId,List<String> fileTypes);

    @Modifying
    @Query(value = "Update oa_file_source set del_flag = 1 where id in ?1",nativeQuery = true)
    void UpdateAllDelFlag(List<Long> ids);

    @Query(value = "select file_type from oa_file_source where file_id = ?1 ",nativeQuery = true)
    List<String> findEmpFileType(String fileId);

    @Modifying
    @Query(value = "Update oa_file_source set file_name = ?1 ,file_source_name = ?2 ,file_path = ?3 ,file_date = ?4 ,file_size = ?5 " +
            " where file_id = ?6 and file_type = ?7",nativeQuery = true)
    void empFileUpdate(String fileName, String fileSourceName, String filePath, Date fileDate, String fileSize,String fileId,String fileType);

    List<FileSource> findByFileIdAndDelFlagAndFileTypeIn(String fileId, String delFlag, List<String> fileType);

    void deleteByFileId(String fileId);

    List<FileSource> findByFileIdAndFileTypeInAndDelFlag(String fileId, List<String> fileTypes,String delFlag);

    List<FileSource> findByFileIdAndFileTypeIn(String fileId, List<String> fileTypes);

    List<FileSource> findByFileSourceNameAndFileType(String filename,String fileType);

    @Query(value = "select * from oa_file_source where file_id = ?1 limit 1",nativeQuery = true)
    FileSource findByFileIdLimit1(String fileId);


    @Query(value = "update oa_file_source set del_flag = 1 where file_id = ?1 and file_type in (?2)",nativeQuery = true)
    @Modifying
    void updateByFileIdAndFileTypeIn(String fileId, List<String> fileTypes);


    @Query(value = "delete oa_file_source where file_id = ?1 and file_type in (?2)",nativeQuery = true)
    @Modifying
    void deleteByFileIdAndFileTypeIn(String fileId, List<String> fileTypes);
}
