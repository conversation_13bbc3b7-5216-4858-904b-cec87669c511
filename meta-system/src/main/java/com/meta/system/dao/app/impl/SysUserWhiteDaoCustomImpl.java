package com.meta.system.dao.app.impl;

import com.meta.common.constant.Constants;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.data.TransactionService;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.system.dao.app.SysUserWhiteDaoCustom;
import com.meta.system.domain.app.SysUserWhite;
import com.meta.system.vo.CreditCardVo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @Date 2025/5/12
 */
@Repository
public class SysUserWhiteDaoCustomImpl implements SysUserWhiteDaoCustom {

    @Resource
    private TransactionService transactionService;


    @Override
    public Page<SysUserWhite> selectUserWhiteList(SysUserWhite sysUserWhite) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sqlHead = new StringBuilder();
        StringBuilder sqlWhere = new StringBuilder();
        sqlHead.append(" select ");
        sqlHead.append(" t1.user_id as userId, ");
        sqlHead.append(" t1.create_by as createBy, ");
        sqlHead.append(" t1.create_time as createTime, ");
        sqlHead.append(" t1.update_by as updateBy, ");
        sqlHead.append(" t1.update_time as updateTime, ");
        sqlHead.append(" t2.user_name as userName ");
        sqlHead.append(" from sys_user_white t1 ");
        sqlHead.append(" left join sys_user t2 on t2.user_id = t1.user_id ");
        sqlHead.append(" where 1 = 1 ");
        if (StringUtils.isNotEmpty(sysUserWhite.getUserName())) {
            sqlWhere.append(" and t2.user_name LIKE ? ");
            params.add("%" + sysUserWhite.getUserName() + "%");
        }

        sqlWhere.append(" and t1.del_flag = '0' ");
        sqlWhere.append(" order by t1.create_time desc ");
        return transactionService.page(sqlHead + sqlWhere.toString(), params, SysUserWhite.class);
    }
}
