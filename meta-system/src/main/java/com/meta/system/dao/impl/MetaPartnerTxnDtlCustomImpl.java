package com.meta.system.dao.impl;

import com.meta.common.utils.StringUtils;
import com.meta.common.utils.data.TransactionService;
import com.meta.system.dao.MetaPartnerTxnDtlCustom;
import com.meta.system.dto.PoolTxnDto;
import com.meta.system.vo.PoolTxnDtlUSDVo;
import org.springframework.data.domain.Page;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2024/06/21/14:00
 */
public class MetaPartnerTxnDtlCustomImpl implements MetaPartnerTxnDtlCustom {
    private final TransactionService transactionService;

    public MetaPartnerTxnDtlCustomImpl(TransactionService transactionService) {
        this.transactionService = transactionService;
    }

    @Override
    public Page<PoolTxnDtlUSDVo> pageForUSD_B(PoolTxnDto poolTxnDto) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sqlHead = new StringBuilder();
        StringBuilder sqlWhere = new StringBuilder();
        sqlHead.append(" select ");
        sqlHead.append(" t.card_no as cardNo, ");
        sqlHead.append(" u.ID AS id, ");
        sqlHead.append(" u.partner_id AS userId, ");
        sqlHead.append(" u.coin_code as coinCode, ");
        sqlHead.append(" u.currency as currency, ");
        sqlHead.append(" u.txn_type AS txnType, ");
        sqlHead.append(" u.txn_amount AS txnAmount, ");
        sqlHead.append(" u.txn_num AS txnNum, ");
        sqlHead.append(" u.card_id AS cardId, ");
        sqlHead.append(" u.txn_fee AS txnFee, ");
        sqlHead.append(" u.txn_time AS txnTime, ");
        sqlHead.append(" u.txn_status AS txnStatus, ");
        sqlHead.append(" u.txn_desc AS txnDesc, ");
        sqlHead.append(" u.asset_balance AS usdBalance, ");
        sqlHead.append(" u.txn_id AS txnId ");
        sqlHead.append(" FROM meta_partner_txn_dtl u ")
                .append(" inner join meta_credit_card t on u.card_id=t.card_id ");
        if (poolTxnDto != null) {

            sqlWhere.append(" where  u.partner_id = ? ");
            params.add(poolTxnDto.getUserId());

            if (poolTxnDto.getTxnStatus() != null) {
                sqlWhere.append(" and u.txn_status = ? ");
                params.add(poolTxnDto.getTxnStatus());
            }


            if (StringUtils.isNotEmpty(poolTxnDto.getCardId())) {
                sqlWhere.append(" and u.card_id = ? ");
                params.add(poolTxnDto.getCardId());
            }

            if (StringUtils.isNotEmpty(poolTxnDto.getCardNo())) {
                sqlWhere.append(" and t.card_no = ? ");
                params.add(poolTxnDto.getCardNo());
            }

            if (StringUtils.isNotEmpty(poolTxnDto.getTxnType())) {
                sqlWhere.append(" and u.txn_type =? ");
                params.add(poolTxnDto.getTxnType());
            }

            // 开始日期
            if (StringUtils.isNotEmpty(poolTxnDto.getStartDate())) {
                sqlWhere.append(" and date_format(u.txn_time,'%Y-%m-%d') >= ? ");
                params.add(poolTxnDto.getStartDate());
            }
            // 结束日期
            if (StringUtils.isNotEmpty(poolTxnDto.getEndDate())) {
                sqlWhere.append(" and date_format(u.txn_time,'%Y-%m-%d') <= ? ");
                params.add(poolTxnDto.getEndDate());
            }
        }
        sqlWhere.append(" order by  u.txn_time desc,u.id desc  ");
        return transactionService.page(sqlHead + sqlWhere.toString(), params, PoolTxnDtlUSDVo.class);
    }


}
