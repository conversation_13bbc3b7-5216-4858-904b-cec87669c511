package com.meta.system.dao;

import com.meta.system.domain.app.MetaExchangeOrderInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/17
 */

@Repository
public interface MetaExchangeOrderInfoDao extends JpaRepository<MetaExchangeOrderInfo, Long>, JpaSpecificationExecutor<MetaExchangeOrderInfoDao> , MetaExchangeOrderInfoCustom {

    @Query(value = "select * from meta_exchange_order_info where user_id = ?1 and order_status = 'PENDING_ORDER_SUBMISSION' and del_flag = 'exist'",nativeQuery = true)
    List<MetaExchangeOrderInfo> getPendingOrderSubmissionByUserId(Long userId);

    @Query(value = "select * from meta_exchange_order_info where merchant_order_id = ?1",nativeQuery = true)
    MetaExchangeOrderInfo findByMerchantOrderId(String merchantOrderId);

    @Query(value = "select * from meta_exchange_order_info where user_id = ?1",nativeQuery = true)
    List<MetaExchangeOrderInfo> getOrderListByUserId(Long userId);

    @Query(value = "select * from meta_exchange_order_info where order_status not in ('SUCCESS', 'FAILURE') and del_flag = 'exist'",nativeQuery = true)
    List<MetaExchangeOrderInfo> getOrdersWithSuspectedProblems();

    @Modifying
    @Transactional
    @Query("UPDATE MetaExchangeOrderInfo e SET " +
            "e.cryptoCurrency = :cryptoCurrency, " +
            "e.fiatCurrency = :fiatCurrency, " +
            "e.orderStatus = :orderStatus, " +
            "e.quotePrice = :quotePrice, " +
            "e.fiatAmount = :fiatAmount, " +
            "e.cryptoAmount = :cryptoAmount, " +
            "e.fiatFee = :fiatFee, " +
            "e.tradeFee = :tradeFee, " +
            "e.merchantFee = :merchantFee, " +
            "e.networkFee = :networkFee, " +
            "e.total = :total, " +
            "e.method = :method, " +
            "e.accountNumber = :accountNumber, " +
            "e.txId = :txId, " +
            "e.createdAt = :createdAt, " +
            "e.updatedAt = :updatedAt, " +
            "e.payTime = :payTime, " +
            "e.dealTime = :dealTime, " +
            "e.network = :network " +
            "WHERE e.merchantOrderId = :merchantOrderId " +
            "AND e.delFlag = 'exist' " +
            "AND e.orderStatus != 'SUCCESS'")
    int updateOrder(
            @Param("cryptoCurrency") String cryptoCurrency,
            @Param("fiatCurrency") String fiatCurrency,
            @Param("orderStatus") String orderStatus,
            @Param("quotePrice") String quotePrice,
            @Param("fiatAmount") String fiatAmount,
            @Param("cryptoAmount") String cryptoAmount,
            @Param("fiatFee") String fiatFee,
            @Param("tradeFee") String tradeFee,
            @Param("merchantFee") String merchantFee,
            @Param("networkFee") String networkFee,
            @Param("total") String total,
            @Param("method") String method,
            @Param("accountNumber") String accountNumber,
            @Param("txId") String txId,
            @Param("createdAt") String createdAt,
            @Param("updatedAt") String updatedAt,
            @Param("payTime") String payTime,
            @Param("dealTime") String dealTime,
            @Param("network") String network,
            @Param("merchantOrderId") String merchantOrderId
    );

}
