package com.meta.system.dao;

import com.meta.common.core.domain.AjaxResult;
import com.meta.system.domain.app.MetaPlatformStateRestriction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/17
 */
@Repository
public interface MetaPlatformStateRestrictionDao  extends JpaRepository<MetaPlatformStateRestriction, Integer>, JpaSpecificationExecutor<MetaPlatformStateRestriction>{

    @Query(value = "select * from meta_platform_state_restriction where platform=?1 and country_code=?2 and open='open' ", nativeQuery = true)
    MetaPlatformStateRestriction getOneByPlatformAndCountryCode(String platform, String loginLocation);
}
