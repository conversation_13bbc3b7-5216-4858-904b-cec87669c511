package com.meta.system.dao.impl;

import com.meta.common.utils.StringUtils;
import com.meta.common.utils.data.TransactionService;
import com.meta.system.dao.MetaCouponInfoCustom;
import com.meta.system.dto.CouponDto;
import com.meta.system.vo.MetaCouponGenCfgVo;
import com.meta.system.vo.MetaCouponInfoVo;
import org.springframework.data.domain.Page;

import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2024/10/09/14:18
 */
public class MetaCouponInfoCustomImpl implements MetaCouponInfoCustom {

    @Resource
    private TransactionService transactionService;

    @Override
    public Page<MetaCouponInfoVo> couponInfoPage(CouponDto couponDto) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();

        getSql(couponDto, params, sql);


        sql.append(" ORDER BY ");
        sql.append(" t.exp_date DESC ");
        return transactionService.page(sql.toString(), params, MetaCouponInfoVo.class);
    }

    @Override
    public Page<MetaCouponInfoVo> couponInfoPageByUser(CouponDto couponDto) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();

        getSql(couponDto, params, sql);

        if (StringUtils.isEmpty(couponDto.getStatus())) {
            sql.append(" and t.status not in ('2','4') ");
        }

        if (StringUtils.isNotEmpty(couponDto.getNowDate())) {

            sql.append(" and date_format(t.eft_date,'%Y-%m-%d') <= ? ");
            params.add(couponDto.getNowDate());

            sql.append(" and date_format(t.exp_date,'%Y-%m-%d') >= ? ");
            params.add(couponDto.getNowDate());

        }

        sql.append(" AND ( (t.adm_user_id = ? AND t.user_id IS NULL) ");
        params.add(couponDto.getUserId());
        sql.append(" OR  t.user_id = ? ");
        params.add(couponDto.getUserId());

        sql.append(" OR  t.use_user_id = ? )");
        params.add(couponDto.getUserId());

        sql.append(" ORDER BY ");
        sql.append(" t.exp_date DESC ");
        return transactionService.page(sql.toString(), params, MetaCouponInfoVo.class);
    }

    private void getSql(CouponDto couponDto, ArrayList<Object> params, StringBuilder sql) {
        sql.append("SELECT                                                   ");
        sql.append("	t.id AS id,                                          ");
        sql.append("	t.coupon_gen_cfg_id AS couponGenCfgId,               ");
        sql.append("	t.adm_user_id AS admUserId,                          ");
        sql.append("	t1.user_name as admUserName,                         ");
        sql.append("	t.user_id as userId,                                 ");
        sql.append("	t2.user_name as userName,                            ");
        sql.append("	t.use_user_id as useUserId,                          ");
        sql.append("	t3.user_name as useUserName,                         ");
        sql.append("	t.coupon_nm as couponNm,                             ");
        sql.append("	t.coupon_type AS couponType,                         ");
        sql.append("	t.coupon_scenarios AS couponScenarios,               ");
        sql.append("	t.coupon_card_type AS couponCardType,                ");
        sql.append("	t.coupon_cardlevel AS couponCardlevel,               ");
        sql.append("	t.coupon_node AS couponNode,                         ");
        sql.append("	t.discount_coin AS discountCoin,                     ");
        sql.append("	t.discount_rate AS discountRate,                     ");
        sql.append("	t.discount_amt AS discountAmt,                       ");
        sql.append("	t.max_discount_amt AS maxDiscountAmt,                ");
        sql.append("	t.min_spend_amt AS minSpendAmt,                      ");
        sql.append("	t.eft_date AS eftDate,                               ");
        sql.append("	t.exp_date AS expDate,                               ");
        sql.append("	t.create_time AS createTime ,                        ");
        sql.append("	t.update_time AS updateTime ,                        ");
        sql.append("	t.used_time as usedTime,                             ");
        sql.append("	t.coupon_usage_limit as couponUsageLimit,            ");
        sql.append("	t.coupon_usage_cnt as couponUsageCnt,                ");
        sql.append("	t.status as status                                   ");
        sql.append(" FROM                                                    ");
        sql.append("	meta_coupon_info t                                   ");
        sql.append("	left join sys_user t1 on t1.user_id=t.adm_user_id    ");
        sql.append("	left join sys_user t2 on t2.user_id=t.user_id        ");
        sql.append("	left join sys_user t3 on t3.user_id=t.use_user_id    ");


        sql.append(" WHERE ");
        sql.append(" 1 = 1 ");


        if (couponDto.getCouponGenCfgId() != null) {
            sql.append(" AND t.coupon_gen_cfg_id = ? ");
            params.add(couponDto.getCouponGenCfgId());
        }
        if (couponDto.getAdmUserId() != null) {
            sql.append(" AND t.adm_user_id = ? ");
            params.add(couponDto.getAdmUserId());
        }
        if (StringUtils.isNotEmpty(couponDto.getStatus())) {
            sql.append(" AND t.status = ? ");
            params.add(couponDto.getStatus());
        }

        if (StringUtils.isNotEmpty(couponDto.getCouponNm())) {
            sql.append(" AND t.coupon_nm = ? ");
            params.add(couponDto.getCouponNm());
        }


        if (StringUtils.isNotEmpty(couponDto.getCouponType())) {
            sql.append(" AND t.coupon_type = ? ");
            params.add(couponDto.getCouponType());
        }

        if (StringUtils.isNotEmpty(couponDto.getCouponScenarios())) {
            sql.append(" AND t.coupon_scenarios = ? ");
            params.add(couponDto.getCouponScenarios());
        }
        if (StringUtils.isNotEmpty(couponDto.getCouponCardType())) {
            sql.append(" AND (t.coupon_card_type LIKE ?  or  t.coupon_card_type is null or  t.coupon_card_type ='' ) ");
            params.add("%" + couponDto.getCouponCardType() + "%");
        }

        if (StringUtils.isNotEmpty(couponDto.getCouponCardlevel())) {
            sql.append(" AND (t.coupon_cardlevel LIKE ? or  t.coupon_cardlevel is null or  t.coupon_cardlevel ='')");
            params.add("%" + couponDto.getCouponCardlevel() + "%");
        }

        if (StringUtils.isNotEmpty(couponDto.getCouponNode())) {
            sql.append(" AND (t.coupon_node LIKE ? or  t.coupon_node is null or  t.coupon_node ='' )");
            params.add("%" + couponDto.getCouponNode() + "%");
        }

        if (StringUtils.isNotEmpty(couponDto.getStartDate())) {
            sql.append(" and date_format(t.create_time,'%Y-%m-%d') >= ? ");
            params.add(couponDto.getStartDate());
        }

        if (StringUtils.isNotEmpty(couponDto.getEndDate())) {
            sql.append(" and date_format(t.create_time,'%Y-%m-%d') <= ? ");
            params.add(couponDto.getEndDate());
        }

        if (StringUtils.isNotEmpty(couponDto.getStartExpDate())) {
            sql.append(" and date_format(t.exp_date,'%Y-%m-%d') >= ? ");
            params.add(couponDto.getStartExpDate());
        }

        if (StringUtils.isNotEmpty(couponDto.getEndExpDate())) {
            sql.append(" and date_format(t.exp_date,'%Y-%m-%d') <= ? ");
            params.add(couponDto.getEndExpDate());
        }
    }
}
