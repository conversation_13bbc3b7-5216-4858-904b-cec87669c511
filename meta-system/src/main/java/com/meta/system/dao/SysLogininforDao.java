package com.meta.system.dao;

import com.meta.system.domain.SysLogininfor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 系统访问日志情况信息 数据层
 *
 * <AUTHOR>
 * @since 1.0  2020-12-11
 */
@Repository
public interface SysLogininforDao extends JpaRepository<SysLogininfor, Long>, JpaSpecificationExecutor<SysLogininfor> {

    void deleteAllByInfoIdIn(List<Long> ids);

    @Modifying
    @Query(value = "truncate table sys_logininfor", nativeQuery = true)
    void truncateTable();


    @Query(value = " SELECT * FROM sys_logininfor where user_name=?1 order by login_time desc    LIMIT 1, 1", nativeQuery = true)
    SysLogininfor findInfor(String userName);

    @Query(value = " SELECT * FROM sys_logininfor where user_name=?1 order by login_time desc    LIMIT 0, 1", nativeQuery = true)
    SysLogininfor getOneByUserName(String userName);

    @Query(value = " SELECT  distinct ipaddr FROM sys_logininfor where user_name=?1 ", nativeQuery = true)
    List<String> selectIplist(String userName);
}
