package com.meta.system.dao.app;



import com.meta.system.domain.app.MetaCardLogistics;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2024/03/08/16:40
 */

@Repository
public interface MetaCardLogisticsDao extends JpaRepository<MetaCardLogistics, Long>, JpaSpecificationExecutor<MetaCardLogistics> , MetaCardLogisticsCustom {

    @Modifying
    @Query(value = "update meta_card_logistics set tracking_number = ?1,type=?2 where id = ?3",nativeQuery = true)
    void updateTrackingNumber(String trackingNumber, String type,Integer id);


    @Query(value = "select * from  meta_card_logistics where card_id = ?1",nativeQuery = true)
    MetaCardLogistics selectByNumber(String cardId);
}
