package com.meta.system.dao;

import com.meta.system.vo.WalletVo;
import org.springframework.data.domain.Page;

/**
 * 钱包 数据层
 *
 * <AUTHOR>
 */

public interface WalletCustom{

    /**
     * 账号管理列表
     *
     * @param username    用户名
     * @param status      状态
     * @param startDate   注册开始时间
     * @param endDate     注册结束日期
     * @param nodeLevel
     * @param channelName
     * @param limitFlag
     * @return
     */
    Page<WalletVo> selectUserList(String nickName,String username, String status, String startDate, String endDate, String nodeLevel, String channelName, String limitFlag);
}
