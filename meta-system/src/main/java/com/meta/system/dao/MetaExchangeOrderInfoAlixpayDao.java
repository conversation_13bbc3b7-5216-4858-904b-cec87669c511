package com.meta.system.dao;

import com.meta.system.domain.app.MetaExchangeOrderInfoAlixpay;
import com.meta.system.vo.MetaExchangeOrderInfoAlixpayVo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/7
 */
@Repository
public interface MetaExchangeOrderInfoAlixpayDao extends JpaRepository<MetaExchangeOrderInfoAlixpay, String>, JpaSpecificationExecutor<MetaExchangeOrderInfoAlixpayDao>, MetaExchangeOrderInfoAlixpayCustom{

    @Query(value = "select * from meta_exchange_order_info_alixpay where merchant_order_id = ?1",nativeQuery = true)
    MetaExchangeOrderInfoAlixpay getOrderByMerchantOrderId(String merchantOrderId);

    @Query(value = "select * from meta_exchange_order_info_alixpay where merchant_order_id = ?1 for update ",nativeQuery = true)
    MetaExchangeOrderInfoAlixpay getOneForUpdate(String merchantOrderId);

    @Query(value = "select * from meta_exchange_order_info_alixpay where status = 'PENDING' for update ",nativeQuery = true)
    List<MetaExchangeOrderInfoAlixpay> getPendingOrder();
}
