package com.meta.system.dao.impl;


import com.meta.common.constant.Constants;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.data.TransactionService;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.system.dao.CreditCardLogCustom;
import com.meta.system.dto.CardLogDto;
import com.meta.system.vo.CreditCardLogVo;
import org.hibernate.query.internal.NativeQueryImpl;
import org.hibernate.transform.Transformers;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/06/01/2:39
 */
public class CreditCardLogCustomImpl implements CreditCardLogCustom {
    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    private TransactionService transactionService;

    @Override
    public Page<CreditCardLogVo> selectData(String cardID, String transactionId, String transactionStartTime, String transactionEndTime, PageRequest pageable) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" from meta_credit_card_log  where 1=1 ");
        sql.append("  and card_id = ?");
        params.add(cardID);
        //sql.append("  TRANSACTION_type in ('CLEARING','AUTH_REVERSAL','AUTH_FAILURE','OTHER','card_deposit','card_withdraw','AUTH','AUTH_QUERY','REVERSAL','REFUND','FEE','TRADE_PROCESS_FEE','TRADE_CROSS_BOARD_FEE','TRADE_REFUND_FEE','FEE_REVERSAL','ORIGINAL_CREDIT','ORIGINAL_CREDIT_REVERSAL')");
        if (StringUtils.isNotEmpty(transactionId)) {
            sql.append("   AND transaction_id= ? ");
            params.add(transactionId);
        }
        if (StringUtils.isNotEmpty(transactionStartTime)) {
            sql.append("   AND transaction_date >= ? ");
            params.add(transactionStartTime);
        }
        if (StringUtils.isNotEmpty(transactionEndTime)) {
            sql.append("   AND  transaction_date <= ? ");
            params.add(transactionEndTime);
        }

        Query totalQuery = entityManager.createNativeQuery("select count(*) " + sql.toString());
        int size = params.size();
        for (int i = 0; i < size; i++) {
            totalQuery.setParameter(i + 1, params.get(i));
        }

        List<?> resultList = totalQuery.getResultList();
        if (resultList.isEmpty()) {
            return Page.empty();
        }
        BigInteger total = (BigInteger) resultList.get(0);

        Query contentQuery = entityManager.createNativeQuery(buildSelect() + sql.toString() + buildOrder());
        for (int i = 0; i < size; i++) {
            contentQuery.setParameter(i + 1, params.get(i));
        }
        contentQuery.unwrap(NativeQueryImpl.class).setResultTransformer(Transformers.aliasToBean(CreditCardLogVo.class));
        contentQuery.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());
        contentQuery.setMaxResults(pageable.getPageSize());
        List<CreditCardLogVo> collect = contentQuery.getResultList();
        return new PageImpl<>(collect, pageable, total.intValue());
    }

    @Override
    public Page<CreditCardLogVo> page(CardLogDto vo) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT      ");
        sql.append(" 	t1.id AS id,  ");
        sql.append(" 	t1.card_id AS cardID,                                                 ");
        sql.append(" 	t2.card_no AS cardNo,                                                 ");
        sql.append(" 	t3.user_name AS userName,                                             ");
        sql.append(" 	t3.nick_name AS nickName,                                             ");
        sql.append(" 	t1.transaction_id AS transactionId,                                   ");
        sql.append(" 	t1.transaction_currency AS transCurrency,                             ");
        sql.append(" 	t1.transaction_amount AS transCurrencyAmt,                            ");
        sql.append(" 	t1.card_currency AS cardCurrency,                                     ");
        sql.append(" 	t1.card_transaction_amount AS cardCurrencyAmt,                        ");
        sql.append(" 	t1.TRANSACTION_type AS transType,                                     ");
        sql.append(" 	t1.transaction_status AS transStatus,                                 ");
        sql.append(" 	t1.transaction_date AS transactionTime,                               ");
        sql.append(" 	t1.fee AS fee,                                                        ");
        sql.append(" 	t1.fee_currency AS feeCurrency,                                       ");
        sql.append(" 	t1.merchant_name AS merchantName,                                     ");
        sql.append(" 	t1.merchant_city AS merchantCity,                                     ");
        sql.append(" 	t1.transaction_detail AS transactionDetail,                           ");
        sql.append(" 	t1.card_available_balance AS cardAvailableBalance,                    ");
        sql.append(" 	t1.card_available_balance_currency AS cardAvailableBalanceCurrency,   ");
        sql.append(" 	t1.description AS description                                         ");
        sql.append(" FROM                                                                     ");
        sql.append(" 	meta_credit_card_log t1                                               ");
        sql.append(" 	LEFT JOIN meta_credit_card t2 ON t2.card_id = t1.card_id              ");
        sql.append(" 	LEFT JOIN sys_user t3 ON t2.user_id = t3.user_id                      ");

        if (vo != null) {
            sql.append(" where  1=1  ");

            if (StringUtils.isNotEmpty(vo.getSysId())) {
                sql.append(" and t2.sys_id = ? ");
                params.add(vo.getSysId());
            }


            if (StringUtils.isNotEmpty(vo.getCardNo())) {
                sql.append(" and t2.card_no = ? ");
                params.add(AESUtils.aesEncrypt(Constants.card_key, vo.getCardNo()));
            }

            if (StringUtils.isNotEmpty(vo.getTransType())) {
                sql.append(" and t1.TRANSACTION_type = ? ");
                params.add(vo.getTransType());
            }

            if (StringUtils.isNotEmpty(vo.getUserName())) {
                sql.append(" and t3.user_name like ? ");
                params.add("%" + vo.getUserName() + "%");
            }
            if (StringUtils.isNotEmpty(vo.getNickName())) {
                sql.append(" and t3.nick_name = ? ");
                params.add(vo.getNickName());
            }
            // 开始日期
            if (StringUtils.isNotEmpty(vo.getStartDate())) {
                sql.append(" and  t1.transaction_date >= ? ");
                params.add(vo.getStartDate() + " 00:00:00");
            }
            // 结束日期
            if (StringUtils.isNotEmpty(vo.getEndDate())) {
                sql.append(" and  t1.transaction_date <= ? ");
                params.add(vo.getEndDate() + " 23:59:59");
            }

        }
        sql.append(" ORDER BY  ");
        sql.append(" 	t1.transaction_date DESC ");
        return transactionService.page(sql.toString(), params, CreditCardLogVo.class);
    }

    private String buildSelect() {
        StringBuffer select = new StringBuffer();

        select.append(" SELECT ");
        select.append(" card_id AS cardID, ");
        select.append(" transaction_id AS transactionId, ");
        select.append(" transaction_date AS transactionTime, ");
        select.append(" card_currency AS cardCurrency, ");
        select.append(" card_transaction_amount AS cardCurrencyAmt, ");
        select.append(" transaction_currency AS transCurrency, ");
        select.append(" transaction_amount AS transCurrencyAmt, ");
        select.append(" transaction_status AS transStatus, ");
        select.append(" TRANSACTION_type AS transType, ");
        select.append(" merchant_name AS merchantName, ");
        select.append(" fee AS fee, ");
        select.append(" fee_currency AS feeCurrency, ");
        select.append(" resp_code AS respCode, ");
        select.append(" description AS respCodeDesc ");
        return select.toString();
    }

    private String buildOrder() {
        return " ORDER BY transaction_date desc ";
    }
}
