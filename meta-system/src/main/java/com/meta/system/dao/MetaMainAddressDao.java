package com.meta.system.dao;

import com.meta.system.domain.MetaMainAddress;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MetaMainAddressDao extends JpaRepository<MetaMainAddress, String>, JpaSpecificationExecutor<MetaMainAddress> {

    @Query(value = "select * from  meta_main_address  where type = ?1  and sys_id =?2", nativeQuery = true)
    MetaMainAddress getData(String type, String sysId);

    @Query(value = "select * from  meta_main_address  where type = ?1 ", nativeQuery = true)
    List<MetaMainAddress> getList(String type);

    @Query(value = "select * from  meta_main_address  where mainaddress = ?1 ", nativeQuery = true)
    MetaMainAddress findAddress(String address);
}
