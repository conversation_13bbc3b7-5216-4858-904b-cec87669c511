package com.meta.system.dao.impl;

import com.meta.common.utils.StringUtils;
import com.meta.common.utils.data.TransactionService;
import com.meta.system.dao.MetaCouponGenCfgCustom;
import com.meta.system.dto.CouponDto;
import com.meta.system.vo.MetaCouponGenCfgVo;
import org.springframework.data.domain.Page;

import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2024/10/09/14:17
 */
public class MetaCouponGenCfgCustomImpl implements MetaCouponGenCfgCustom {


    @Resource
    private TransactionService transactionService;

    @Override
    public Page<MetaCouponGenCfgVo> getCouponConfigList(CouponDto couponDto) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT ");
        sql.append(" t.id AS id, ");
        sql.append(" t.adm_user_id AS admUserId, ");
        sql.append(" t1.user_name as admUserName, ");
        sql.append(" t.coupon_type AS couponType, ");
        sql.append(" t.coupon_scenarios AS couponScenarios, ");
        sql.append(" t.coupon_card_type AS couponCardType, ");
        sql.append(" t.coupon_cardlevel AS couponCardlevel, ");
        sql.append(" t.coupon_node AS couponNode, ");
        sql.append(" t.discount_coin AS discountCoin, ");
        sql.append(" t.discount_rate AS discountRate, ");
        sql.append(" t.discount_amt AS discountAmt, ");
        sql.append(" t.max_discount_amt AS maxDiscountAmt, ");
        sql.append(" t.min_spend_amt AS minSpendAmt, ");
        sql.append(" t.eft_date AS eftDate, ");
        sql.append(" t.exp_date AS expDate, ");
        sql.append(" t.coupon_tot AS couponTot, ");
        sql.append(" t.coupon_used AS couponUsed, ");
        sql.append(" t.coupon_ava AS couponAva, ");
        sql.append(" t.coupon_usage_limit AS couponUsageLimit, ");
        sql.append(" t.create_time AS createTime ");
        sql.append(" FROM ");
        sql.append(" meta_coupon_gen_cfg t");
        sql.append(" left join sys_user t1 on t1.user_id=t.adm_user_id");
        sql.append(" WHERE ");
        sql.append(" 1 = 1 ");


        if (couponDto.getAdmUserId() != null) {
            sql.append(" AND t.adm_user_id = ? ");
            params.add(couponDto.getAdmUserId());
        }

        if (StringUtils.isNotEmpty(couponDto.getCouponType())) {
            sql.append(" AND t.coupon_type = ? ");
            params.add(couponDto.getCouponType());
        }

        if (StringUtils.isNotEmpty(couponDto.getCouponScenarios())) {
            sql.append(" AND t.coupon_scenarios = ? ");
            params.add(couponDto.getCouponScenarios());
        }
        if (StringUtils.isNotEmpty(couponDto.getCouponCardType())) {
            sql.append(" AND t.coupon_card_type LIKE ? ");
            params.add("%" + couponDto.getCouponCardType() + "%");
        }

        if (StringUtils.isNotEmpty(couponDto.getCouponCardlevel())) {
            sql.append(" AND t.coupon_cardlevel LIKE ? ");
            params.add("%" + couponDto.getCouponCardlevel() + "%");
        }

        if (StringUtils.isNotEmpty(couponDto.getCouponNode())) {
            sql.append(" AND t.coupon_node LIKE ? ");
            params.add("%" + couponDto.getCouponNode() + "%");
        }

        if (StringUtils.isNotEmpty(couponDto.getStartDate())) {
            sql.append(" and date_format(t.create_time,'%Y-%m-%d') >= ? ");
            params.add(couponDto.getStartDate());
        }

        if (StringUtils.isNotEmpty(couponDto.getEndDate())) {
            sql.append(" and date_format(t.create_time,'%Y-%m-%d') <= ? ");
            params.add(couponDto.getEndDate());
        }

        sql.append(" ORDER BY ");
        sql.append(" t.create_time DESC ");
        return transactionService.page(sql.toString(), params, MetaCouponGenCfgVo.class);

    }
}
