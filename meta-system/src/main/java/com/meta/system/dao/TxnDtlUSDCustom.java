package com.meta.system.dao;

import com.meta.system.dto.TxnDto;
import com.meta.system.vo.TradeDayTotalVo;
import com.meta.system.vo.TxnDtlUSDVo;
import org.springframework.data.domain.Page;

public interface TxnDtlUSDCustom {

    Page<TxnDtlUSDVo> page(TxnDto txnDto);

    TxnDtlUSDVo selectVoById(Long id, Long userId);

    Page<TradeDayTotalVo> tradeReportPage(String startDate, String endDate);
}
