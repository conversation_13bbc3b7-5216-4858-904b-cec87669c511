package com.meta.system.dao.app;

import com.meta.system.domain.app.AllTransaction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2024/07/13/14:42
 */
@Repository
public interface AllTransactionDao extends JpaRepository<AllTransaction, String>, JpaSpecificationExecutor<AllTransaction>,AllTransactionCustom {

}
