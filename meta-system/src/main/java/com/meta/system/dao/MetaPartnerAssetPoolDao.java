package com.meta.system.dao;

import com.meta.system.domain.groupkey.MetaPartnerAssetPoolKey;
import com.meta.system.domain.groupkey.PartnerKey;
import com.meta.system.domain.partner.MetaPartnerAssetPool;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

@Repository
public interface MetaPartnerAssetPoolDao extends JpaRepository<MetaPartnerAssetPool, PartnerKey>, JpaSpecificationExecutor<MetaPartnerAssetPool>, PartnerAssetPool {


//    @Modifying
//    @Query(value = "update meta_partner_asset_pool  set usdt_balacne = ?1 where partner_id = ?2 and coin_code=?3 ", nativeQuery = true)
//    void updatePool(BigDecimal usdYe, long userId, String coinCode);

    @Modifying
    @Query(value = "update meta_partner_asset_pool  set usdt_balacne = ?2,freeze_usdt_balacne=?3 where partner_id = ?1 ", nativeQuery = true)
    void updateBalance(long userId, BigDecimal usdtBalacne, BigDecimal freezeUstdBalacne);

    @Query(value = "select * from  meta_partner_asset_pool  where partner_id = ?1 ", nativeQuery = true)
    MetaPartnerAssetPool selectPartner(Long userId);

    @Modifying
    @Query(value = "update meta_partner_asset_pool  set api_url = ?1 where sys_id = ?2 ", nativeQuery = true)
    void updateNotifyUrl(String url, String sysId);

    @Query(value = "select * from  meta_partner_asset_pool  where sys_id = ?1 ", nativeQuery = true)
    MetaPartnerAssetPool selectPartnerBySysId(String sysId);

    @Query(value = "select * from  meta_partner_asset_pool  where sys_id = ?1 for update", nativeQuery = true)
    MetaPartnerAssetPool selectBySysIdForUpdate(String sysId);

    @Query(value = "select * from  meta_partner_asset_pool  where partner_id = ?1 for update", nativeQuery = true)
    MetaPartnerAssetPool selectByUserIdForUpdate(Long userId);

    @Query(value = "select * from  meta_partner_asset_pool  where sys_id = ?1", nativeQuery = true)
    MetaPartnerAssetPool selectPool(String sysId);
}
