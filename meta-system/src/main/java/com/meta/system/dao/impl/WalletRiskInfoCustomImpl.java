package com.meta.system.dao.impl;

import com.meta.common.exception.CustomException;
import com.meta.common.utils.data.TransactionService;
import com.meta.system.dao.WalletRiskInfoCustom;
import com.meta.system.domain.WalletRiskInfo;
import com.meta.system.vo.CollectRoportVo;
import org.springframework.data.domain.Page;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/27/17:18
 */
public class WalletRiskInfoCustomImpl implements WalletRiskInfoCustom {
    @Resource
    private TransactionService transactionService;


    @Override
    public List<WalletRiskInfo> addressForlist(String trc, String address, int timestamp) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT ");


        sql.append(" t1.fromaddress AS fromaddress, ");
        sql.append(" t2.risk_level AS riskLevel, ");
        sql.append(" t2.risk_score AS riskScore ");
        sql.append("         FROM ");
        sql.append(" (SELECT fromaddress ");

        if("TRC".equals(trc)){
            sql.append("  FROM meta_trc20_transactions ");
        }else if("BEP".equals(trc)){
            sql.append("  FROM meta_bep20_transactions ");
        }else if("ARB".equals(trc)){
            sql.append("  FROM meta_arb_transactions ");
        }else if ("BASE".equals(trc)){
            sql.append("  FROM meta_base_transactions ");
        }else{
            throw new CustomException("不支持的链类型");
        }

        sql.append("  WHERE type = 'receive' ");
        sql.append("  AND issync = '1' ");
        sql.append("  AND address = ? ");
        params.add(address);
        if (timestamp > 0) {
            sql.append(" and TIMESTAMP >= ? ");
            params.add(timestamp);
        }
        sql.append(" GROUP BY  fromaddress ) t1 ");
        sql.append(" LEFT JOIN meta_wallet_risk_info t2 ");
        sql.append(" ON t1.fromaddress=t2.wallet_address ");



        return transactionService.list(sql.toString(), params, WalletRiskInfo.class);
    }
}
