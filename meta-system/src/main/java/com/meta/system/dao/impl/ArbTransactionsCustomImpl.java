package com.meta.system.dao.impl;

import com.meta.common.utils.StringUtils;
import com.meta.common.utils.data.TransactionService;
import com.meta.system.dao.ArbTransactionsCustom;
import com.meta.system.dto.RechargeDto;
import com.meta.system.vo.CollectRoportVo;
import com.meta.system.vo.RechargeDataVo;
import org.springframework.data.domain.Page;

import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2023/11/24/11:47
 */
public class ArbTransactionsCustomImpl implements ArbTransactionsCustom {

    @Resource
    private TransactionService transactionService;

    @Override
    public Page<CollectRoportVo> getReportPage(long startStamp, long endStamp) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT ");

        sql.append(" DATE_FORMAT( FROM_UNIXTIME( t.TIMESTAMP ), '%Y%m%d' ) AS date, ");
        sql.append(" count( 1 ) AS count, ");
        sql.append(" sum( t.amount ) AS amount, ");
        sql.append(" sum( t.fee ) AS arbEthFee ");
        sql.append(" FROM ");
        sql.append(" meta_arb_transactions t ");
        sql.append(" WHERE ");
        sql.append(" t.type = 'collect' ");
        if (startStamp > 0) {
            sql.append(" and t.TIMESTAMP >= ? ");
            params.add(startStamp);
        }

        if (endStamp > 0) {
            sql.append(" and t.TIMESTAMP < ? ");
            params.add(endStamp);
        }
        sql.append(" GROUP BY ");
        sql.append(" 1 ");
        sql.append(" ORDER BY ");
        sql.append(" t.TIMESTAMP DESC ");

        return transactionService.page(sql.toString(), params, CollectRoportVo.class);
    }


    @Override
    public Page<RechargeDataVo> page(RechargeDto dto) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        StringBuilder trcSql = new StringBuilder();
        StringBuilder bepSql = new StringBuilder();
        if ("TRC20".equals(dto.getCoinNet())) {
            createTrc(dto, trcSql, params);
            sql = trcSql;
        } else if ("BEP20".equals(dto.getCoinNet())) {
            createBep(dto, bepSql, params);
            sql = bepSql;
        }
        return transactionService.page(sql.toString(), params, RechargeDataVo.class);
    }

    private void createBep(RechargeDto dto, StringBuilder bepSql, ArrayList<Object> params) {

        bepSql.append("  SELECT ");
        bepSql.append("  'BEP20' AS coinNet, ");
        bepSql.append(" 'USDT' as txnCoin, ");
        bepSql.append("  t1.id, ");
        bepSql.append("          t1.txid, ");
        bepSql.append("          t1.blockheight, ");
        bepSql.append("          t1.address, ");
        bepSql.append("          t1.fromaddress, ");
        bepSql.append("          t1.contract, ");
        bepSql.append("          t1.amount, ");
        bepSql.append("          t1.fee, ");
        bepSql.append("          DATE_FORMAT( FROM_UNIXTIME( t1.TIMESTAMP ), '%Y-%m-%d %H:%i:%s' ) AS date, ");
        bepSql.append("  t1.type, ");
        bepSql.append("          t1.issync, ");
        bepSql.append("          t3.user_name as userName , ");
        bepSql.append("          t3.nick_name  as nickName");
        bepSql.append("  FROM ");
        bepSql.append("  meta_arb_transactions t1 ");
        bepSql.append("  LEFT JOIN meta_bep20_cstaddressinfo t2 ON t1.address = t2.cst_address ");
        bepSql.append("  LEFT JOIN sys_user t3 ON t2.cst_id = t3.user_id");
        bepSql.append("  WHERE");
        bepSql.append("  t1.type = 'receive' and t1.issync in ('1','0') and  t1.amount>=1  ");
        if (StringUtils.isNotEmpty(dto.getTxid())) {
            bepSql.append("  and  t1.txid = ?");
            params.add(dto.getTxid());
        }

        if (StringUtils.isNotEmpty(dto.getNickName())) {
            bepSql.append(" and t3.nick_name = ?");
            params.add(dto.getNickName());
        }

        if (StringUtils.isNotEmpty(dto.getUserName())) {
            bepSql.append(" and  t3.user_name = ?");
            params.add(dto.getUserName());
        }
        if (StringUtils.isNotEmpty(dto.getUserName())) {
            bepSql.append("  and t3.user_name = ?");
            params.add(dto.getUserName());
        }

        if (StringUtils.isNotEmpty(dto.getTxnStatus())) {
            bepSql.append(" and t1.issync = ?");
            params.add(dto.getTxnStatus());
        }


        if (StringUtils.isNotEmpty(dto.getStartDate())) {
            bepSql.append(" and DATE_FORMAT( FROM_UNIXTIME( t1.TIMESTAMP ), '%Y-%m-%d' ) >= ?");
            params.add(dto.getStartDate());
        }

        if (StringUtils.isNotEmpty(dto.getEndDate())) {
            bepSql.append(" and DATE_FORMAT( FROM_UNIXTIME( t1.TIMESTAMP ), '%Y-%m-%d' ) <= ?");
            params.add(dto.getEndDate());
        }

        bepSql.append("   order by   t1.TIMESTAMP desc");

    }

    private void createTrc(RechargeDto dto, StringBuilder trcSql, ArrayList<Object> params) {
        trcSql.append("  SELECT ");
        trcSql.append("  'TRC20' AS coinNet, ");
        trcSql.append(" 'USDT' as txnCoin, ");
        trcSql.append("  t1.id, ");
        trcSql.append("          t1.txid, ");
        trcSql.append("          t1.blockheight, ");
        trcSql.append("          t1.address, ");
        trcSql.append("          t1.fromaddress, ");
        trcSql.append("          t1.contract, ");
        trcSql.append("          t1.amount, ");
        trcSql.append("          t1.fee, ");
        trcSql.append("          DATE_FORMAT( FROM_UNIXTIME( t1.TIMESTAMP ), '%Y-%m-%d %H:%i:%s' ) AS date, ");
        trcSql.append("  t1.type, ");
        trcSql.append("          t1.issync, ");
        trcSql.append("          t3.user_name as userName , ");
        trcSql.append("          t3.nick_name  as nickName");
        trcSql.append("  FROM ");
        trcSql.append("  meta_trc20_transactions t1 ");
        trcSql.append("  LEFT JOIN meta_trc20_cstaddressinfo t2 ON t1.address = t2.cst_address ");
        trcSql.append("  LEFT JOIN sys_user t3 ON t2.cst_id = t3.user_id");
        trcSql.append("  WHERE");
        trcSql.append("  t1.type = 'receive' and t1.issync in ('1','0') and  t1.amount>=1 ");
        if (StringUtils.isNotEmpty(dto.getTxid())) {
            trcSql.append(" and t1.txid = ?");
            params.add(dto.getTxid());
        }

        if (StringUtils.isNotEmpty(dto.getNickName())) {
            trcSql.append(" and  t3.nick_name = ?");
            params.add(dto.getNickName());
        }

        if (StringUtils.isNotEmpty(dto.getUserName())) {
            trcSql.append(" and t3.user_name = ?");
            params.add(dto.getUserName());
        }
        if (StringUtils.isNotEmpty(dto.getUserName())) {
            trcSql.append(" and  t3.user_name = ?");
            params.add(dto.getUserName());
        }

        if (StringUtils.isNotEmpty(dto.getTxnStatus())) {
            trcSql.append(" and  t1.issync = ?");
            params.add(dto.getTxnStatus());
        }


        if (StringUtils.isNotEmpty(dto.getStartDate())) {
            trcSql.append("  and DATE_FORMAT( FROM_UNIXTIME( t1.TIMESTAMP ), '%Y-%m-%d' ) >= ?");
            params.add(dto.getStartDate());
        }

        if (StringUtils.isNotEmpty(dto.getEndDate())) {
            trcSql.append(" and DATE_FORMAT( FROM_UNIXTIME( t1.TIMESTAMP ), '%Y-%m-%d' ) <= ?");
            params.add(dto.getEndDate());
        }
        trcSql.append("   order by   t1.TIMESTAMP desc");
    }
}

