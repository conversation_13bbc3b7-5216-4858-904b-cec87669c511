package com.meta.system.dao;


import com.meta.system.domain.app.BepCstaddress;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BepCstaddressDao extends JpaRepository<BepCstaddress, Long> {
    @Query(value = " select * from meta_bep20_cstaddressinfo where cst_id=?1 and sys_id=?2",nativeQuery = true)
    List<BepCstaddress> findByCstId(Long cstId,String sysId);

    @Query(value = " select * from meta_bep20_cstaddressinfo where cst_address=?1 ",nativeQuery = true)
    BepCstaddress findByAddress(String  from);

    @Modifying
    @Query(value = " update meta_bep20_cstaddressinfo set cst_id=?2 where id=?1 ",nativeQuery = true)
    void updateCstId(Long id, Long cstId);

    @Query(value = " select * from meta_bep20_cstaddressinfo where sys_id=?1 ",nativeQuery = true)
    List<BepCstaddress> findBySysId(String sysId);
}
