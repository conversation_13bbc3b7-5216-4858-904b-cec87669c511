package com.meta.system.dao;

import com.meta.system.domain.partner.MetaPushData;
import com.meta.system.dto.NotifyDto;
import com.meta.system.vo.MetaPushDataVo;
import org.springframework.data.domain.Page;

/**
 * <AUTHOR>
 * @Date 2024/11/23
 */


public interface MetaPushDataDaoCustom {
    Page<MetaPushDataVo> getNotifyList(NotifyDto notifyDto);

//    void updateStatue(String data, String statue);
}
