package com.meta.system.dao.app;

import com.meta.system.domain.app.MetaUcardCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface MetaUcardCodeDao extends JpaRepository<MetaUcardCode, Long>, JpaSpecificationExecutor<MetaUcardCode> {

    @Query(value = "select * from  meta_ucard_code where card_no = ?1 order by create_time desc limit 1 ", nativeQuery = true)
    MetaUcardCode findByNo(String cardNo);

    @Query(value = "select * from  meta_ucard_code where card_id = ?1 order by create_time desc limit 1 ", nativeQuery = true)
    MetaUcardCode fingByTemporaryCardID(String cardID);
}
