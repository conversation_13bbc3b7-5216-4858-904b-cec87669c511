package com.meta.system.dao;

import com.meta.common.core.domain.entity.SysUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 用户表 数据层
 *
 * <AUTHOR>
 * @since 1.0  2020-12-11
 */
@Repository
public interface SysUserDao extends JpaRepository<SysUser, Long>, JpaSpecificationExecutor<SysUser>, SysUserDaoCustom {

    Optional<SysUser> findByUserName(String userName);

    Optional<SysUser> findByPhonenumber(String phonenumber);

    Optional<SysUser> findByEmail(String email);

    @Modifying
    @Query(value = " update sys_user set avatar = ?1 where user_name = ?2 ", nativeQuery = true)
    Integer updateUserAvatar(String userName, String avatar);

    Integer deleteByUserIdIn(List<Long> userIds);

    @Modifying
    @Query(value = " update sys_user set google_secret_key = ?2 where user_id = ?1 ",nativeQuery = true)
    void saveGoogleSecretKey(Long userId, String secretKey);

    @Modifying
    @Query(value = " update sys_user set login_ip = ?2,location = ?3,device = ?4 ,os = ?5,login_date = now() where user_id = ?1 ",nativeQuery = true)
    void updateLoginInfo(Long userId,String loginIp, String location, String device, String os);

    @Modifying
    @Query(value = " update sys_user set login_ip = ?2,login_date = now() where user_id = ?1 ",nativeQuery = true)
    void updateLoginInfo(Long userId,String loginIp);

    SysUser findByInviteCode(String inviteCode);

    @Modifying
    @Query(value = " update sys_user set nick_name = ?2 where user_id = ?1 ",nativeQuery = true)
    void updateNickname(Long userId,String nickname);

    @Modifying
    @Query(value = " select user_name from sys_user where user_id in (?1) ",nativeQuery = true)
    List<String> findByIds(List<Long> userIds);

    @Query(value = " update sys_user set trade_limit_expired = ?2 where user_id = ?1 ",nativeQuery = true)
    @Modifying
    void updateTradeLimitExpired(Long userId, Date date);

    @Query(value = " update sys_user set login_limit_expired = ?2 where user_name = ?1 ",nativeQuery = true)
    @Modifying
    void updateLoginLimitExpired(String username, Date date);

    @Query(value = " update sys_user set login_limit_expired = ?2 where user_id = ?1 ",nativeQuery = true)
    @Modifying
    void updateLoginLimitExpired(Long userId, Date date);

    @Modifying
    @Query(value = " update sys_user set google_secret_key = '' where user_id = ?1 ",nativeQuery = true)
    void delGoogleSecretKey(Long userId);

    @Query(value = "SELECT METAUUId() ",nativeQuery = true)
    String  selectUid();

    @Query(value = " select * from sys_user where uuid =?1 ",nativeQuery = true)
    SysUser selectUserByUuid(String uuid);

    @Modifying
    @Query(value = " update sys_user set language = ?2 where user_id = ?1 ",nativeQuery = true)
    void updateLanguage(Long userId, String language);

    @Query(value = "SELECT * FROM sys_user t1 INNER JOIN( SELECT * FROM sys_user_role WHERE role_id IN ( SELECT role_id FROM sys_role_menu WHERE menu_id IN ( SELECT menu_id FROM sys_menu WHERE perms = ?1))) t2 ON t1.user_id = t2.user_id ",nativeQuery = true)
    List<SysUser> admUserSelectList(String menustr);
}
