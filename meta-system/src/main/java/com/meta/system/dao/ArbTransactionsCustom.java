package com.meta.system.dao;

import com.meta.system.dto.RechargeDto;
import com.meta.system.vo.CollectRoportVo;
import com.meta.system.vo.RechargeDataVo;
import org.springframework.data.domain.Page;

/**
 * <AUTHOR>
 * @date 2023/11/24/11:46
 */
public interface ArbTransactionsCustom {
    Page<CollectRoportVo> getReportPage(long startTimes, long endTimes);

    Page<RechargeDataVo> page(RechargeDto dto);
}
