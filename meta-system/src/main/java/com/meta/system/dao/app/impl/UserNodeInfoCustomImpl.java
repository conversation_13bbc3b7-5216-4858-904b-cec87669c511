package com.meta.system.dao.app.impl;

import com.meta.common.utils.StringUtils;
import com.meta.common.utils.data.TransactionService;
import com.meta.system.dao.app.UserNodeInfoCustom;
import com.meta.system.vo.PartnerNodeInfoVo;
import com.meta.system.vo.UserInfoVo;
import com.meta.system.vo.UserNodeInfoVo;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
public class UserNodeInfoCustomImpl implements UserNodeInfoCustom {
    @Resource
    private TransactionService transactionService;


    @Override
    public Page<UserNodeInfoVo> getChildNodeInfos(Long userId, String email) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ");
        sql.append(" su.user_id AS userId, ");
        sql.append(" su.nick_name AS nickname, ");
        sql.append(" su.email AS email, ");
        sql.append(" nc.node_level AS nodeLevel, ");
        sql.append(" ifnull(inf.card_open_rebate,nc.card_open_rebate) AS cardOpenRebate, ");
        sql.append(" ifnull(inf.card_recharge_rebate ,nc.card_recharge_rebate) AS cardRechargeRebate, ");
        sql.append(" ifnull(inf.recommend_rebate ,nc.recommend_rebate) AS recommendRebate, ");
        sql.append(" ifnull(concat(inf.is_Valid,'') ,'Y') AS isValid, ");
        sql.append(" ifnull(inf.remark ,'') AS remark, ");
        sql.append(" case when inf.user_id is not null then '1' else '0' end AS `exists` ");
        sql.append(" FROM ");
        sql.append(" meta_user_info mui ");
        sql.append(" INNER JOIN sys_user su ON su.user_id = mui.user_id ");
        sql.append(" left join meta_user_node_info inf on inf.user_id = mui.user_id ");
        sql.append(" INNER JOIN meta_node_config nc ON nc.node_level = '04' ");
        sql.append(" WHERE ");
        sql.append(" mui.referrer_id = ? ");
        sql.append(" AND mui.node_level = '04' ");
        params.add(userId);
        if (StringUtils.isNotEmpty(email)){
            sql.append(" and su.email = ? ");
            params.add(email);
        }
        sql.append(" order by case when inf.create_time is null then 0 else 1 end desc, inf.create_time desc ");
        return transactionService.page(sql.toString(), params, UserNodeInfoVo.class);
    }

    /**
     * 查询未自定义返佣的用户数据
     *
     * @param userId 合伙人id
     * @param email
     * @return 分页数据
     */
    @Override
    public Page<UserInfoVo> getNotCustomChildUsers(Long userId, String email) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select  ");
        sql.append(" su.user_id as userId, ");
        sql.append(" su.nick_name as nickname, ");
        sql.append(" su.email as email ");
        sql.append(" from  ");
        sql.append(" meta_user_info mui  ");
        sql.append(" inner join sys_user su on su.user_id = mui.user_id  ");
        sql.append(" where mui.referrer_id = ? and mui.node_level = '04' ");
        if (StringUtils.isNotEmpty(email)){
            sql.append(" and su.email like ? ");
            params.add("%"+email+"%");
        }
        params.add(userId);
        return transactionService.page(sql.toString(), params, UserInfoVo.class);
    }

    @Override
    public List<UserInfoVo> getAllChildNodeInfos(Long userId, String email) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select  ");
        sql.append(" su.user_id as userId, ");
        sql.append(" su.nick_name as nickname, ");
        sql.append(" su.email as email ");
        sql.append(" from  ");
        sql.append(" meta_user_info mui  ");
        sql.append(" inner join sys_user su on su.user_id = mui.user_id  ");
        sql.append(" where mui.referrer_id = ? and mui.node_level = '04' ");
        if (StringUtils.isNotEmpty(email)){
            sql.append(" and su.email like ? ");
            params.add("%"+email+"%");
        }
        params.add(userId);
        return transactionService.list(sql.toString(), params, UserInfoVo.class);
    }

    @Override
    public Page<PartnerNodeInfoVo> getChildInfos(Long userId, String uuid) {
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT ");
        sql.append(" t1.partner_id  as partnerId, ");
        sql.append("         t2.child_id as childId, ");
        sql.append("         t4.uuid as uuid, ");
        sql.append("         t4.email as email, ");
        sql.append("         t4.user_name as userName, ");
        sql.append("         t3.node_level  as nodeLevel, ");
        sql.append("         t3.silver_active_code_num as silverActiveCodeNum, ");
        sql.append("         t3.golden_active_code_num  as goldenActiveCodeNum, ");
        sql.append("         t5.silverNum  as silverTransferredNum, ");
        sql.append("         t5.goldenNum  as goldenTransferredNum");

        sql.append(" FROM ");
        sql.append(" meta_partner_asset_pool t1 ");
        sql.append(" LEFT JOIN meta_user_node_mapping t2 ON t1.partner_id = t2.user_id ");
        sql.append(" LEFT JOIN meta_user_info t3 ON t2.child_id = t3.user_id ");
        sql.append(" LEFT JOIN sys_user t4 ON t3.user_id = t4.user_id ");

        sql.append(" left join (  ");
        sql.append("          SELECT sum(case when code_type='01' then -txn_num end) as silverNum, sum(case when code_type='02' then -txn_num end ) as goldenNum, from_userid, to_userid " +
                   "          FROM meta_txn_dtl_code WHERE user_id = ? AND txn_type = '31' GROUP BY from_userid, to_userid ");
        params.add(userId);
        sql.append(" ) t5 on t2.child_id = t5.to_userid  ");

        sql.append(" WHERE ");
        sql.append(" t1.partner_id = ? ");
        params.add(userId);
        sql.append("  and t2.child_id != ?");
        params.add(userId);
        if (StringUtils.isNotEmpty(uuid)){
            sql.append(" and t4.uuid  like ? ");
            params.add("%"+uuid+"%");
        }

        sql.append(" and t4.uuid is not null ");
        sql.append(" order by t2.child_id asc ");

        return transactionService.page(sql.toString(), params, PartnerNodeInfoVo.class);
    }
}
