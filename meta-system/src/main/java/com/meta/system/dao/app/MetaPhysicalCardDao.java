package com.meta.system.dao.app;

/**
 * <AUTHOR>
 * @date 2025/03/15/9:44
 */

import com.meta.system.domain.app.MetaPhysicalCard;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface MetaPhysicalCardDao extends JpaRepository<MetaPhysicalCard, Long> {
    Optional<MetaPhysicalCard> findByCardNo(String cardNo);


    @Query("SELECT p FROM MetaPhysicalCard p  WHERE  " +
            "(:sysId IS NULL OR p.sysId = :sysId) AND " +
            "(:cardNo IS NULL OR p.cardNo = :cardNo) AND " +
            "(:lastNo IS NULL OR p.lastNo = :lastNo)")
    Page<MetaPhysicalCard> findBySearchParams(
            @Param("sysId") String sysId,
            @Param("cardNo") String cardNo,
            @Param("lastNo") String lastNo,
            Pageable pageable);

    @Query(value = "select * from  meta_physical_card_list where card_no = ?1 and sys_id=?2 ", nativeQuery = true)
    MetaPhysicalCard findByCardNoAndSysId(String cardNo, String sysId);

    @Modifying
    @Query(value = "update meta_physical_card_list set is_active = 'Y' where card_no = ?1", nativeQuery = true)
    void updateActive(String cardNo);
}
