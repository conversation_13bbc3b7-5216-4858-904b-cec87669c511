package com.meta.system.dao.app;

import com.meta.system.domain.app.MetaFxUserInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface MetaFxUserInfoDao extends JpaRepository<MetaFxUserInfo, Long>, JpaSpecificationExecutor<MetaFxUserInfo> {
    @Query(value = "select * from meta_fornax_user_info where email = ?1" , nativeQuery = true)
    MetaFxUserInfo findByEmail(String email);
}
