package com.meta.system.dao;

import com.meta.system.domain.TrcTransactions;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/23/17:21
 */
@Repository
public interface TrcTransactionsDao extends JpaRepository<TrcTransactions, Long>, TrcTransactionsCustom {


    @Query(value = "select * from meta_trc20_transactions where type = 'collect' and  fromaddress=?1 order by `timestamp` desc limit 1 ", nativeQuery = true)
    TrcTransactions findTime(String walletAddress);

    @Query(value = "SELECT fromaddress FROM meta_trc20_transactions WHERE type = 'receive' AND issync = '1' and address=?1 and `timestamp`>?2 group by fromaddress", nativeQuery = true)
    List<String> getList(String walletAddress, int timestamp);

    @Query(value = "SELECT fromaddress FROM meta_trc20_transactions WHERE type = 'receive' AND issync = '1' and address=?1 group by fromaddress ", nativeQuery = true)
    List<String> getList(String walletAddress);


    @Query(value = "SELECT fromaddress FROM meta_trc20_transactions WHERE type = 'receive' AND issync = '0' and address=?1 ", nativeQuery = true)
    List<TrcTransactions> getUnprocessedListByAddress(String walletAddress);

    @Modifying
    @Query(value = "update meta_trc20_transactions set issync=1 where address=?1 and type='receive' and issync = '0' ", nativeQuery = true)
    void updateTransactionsS(String address);

//    @Modifying
//    @Query(value = "update meta_trc20_transactions set issync=1 where address=?1 and type='receive' and issync = '0' and blockheight < ?2 ", nativeQuery = true)
//    void updateTransactionsS(String address, int blockHeight);

    @Modifying
    @Query(value = "update meta_trc20_transactions set issync=?1 where txid=?2 ", nativeQuery = true)
    void updateTransactions(int isSync, String txid);

    @Query(value = " select * from meta_trc20_transactions where txid=?1 ", nativeQuery = true)
    TrcTransactions findByTxid(String txid);
}
