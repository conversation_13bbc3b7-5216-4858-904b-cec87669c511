package com.meta.system.dao.app;

import com.meta.system.domain.app.MetaArbitrumAddress;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/05/10/15:26
 */
@Repository
public interface MetaArbitrumAddressDao extends JpaRepository<MetaArbitrumAddress, Long>, JpaSpecificationExecutor<MetaArbitrumAddress> {
    @Query(value = "select * from  meta_arbitrum_address where exists_nft = 'Y' ", nativeQuery = true)
    List<MetaArbitrumAddress> getFiatNFT();


    @Modifying
    @Query(value = "update  meta_arbitrum_address set exists_nft = 'N' where address = ?1 ", nativeQuery = true)
    void updateNft(String address);


}
