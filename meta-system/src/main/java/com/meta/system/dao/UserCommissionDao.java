package com.meta.system.dao;

import com.meta.system.domain.app.UserCommission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface UserCommissionDao extends JpaRepository<UserCommission, Long>, UserCommissionCustom {
    @Query(value = "select count(1) from meta_user_node_mapping t1 where (t1.user_id=?1 and t1.level_no=1);", nativeQuery = true)
    Integer getCommissionCount(Long userId);
}
