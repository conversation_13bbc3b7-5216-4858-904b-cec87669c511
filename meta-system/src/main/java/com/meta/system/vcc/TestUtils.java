package com.meta.system.vcc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meta.common.utils.uuid.UniqueIDGenerator;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.common.utils.vcc.DESUtils;
import com.meta.common.utils.vcc.RSAUtils;
import com.meta.common.utils.vcc.StringUtil;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.HashMap;
import java.util.Map;

@Component
public class TestUtils {
    private static final Logger log = LoggerFactory.getLogger(TestUtils.class);
    private static String hexJsonEnc;
    private static String hexKeyEnc;
    private static String hexSign;

    private static  String urlPrefix = "http://127.0.0.1:8890/meta/api/card";
    //test服务器的私钥
    private static String priKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC9E7fupXdmwZx5fGQZgj+FczgCEI9u6LyFxY/RFrkYXHojY7qPgyjaznqCrN3xfGolWO7iq9EbpoRetR5/ovLG8eWlMfWVkYfKZeQ6pfv1eVl+9p4O0rlBpVGVNzbDtLTuwX7Xw0RxFFJyZVR2EQzo0cb8XyZG3Z7XxDPMnOzqGIPVcHp0nL8vE8IPFyrg1z1AYoWUy90B7Kx05h/nNX7jtjNrVnpxHNpmK8kx5DQXrEjZZPVrunXzHRXg2vGkpb6DkRPHUCfuMpFTTL5Oge8O9sjM71tJeGPSmb++k8KL/6j97S4/a69PUH3ZldIMvgsKuzMYCY0O7Pwwrf5B6iTrAgMBAAECggEALlM7eHwQAhwjs1w3xkw0NgUhztex3NGnBvt9nhP8K6zUvAD+P5U6GEoImCW0hysdcqMUfHLuW+Dzg6TKoSkSZI313wCblBbA92T5gykRz3X46HOSDD2y6BOSJoYNo+uNfQXphwGvrij1flO3WuoYiJ6FK2ZAoZJBDcpjiplULpKBOih/MhvQ6CyN3fIyjvaKWJnFNnb5k+lG9i8ff95tuJVBey1OPFNuJ/TvDysAJkzUV9pl8MItlVdYjLLbWrBnVLojpdOI2/OcKjp/TJk+Y+3pw4R1kAH29LvH5nSO8kaaPu186BbSQOLl9/Dw43FwtPudWoePbucZCyQI/TTmqQKBgQDhyQ+e6gnCIvRSwgqBFUduZ5Pk/PWRA7NTKxTF8gRA9vsPju5vll3H3NXJdEwHA7MNlLXgWDsur/dwa9LUkdWPkrzPZiPZK8p5LcudVmpLgvoDzg9paSflA9tKVFyS2BRWoT91BZ+0k8lYkiuZ/iOfC4IF7UZocIaEpEBvQKOl/QKBgQDWYRrcfP2JYSwkDplSdKty1iB3fN/ISb/Q1s1azYrx5DOnWoVCfHauveY0eqXh1lZksZyr1SQ0kY6N6IihgUWGZy8bAezsKKy+b8oHVLa0UPoMUZGRF/KjZu0RBEyfoHGec1J7tSttCLTff4U+06fsYX37X8ZZJOuoSHoOVuh3BwKBgQDH9B6QVqWTtw72p39T97tNzA7O0TLMXSGXeuSntIAN5GxMyADi86BT2n++K+8UmzMbyOIVLy4iV5XjiqmotQoTXxk09ziyIDTsgiD7UsdJ3lF5wygk6wp4p6Sxu+pL5W6FlcGz6eoYqnS6qqBQfR+gvzlD9HDRFy4aE7g4jl8fJQKBgHxaAVnKSsroidE0gq04rcbD/DszPR+R3+kE4EN+nM0pIOk/cbMaPUGpN6JTTmuMh7qK6CUoUoMHt5gLPU3pjWmj6sgQutxVz7X7ZEYSVpSLsC97FSQDryQnbPYE92lUiPNvU3Ycpd7uQPSvpJvH7E0KVCh+6rAE4YlQ2TP+J4P7AoGBANd0ZmRORBn7tXJZpRwJPrliKwyFj4dGIyWCgaUiVYwWSYg3TkHm4LU+zMLcUfF1Cu7qBPhFM/zdhFEnaffqAv4WH9P/BKVba9BhL6z6nhWEI7be2waFUSDNhLkJhxmot0NUpdlsSau+x60HGuaOY1weEZf6bLarn4TI1Le+vxtI";
    //我对外发布的公钥
    private static String pubKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt7Bct5InySYB0lyYeMkwJtCnF/q0eHNQ2S7UiquPb4VA8SHvu52RQO37RFViPpqmWp7qtke3QSN3VizZlT/oBKifWW4p1mDTK7uRVSNWOnBkEdWijpGXcD1m6dvCAKoiyPSM+C696viETdTrfx7RanLcfWoKbe/TQliIMzUkdVG2o/gXboI/fw83cG9vAhh7bjB3ONuAd/Dr1LYoJNkhroBWj40yDC49Sw8xVlZGBO8dtDrnm+1EEyvju1iIIQr1me8ucZs5uPFQheeGhlgjeV12s1eDIgp3kdAQN/mu9sGADeYkiDgTRMqCnAl2GFUOdaJsacjWwflxCSzOvwPeSQIDAQAB";

    public static void main(String[] args) throws Exception {
        JSONObject req = new JSONObject();
        req.put("cardno", "****************");
        req.put("cvv","118");
        req.put("expdate","2602");
        req.put("userid","10000017");
        String requestNo = UniqueIDGenerator.generateID();
        TestUtils yj = new TestUtils();
        yj.encryptAndSign(req, "authorize",requestNo);
        yj.doPost(requestNo,"authorize");

    }

    /**
     *
     * @param requsetNo
     * @param apiCode
     */
    private void card(String requsetNo , String apiCode){
        JSONObject bodyRequestParam = getRequestParam(apiCode);
        try {
            encryptAndSign(bodyRequestParam, apiCode, requsetNo);
            doPost(requsetNo, apiCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private JSONObject getRequestParam(String  apiCode) {
        JSONObject req = new JSONObject();
        switch (apiCode) {
            case "authorize":
                req.put("cardno", "****************");
                req.put("cvv","118");
                req.put("expdate","2602");
                req.put("userid","10000017");
                break;
            case "balance":
                req.put("cardno", "****************");
                req.put("currency","USD");
                req.put("secret","118");
                break;
            case "recharge":
                req.put("cardno", "****************");
                req.put("currency","USD");
                req.put("amount","23");
                req.put("secret","118");
                break;
            case "derate":
                req.put("cardno", "****************");
                req.put("currency","USD");
                req.put("damount","21");
                req.put("secret","118");
                break;
            default:
                break;
        }
        return req;
    }

    private void doPost(String requestNo, String apiCode) throws Exception {
        JSONObject req = new JSONObject();

        JSONObject requestHead = new JSONObject();
        requestHead.put("sysId", "123456");
        requestHead.put("apiCode", apiCode);
        requestHead.put("version", "1.0");
        requestHead.put("requestNo", requestNo);
        requestHead.put("sign", hexSign);
        requestHead.put("keyEnc", hexKeyEnc);
        req.put("head", requestHead);

        JSONObject requestBody = new JSONObject();
        requestBody.put("encrypt", hexJsonEnc);
        req.put("body", requestBody);

        log.debug("请求参数："+req.toJSONString());

        Map headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        HttpResponse resultRep  = HttpUtils.doPost(urlPrefix+"/"+apiCode, "", "POST", headers, null, req.toJSONString());
        if (resultRep.getStatusLine().getStatusCode() == 200) {
            /**读取服务器返回过来的json字符串数据**/
            String str = EntityUtils.toString(resultRep.getEntity());
            System.out.println(str);
            /**把json字符串转换成json对象**/
            JSONObject jsonResult = JSONObject.parseObject(str);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(jsonResult.toJSONString());
            String code =  rootNode.get("head").get("code").asText();
            String detail =  rootNode.get("head").get("detail").asText();
            String result = decryptAndVeriSign(jsonResult);
            System.out.println(result);
        }else{
            log.debug("网络波动,接口调用失败");
        }

    }

    /**
     * 加密加签
     *
     * @param jsonObject
     *
     * @throws Exception
     */
    private void encryptAndSign(JSONObject jsonObject, String apiCode, String requestNo) throws Exception {
        String key  = AESUtils.generateAesKey();
        String data = JSON.toJSONString(jsonObject);
        log.debug("明文:" + data);
        log.debug("密钥:key:" + key);
        try {
            hexJsonEnc = AESUtils.aesEncrypt(key, data);
            log.debug("明文加密结果encrypt:hexJsonEnc:" + hexJsonEnc);

            RSAUtils rsaUtils   = new RSAUtils();
            //测试服务器的私钥
            PrivateKey privatekey = rsaUtils.getPrivateKey(priKey);
            //我对外公布的公钥
            PublicKey  publicKey  = rsaUtils.getPublicKey(pubKey);
            rsaUtils.initKey(privatekey, publicKey);

            // 密钥加密
            byte[] bkey = rsaUtils.encryptRSA(key.getBytes(), false, "utf-8");
            hexKeyEnc = DESUtils.toHexString(bkey);
            log.debug("密钥加密结果:keyEnc:hexKeyEnc:" + hexKeyEnc);
            // 签名
            String signStr = "123456" + "|" + apiCode + "|" + "1.0" + "|" + requestNo + "|" + hexJsonEnc;
            byte[] sign    = rsaUtils.signRSA(signStr.getBytes("utf-8"), false, "utf-8");
            hexSign = DESUtils.toHexString(sign);
            log.debug("签名结果:sign:hexSign:" + hexSign);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 解密验签
     *
     * @throws Exception
     */
    private static String decryptAndVeriSign(JSONObject jsonResult) throws Exception {
        if(jsonResult ==  null || jsonResult.isEmpty())
                return null;
        JSONObject responseHead = (JSONObject) jsonResult.get("head");
        String     apiCode      = responseHead.getString("apiCode");
        String     requestNo    = responseHead.getString("requestNo");
        String     code         = responseHead.getString("code");
        String     detail       = responseHead.getString("detail");
        String     hexKeyEnc    = responseHead.getString("keyEnc");
        String     hexSign      = responseHead.getString("sign");
        String     sysId      = responseHead.getString("sysId");
        String     hexJsonEnc   = null;

        JSONObject responseBody = (JSONObject) jsonResult.get("body");
        if (responseBody != null) {
            hexJsonEnc = responseBody.getString("encrypt");
        }
        RSAUtils   rsaUtils   = new RSAUtils();
        PrivateKey privatekey = rsaUtils.getPrivateKey(priKey);
        PublicKey  publicKey  = rsaUtils.getPublicKey(pubKey);
        rsaUtils.initKey(privatekey, publicKey);
        if(!StringUtil.isBlank(hexKeyEnc)){
            byte[] bkey = rsaUtils.decryptRSA(DESUtils.convertHexString(hexKeyEnc), false, "utf-8");
            String key  = new String(bkey);
            log.debug("密钥解密结果:" + key);
            String json = StringUtil.isBlank(hexJsonEnc) ? hexJsonEnc: AESUtils.aesDecrypt(key, hexJsonEnc);
            log.debug("明文解密结果:" + json);
            String signStr = sysId + "|" + apiCode + "|" + "1.0" + "|" + requestNo + "|" + code + "|" + detail;
            signStr = signStr + "|" + hexJsonEnc;
            byte[]  bsign = DESUtils.convertHexString(hexSign);
            boolean ret   = rsaUtils.verifyRSA(signStr.getBytes("utf-8"), bsign, false, "utf-8");
            log.debug("验签结果:" + ret);
            if(ret)
                return json;
        }
        return null;
    }



}
