package com.meta.system.vcc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meta.common.enums.vcc.ApiCode;
import com.meta.common.utils.JsonUtils;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.sign.Md5Utils;
import com.meta.common.utils.uuid.UniqueIDGenerator;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.common.utils.vcc.DESUtils;
import com.meta.common.utils.vcc.RSAUtils;
import com.meta.common.utils.vcc.StringUtil;
import com.meta.system.config.VccConfig;
import com.meta.system.domain.app.VccReq;
import com.meta.system.domain.vcc.MetaVccLog;
import com.meta.system.domain.vcc.VccCreateCardResp;
import com.meta.system.service.MetaVccLogService;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Component
public class YJCardUtils {
    private static final Logger log = LoggerFactory.getLogger(YJCardUtils.class);
    private static String hexJsonEnc;
    private static String hexKeyEnc;
    private static String hexSign;

    @Autowired
    private MetaVccLogService metaVccLogService;
    public static void main(String[] args) throws Exception {
        JSONObject req = new JSONObject();
        req.put("cardNo", "****************");
        req.put("cvv","118");
        req.put("expdate","2602");
        req.put("userid","10000017");
        String requestNo = UniqueIDGenerator.generateID();
        YJCardUtils yj = new YJCardUtils();
        yj.encryptAndSign(req, "authorize",requestNo);
        yj.doPost(requestNo,"authorize");

    }

    /**
     *  vcc卡接口
     * @param r
     * @param apiCode
     * @return
     */
    public VccCreateCardResp card(VccReq r, ApiCode apiCode){
        JSONObject bodyRequestParam = getRequestParam(apiCode,r);
        String apiCodeName = apiCode.getName();
        VccCreateCardResp res = new VccCreateCardResp();
        try {
            encryptAndSign(bodyRequestParam, apiCodeName, r.getRequestNo());
            res = doPost(r.getRequestNo(), apiCodeName);
        } catch (Exception e) {
            e.printStackTrace();
            res.setCode("ERROR");
            return res;
        }
        return res;
    }

    private JSONObject getRequestParam(ApiCode apiCode, VccReq r) {
        JSONObject req = new JSONObject();
        switch (apiCode) {
            case CreateCard:
                req.put("firstName", r.getFirstName());
                req.put("lastName", r.getLastName());
                req.put("email", r.getEmail());
                req.put("mobile","");//暂时放空
                req.put("cardBin", r.getCardBin());
                req.put("depositAmount", r.getDepositAmount());
                break;
            case QueryCardDetail:
                req.put("cardId",r.getCardId());
                break;
            case CloseCard:
                req.put("cardId",r.getCardId());
                break;
            case RechargeCard:
                req.put("cardId",r.getCardId());
                req.put("rechargeAmount",r.getRechargeAmount());
                break;
            case WithdrawCard:
                req.put("cardId",r.getCardId());
                req.put("withdrawAmount",r.getWithdrawAmount());
                break;
            case SuspendCard:
                req.put("cardId",r.getCardId());
                break;
            case UnSuspendCard:
                req.put("cardId",r.getCardId());
                break;
            case QueryTransactionPage:
                req.put("cardId",r.getCardId());
                if(!StringUtil.isEmpty(r.getTransactionId()))
                    req.put("transactionId",r.getTransactionId());
                if(!StringUtil.isEmpty(r.getTransactionStatus()))
                    req.put("transactionStatus",r.getTransactionStatus());
                if(!StringUtil.isEmpty(r.getTransactionType()))
                    req.put("transactionType",r.getTransactionType());
                if(!StringUtil.isEmpty(r.getTransactionStartTime()))
                    req.put("transactionStartTime",r.getTransactionStartTime());
                if(!StringUtil.isEmpty(r.getTransactionEndTime()))
                    req.put("transactionEndTime",r.getTransactionEndTime());
                if(r.getPageNum() != null)
                    req.put("pageNum",r.getPageNum());
                if(r.getPageSize() != null)
                    req.put("pageSize",r.getPageSize());
                break;
            default:
                break;
        }
        return req;
    }

    private VccCreateCardResp doPost(String requestNo, String apiCode) throws Exception {
        JSONObject req = new JSONObject();

        JSONObject requestHead = new JSONObject();
        requestHead.put("partnerId", VccConfig.partnerId);
        requestHead.put("apiCode", apiCode);
        requestHead.put("version", VccConfig.version);
        requestHead.put("requestNo", requestNo);
        requestHead.put("sign", hexSign);
        requestHead.put("keyEnc", hexKeyEnc);
        req.put("head", requestHead);

        JSONObject requestBody = new JSONObject();
        requestBody.put("encrypt", hexJsonEnc);
        req.put("body", requestBody);

        log.debug("请求参数："+req.toJSONString());

        Map headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        HttpResponse resultRep  = HttpUtils.doPost(VccConfig.url, "", "POST", headers, null, req.toJSONString());
        VccCreateCardResp resp = new VccCreateCardResp();
        resp.setCode("ERROR");
//        resp.setDetail(MessageUtils.message("vcc.sys.error"));
        if (resultRep.getStatusLine().getStatusCode() == 200) {
            /**读取服务器返回过来的json字符串数据**/
            String str = EntityUtils.toString(resultRep.getEntity());

            /**把json字符串转换成json对象**/
            JSONObject jsonResult = JSONObject.parseObject(str);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(jsonResult.toJSONString());
            String code =  rootNode.get("head").get("code").asText();
            String detail =  rootNode.get("head").get("detail").asText();
            resp.setCode(code);
            resp.setDetail(detail);
            resp.setApiCode(apiCode);
            resp.setRequestNo(requestNo);
            String result = decryptAndVeriSign(jsonResult);
            resp.setRes(result);
            //保存调用记录
            save(req.toJSONString(),jsonResult.toJSONString(),apiCode,requestNo,code,detail);
        }else{
            log.debug("网络波动,vcc接口调用失败:"+EntityUtils.toString(resultRep.getEntity()));
        }

        return resp;
    }

    /**
     * 加密加签
     *
     * @param jsonObject
     *
     * @throws Exception
     */
    private void encryptAndSign(JSONObject jsonObject, String apiCode, String requestNo) throws Exception {
        String key  = AESUtils.generateAesKey();
        String data = JSON.toJSONString(jsonObject);
        log.debug("明文:" + data);
        log.debug("密钥:key:" + key);
        try {
            hexJsonEnc = AESUtils.aesEncrypt(key, data);
            log.debug("明文加密结果encrypt:hexJsonEnc:" + hexJsonEnc);

            RSAUtils rsaUtils   = new RSAUtils();
            PrivateKey privatekey = rsaUtils.getPrivateKey(VccConfig.privateStr);
            PublicKey  publicKey  = rsaUtils.getPublicKey(VccConfig.publicKey);
            rsaUtils.initKey(privatekey, publicKey);

            // 密钥加密
            byte[] bkey = rsaUtils.encryptRSA(key.getBytes(), false, "utf-8");
            hexKeyEnc = DESUtils.toHexString(bkey);
            log.debug("密钥加密结果:keyEnc:hexKeyEnc:" + hexKeyEnc);
            // 签名
            String signStr = VccConfig.partnerId + "|" + apiCode + "|" + VccConfig.version + "|" + requestNo + "|" + hexJsonEnc;
            byte[] sign    = rsaUtils.signRSA(signStr.getBytes("utf-8"), false, "utf-8");
            hexSign = DESUtils.toHexString(sign);
            log.debug("签名结果:sign:hexSign:" + hexSign);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void save(String req,String resp,String apiCode,String requestNo,String code,String detail){
        MetaVccLog log = new MetaVccLog();
        log.setApicode(apiCode);
        log.setRequest(req);
        log.setResponse(resp);
        log.setCreatedAt(LocalDateTime.now());
        log.setRequestNo(requestNo);
        log.setCode(code);
        log.setDetail(detail);
        metaVccLogService.save(log);
    }
    /**
     * 解密验签
     *
     * @throws Exception
     */
    private static String decryptAndVeriSign(JSONObject jsonResult) throws Exception {
        if(jsonResult ==  null || jsonResult.isEmpty())
                return null;
        JSONObject responseHead = (JSONObject) jsonResult.get("head");
        String     apiCode      = responseHead.getString("apiCode");
        String     requestNo    = responseHead.getString("requestNo");
        String     code         = responseHead.getString("code");
        String     detail       = responseHead.getString("detail");
        String     hexKeyEnc    = responseHead.getString("keyEnc");
        String     hexSign      = responseHead.getString("sign");
        String     hexJsonEnc   = null;

        JSONObject responseBody = (JSONObject) jsonResult.get("body");
        if (responseBody != null) {
            hexJsonEnc = responseBody.getString("encrypt");
        }
        RSAUtils   rsaUtils   = new RSAUtils();
        PrivateKey privatekey = rsaUtils.getPrivateKey(VccConfig.privateStr);
        PublicKey  publicKey  = rsaUtils.getPublicKey(VccConfig.publicKey);
        rsaUtils.initKey(privatekey, publicKey);
        if(!StringUtil.isBlank(hexKeyEnc)){
            byte[] bkey = rsaUtils.decryptRSA(DESUtils.convertHexString(hexKeyEnc), false, "utf-8");
            String key  = new String(bkey);
            log.debug("密钥解密结果:" + key);
            String json = StringUtil.isBlank(hexJsonEnc) ? hexJsonEnc: AESUtils.aesDecrypt(key, hexJsonEnc);
            log.debug("明文解密结果:" + JsonUtils.desensitizeJson(json, new String[]{"cardNo","cvv2"}));
            String signStr = VccConfig.partnerId + "|" + apiCode + "|" + VccConfig.version + "|" + requestNo + "|" + code + "|" + detail;
            signStr = signStr + "|" + hexJsonEnc;
            byte[]  bsign = DESUtils.convertHexString(hexSign);
            boolean ret   = rsaUtils.verifyRSA(signStr.getBytes("utf-8"), bsign, false, "utf-8");
            log.debug("验签结果:" + ret);
            if(ret)
                return json;
        }
        return null;
    }


}
