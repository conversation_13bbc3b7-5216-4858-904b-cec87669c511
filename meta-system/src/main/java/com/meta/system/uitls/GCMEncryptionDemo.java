package com.meta.system.uitls;

/**
 * AES-GCM模式加密演示
 * 展示GCM模式的安全特性和优势
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public class GCMEncryptionDemo {

    public static void main(String[] args) {
        System.out.println("=== AES-GCM私钥加密工具演示 ===");
        System.out.println();

        // 1. 基本GCM加密功能
        demonstrateBasicGCMEncryption();

        System.out.println();

        // 2. GCM模式的安全特性
        demonstrateGCMSecurityFeatures();

        System.out.println();

        // 3. 性能和兼容性测试
        demonstratePerformanceAndCompatibility();

        System.out.println();

        // 4. 实际应用场景
        demonstrateRealWorldUsage();
    }

    /**
     * 基本GCM加密功能演示
     */
    private static void demonstrateBasicGCMEncryption() {
        System.out.println("1. 基本AES-GCM加密功能");
        System.out.println("----------------------");

        String originalKey = "f90bd80f933cfef74730d69c133b85288c920ffc253300fe8cb326f668eba517";

        try {
            // 加密
            String encryptedKey = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(originalKey);
            System.out.println("原始私钥: " + originalKey);
            System.out.println("加密模式: AES-128-GCM");
            System.out.println("加密结果: " + encryptedKey.substring(0, 48) + "...");

            // 解密
            String decryptedKey = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encryptedKey);
            System.out.println("解密结果: " + decryptedKey);

            // 验证
            boolean isValid = originalKey.equals(decryptedKey);
            System.out.println("验证结果: " + (isValid ? "✓ 成功" : "✗ 失败"));

        } catch (Exception e) {
            System.err.println("✗ 测试失败: " + e.getMessage());
        }
    }

    /**
     * GCM模式安全特性演示
     */
    private static void demonstrateGCMSecurityFeatures() {
        System.out.println("2. GCM模式安全特性");
        System.out.println("------------------");

        String testData = "sensitive_private_key_data_12345";

        try {
            // 特性1：相同明文产生不同密文
            System.out.println("特性1: 随机IV确保安全性");
            String encrypted1 = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(testData);
            String encrypted2 = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(testData);
            String encrypted3 = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(testData);

            System.out.println("  第1次加密: " + encrypted1.substring(0, 32) + "...");
            System.out.println("  第2次加密: " + encrypted2.substring(0, 32) + "...");
            System.out.println("  第3次加密: " + encrypted3.substring(0, 32) + "...");
            System.out.println("  结果都不同: " + (!encrypted1.equals(encrypted2) && !encrypted2.equals(encrypted3)));

            // 特性2：所有加密结果都能正确解密
            System.out.println("特性2: 认证解密");
            String decrypted1 = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encrypted1);
            String decrypted2 = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encrypted2);
            String decrypted3 = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encrypted3);

            boolean allValid = testData.equals(decrypted1) && testData.equals(decrypted2) && testData.equals(decrypted3);
            System.out.println("  所有解密都成功: " + (allValid ? "✓ 是" : "✗ 否"));

            // 特性3：数据完整性验证
            System.out.println("特性3: 数据完整性验证");
            System.out.println("  GCM模式自动验证数据完整性");
            System.out.println("  如果数据被篡改，解密会自动失败");
            System.out.println("  提供认证加密 (AEAD) 保护");

        } catch (Exception e) {
            System.err.println("✗ 安全特性测试失败: " + e.getMessage());
        }
    }

    /**
     * 性能和兼容性测试
     */
    private static void demonstratePerformanceAndCompatibility() {
        System.out.println("3. 性能和兼容性测试");
        System.out.println("------------------");

        // 性能测试
        System.out.println("性能测试:");
        String[] testKeys = {
            "key1_f90bd80f933cfef74730d69c133b85288c920ffc253300fe8cb326f668eba517",
            "key2_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
            "key3_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
            "key4_fedcba0987654321fedcba0987654321fedcba0987654321fedcba0987654321",
            "key5_0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef"
        };

        long startTime = System.currentTimeMillis();
        int successCount = 0;

        for (int i = 0; i < testKeys.length; i++) {
            try {
                String encrypted = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(testKeys[i]);
                String decrypted = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encrypted);

                if (testKeys[i].equals(decrypted)) {
                    successCount++;
                }

                System.out.println("  [" + (i + 1) + "] " + testKeys[i].substring(0, 20) + "... -> ✓");

            } catch (Exception e) {
                System.err.println("  [" + (i + 1) + "] " + testKeys[i].substring(0, 20) + "... -> ✗ " + e.getMessage());
            }
        }

        long endTime = System.currentTimeMillis();
        System.out.println("  批量处理结果: " + successCount + "/" + testKeys.length + " 成功");
        System.out.println("  处理时间: " + (endTime - startTime) + "ms");

        // 兼容性测试
        System.out.println("兼容性测试:");
        System.out.println("  ✓ 支持任意长度的私钥数据");
        System.out.println("  ✓ 支持UTF-8编码");
        System.out.println("  ✓ Base64编码便于存储和传输");
        System.out.println("  ✓ 向后兼容未加密数据");
    }

    /**
     * 实际应用场景演示
     */
    private static void demonstrateRealWorldUsage() {
        System.out.println("4. 实际应用场景");
        System.out.println("---------------");

        // 场景1：数据库存储
        System.out.println("场景1: 数据库存储");
        String dbPrivateKey = "database_stored_private_key_example_123456789abcdef";
        try {
            String encryptedForDB = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(dbPrivateKey);
            System.out.println("  存储到数据库: " + encryptedForDB.substring(0, 40) + "...");

            // 从数据库读取并使用
            String retrievedFromDB = encryptedForDB; // 模拟从数据库读取
            String keyForUse = SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(retrievedFromDB);
            System.out.println("  从数据库读取使用: " + (dbPrivateKey.equals(keyForUse) ? "✓ 成功" : "✗ 失败"));

        } catch (Exception e) {
            System.err.println("  数据库场景测试失败: " + e.getMessage());
        }

        // 场景2：配置文件
        System.out.println("场景2: 配置文件");
        String configPrivateKey = "config_file_private_key_987654321fedcba";
        try {
            String encryptedForConfig = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(configPrivateKey);
            System.out.println("  配置文件存储: " + encryptedForConfig.substring(0, 40) + "...");

            // 应用启动时读取
            String keyFromConfig = SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(encryptedForConfig);
            System.out.println("  应用启动读取: " + (configPrivateKey.equals(keyFromConfig) ? "✓ 成功" : "✗ 失败"));

        } catch (Exception e) {
            System.err.println("  配置文件场景测试失败: " + e.getMessage());
        }

        // 场景3：TronUtils集成
        System.out.println("场景3: TronUtils集成");
        String tronPrivateKey = "f90bd80f933cfef74730d69c133b85288c920ffc253300fe8cb326f668eba517";
        try {
            // 模拟存储的加密私钥
            String encryptedTronKey = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(tronPrivateKey);
            System.out.println("  加密存储: " + encryptedTronKey.substring(0, 40) + "...");

            // TronUtils使用时自动解密
            String keyForTronUtils = SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(encryptedTronKey);
            System.out.println("  TronUtils使用: " + (tronPrivateKey.equals(keyForTronUtils) ? "✓ 成功" : "✗ 失败"));

            // 测试未加密的私钥（向后兼容）
            String plainTronKey = "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef";
            String keyForTronUtils2 = SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(plainTronKey);
            System.out.println("  未加密兼容: " + (plainTronKey.equals(keyForTronUtils2) ? "✓ 成功" : "✗ 失败"));

        } catch (Exception e) {
            System.err.println("  TronUtils集成测试失败: " + e.getMessage());
        }

        System.out.println();
        System.out.println("=== GCM模式优势总结 ===");
        System.out.println("✓ 认证加密 (AEAD) - 同时提供机密性和完整性");
        System.out.println("✓ 抗篡改攻击 - 数据被修改时解密自动失败");
        System.out.println("✓ 随机IV - 相同明文产生不同密文");
        System.out.println("✓ 高性能 - 支持并行处理");
        System.out.println("✓ 业界标准 - 广泛应用于TLS、IPSec等协议");
        System.out.println("✓ 向后兼容 - 智能处理未加密数据");
    }
}
