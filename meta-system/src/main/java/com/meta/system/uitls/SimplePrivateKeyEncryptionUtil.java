package com.meta.util;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 私钥加密工具类
 * 使用AES-GCM模式提供认证加密，确保数据的机密性和完整性
 * <p>
 * 特性：
 * - 加密算法：AES-128-GCM
 * - 认证加密：提供数据完整性验证
 * - 随机IV：每次加密使用不同的初始化向量
 * - Base64编码：便于存储和传输
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public class SimplePrivateKeyEncryptionUtil {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/GCM/NoPadding";
    private static final int GCM_IV_LENGTH = 12; // GCM推荐的IV长度
    private static final int GCM_TAG_LENGTH = 16; // GCM认证标签长度

    // 默认密钥（实际使用时应该从配置文件读取）
    private static final String DEFAULT_SECRET_KEY = "MetaWallet123456"; // 16字节密钥

    // 是否启用加密（可以通过配置控制）
    private static boolean encryptionEnabled = true;

    /**
     * 设置是否启用加密
     */
    public static void setEncryptionEnabled(boolean enabled) {
        encryptionEnabled = enabled;
    }

    /**
     * 使用AES-GCM模式加密私钥
     * <p>
     * 安全特性：
     * - 每次加密使用随机IV，相同明文产生不同密文
     * - 提供认证标签，确保数据完整性
     * - 抗篡改攻击
     *
     * @param privateKey 原始私钥
     * @return 加密后的私钥（Base64编码，包含IV+密文+认证标签）
     */
    public static String encryptPrivateKey(String privateKey) {
        if (privateKey == null || privateKey.trim().isEmpty()) {
            throw new IllegalArgumentException("私钥不能为空");
        }

        // 如果未启用加密，直接返回原始私钥
        if (!encryptionEnabled) {
            return privateKey;
        }

        try {
            SecretKeySpec secretKey = new SecretKeySpec(DEFAULT_SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);

            // 生成随机IV
            byte[] iv = new byte[GCM_IV_LENGTH];
            new SecureRandom().nextBytes(iv);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv); // 标签长度以位为单位

            cipher.init(Cipher.ENCRYPT_MODE, secretKey, gcmSpec);
            byte[] encryptedBytes = cipher.doFinal(privateKey.getBytes(StandardCharsets.UTF_8));

            // 将IV和加密数据组合
            byte[] result = new byte[GCM_IV_LENGTH + encryptedBytes.length];
            System.arraycopy(iv, 0, result, 0, GCM_IV_LENGTH);
            System.arraycopy(encryptedBytes, 0, result, GCM_IV_LENGTH, encryptedBytes.length);

            return Base64.getEncoder().encodeToString(result);

        } catch (Exception e) {
            throw new RuntimeException("私钥加密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用AES-GCM模式解密私钥
     * <p>
     * 安全特性：
     * - 自动验证数据完整性
     * - 如果数据被篡改，解密会失败
     * - 提供认证解密
     *
     * @param encryptedPrivateKey 加密后的私钥（Base64编码，包含IV+密文+认证标签）
     * @return 原始私钥
     */
    public static String decryptPrivateKey(String encryptedPrivateKey) {
        if (encryptedPrivateKey == null || encryptedPrivateKey.trim().isEmpty()) {
            throw new IllegalArgumentException("加密私钥不能为空");
        }

        // 如果未启用加密，直接返回原始数据
        if (!encryptionEnabled) {
            return encryptedPrivateKey;
        }

        try {
            byte[] data = Base64.getDecoder().decode(encryptedPrivateKey);

            // 提取IV和加密数据
            byte[] iv = new byte[GCM_IV_LENGTH];
            byte[] encryptedBytes = new byte[data.length - GCM_IV_LENGTH];
            System.arraycopy(data, 0, iv, 0, GCM_IV_LENGTH);
            System.arraycopy(data, GCM_IV_LENGTH, encryptedBytes, 0, encryptedBytes.length);

            SecretKeySpec secretKey = new SecretKeySpec(DEFAULT_SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);

            cipher.init(Cipher.DECRYPT_MODE, secretKey, gcmSpec);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);

        } catch (Exception e) {
            throw new RuntimeException("私钥解密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 判断私钥是否已加密
     * 判断逻辑：
     * 1. 必须是有效的Base64编码
     * 2. 长度必须是16的倍数（AES块大小）
     * 3. 不能是纯十六进制字符串（原始私钥通常是十六进制）
     *
     * @param privateKey 私钥
     * @return true-已加密，false-未加密
     */
    public static boolean isEncrypted(String privateKey) {
        if (privateKey == null || privateKey.trim().isEmpty()) {
            return false;
        }

        // 如果未启用加密，认为都是未加密的
        if (!encryptionEnabled) {
            return false;
        }

        // 如果是纯十六进制字符串，很可能是原始私钥
        if (isHexString(privateKey)) {
            return false;
        }

        try {
            // 尝试Base64解码，如果成功且长度合理，可能是加密的
            byte[] decoded = Base64.getDecoder().decode(privateKey);
            // GCM模式：数据长度应该 >= IV长度 + 认证标签长度 + 至少1字节数据
            return decoded.length >= (GCM_IV_LENGTH + GCM_TAG_LENGTH + 1);
        } catch (Exception e) {
            // 解码失败，可能不是加密的
            return false;
        }
    }

    /**
     * 判断字符串是否为十六进制格式
     */
    private static boolean isHexString(String str) {
        if (str == null || str.length() % 2 != 0) {
            return false;
        }
        return str.matches("^[0-9a-fA-F]+$");
    }

    /**
     * 生成随机AES密钥（用于初始化配置）
     *
     * @return Base64编码的密钥
     */
    public static String generateSecretKey() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
            keyGenerator.init(128); // 128位密钥
            SecretKey secretKey = keyGenerator.generateKey();
            return Base64.getEncoder().encodeToString(secretKey.getEncoded());
        } catch (Exception e) {
            throw new RuntimeException("生成密钥失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证密钥格式是否正确
     *
     * @param secretKey 密钥
     * @return true-格式正确，false-格式错误
     */
    public static boolean isValidSecretKey(String secretKey) {
        if (secretKey == null || secretKey.trim().isEmpty()) {
            return false;
        }

        // AES密钥长度应该是16、24或32字节
        byte[] keyBytes = secretKey.getBytes(StandardCharsets.UTF_8);
        return keyBytes.length == 16 || keyBytes.length == 24 || keyBytes.length == 32;
    }

    /**
     * 测试AES-GCM加密解密功能
     */
    public static void testEncryption() {
        System.out.println("=== AES-GCM私钥加密工具测试 ===");

        String testPrivateKey = "f90bd80f933cfef74730d69c133b85288c920ffc253300fe8cb326f668eba517";

        System.out.println("原始私钥: " + testPrivateKey);
        System.out.println("加密模式: AES-GCM (认证加密)");
        System.out.println("加密启用状态: " + encryptionEnabled);
        System.out.println();

        try {
            // 测试加密
            String encrypted = encryptPrivateKey(testPrivateKey);
            System.out.println("加密后: " + encrypted);

            // 测试是否已加密判断
            boolean isEncryptedResult = isEncrypted(encrypted);
            System.out.println("是否已加密: " + isEncryptedResult);

            // 测试解密
            String decrypted = decryptPrivateKey(encrypted);
            System.out.println("解密后: " + decrypted);

            // 验证结果
            boolean success = testPrivateKey.equals(decrypted);
            System.out.println("加密解密成功: " + success);

            // 测试GCM模式的优势：相同明文产生不同密文
            String encrypted2 = encryptPrivateKey(testPrivateKey);
            System.out.println("第二次加密结果: " + encrypted2.substring(0, 32) + "...");
            System.out.println("相同明文产生不同密文: " + !encrypted.equals(encrypted2));

            // 验证第二次加密的解密
            String decrypted2 = decryptPrivateKey(encrypted2);
            System.out.println("第二次解密成功: " + testPrivateKey.equals(decrypted2));

            System.out.println();

            // 测试禁用加密
            System.out.println("--- 测试禁用加密 ---");
            setEncryptionEnabled(false);
            String disabledResult = encryptPrivateKey(testPrivateKey);
            System.out.println("禁用加密时结果: " + disabledResult);
            System.out.println("是否与原始相同: " + testPrivateKey.equals(disabledResult));

            // 恢复加密状态
            setEncryptionEnabled(true);

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 演示在TronUtils中的使用
     */
    public static String getDecryptedPrivateKey(String privateKey) {
        // 这个方法模拟TronUtils.getApiWrapper中的逻辑
        return isEncrypted(privateKey) ? decryptPrivateKey(privateKey) : privateKey;
    }
}
