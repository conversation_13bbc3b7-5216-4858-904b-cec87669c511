package com.meta.system.uitls;

// {{CHENGQI:
// Action: Added
// Timestamp: 2025-05-30 17:40:00 +08:00
// Reason: Per P3-AR-001 to provide the R<T> wrapper class for deserialization.
// Principle_Applied: KISS - Used user-provided structure with minimal changes.
// Optimization: None needed for this step.
// Architectural_Note (AR): Placed in com.meta.system.uitls to be co-located with other http utility classes for this module.
// Documentation_Note (DW): Will be referenced in SolHttpManager changes.
// }}
// {{START MODIFICATIONS}}
// +
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
// import java.io.Serial; // Removed as it might not be available/needed and can cause compilation issues in some environments.

/**
 * 响应信息主体 (Adapted from org.dromara.common.core.domain.R)
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class R<T> implements Serializable {

    // @Serial // Removed
    private static final long serialVersionUID = 1L;

    /**
     * 成功
     */
    public static final int SUCCESS = 200; // Standard HTTP OK

    /**
     * 失败
     */
    public static final int FAIL = 500; // Standard HTTP Internal Server Error

    private int code;

    private String msg;

    private T data;

    public static <T> R<T> ok() {
        return restResult(null, SUCCESS, "操作成功");
    }

    public static <T> R<T> ok(T data) {
        return restResult(data, SUCCESS, "操作成功");
    }

    public static <T> R<T> ok(String msg) {
        return restResult(null, SUCCESS, msg);
    }

    public static <T> R<T> ok(String msg, T data) {
        return restResult(data, SUCCESS, msg);
    }

    public static <T> R<T> fail() {
        return restResult(null, FAIL, "操作失败");
    }

    public static <T> R<T> fail(String msg) {
        return restResult(null, FAIL, msg);
    }

    public static <T> R<T> fail(T data) {
        return restResult(data, FAIL, "操作失败");
    }

    public static <T> R<T> fail(String msg, T data) {
        return restResult(data, FAIL, msg);
    }

    public static <T> R<T> fail(int code, String msg) {
        return restResult(null, code, msg);
    }

    /**
     * 返回警告消息
     *
     * @param msg 返回内容
     * @return 警告消息
     */
    public static <T> R<T> warn(String msg) {
        // Assuming HttpStatus.WARN maps to a specific code, e.g., 400 or a custom one
        // For now, using a generic client error code if HttpStatus.WARN is not directly available or defined
        // Replace 400 with actual HttpStatus.WARN if available and different
        return restResult(null, 400, msg); // Example: using 400 for WARN
    }

    /**
     * 返回警告消息
     *
     * @param msg 返回内容
     * @param data 数据对象
     * @return 警告消息
     */
    public static <T> R<T> warn(String msg, T data) {
        // Replace 400 with actual HttpStatus.WARN if available and different
        return restResult(data, 400, msg); // Example: using 400 for WARN
    }

    private static <T> R<T> restResult(T data, int code, String msg) {
        R<T> r = new R<>();
        r.setCode(code);
        r.setData(data);
        r.setMsg(msg);
        return r;
    }

    public static <T> Boolean isError(R<T> ret) {
        return !isSuccess(ret);
    }

    public static <T> Boolean isSuccess(R<T> ret) {
        return R.SUCCESS == ret.getCode();
    }
}
// {{END MODIFICATIONS}} 