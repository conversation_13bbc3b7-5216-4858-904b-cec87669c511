package com.meta.system.uitls;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meta.system.dto.RechargeDto;
import com.meta.system.service.ISysConfigService;
import com.meta.system.uitls.http.HttpClient;
import com.meta.system.vo.RechargeDataVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/30 15:32
 **/
@Slf4j
@Component
public class SolHttpManager {
    private final ObjectMapper objectMapper;
    private final HttpClient httpClient;

    @Resource
    private ISysConfigService sysConfigService;

    /**
     * BK服务地址，用于系统配置等接口
     * 对应于配置文件中的 meta.api.bk
     */

    public SolHttpManager(ObjectMapper objectMapper, HttpClient httpClient) {
        this.objectMapper = objectMapper;
        this.httpClient = httpClient;
    }

    /**
     * 查询未处理入账的交易记录 (使用钱包服务)
     * GET <a href="http://localhost:8080/sol/solanaTransactions/queryRechargeData?txnStatus=0&pageNum=1&pageSize=10">...</a>
     */
    public List<RechargeDataVo> queryRechargeData(RechargeDto rechargeDto) {

        Map<String, String> params = objectMapper.convertValue(rechargeDto, new TypeReference<Map<String, String>>() {});
        String solUri = sysConfigService.selectConfigByKey("SOL_URI");
        try {
            String jsonResponse = httpClient.get(solUri, "sol/solanaTransactions/queryRechargeData", params);
            log.debug("queryRechargeData 的原始JSON响应: {}", jsonResponse);

            JavaType listRechargeDataVoType = objectMapper.getTypeFactory().constructCollectionType(List.class, RechargeDataVo.class);
            List<RechargeDataVo> rechargeDataList = objectMapper.readValue(jsonResponse, listRechargeDataVoType);

            if (rechargeDataList != null) {
                return rechargeDataList;
            } else {
                log.warn("queryRechargeData 调用成功但返回的列表为空或null。钱包服务 URL: {}, 路径: {}, 参数: {}",
                        solUri, "sol/solanaTransactions/queryRechargeData", params);
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("调用钱包服务 queryRechargeData 时发生异常 (URL: {}, 路径: {}, 参数: {}): {}",
                    solUri, "sol/solanaTransactions/queryRechargeData", params, e.getMessage(), e);
            throw new RuntimeException("从钱包服务查询充值数据时出错: " + e.getMessage(), e);
        }
    }

}
