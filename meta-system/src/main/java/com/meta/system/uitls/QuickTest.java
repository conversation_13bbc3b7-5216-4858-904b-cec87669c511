package com.meta.util;

/**
 * 快速测试加密功能
 */
public class QuickTest {
    public static void main(String[] args) {
        System.out.println("=== 快速测试AES-GCM加密功能 ===");
        
        String testKey = "f90bd80f933cfef74730d69c133b85288c920ffc253300fe8cb326f668eba517";
        
        try {
            // 测试加密
            String encrypted = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(testKey);
            System.out.println("✓ 加密成功: " + encrypted.substring(0, 32) + "...");
            
            // 测试解密
            String decrypted = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encrypted);
            System.out.println("✓ 解密成功: " + decrypted.substring(0, 32) + "...");
            
            // 验证结果
            boolean success = testKey.equals(decrypted);
            System.out.println("✓ 验证结果: " + (success ? "成功" : "失败"));
            
            // 测试智能处理
            String processed = SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(encrypted);
            boolean smartSuccess = testKey.equals(processed);
            System.out.println("✓ 智能处理: " + (smartSuccess ? "成功" : "失败"));
            
            System.out.println("\n=== 所有测试通过！AES-GCM加密工具可以正常使用 ===");
            
        } catch (Exception e) {
            System.err.println("✗ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
