package com.meta.system.uitls;

import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/28 17:59
 **/
@Component
@RequiredArgsConstructor
public class CacheHelper {
    private final StringRedisTemplate redisTemplate;

    public void deletePattern(String pattern) {
        // 1. 使用 SCAN 收集要删除的键
        List<String> keysToDelete = redisTemplate.execute((RedisCallback<List<String>>) connection -> {
            List<String> keys = new ArrayList<>();
            Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().match(pattern).count(100).build());
            while (cursor.hasNext()) {
                keys.add(new String(cursor.next()));
            }
            try {
                cursor.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            return keys;
        });

        // 2. 使用管道删除收集到的键
        if (keysToDelete != null && !keysToDelete.isEmpty()) {
            List<Object> results = redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                for (String key : keysToDelete) {
                    connection.del(key.getBytes());
                }
                return null;
            });
            System.out.println("Delete pipeline results: " + results);
        }

    }
}
