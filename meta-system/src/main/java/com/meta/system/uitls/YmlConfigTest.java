package com.meta.system.uitls;

import com.meta.system.config.EncryptionProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * YML配置测试类
 * 验证从yml配置文件读取加密设置的功能
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Component
public class YmlConfigTest implements CommandLineRunner {

    @Autowired
    private EncryptionProperties encryptionProperties;

    @Autowired
    private SimplePrivateKeyEncryptionUtil encryptionUtil;

    @Override
    public void run(String... args) throws Exception {
        // 只在特定条件下运行测试，避免每次启动都执行
        String runTest = System.getProperty("test.encryption.yml");
        if (!"true".equals(runTest)) {
            return;
        }

        System.out.println("\n=== YML配置功能测试 ===");
        
        testConfigurationLoading();
        testEncryptionWithYmlConfig();
        
        System.out.println("=== YML配置测试完成 ===\n");
    }

    /**
     * 测试配置加载
     */
    private void testConfigurationLoading() {
        System.out.println("1. 配置加载测试");
        System.out.println("   启用状态: " + encryptionProperties.isEnabled());
        System.out.println("   算法: " + encryptionProperties.getAlgorithm());
        System.out.println("   编码: " + encryptionProperties.getEncoding());
        System.out.println("   密钥长度: " + encryptionProperties.getSecretKey().length() + " 字节");
        System.out.println("   配置来源: yml配置文件");
        
        // 验证密钥长度
        int keyLength = encryptionProperties.getSecretKey().length();
        if (keyLength == 16 || keyLength == 24 || keyLength == 32) {
            System.out.println("   ✅ 密钥长度验证通过");
        } else {
            System.out.println("   ❌ 密钥长度无效: " + keyLength);
        }
        System.out.println();
    }

    /**
     * 测试使用yml配置进行加密解密
     */
    private void testEncryptionWithYmlConfig() {
        System.out.println("2. 加密解密功能测试");
        
        String testData = "test_private_key_from_yml_config_123456";
        System.out.println("   测试数据: " + testData);
        
        try {
            // 测试加密
            String encrypted = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(testData);
            System.out.println("   加密结果: " + encrypted.substring(0, Math.min(32, encrypted.length())) + "...");
            
            // 测试解密
            String decrypted = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encrypted);
            System.out.println("   解密结果: " + decrypted);
            
            // 验证结果
            boolean success = testData.equals(decrypted);
            System.out.println("   验证结果: " + (success ? "✅ 成功" : "❌ 失败"));
            
            // 测试智能解密
            String smartResult = SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(encrypted);
            boolean smartSuccess = testData.equals(smartResult);
            System.out.println("   智能解密: " + (smartSuccess ? "✅ 成功" : "❌ 失败"));
            
            // 测试加密状态检测
            boolean isEncrypted = SimplePrivateKeyEncryptionUtil.isEncrypted(encrypted);
            boolean isPlaintext = SimplePrivateKeyEncryptionUtil.isEncrypted(testData);
            System.out.println("   加密检测: 密文=" + isEncrypted + ", 明文=" + isPlaintext);
            
        } catch (Exception e) {
            System.out.println("   ❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        System.out.println();
    }

    /**
     * 手动测试方法（可以在其他地方调用）
     */
    public static void manualTest() {
        System.out.println("=== 手动YML配置测试 ===");
        
        String testKey = "manual_test_key_12345";
        
        try {
            // 测试基本功能
            String encrypted = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(testKey);
            String decrypted = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encrypted);
            
            System.out.println("原始数据: " + testKey);
            System.out.println("加密结果: " + encrypted.substring(0, 32) + "...");
            System.out.println("解密结果: " + decrypted);
            System.out.println("测试结果: " + (testKey.equals(decrypted) ? "✅ 成功" : "❌ 失败"));
            
        } catch (Exception e) {
            System.out.println("❌ 手动测试失败: " + e.getMessage());
        }
        
        System.out.println("===================");
    }
}

/**
 * 独立的配置验证工具
 */
class EncryptionConfigValidator {
    
    public static void validateConfiguration(EncryptionProperties properties) {
        System.out.println("=== 配置验证 ===");
        
        // 验证基本配置
        if (properties == null) {
            System.out.println("❌ 配置对象为空");
            return;
        }
        
        // 验证密钥
        String secretKey = properties.getSecretKey();
        if (secretKey == null || secretKey.trim().isEmpty()) {
            System.out.println("❌ 密钥不能为空");
        } else {
            int length = secretKey.length();
            if (length == 16 || length == 24 || length == 32) {
                System.out.println("✅ 密钥长度有效: " + length + " 字节");
            } else {
                System.out.println("❌ 密钥长度无效: " + length + " 字节（应为16、24或32）");
            }
            
            // 检查是否使用默认密钥
            if ("MetaWallet123456".equals(secretKey)) {
                System.out.println("⚠️  警告: 使用默认密钥，生产环境请更换");
            }
        }
        
        // 验证算法
        String algorithm = properties.getAlgorithm();
        if (!"AES".equals(algorithm)) {
            System.out.println("⚠️  警告: 当前只支持AES算法，配置值: " + algorithm);
        }
        
        // 验证编码
        String encoding = properties.getEncoding();
        if (!"BASE64".equals(encoding)) {
            System.out.println("⚠️  警告: 当前只支持BASE64编码，配置值: " + encoding);
        }
        
        System.out.println("启用状态: " + properties.isEnabled());
        System.out.println("===============");
    }
}
