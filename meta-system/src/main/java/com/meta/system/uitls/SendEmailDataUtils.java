package com.meta.system.uitls;

import com.meta.common.constant.Constants;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.enums.app.TxnStatus;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.ThreadPoolUtil;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.system.domain.CreditCard;
import com.meta.system.email.Mail;
import com.meta.system.email.SendEmail;
import com.meta.system.service.CreditCardService;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/09/06/11:40
 */
@Slf4j
@Component
public class SendEmailDataUtils {

    @Autowired
    private SendEmail sendEmail;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private CreditCardService creditCardService;

    @Autowired
    private ISysUserService sysUserService;


    /**
     * gate.io 邮箱绑定成功
     *
     * @param language
     * @param email
     * @param nonce
     */
    public void sendGateBind(String language, String email, String nonce) {
        //发送邮件告知系统初始登录密码
        Mail mail = new Mail();
        mail.setEmail(email);
        String content = "";
        String subject = "";
        if ("cn".equals(language)) {
            subject = "邮箱绑定成功";

            content += "邮箱绑定成功！请立即访问 https://app.kazepay.io/  查看更多信息。<br>";
            content += "您的邮箱与Gate.io账户相同。<br>";
            content += "首次登录密码是： " + nonce + "<br>";
            content += "<a href=\"https://m.kazepay.io/download/user-guide_cn.pdf\">点击查看用户指南</a><br>";
            content += "如有任何疑问，请随时联系我们的客服：<br>";
            content += "电报：https://t.me/kazepayofficial <br>";
            content += "邮箱：<EMAIL><br>";
            content += "感谢您选择KazePay！携手KazePay，让数字资产管理更简单！您可以通过以下链接下载KazePay应用程序：<br>";
            content += " https://app.kazepay.io/#/pages/downloadClient/downloadClient";
            content += "<br><br>";
            content += "KazePay 团队";
            content += "<br><br>";
            content += "系统邮件，请勿回复。";
        } else {
            subject = "Email binding successful";

            content += "Please visit https://kazepay.io/ to log in to your account and claim your FREE silver/gold virtual card. Use the same email as your Gate.io account. For your first login, the password is: " + nonce + "<br>";

            content += " <a href=\"https://m.kazepay.io/download/user-guide_en.pdf\">Click to view the user guide</a><br>";
            content += "If you have any questions, feel free to reach out to our customer service team: <br>";
            content += "Telegram: https://t.me/kazepayofficial <br>";
            content += "Email: <EMAIL> <br>";
            content += "Thank you for choosing KazePay. Let’s make spending crypto easy!<br>";
            content += "Download the KazePay app here: https://app.kazepay.io/#/pages/downloadClient/downloadClient ";
            content += "<br><br>";
            content += "Thank you,";
            content += "<br>";
            content += "Kazepay Team";
            content += "<br><br>";

            content += "This is a system-generated email. Please do not reply.";
        }


        mail.setContent(content);
        mail.setSubject(subject);
        sendEmail.sendCheckEmail(mail);
    }

    /**
     * 卡交易验证码
     *
     * @param language
     * @param email
     * @param cardNo
     * @param code
     * @param formattedDateTime
     */
    public void sendVerificationCode(String language, String email, String cardNo, String code, String formattedDateTime) {
        Mail mail = new Mail();
        mail.setEmail(email);
        if ("cn".equals(language)) {

            mail.setSubject("卡交易验证码");
            String con = "尊敬的用户，";
            con += "<br><br>";
            con += "您的卡交易验证详情如下：";
            con += "<br><br>";

            con += "卡号: " + cardNo;
            con += "<br>";
            con += "交易验证: " + code;
            con += "<br>";
            con += "时间: " + formattedDateTime;
            con += "<br><br>";

            con += "感谢您使用 Kazepay。";
            con += "<br><br>";


            con += "KazePay 团队";
            con += "<br><br>";

            con += "系统邮件，请勿回复。";
            mail.setContent(con);
        } else {
            mail.setSubject("Card Transaction Verification Code");
            String con = "Dear User,";
            con += "<br><br>";
            con += "Please find your card transaction verification details below:";
            con += "<br><br>";

            con += "Card Number: " + cardNo;
            con += "<br>";
            con += "Transaction Verification Code: " + code;
            con += "<br>";
            con += "Time: " + formattedDateTime;
            con += "<br><br>";

            con += "Thank you for using Kazepay.";
            con += "<br><br>";

            con += "Best regards,";
            con += "<br>";
            con += "Kazepay Team";
            con += "<br><br>";

            con += "This is a system-generated email. Please do not reply.";
            mail.setContent(con);
        }

        //发送通知邮件
        sendEmail.sendCheckEmail(mail);
    }

    /**
     * 发送充值结果
     *
     * @param language
     * @param email
     * @param cardNo
     * @param receiveAmount
     */
    public void sendRechargeResult(String language, String email, String cardNo, String receiveAmount, String currency) {

        ThreadPoolUtil.execute(new Runnable() {
            @Override
            public void run() {
                Mail mail = new Mail();
                mail.setEmail(email);
                if ("cn".equals(language)) {
                    String con = "尊敬的用户，您的尾号为" + cardNo.substring(cardNo.length() - 4, cardNo.length()) + "的卡已成功充值，收到金额为 " + receiveAmount + currency + "。";
                    con += "<br><br>";
                    con += "KazePay 团队";
                    con += "<br><br>";
                    con += "系统邮件，请勿回复。";
                    mail.setContent(con);
                    mail.setSubject("卡片充值成功");
                } else {
                    String con = "Dear User,";
                    con += "Your card ending in " + cardNo.substring(cardNo.length() - 4, cardNo.length()) + " has been successfully recharged with an amount of " + receiveAmount + currency + ".";
                    con += "<br><br>";
                    con += "Thank you,";
                    con += "<br>";
                    con += "Kazepay Team";
                    con += "<br><br>";

                    con += "This is a system-generated email. Please do not reply.";
                    mail.setContent(con);
                    mail.setSubject("Card Recharge Completed Successfully");
                }
                //发送通知邮件
                sendEmail.sendCheckEmail(mail);
            }
        });
    }

    /**
     * 实体卡3DS
     *
     * @param email
     * @param cardNo
     * @param authResult
     */
    public void send3DSEmail(String language, String email, String cardNo, String authResult) {
        ThreadPoolUtil.execute(new Runnable() {
            @Override
            public void run() {
                Mail mail = new Mail();
                mail.setEmail(email);
                String result = "";
                String con = "";
                if ("cn".equals(language)) {
                    if ("UNAUTHORIZED".equals(authResult)) {
                        //未授权
                        result = "未授权";

                    } else if ("AUTHORIZED".equals(authResult)) {
                        //授权
                        result = "已授权";
                    } else if ("AUTHORIZATION_CANCELED".equals(authResult)) {
                        //授权取消
                        result = "授权取消";
                    } else if ("AUTHORIZATION_TIMEOUT".equals(authResult)) {
                        //授权超时
                        result = "授权超时";
                    }

                    if ("UNAUTHORIZED".equals(authResult)) {
                        con += "尊敬的用户，您的尾号为" + cardNo.substring(cardNo.length() - 4, cardNo.length()) + "的卡正在进行3DS授权，请到KazePay上进行授权确认。";
                    } else {
                        con += "尊敬的用户，您的尾号为" + cardNo.substring(cardNo.length() - 4, cardNo.length()) + "的卡3DS授权结果: " + result;
                    }
                    con += "<br><br>";
                    con += "KazePay 团队";
                    con += "<br><br>";
                    con += "系统邮件，请勿回复。";
                    mail.setContent(con);
                    mail.setSubject("3DS验证");
                } else {
                    result = authResult;
                    con += "Dear user,";
                    con += "<br>";
                    if ("UNAUTHORIZED".equals(authResult)) {
                        con += "Your card ending in " + cardNo.substring(cardNo.length() - 4, cardNo.length()) + " is currently undergoing 3DS authorization. Please visit Kazepay to confirm the authorization.";

                    } else {
                        con += "3DS authorization results for your card ending in " + cardNo.substring(cardNo.length() - 4, cardNo.length()) + ": " + result;
                    }

                    con += "<br><br>";
                    con += "Thank you,";
                    con += "<br>";
                    con += "Kazepay Team";
                    con += "<br><br>";

                    con += "This is a system-generated email. Please do not reply.";

                    mail.setContent(con);
                    mail.setSubject("3DS Card Authorization Notice");
                }

                //发送通知邮件
                sendEmail.sendCheckEmail(mail);
            }
        });
    }

    /**
     * 发送实卡kyc结果
     *
     * @param language
     * @param email
     * @param kycStatus
     */
    public void sendKycEmail(String language, String email, String kycStatus) {
        ThreadPoolUtil.execute(new Runnable() {
            @Override
            public void run() {

                Mail mail = new Mail();
                mail.setEmail(email);
                String con = "";
                if ("cn".equals(language)) {
                    if ("REJECTED".equals(kycStatus)) {//拒绝
                        con += "尊敬的用户，您的KYC审核被拒绝。";
                    } else if ("PASSED".equals(kycStatus)) {//通过
                        con += "尊敬的用户，您的KYC审核已通过。";
                    } else if ("EXPIRED".equals(kycStatus)) {//过期
                        con += "尊敬的用户，您的KYC审核已过期。";
                    }
                    con += "<br><br>";
                    con += "KazePay 团队";
                    con += "<br><br>";
                    con += "系统邮件，请勿回复。";
                    mail.setContent(con);
                    mail.setSubject("KYC审核结果");
                } else {
                    con += "Dear user,";
                    con += "<br>";
                    if ("REJECTED".equals(kycStatus)) {//拒绝
                        con += "Your KYC verification has been rejected.";
                    } else if ("PASSED".equals(kycStatus)) {//通过
                        con += "Your KYC verification has been approved.";
                    } else if ("EXPIRED".equals(kycStatus)) {//过期
                        con += "Your KYC verification has expired.";
                    }
                    con += "<br><br>";
                    con += "Thank you,";
                    con += "<br>";
                    con += "Kazepay Team";
                    con += "<br><br>";

                    con += "This is a system-generated email. Please do not reply.";

                    mail.setContent(con);
                    mail.setSubject("KYC Verification Result");
                }
                //发送通知邮件
                sendEmail.sendCheckEmail(mail);
            }
        });
    }

    /**
     * 发送验证码 注册/重置谷歌验证器/gate.io邮箱绑定
     *
     * @param language
     * @param email
     * @param subject
     * @param code
     * @param minument
     */
    public boolean sendCodeEmail(String language, String email, String subject, String code, int minument) {

        Mail mail = new Mail();
        mail.setEmail(email);
        String con = "";
        if ("cn".equals(language)) {

            con += "尊敬的用户，";

            con += "<br><br>";
            con += "【KazePay】验证码：" + code + " 。请在" + minument + "分钟之内完成操作。如非本人，请忽略。";
            con += "<br><br>";
            con += "KazePay 团队";
            con += "<br><br>";
            con += "系统邮件，请勿回复。";
            mail.setContent(con);

        } else {
            con += "Dear user,";
            con += "<br><br>";
            con += "Your KazePay verification code is " + code + ". Please complete the process within " + minument + " minutes. If this wasn't you, please disregard this message.";
            con += "<br><br>";
            con += "Thank you,";
            con += "<br>";
            con += "Kazepay Team";
            con += "<br><br>";
            con += "This is a system-generated email. Please do not reply.";

            mail.setContent(con);

        }
        //发送通知邮件
        mail.setSubject(subject);
        sendEmail.sendCheckEmail(mail);
        return true;
    }


    public void sendTgBind(String language, String email, String nonce) {
        //发送邮件告知系统初始登录密码
        Mail mail = new Mail();
        mail.setEmail(email);
        String content = "";
        String subject = "";
        if ("cn".equals(language)) {
            subject = "邮箱绑定成功";

            content += "邮箱绑定成功！请立即访问 https://app.kazepay.io/  查看更多信息。<br>";

            content += "首次登录密码是： " + nonce + "<br>";
            content += "<a href=\"https://m.kazepay.io/download/user-guide_cn.pdf\">点击查看用户指南</a><br>";
            content += "如有任何疑问，请随时联系我们的客服：<br>";
            content += "电报：https://t.me/kazepayofficial <br>";
            content += "邮箱：<EMAIL><br>";
            content += "感谢您选择KazePay！携手KazePay，让数字资产管理更简单！您可以通过以下链接下载KazePay应用程序：<br>";
            content += " https://app.kazepay.io/#/pages/downloadClient/downloadClient";
            content += "<br><br>";
            content += "KazePay 团队";
            content += "<br><br>";
            content += "系统邮件，请勿回复。";
        } else {
            subject = "Email binding successful";

            content += "Please visit https://kazepay.io/ to log in to your account and claim your FREE silver/gold virtual card. For your first login, the password is: " + nonce + "<br>";

            content += " <a href=\"https://m.kazepay.io/download/user-guide_en.pdf\">Click to view the user guide</a><br>";
            content += "If you have any questions, feel free to reach out to our customer service team: <br>";
            content += "Telegram: https://t.me/kazepayofficial <br>";
            content += "Email: <EMAIL> <br>";
            content += "Thank you for choosing KazePay. Let’s make spending crypto easy!<br>";
            content += "Download the KazePay app here: https://app.kazepay.io/#/pages/downloadClient/downloadClient ";
            content += "<br><br>";
            content += "Thank you,";
            content += "<br>";
            content += "Kazepay Team";
            content += "<br><br>";

            content += "This is a system-generated email. Please do not reply.";
        }


        mail.setContent(content);
        mail.setSubject(subject);
        sendEmail.sendCheckEmail(mail);
    }

    /**
     * 发送实体卡激活码
     *
     * @param language
     * @param email
     * @param cardNo
     * @param code
     */
    public void sendActivationCode(String language, String email, String cardNo, String code) {
        ThreadPoolUtil.execute(new Runnable() {
            @Override
            public void run() {
                Mail mail = new Mail();
                mail.setEmail(email);
                String con = "";

                if ("cn".equals(language)) {

                    con += "尊敬的用户";
                    con += "<br>";
                    con += "您的尾号为" + cardNo.substring(cardNo.length() - 4, cardNo.length()) + "的卡激活码为：" + code + "，请到KazePay上进行激活。";

                    con += "<br><br>";
                    con += "KazePay 团队";
                    con += "<br><br>";
                    con += "系统邮件，请勿回复。";
                    mail.setContent(con);
                    mail.setSubject("实体卡激活码");

                } else {

                    con += "Dear user,";
                    con += "<br>";
                    con += "The activation code for your card ending with " + cardNo.substring(cardNo.length() - 4, cardNo.length()) + " is: " + code + ". Please activate it on KazePay.";
                    con += "<br><br>";
                    con += "Thank you,";
                    con += "<br>";
                    con += "Kazepay Team";
                    con += "<br><br>";
                    con += "This is a system-generated email. Please do not reply.";

                    mail.setContent(con);
                    mail.setSubject("Physical card activation code");
                }

                //发送通知邮件
                sendEmail.sendCheckEmail(mail);
            }
        });
    }

    /**
     * 发送邮件提醒
     *
     * @param email    邮箱
     * @param isPickUp 是否自取r
     */
    public void sendPhysicalCardReminder(String email, String isPickUp) {
        ThreadPoolUtil.execute(new Runnable() {
            @Override
            public void run() {
                String str = "取卡方式：";
                // 1:邮寄（到付） 2：邮寄 3：自取
                if ("1".equals(isPickUp)) {
                    str = str + "邮寄（邮费到付）。";

                } else if ("2".equals(isPickUp)) {
                    str = str + "邮寄（已付邮费）。";

                } else if ("3".equals(isPickUp)) {
                    str = str + "自取。";

                }
                String auditEmail = configService.selectConfigByKey("audit_email");
                if (StringUtils.isNotEmpty(auditEmail)) {
                    if (auditEmail.contains(",")) {
                        String[] result = auditEmail.split(",");
                        for (String address : result) {
                            Mail mail = new Mail();
                            mail.setEmail(address);
                            mail.setContent("用户（" + email + "）申请了一张实体卡。" + str);
                            mail.setSubject("实体卡订单通知");
                            sendEmail.sendCheckEmail(mail);

                        }
                    } else {
                        Mail mail = new Mail();
                        mail.setEmail(auditEmail);
                        mail.setContent("用户（" + email + "）申请了一张实体卡。" + str);
                        mail.setSubject("实体卡订单通知");
                        sendEmail.sendCheckEmail(mail);
                    }

                }

            }
        });
    }

    /**
     * 发送开卡结果
     *
     * @param status 结果
     * @param cardId 卡id
     */
    public void sendOpenCardResult(String status, String cardId) {
        String brandShowName = "KazePay";
        String tgUrl = "https://t.me/kazepayofficial";

        CreditCard card = creditCardService.findByCardId(cardId);
        SysUser sysUser = sysUserService.selectUserByUserId(card.getUserId());
        String language = sysUser.getLanguage();
        String email = sysUser.getEmail();
        String cardNo = card.getCardNo();


        if (TxnStatus.SUCCESS.getName().equals(status)) {
            log.info("开卡成功发送邮件提醒");

            ThreadPoolUtil.execute(new Runnable() {
                @Override
                public void run() {
                    String no = AESUtils.aesDecrypt(Constants.card_key, cardNo);
                    String cardLast = no.substring(no.length() - 4, no.length());
                    log.info("发送中");
                    Mail mail = new Mail();
                    mail.setEmail(email);
                    String con = "";
                    String subject = "";


                    if ("cn".equals(language)) {


                        subject = "恭喜！您尾号" + cardLast + "的卡片已成功通过审核";

                        con += "亲爱的[" + email + "]，";
                        con += "<br><br>";
                        con += "感谢您申请 " + brandShowName + " 卡片。";
                        con += "<br>";
                        con += "我们非常高兴地通知您，您尾号为 " + cardLast + " 的卡片已经成功通过审核！";
                        con += "<br><br>";
                        con += "您现在可以登录账户，查看并开始使用您的卡片。";
                        con += "<br><br>";
                        con += "如有任何问题或需要协助，欢迎随时联系我们的支持团队：";
                        con += "<br>";
                        con += "Telegram 客服：" + tgUrl;
                        con += "<br><br>";
                        con += "感谢您对 " + brandShowName + " 的信任与支持！";
                        con += "<br><br>";
                        con += " 此致，";
                        con += "<br>";
                        con += brandShowName + " 团队";

                        mail.setContent(con);

                    } else if ("ru".equals(language)) {
                        subject = "Поздравляем! Ваша карта с номером, оканчивающимся на " + cardLast + ", успешно одобрена";

                        con += "Уважаемый(ая)  [" + email + "],";
                        con += "<br><br>";
                        con += "Благодарим вас за оформление карты " + brandShowName;
                        con += "<br>";
                        con += "Рады сообщить, что ваша карта с номером, оканчивающимся на " + brandShowName + ", успешно одобрена!";
                        con += "<br><br>";
                        con += "Теперь вы можете войти в свой аккаунт, чтобы просмотреть и начать использовать вашу карту.";
                        con += "<br><br>";
                        con += "Если у вас есть какие-либо вопросы или вам нужна помощь, пожалуйста, свяжитесь с нашей службой поддержки в Telegram: " + tgUrl;
                        con += "<br><br>";
                        con += "Спасибо за ваше доверие и поддержку " + brandShowName + "!";
                        con += "<br><br>";
                        con += "С уважением,";
                        con += "<br>";
                        con += "Команда" + brandShowName;
                        mail.setContent(con);


                    } else {

                        subject = "Congratulations! Your card ending in " + cardLast + " has been successfully approved";

                        con += "Dear [" + email + "],";
                        con += "<br><br>";
                        con += "Thank you for applying for a " + brandShowName + " card.";
                        con += "<br>";
                        con += "We are pleased to inform you that your card ending in " + cardLast + " has been successfully approved!";
                        con += "<br><br>";
                        con += "You can now log into your account to view and start using your card.";
                        con += "<br><br>";
                        con += "If you have any questions or need assistance, please feel free to contact our support team on Telegram: " + tgUrl;
                        con += "<br><br>";
                        con += "Thank you for trusting and supporting " + brandShowName + "!";
                        con += "<br><br>";
                        con += "Best regards,";
                        con += "<br>";
                        con += brandShowName + " Team";
                        mail.setContent(con);

                    }
                    //发送通知邮件
                    mail.setSubject(subject);
                    sendEmail.sendCheckEmail(mail);


                }

            });
        }
    }
}
