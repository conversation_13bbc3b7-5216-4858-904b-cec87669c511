package com.meta.system.uitls;

import com.meta.common.utils.StringUtils;
import com.meta.common.utils.file.FileUtils;
import com.meta.system.domain.app.FileSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletResponse;
import java.util.Objects;
public class DownloadUtils {
    private static final Logger log = LoggerFactory.getLogger(DownloadUtils.class);

    /**
     * 单文件下载
     * @param file
     * @param response
     */
    public static boolean download(FileSource file, HttpServletResponse response){
        if (!Objects.isNull(file)){
            try {
                if (!com.meta.common.utils.file.FileUtils.checkAllowDownload(file.getFileName())) {
                    throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", file.getFileName()));
                }
                String realFileName = file.getFileSourceName();
                response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
                com.meta.common.utils.file.FileUtils.setAttachmentResponseHeader(response, realFileName);
                com.meta.common.utils.file.FileUtils.writeBytes(file.getAbsolutePath(), response);
            } catch (Exception e) {
                log.error("下载文件失败", e);
                return false;
            }
            return true;
        }
        log.error("id为" + file.getId() + "的文件不存在");
        return false;
    }

    /**
     * 文件下载
     * @param fileName 文件名称
     * @param path 文件路径
     * @param fileSourceName 文件原名
     * @param response
     */
    public static void download(String fileName, String path, String fileSourceName,boolean delete , HttpServletResponse response){
        FileSource source = new FileSource();
        source.setFileName(fileName);
        source.setFilePath(path);
        source.setFileSourceName(fileSourceName);
        source.setFileId("");
        download(source,response);
        if (delete){
            FileUtils.deleteFile(source.getFilePath()+source.getFileName());
        }
    }



}
