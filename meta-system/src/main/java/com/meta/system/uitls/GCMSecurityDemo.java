package com.meta.util;

import java.util.Base64;

/**
 * GCM模式安全性验证演示
 * 展示GCM模式如何防止各种攻击
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
public class GCMSecurityDemo {

    public static void main(String[] args) {
        System.out.println("=== AES-GCM安全性验证演示 ===");
        System.out.println();
        
        // 1. 防止重放攻击
        demonstrateReplayAttackPrevention();
        
        System.out.println();
        
        // 2. 防止篡改攻击
        demonstrateTamperDetection();
        
        System.out.println();
        
        // 3. 密文不可预测性
        demonstrateCiphertextUnpredictability();
        
        System.out.println();
        
        // 4. 性能对比
        demonstratePerformanceComparison();
    }

    /**
     * 演示防止重放攻击
     */
    private static void demonstrateReplayAttackPrevention() {
        System.out.println("1. 防止重放攻击");
        System.out.println("----------------");
        
        String sensitiveData = "wallet_private_key_12345";
        
        try {
            // 多次加密相同数据
            String encrypted1 = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(sensitiveData);
            String encrypted2 = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(sensitiveData);
            String encrypted3 = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(sensitiveData);
            
            System.out.println("原始数据: " + sensitiveData);
            System.out.println("第1次加密: " + encrypted1.substring(0, 40) + "...");
            System.out.println("第2次加密: " + encrypted2.substring(0, 40) + "...");
            System.out.println("第3次加密: " + encrypted3.substring(0, 40) + "...");
            
            // 验证密文都不相同
            boolean allDifferent = !encrypted1.equals(encrypted2) && 
                                 !encrypted2.equals(encrypted3) && 
                                 !encrypted1.equals(encrypted3);
            
            System.out.println("密文都不相同: " + (allDifferent ? "✓ 是" : "✗ 否"));
            System.out.println("防重放攻击: " + (allDifferent ? "✓ 有效" : "✗ 无效"));
            
            // 验证都能正确解密
            String decrypted1 = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encrypted1);
            String decrypted2 = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encrypted2);
            String decrypted3 = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encrypted3);
            
            boolean allCorrect = sensitiveData.equals(decrypted1) && 
                               sensitiveData.equals(decrypted2) && 
                               sensitiveData.equals(decrypted3);
            
            System.out.println("解密都正确: " + (allCorrect ? "✓ 是" : "✗ 否"));
            
        } catch (Exception e) {
            System.err.println("✗ 重放攻击防护测试失败: " + e.getMessage());
        }
    }

    /**
     * 演示篡改检测
     */
    private static void demonstrateTamperDetection() {
        System.out.println("2. 篡改检测");
        System.out.println("-----------");
        
        String originalData = "important_private_key_data";
        
        try {
            // 正常加密
            String encrypted = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(originalData);
            System.out.println("原始数据: " + originalData);
            System.out.println("正常加密: " + encrypted.substring(0, 40) + "...");
            
            // 正常解密
            String decrypted = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encrypted);
            System.out.println("正常解密: " + (originalData.equals(decrypted) ? "✓ 成功" : "✗ 失败"));
            
            // 模拟篡改攻击
            System.out.println();
            System.out.println("模拟篡改攻击:");
            
            // 篡改1：修改密文中的一个字符
            String tamperedEncrypted1 = tamperData(encrypted, 10, 'X');
            System.out.println("篡改方式1: 修改密文第10个字符");
            testTamperedData(tamperedEncrypted1, "篡改检测1");
            
            // 篡改2：修改密文末尾
            String tamperedEncrypted2 = tamperData(encrypted, encrypted.length() - 5, 'Y');
            System.out.println("篡改方式2: 修改密文末尾字符");
            testTamperedData(tamperedEncrypted2, "篡改检测2");
            
            // 篡改3：截断密文
            String tamperedEncrypted3 = encrypted.substring(0, encrypted.length() - 4);
            System.out.println("篡改方式3: 截断密文");
            testTamperedData(tamperedEncrypted3, "篡改检测3");
            
        } catch (Exception e) {
            System.err.println("✗ 篡改检测测试失败: " + e.getMessage());
        }
    }

    /**
     * 篡改数据
     */
    private static String tamperData(String data, int position, char newChar) {
        if (position >= 0 && position < data.length()) {
            char[] chars = data.toCharArray();
            chars[position] = newChar;
            return new String(chars);
        }
        return data;
    }

    /**
     * 测试被篡改的数据
     */
    private static void testTamperedData(String tamperedData, String testName) {
        try {
            String result = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(tamperedData);
            System.out.println("  " + testName + ": ✗ 篡改未被检测到 (这不应该发生!)");
        } catch (Exception e) {
            System.out.println("  " + testName + ": ✓ 篡改被成功检测");
        }
    }

    /**
     * 演示密文不可预测性
     */
    private static void demonstrateCiphertextUnpredictability() {
        System.out.println("3. 密文不可预测性");
        System.out.println("----------------");
        
        // 测试相似数据的加密结果
        String[] similarData = {
            "private_key_000001",
            "private_key_000002", 
            "private_key_000003",
            "private_key_000004",
            "private_key_000005"
        };
        
        System.out.println("测试相似数据的加密结果:");
        
        try {
            String[] encrypted = new String[similarData.length];
            
            for (int i = 0; i < similarData.length; i++) {
                encrypted[i] = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(similarData[i]);
                System.out.println("  数据" + (i+1) + ": " + similarData[i] + " -> " + encrypted[i].substring(0, 32) + "...");
            }
            
            // 检查密文的相似性
            System.out.println();
            System.out.println("密文相似性分析:");
            
            boolean anyMatch = false;
            for (int i = 0; i < encrypted.length; i++) {
                for (int j = i + 1; j < encrypted.length; j++) {
                    // 比较前32个字符
                    String prefix1 = encrypted[i].substring(0, Math.min(32, encrypted[i].length()));
                    String prefix2 = encrypted[j].substring(0, Math.min(32, encrypted[j].length()));
                    
                    if (prefix1.equals(prefix2)) {
                        anyMatch = true;
                        break;
                    }
                }
                if (anyMatch) break;
            }
            
            System.out.println("  密文前缀相同: " + (anyMatch ? "✗ 是 (安全风险)" : "✓ 否 (安全)"));
            System.out.println("  不可预测性: " + (anyMatch ? "✗ 差" : "✓ 优秀"));
            
        } catch (Exception e) {
            System.err.println("✗ 不可预测性测试失败: " + e.getMessage());
        }
    }

    /**
     * 性能对比演示
     */
    private static void demonstratePerformanceComparison() {
        System.out.println("4. 性能测试");
        System.out.println("-----------");
        
        String testData = "performance_test_private_key_data_1234567890abcdef";
        int iterations = 1000;
        
        System.out.println("测试数据: " + testData);
        System.out.println("测试次数: " + iterations);
        System.out.println();
        
        try {
            // 加密性能测试
            long encryptStartTime = System.currentTimeMillis();
            String[] encrypted = new String[iterations];
            
            for (int i = 0; i < iterations; i++) {
                encrypted[i] = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(testData + "_" + i);
            }
            
            long encryptEndTime = System.currentTimeMillis();
            long encryptTime = encryptEndTime - encryptStartTime;
            
            System.out.println("加密性能:");
            System.out.println("  总时间: " + encryptTime + "ms");
            System.out.println("  平均时间: " + (encryptTime / (double) iterations) + "ms/次");
            System.out.println("  吞吐量: " + (iterations * 1000 / encryptTime) + "次/秒");
            
            // 解密性能测试
            long decryptStartTime = System.currentTimeMillis();
            int successCount = 0;
            
            for (int i = 0; i < iterations; i++) {
                try {
                    String decrypted = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encrypted[i]);
                    if (decrypted.startsWith(testData)) {
                        successCount++;
                    }
                } catch (Exception e) {
                    // 解密失败
                }
            }
            
            long decryptEndTime = System.currentTimeMillis();
            long decryptTime = decryptEndTime - decryptStartTime;
            
            System.out.println();
            System.out.println("解密性能:");
            System.out.println("  总时间: " + decryptTime + "ms");
            System.out.println("  平均时间: " + (decryptTime / (double) iterations) + "ms/次");
            System.out.println("  吞吐量: " + (iterations * 1000 / decryptTime) + "次/秒");
            System.out.println("  成功率: " + (successCount * 100 / iterations) + "%");
            
            // 总体性能
            long totalTime = encryptTime + decryptTime;
            System.out.println();
            System.out.println("总体性能:");
            System.out.println("  加密+解密总时间: " + totalTime + "ms");
            System.out.println("  完整周期平均时间: " + (totalTime / (double) iterations) + "ms/次");
            
        } catch (Exception e) {
            System.err.println("✗ 性能测试失败: " + e.getMessage());
        }
        
        System.out.println();
        System.out.println("=== 安全性总结 ===");
        System.out.println("✓ 防重放攻击 - 随机IV确保相同明文产生不同密文");
        System.out.println("✓ 防篡改攻击 - 认证标签自动检测数据完整性");
        System.out.println("✓ 密文不可预测 - 即使相似数据也产生完全不同的密文");
        System.out.println("✓ 高性能 - 适合大规模应用场景");
        System.out.println("✓ 业界标准 - 符合现代密码学最佳实践");
    }
}
