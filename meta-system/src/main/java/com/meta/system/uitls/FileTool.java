package com.meta.system.uitls;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.meta.common.config.MetaConfig;
import com.meta.common.utils.DateUtils;
import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.file.FileUploadUtils;
import com.meta.system.domain.app.FileSource;
import org.apache.commons.io.FilenameUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

public class FileTool {

    /**
     *
     * @param file 文件
     * @param fileId 文件id
     * @param fileType 文件类型
     * @param fileName 重命名文件
     * @param addTime 保存的文件是否加时间戳
     * @return
     */

    public static FileSource singleUpload(MultipartFile file, String fileId , String fileType, String fileName, boolean addTime){
        if (StringUtils.isEmpty(fileId)){
            fileId = UUID.randomUUID().toString().replace("-","");
        }
        String filePath = MetaConfig.getProfile()+"/";
        return createFile(file,filePath,fileType,fileId,fileName,addTime);
    }

    public static Map<String, List<FileSource>> multiUpload(List<MultipartFile> file,String fileId ,String fileTypes){
        Map<String,String> types = stringToMap(fileTypes);
        if (StringUtils.isEmpty(fileId)){
            fileId = UUID.randomUUID().toString().replace("-","");
        }
        String filePath = MetaConfig.getProfile()+"/";

        List<FileSource> success = new ArrayList();
        List<FileSource> error = new ArrayList();
        for (MultipartFile multipartFile : file) {
            String fileType = types.get(multipartFile.getOriginalFilename());
            if (StringUtils.isEmpty(fileType)){
                FileSource source = new FileSource();
                source.setFileSourceName(multipartFile.getOriginalFilename());
                error.add(source);
                continue;
            }
            FileSource source = createFile(multipartFile, filePath, fileType, fileId,null,true);
            if (source.getFileSize() == null){
                error.add(source);
            }else {
                success.add(source);
            }
        }
        Map<String,List<FileSource>> map = new HashMap<>();
        map.put("success",success);
        map.put("error",error);
        return map;
    }



    /**
     *
     * @param multipartFile
     * @param filePath
     * @param fileType
     * @param fileId
     * @param realFileName
     * @param addTime 保存的文件是否加时间戳
     * @return
     */
    public static FileSource createFile(MultipartFile multipartFile,String filePath,String fileType,String fileId,String realFileName,boolean addTime){
        FileSource source = new FileSource();
        if (StringUtils.isEmpty(realFileName)){
            realFileName  =  multipartFile.getOriginalFilename();
        }else {
            String extension = FilenameUtils.getExtension(multipartFile.getOriginalFilename());
            if (StringUtils.isNotEmpty(extension)){
                realFileName = realFileName + "." + extension;
            }
        }
        source.setFileSourceName(realFileName);
        int fileNameLength = multipartFile.getOriginalFilename().length();

        String newFilePath = filePath + fileType + "/";
        File file_dir= new File(newFilePath);
        if(!file_dir.exists()) {
            file_dir.mkdirs();
        }

        //文件名长度限制
        if (fileNameLength > FileUploadUtils.DEFAULT_FILE_NAME_LENGTH) {
            source.setErrorMsg("文件名过长,请不要超过"+FileUploadUtils.DEFAULT_FILE_NAME_LENGTH+"个字符!");
            return source;

        }

        //文件大小限制
        long fileSize = multipartFile.getSize();


        if (FileUploadUtils.DEFAULT_MAX_SIZE != -1 && fileSize > FileUploadUtils.DEFAULT_MAX_SIZE) {
            source.setErrorMsg("文件大小不得超过"+FileUploadUtils.DEFAULT_MAX_SIZE+"M!");
            return source;
        }


        try {
            if (fileType == null){
                source.setErrorMsg("文件类型为空!");
                return source;
            }
            String fileName = "";
            if (addTime){
                fileName = System.currentTimeMillis() + "-" +realFileName;
            }else {
                fileName = realFileName;
            }

            File desc = createAbsoluteFile(newFilePath, fileName);

            multipartFile.transferTo(desc);
            source.setFileId(fileId);
            source.setFilePath(newFilePath.replace(MetaConfig.getProfile(),""));
            source.setFileName(fileName);
            source.setFileSourceName(realFileName);
            source.setFileType(fileType);
            source.setDelFlag("0");
            source.setFileReleaseUser(SecurityUtils.getUsername());
            source.setFileDate(DateUtils.getNowDate());
            BigDecimal size = new BigDecimal(multipartFile.getSize());
            if (multipartFile.getSize() >= 1024 * 1024){
                size = size.divide(new BigDecimal(1024 * 1024), 2, BigDecimal.ROUND_HALF_DOWN);
                source.setFileSize(size + " MB");
            }else if(multipartFile.getSize() >= 1024){
                size = size.divide(new BigDecimal(1024), 2, BigDecimal.ROUND_HALF_DOWN);
                source.setFileSize(size + " KB");
            }else{
                source.setFileSize(size + " Byte");
            }
        }catch (IOException e) {
            e.printStackTrace();
        }

        return source;
    }


    public static final File createAbsoluteFile(String uploadDir, String fileName) throws IOException {
        File desc = new File(uploadDir + File.separator + fileName);

        if (!desc.getParentFile().exists())
        {
            desc.getParentFile().mkdirs();
        }
        if (!desc.exists())
        {
            desc.createNewFile();
        }
        return desc;
    }

    /**
     * 字符串转map
     * @param mapStr
     * @return
     */
    public static Map<String,String> stringToMap(String mapStr){
        HashMap<String, String> map = JSON.parseObject(mapStr, new TypeReference<HashMap<String, String>>() {});
        return map;
    }
}
