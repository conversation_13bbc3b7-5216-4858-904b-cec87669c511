package com.meta.system.uitls;

/**
 * 简化版私钥加密工具演示
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public class SimpleEncryptionDemo {

    public static void main(String[] args) {
        System.out.println("=== Meta钱包私钥加密工具演示 ===");
        System.out.println();

        // 1. 基本功能测试
        testBasicEncryption();

        System.out.println();

        // 2. TronUtils集成演示
        demonstrateTronUtilsIntegration();

        System.out.println();

        // 3. 批量处理演示
        demonstrateBatchProcessing();

        System.out.println();

        // 4. 配置管理演示
        demonstrateConfigurationManagement();
    }

    /**
     * 基本加密功能测试
     */
    private static void testBasicEncryption() {
        System.out.println("1. 基本加密功能测试");
        System.out.println("-------------------");

        String originalKey = "f90bd80f933cfef74730d69c133b85288c920ffc253300fe8cb326f668eba517";

        try {
            // 加密
            String encryptedKey = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(originalKey);
            System.out.println("原始私钥: " + originalKey);
            System.out.println("加密后的私钥: " + encryptedKey);

            // 检查是否已加密
            boolean isEncrypted = SimplePrivateKeyEncryptionUtil.isEncrypted(encryptedKey);
            System.out.println("是否已加密: " + isEncrypted);

            // 解密
            String decryptedKey = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encryptedKey);
            System.out.println("解密后的私钥: " + decryptedKey);

            // 验证
            boolean isValid = originalKey.equals(decryptedKey);
            System.out.println("加密解密验证: " + (isValid ? "✓ 成功" : "✗ 失败"));

        } catch (Exception e) {
            System.err.println("✗ 测试失败: " + e.getMessage());
        }
    }

    /**
     * TronUtils集成演示
     */
    private static void demonstrateTronUtilsIntegration() {
        System.out.println("2. TronUtils集成演示");
        System.out.println("-------------------");

        // 模拟数据库中存储的私钥（已加密）
        String originalKey = "f90bd80f933cfef74730d69c133b85288c920ffc253300fe8cb326f668eba517";
        String encryptedKeyInDB = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(originalKey);

        System.out.println("数据库中的加密私钥: " + encryptedKeyInDB);

        // 模拟TronUtils.getApiWrapper方法中的处理逻辑
        String keyForApiWrapper = SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(encryptedKeyInDB);
        System.out.println("用于ApiWrapper的私钥: " + keyForApiWrapper);
        System.out.println("解密是否正确: " + (originalKey.equals(keyForApiWrapper) ? "✓ 正确" : "✗ 错误"));

        // 测试未加密的私钥
        String plainKey = "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef";
        String keyForApiWrapper2 = SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(plainKey);
        System.out.println("未加密私钥处理: " + (plainKey.equals(keyForApiWrapper2) ? "✓ 保持不变" : "✗ 发生变化"));
    }

    /**
     * 批量处理演示
     */
    private static void demonstrateBatchProcessing() {
        System.out.println("3. 批量处理演示");
        System.out.println("---------------");

        String[] privateKeys = {
            "f90bd80f933cfef74730d69c133b85288c920ffc253300fe8cb326f668eba517",
            "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
            "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
        };

        System.out.println("批量加密 " + privateKeys.length + " 个私钥:");

        String[] encryptedKeys = new String[privateKeys.length];
        for (int i = 0; i < privateKeys.length; i++) {
            try {
                encryptedKeys[i] = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(privateKeys[i]);
                System.out.println("  [" + (i + 1) + "] " + privateKeys[i].substring(0, 16) + "... -> " +
                                 encryptedKeys[i].substring(0, 16) + "...");
            } catch (Exception e) {
                System.err.println("  [" + (i + 1) + "] 加密失败: " + e.getMessage());
            }
        }

        System.out.println("批量解密验证:");
        int successCount = 0;
        for (int i = 0; i < encryptedKeys.length; i++) {
            try {
                String decrypted = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encryptedKeys[i]);
                boolean isValid = privateKeys[i].equals(decrypted);
                System.out.println("  [" + (i + 1) + "] " + (isValid ? "✓ 成功" : "✗ 失败"));
                if (isValid) successCount++;
            } catch (Exception e) {
                System.err.println("  [" + (i + 1) + "] 解密失败: " + e.getMessage());
            }
        }

        System.out.println("批量处理结果: " + successCount + "/" + privateKeys.length + " 成功");
    }

    /**
     * 配置管理演示
     */
    private static void demonstrateConfigurationManagement() {
        System.out.println("4. 配置管理演示");
        System.out.println("---------------");

        String testKey = "test1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab";

        // 测试启用加密
        System.out.println("启用加密模式:");
        SimplePrivateKeyEncryptionUtil.setEncryptionEnabled(true);
        try {
            String encrypted = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(testKey);
            System.out.println("  加密结果: " + encrypted.substring(0, 32) + "...");
            System.out.println("  是否已加密: " + SimplePrivateKeyEncryptionUtil.isEncrypted(encrypted));
        } catch (Exception e) {
            System.err.println("  加密失败: " + e.getMessage());
        }

        // 测试禁用加密
        System.out.println("禁用加密模式:");
        SimplePrivateKeyEncryptionUtil.setEncryptionEnabled(false);
        try {
            String result = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(testKey);
            System.out.println("  处理结果: " + result.substring(0, 32) + "...");
            System.out.println("  是否与原始相同: " + testKey.equals(result));
            System.out.println("  是否已加密: " + SimplePrivateKeyEncryptionUtil.isEncrypted(result));
        } catch (Exception e) {
            System.err.println("  处理失败: " + e.getMessage());
        }

        // 恢复加密状态
        SimplePrivateKeyEncryptionUtil.setEncryptionEnabled(true);

        // 密钥管理
        System.out.println("密钥管理:");
        try {
            String newKey = SimplePrivateKeyEncryptionUtil.generateSecretKey();
            System.out.println("  生成的新密钥: " + newKey);

            boolean isValid1 = SimplePrivateKeyEncryptionUtil.isValidSecretKey("MetaWallet123456"); // 16字节
            boolean isValid2 = SimplePrivateKeyEncryptionUtil.isValidSecretKey("short"); // 太短
            System.out.println("  密钥验证 (16字节): " + (isValid1 ? "✓ 有效" : "✗ 无效"));
            System.out.println("  密钥验证 (短密钥): " + (isValid2 ? "✓ 有效" : "✗ 无效"));

        } catch (Exception e) {
            System.err.println("  密钥管理失败: " + e.getMessage());
        }
    }
}
