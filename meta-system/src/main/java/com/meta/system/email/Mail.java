package com.meta.system.email;

import com.meta.common.constant.Constants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Mail {
    // 主题
    String subject;

    // 内容
    String content;

    // 接收者
    @Email(regexp = Constants.EMAIL_REGULAR,message = "邮箱格式错误!")
    String email;
}
