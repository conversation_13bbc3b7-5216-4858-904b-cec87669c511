package com.meta.system.fornax.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/05/08/10:02
 */
@Data
public class ApplyCardReq {

    private String userReqNo;//唯一单号

    private String localCurrency;//开卡币种
    private String startDate;//卡生效日期
    private String endDate;//卡失效日期
    private BigDecimal authLimitAmount;//授 权额度
    private Integer enableMultiUse;//是否多次卡
    private Integer enableCurrencyCheck;//是否限制交易币种
    private String cardAlias;//卡别名
    private String binRangeId;//卡头
    private String channelType;//卡类型
    private String cardUserId;//持卡人Id
    private Integer groupId;//共享卡组的唯一标识符[当channelType = 2时需要]
    private List<Integer> sceneIds;//场景id
    private Integer  sceneType;//场景类型



}
