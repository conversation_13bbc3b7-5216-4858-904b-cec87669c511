package com.meta.system.fornax.constants;

import com.meta.common.enums.BaseEnum;

/**
 * <AUTHOR>
 * @date 2025/05/08/14:17
 */
public enum FornaxChannelType implements BaseEnum<FornaxChannelType> {

    REGULAR("1", "常规卡"),
    SHARED("2", "共享-附属卡"),
    ;

    String value;
    String name;

    FornaxChannelType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public String getValue() {
        return this.value;
    }


    @Override
    public String getOrdinal() {
        return String.valueOf(this.ordinal());
    }
}
