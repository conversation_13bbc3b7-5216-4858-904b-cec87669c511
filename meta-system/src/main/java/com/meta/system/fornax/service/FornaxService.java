package com.meta.system.fornax.service;

import com.alibaba.fastjson.JSONObject;
import com.meta.system.domain.CreditCard;
import com.meta.system.domain.app.CardConfig;
import com.meta.system.domain.app.MetaFxUserInfo;
import com.meta.system.domain.app.MetaOrder;
import com.meta.system.models.CardInfo;
import com.meta.system.models.CardRechargeResult;
import com.meta.system.models.CardResult;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/05/07/15:25
 */
public interface FornaxService {
    /**
     * 开卡
     *
     * @param cardConfig 卡配置
     * @return
     */
    CardResult applyCard(CardConfig cardConfig, MetaOrder metaOrder);





    /**
     * 卡片查询
     *
     * @param card 卡
     * @return
     */
    JSONObject cardInfo(CreditCard card);

    /**
     * 卡片余额
     *
     * @param card 卡
     * @return
     */
    JSONObject cardBalance(CreditCard card);


    /**
     * 退款
     *
     * @param card   卡
     * @param amount 金额
     * @return
     */
    JSONObject refund(CreditCard card, BigDecimal amount);


    /**
     * 销卡
     *
     * @param card 卡
     * @return
     */
    JSONObject closeCard(CreditCard card);

    /**
     * 持卡人信息
     *
     * @return
     */
    JSONObject cardholder(MetaFxUserInfo metaFxUserInfo);


    /**
     * 持卡人信息删除
     *
     * @returnD
     */
    JSONObject cardholderDrop(String cardUserId);

    /**
     * 卡片状态
     *
     * @param cardStatus
     * @return
     */
    String getCardStatus(String cardStatus);


    /**
     * 卡片冻结/解冻
     *
     * @param card
     * @param status
     */
    JSONObject updateCard(CreditCard card, String status);

    /**
     * 开卡查询
     * @param cardID
     * @return
     */
    JSONObject queryOrderOpenCard(String cardID);
    /**
     * 充值查询
     * @param requestNo
     * @return
     */
    JSONObject queryOrderRechargeCard(String requestNo);

    /**
     * 获取卡片信息
     * @param c
     * @return
     */
    CardInfo getCardInfo(CreditCard c);

    /**
     * 充值
     *
     * @param card 卡
     * @param metaOrder 金额
     * @return
     */
    CardRechargeResult rechargeCard(CreditCard card, MetaOrder metaOrder);

    String findTradeType(String transType);

    String getDesc(String transType);
}
