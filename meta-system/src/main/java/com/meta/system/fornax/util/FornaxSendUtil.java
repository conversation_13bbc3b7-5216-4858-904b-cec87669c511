package com.meta.system.fornax.util;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meta.common.utils.StringUtils;
import com.meta.system.config.FornaxConfig;
import com.meta.system.domain.app.MetaFornaxLog;
import com.meta.system.fornax.constants.FornaxMethod;
import com.meta.system.fornax.http.HttpResult;
import com.meta.system.fornax.http.PooledHttpService;
import com.meta.system.service.MetaFornaxLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/05/08/10:49
 */
@Slf4j
@Component
public class FornaxSendUtil {
    @Resource
    private PooledHttpService pooledHttpService;

    @Resource
    private MetaFornaxLogService metaFornaxLogService;

    /**
     * 发送请求
     *
     * @param data 数据
     * @return
     */
    public JSONObject send(String data, String method, String requestNo, String cardId) {
        JSONObject resultJson = new JSONObject();
        String code = "";
        String errorMsg = "";
        try {
            Map<String, String> req = new HashMap<>();
            req.put("dataType", FornaxConfig.dataType);
            req.put("version", FornaxConfig.version);
            req.put("userNo", FornaxConfig.userNo);
            req.put("dataContent", FormatUtil.byte2Hex(RsaUtil.encryptToBytes(Base64.encode(data), RsaUtil.getPrivateKey(FornaxConfig.privateKey))));

            log.info("请求内容:" + data);
            log.info("请求方法:" + FornaxConfig.url + method);


            HttpResult httpResult = pooledHttpService.doPost(FornaxConfig.url + method, JSON.toJSONString(req));
            code = String.valueOf(httpResult.getStatus());
            JSONObject jsonObject = JSON.parseObject(httpResult.getData());
            String result = jsonObject.getString("result");
            log.info("返回的async：" + jsonObject.getBoolean("async"));
            Boolean success = jsonObject.getBoolean("success");
            log.info("返回的success：" + success);
            if (!success) {
                code = jsonObject.getString("errorCode");
                errorMsg = jsonObject.getString("errorMsg");
                log.info("返回msg" + jsonObject.toString());

            }

            if (StringUtils.isNotEmpty(result)) {

                String decrypted = RsaUtil.decryptToString(jsonObject.getString("result"), RsaUtil.getPublicKey(FornaxConfig.publicKey));

                if (StringUtils.isNotEmpty(decrypted)) {
                    resultJson.put("result", JSONObject.parseObject(decrypted));
                    JSONObject object = JSONObject.parseObject(decrypted);
                    //特殊数据在日志不打印
                    if (FornaxMethod.applyCard.equals(method) || FornaxMethod.cardInfo.equals(method)) {
                        object.put("cardNo", "***");
                        object.put("cardVerifyNo", "***");
                        object.put("cardExpiryDate", "***");
                    }


                    log.info("返回的结果:" + object.toString());
                }

            }
            resultJson.put("async", jsonObject.getBoolean("async"));
            resultJson.put("success", jsonObject.getBoolean("success"));

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeySpecException | IOException e) {
            e.printStackTrace();
        } finally {
            // 保存请求日志
            MetaFornaxLog fornaxLog = new MetaFornaxLog();
            fornaxLog.setApicode(method);
            fornaxLog.setRequest(data);
            if (resultJson != null) {
                fornaxLog.setResponse(resultJson.toString());
                fornaxLog.setCode(code);
                fornaxLog.setDetail(resultJson.getString("success"));
            }

            fornaxLog.setCreatedAt(LocalDateTime.now());
            fornaxLog.setRequestNo(requestNo);
            fornaxLog.setCardId(cardId);
            fornaxLog.setDetail(errorMsg);
            metaFornaxLogService.save(fornaxLog);
        }
        return resultJson;
    }
}
