package com.meta.system.fornax.constants;

import com.meta.common.enums.BaseEnum;

/**
 * kun卡状态
 * <AUTHOR>
 * @date 2024/10/26/13:49
 */
public enum FornaxCardStatus implements BaseEnum<FornaxCardStatus> {


    ACTIVE("1", "正常"),
    CANCELED("2", "已注销"),
    FROZEN("0", "已冻结"),

    ;

    String value;
    String name;

    FornaxCardStatus(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public String getValue() {
        return this.value;
    }

    @Override
    public String getOrdinal() {
        return String.valueOf(this.ordinal());
    }

}
