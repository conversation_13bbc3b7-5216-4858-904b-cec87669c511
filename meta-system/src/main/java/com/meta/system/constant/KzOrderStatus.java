package com.meta.system.constant;

import com.meta.common.enums.BaseEnum;

/**
 * <AUTHOR>
 * @date 2025/05/12/14:16
 */
public enum KzOrderStatus implements BaseEnum<KzOrderStatus> {


    fail("fail", "失败"),
    success("success", "成功"),
    process("process", "处理中"),
    create("create", "创建"),
    ;

    String value;
    String name;

    KzOrderStatus(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public String getValue() {
        return this.value;
    }

    @Override
    public String getOrdinal() {
        return String.valueOf(this.ordinal());
    }
}
