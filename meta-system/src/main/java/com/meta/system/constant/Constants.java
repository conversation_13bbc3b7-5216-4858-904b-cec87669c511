package com.meta.system.constant;


import java.util.ArrayList;
import java.util.List;

/**
 * 通用常量信息
 */
public class Constants {
    public static final String BEP_ADDRESS = "biz_bep20:";

    /**
     * wello redis key
     */
    public static final String WELLO_TRADING_PAIR_KEY="wello_trading_pair";
    public static final String WELLO_ORDER_LOCK_KEY="wello_order_lock:";
    public static final String WELLO_ORDER_EXPIRATION_KEY="wello_order_expiration:";
    public static final String WELLO_MERCHANT_WALLET_ADDRESS_KEY="wello_merchant_wallet_address";
    public static final String WELLO_CHECKOUT_TOKEN_KEY="wello_checkout_token:";

    /**
     * wello
     */
    public static final String WELLO_NETWORK = "BSC";

    /**
     * alixpay redis key
     */
    public static final String ALIXPAY_CC_AMOUNT_KEY="alixpay:ccAmount:";
    public static final String ALIXPAY_QR_PAY_PARAMETERS_KEY="alixpay:QRPayParameters:";
    public static final String ALIXPAY_READ_TIMEOUT_KEY="alixpay:readTimeout:";
    public static final String ALIXPAY_DAY_LIMIT_KEY="alixpay:dayLimit:";

    //充值监控
    public static final String recharge_monitoring_key="recharge_monitoring_key:";
    //提币监控
    public static final String widthdraw_monitoring_key="widthdraw_monitoring_key:";
    //划转监控
    public static final String thransfer_monitoring_key="thransfer_monitoring_key:";
    //所有监控
    public static final String all_monitoring_key="all_monitoring_key:";

    //用户充值的键
    public static final String user_rechage_key="user_rechage_key:";
    //充值的卡的键
    public static final String user_rechage_card_key="user_rechage_card_key:";

    //订单的键
    public static final String order_lock="lock:order:";
    //订单结果处理
    public static final String order_lock_deal="lock:order:deal:";


    /**
     * kazepay支持的加密货币集合
     */
    public static final List<String> kazepay_currency_gather = new ArrayList<String>() {{
        add("USDT");
        add("USDC");
    }};


}
