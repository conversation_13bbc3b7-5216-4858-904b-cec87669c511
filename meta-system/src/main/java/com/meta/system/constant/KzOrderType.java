package com.meta.system.constant;

import com.meta.common.enums.BaseEnum;

/**
 * <AUTHOR>
 * @date 2025/05/12/14:16
 */
public enum KzOrderType implements BaseEnum<KzOrderType> {


    open("open", "开卡"),
    recharge("recharge", "卡充值"),
    close("close", "销卡"),
    withdrawal("withdrawal", "卡提现"),
    ;

    String value;
    String name;

    KzOrderType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public String getValue() {
        return this.value;
    }

    @Override
    public String getOrdinal() {
        return String.valueOf(this.ordinal());
    }
}
