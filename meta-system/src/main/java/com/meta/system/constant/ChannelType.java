package com.meta.system.constant;

import com.meta.common.enums.BaseEnum;

/**
 * <AUTHOR>
 * @date 2025/05/09/14:33
 */
public enum ChannelType implements BaseEnum<ChannelType> {

    MOONBANK("moonbank", "moonbank"),
    KUN("kun", "kun"),
    FORNAX("fornax", "fornax"),
    ;

    String value;
    String name;

    ChannelType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public String getValue() {
        return this.value;
    }


    @Override
    public String getOrdinal() {
        return String.valueOf(this.ordinal());
    }
}
