package com.meta.system.moonbank.util;

import com.alibaba.fastjson.JSONObject;
import com.meta.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/06/04/13:43
 */
@Component
public class EasyEuroUtil {

    @Autowired
    private ISysConfigService sysConfigService;
    private String appKey = "Adv1UvC7";
    private String appSecret = "Z2OTi2bRTFpGRzNozZ557Pbg8lCrhbkh";
    private String easyEuroUrl = "https://p2p-sandbox.easyeuro.eu:9091";
//    private String callbackI = "47.254.159.15";
//    private String MasterWalletId = "******************";
//    private String EURFundsAccountId = "******************";
//    private String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCi9/4odi65CwJerWnkWoLsX1XstPsmN0tkt5bWQ8CC6Zm0mWJLU82EEuv3xMFHGan7u9efkOt8D1iHwkXtV1ADOEJTcppLOXwvQX4DWaLN9dzt5fPzJ6W3ik3D34W+Grn7V0HX/A5Zo7+vta8FJtQ9699XCIRTKVW9H19I4n5/WQIDAQAB";

    public HttpHeaders getHttpHeaders() {
//        String appKey = sysConfigService.selectConfigByKey("easy_euro_appkey");
//        String appSecret = sysConfigService.selectConfigByKey("easy_euro_appSecret");
        HttpHeaders headers = new HttpHeaders();
        headers.add("AppKey", appKey);
        headers.add("AppSecret", appSecret);
        headers.add("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }

    /**
     * 获取Authentication
     */
    public String getToken() {
//        String easyEuroUrl = sysConfigService.selectConfigByKey("easy_euro_endPoint");
        String url = easyEuroUrl + "/authentication/token";
        HttpHeaders headers = getHttpHeaders();

        HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<String> responseEntity = new RestTemplate().exchange(url, HttpMethod.POST, httpEntity, String.class);

        HttpStatus statusCode = responseEntity.getStatusCode();
        if (statusCode.is2xxSuccessful()) {
            JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
            String accessToken = jsonObject.getString("accessToken");
            Integer expiresIn = jsonObject.getInteger("expiresIn");
            System.out.println(accessToken);
            System.out.println(expiresIn);
            return accessToken;
        }
        return null;
    }





    public static void main(String[] args) {
        EasyEuroUtil easyEuroUtil=new EasyEuroUtil();
        easyEuroUtil.getToken();
    }
}
