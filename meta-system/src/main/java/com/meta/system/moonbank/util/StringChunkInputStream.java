package com.meta.system.moonbank.util;


import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.nio.charset.CharsetEncoder;
import java.nio.charset.CoderResult;
/**
 * <AUTHOR>
 * @date 2025/04/23/20:43
 */
public class StringChunkInputStream extends InputStream {
    private final CharBuffer charBuffer;
    private final CharsetEncoder encoder;
    private ByteBuffer byteBuffer = ByteBuffer.allocate(4096);
    private boolean endOfInput = false;

    public StringChunkInputStream(String data, Charset charset) {
        this.charBuffer = CharBuffer.wrap(data);
        this.encoder = charset.newEncoder();
        this.byteBuffer.flip(); // 初始为空
    }

    @Override
    public int read() throws IOException {        if (!byteBuffer.hasRemaining()) {
        if (!fillBuffer()) {
            return -1;
        }
    }
        return byteBuffer.get() & 0xFF;
    }

    private boolean fillBuffer() throws IOException {
        if (endOfInput) return false;
        encoder.reset();
        byteBuffer.clear();
        CoderResult result = encoder.encode(charBuffer, byteBuffer, endOfInput);
        if (result.isOverflow()) {
            // byteBuffer已满，正常处理
        } else if (result.isUnderflow()) {
            endOfInput = true;
        } else if (result.isError()) {
            result.throwException();
        }
        byteBuffer.flip();
        return byteBuffer.hasRemaining();
    }
}
