package com.meta.system.domain.app;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.common.enums.app.KYCType;
import com.meta.common.serializer.LocalDateDeserializer;
import com.meta.common.serializer.LocalDateSerializer;
import com.meta.common.serializer.LocalDateTimeSerializer;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.request.IUpdate;
import com.meta.system.dto.kyc.Enterprise;
import com.meta.system.dto.kyc.Personal;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "meta_kyc_application")
public class KYCApplication {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    @NotNull(message = "主键不能为空",groups = {IUpdate.class})
    private Long id; // 自增主键


    @Column(name = "user_id", nullable = false)
    @NotNull(message = "用户ID不能为空",groups = {IUpdate.class})
    private Long userId; // 用户ID

    /**
     * {@link KYCType}
     */
    @Column(name = "kyc_type", nullable = false, length = 10)
    @NotEmpty(message = "kyc 认证类型不能为空",groups = {Personal.class, Enterprise.class})
    private String kycType; // kyc 认证类型

    @Column(name = "nick_name", length = 20)
    @NotEmpty(message = "申请人姓名不能为空",groups = {Personal.class})
    private String nickname; // 申请人姓名

    @Column(name = "ent_name", length = 50)
    @NotEmpty(message = "企业名称不能为空",groups = {Enterprise.class})
    private String entName; // 企业名称

    @Column(name = "cert_type",  length = 20)
    @NotNull(message = "证件类型不能为空",groups = {Personal.class})
    private String certType; // 证件类型


    @Column(name = "cert_no",  length = 20)
    @NotEmpty(message = "申请人身份证号码不能为空",groups = {Personal.class})
    private String certNo; // 申请人身份证号码


    @Column(name = "issue_date")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate issueDate; // 证件签发日期

    @Column(name = "expire_date")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate expireDate; // 证件到期日期

    @Column(name = "ent_register_number", length = 50)
    @NotEmpty(message = "企业注册号码不能为空",groups = {Enterprise.class})
    private String entRegisterNumber; // 企业注册号码

    @Column(name = "ent_register_date")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @NotNull(message = "企业注册日期不能为空",groups = {Enterprise.class})
    private LocalDate entRegisterDate; // 企业注册日期

    @Column(name = "ent_city", length = 50)
    @NotEmpty(message = "企业所在城市不能为空",groups = {Enterprise.class})
    private String entCity; // 企业所在城市

    @Column(name = "ent_address", length = 100)
    @NotEmpty(message = "企业详细地址不能为空",groups = {Enterprise.class})
    private String entAddress; // 企业详细地址

    /**
     * {@link com.meta.common.enums.ApprovalStatus}
     */
    @Column(name = "approval_status", nullable = false, length = 20)
    private String approvalStatus; // 待审核、已通过、拒绝等

    @Column(name = "created_at" )
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createdAt; // 创建时间

    @Column(name = "updated_at" )
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime updatedAt; // 更新时间

    @Column(name = "file_id", length = 64)
    @NotEmpty(message = "文件识别id不能为空",groups = {Personal.class, Enterprise.class})
    private String fileId; // 文件识别id

    @Column
    private String remark; //  备注


    // 去除空格
    public void trim() {
        this.kycType = StringUtils.isNotEmpty(this.kycType)?this.kycType.trim():null;
        this.nickname = StringUtils.isNotEmpty(this.nickname)?this.nickname.trim():null;
        this.entName = StringUtils.isNotEmpty(this.entName)?this.entName.trim():null;
        this.certType = StringUtils.isNotEmpty(this.certType)?this.certType.trim():null;
        this.certNo = StringUtils.isNotEmpty(this.certNo)?this.certNo.trim():null;
        this.entRegisterNumber = StringUtils.isNotEmpty(this.entRegisterNumber)?this.entRegisterNumber.trim():null;
        this.entCity = StringUtils.isNotEmpty(this.entCity)?this.entCity.trim():null;
        this.entAddress = StringUtils.isNotEmpty(this.entAddress)?this.entAddress.trim():null;
        this.approvalStatus = StringUtils.isNotEmpty(this.approvalStatus)?this.approvalStatus.trim():null;
        this.fileId = StringUtils.isNotEmpty(this.fileId)?this.fileId.trim():null;
        this.remark = StringUtils.isNotEmpty(this.remark)?this.remark.trim():null;
    }
}
