package com.meta.system.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "meta_credit_card_application")
@Data
@AllArgsConstructor
@NoArgsConstructor
@DynamicInsert
@DynamicUpdate
public class CreditCardApplication {
    @Id
    @Column(name = "request_no")
    private String requestNo;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "last_name")
    private String lastName;

//    @Column(name = "date_of_birth")
//    private Date dateOfBirth;
//
//    @Column(name = "country")
//    private String country;
//
//    @Column(name = "province")
//    private String province;
//
//    @Column(name = "address")
//    private String address;

//    @Column(name = "post_code")
//    private String postCode;

    @Column(name = "mobile")
    private String mobile;

    @Column(name = "email")
    private String email;

    @Column(name = "card_level")
    private String cardLevel;

    @Column(name = "card_type")
    private String cardType;

    @Column(name = "card_fee")
    private BigDecimal cardFee;

    @Column(name = "card_bin")
    private String cardBin;

    @Column(name = "card_alias")
    private String cardAlias;

    @Column(name = "card_id")
    private String cardId;

    @Column(name = "deposit_amount")
    private BigDecimal depositAmount;

    @Column(name = "status")
    private String status;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "moonbank_uid") // moonbank用户注册的uid
    private String moonbankUid;

    @Transient
    private String cardNo;

    @Transient
    private String currency;

}
