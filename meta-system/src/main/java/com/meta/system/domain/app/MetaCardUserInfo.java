package com.meta.system.domain.app;

import com.meta.system.domain.groupkey.CardUserInfoKey;
import lombok.Data;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2024/06/07/15:28
 */
@Entity
@Table(name = "meta_card_user_info")
@Data
public class MetaCardUserInfo implements  Cloneable{

    @EmbeddedId
    private CardUserInfoKey id;


    @Column(name = "user_id")
    private String userId;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "first_name_english")
    private String firstNameEnglish;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "last_name_english")
    private String lastNameEnglish;

    @Column(name = "date_of_birth")
    private String dateOfBirth;

    @Column(name = "nationality")
    private String nationality;

    @Column(name = "address_line1")
    private String addressLine1;


    @Column(name = "address_line2")
    private String addressLine2;


    @Column(name = "city")
    private String city;

    @Column(name = "state")
    private String state;

    @Column(name = "country_code")
    private String countryCode;

    @Column(name = "post_code")
    private String postCode;


    @Column(name = "identification_type")
    private String identificationType;

    @Column(name = "identification_number")
    private String identificationNumber;

    @Column(name = "identification_expiry_date")
    private String identificationExpiryDate;

    @Column(name = "visa_number")
    private String visaNumber;

    @Column(name = "visa_expiry_date")
    private String visaExpiryDate;


    @Column(name = "wallet_id")
    private String walletId;

    @Column(name = "kyc_statue")
    private String kycStatue;

    @Column(name = "kyc_country")
    private String kycCountry;

    @Column(name = "kyc_id_type")
    private String kycIdType;

    @Column(name = "kyc_reason")
    private String kycReason;

    @Column(name = "kyc_url")
    private String kycUrl;

    @Column(name = "file_id")
    private String fileId; // 文件识别id
    @Column(name = "visa_url")
    private String visaUrl; // moonbank 图片地址

    @Column(name = "front_img_file_id")
    private String frontImgFileId; // 照片前面id
    @Column(name = "back_img_file_id")
    private String backImgFileId; // 照片后面id
    @Column(name = "handheld_img_file_id")
    private String handheldImgFileId; // 手照片id

    @Column(name = "mobile_prefix_country")//手机前缀的国家代码
    private String mobilePrefixCountry;

    @Transient
    private boolean addImg;


    @Transient
    private String address;

    @Transient
    private String cityName;

    @Transient
    private String description;


    @Override
    public MetaCardUserInfo clone() {
        try {
            return (MetaCardUserInfo) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new RuntimeException(e);
        }
    }


}
