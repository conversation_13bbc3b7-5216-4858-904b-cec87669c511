package com.meta.system.domain.app;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.common.serializer.LocalDateTimeDeserializer;
import com.meta.common.serializer.LocalDateTimeSerializer;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * moonbank接口调用日志
 */
@Data
@Entity
@Table(name = "meta_fornax_log")
public class MetaFornaxLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 请求编号
     */
    @Column(name = "request_no", nullable = false)
    private String requestNo;

    /**
     * 请求体
     */
    @Column(name = "request", nullable = false)
    private String request;

    /**
     * 返回体
     */
    @Column(name = "response")
    private String response;

    /**
     * apicode
     */
    @Column(name = "apicode")
    private String apicode;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createdAt;

    @Column(name = "code")
    private String code;

    @Column(name = "detail")
    private String detail;

    /**
     * 卡id
     */
    @Column(name = "card_id")
    private String cardId;

}
