package com.meta.system.domain.app;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/03/08/16:29
 */
@Entity
@Table(name = "meta_card_country")
@Data
public class MetaCardCountry implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 地区代码
     */
    @Column(name = "code")
    private String code;
    /**
     * 英文名称
     */
    @Column(name = "name")
    private String name;
    /**
     * 中文名称
     */
    @Column(name = "cname")
    private String cname;
    /**
     * 中文繁体
     */
    @Column(name = "tcname")
    private String tcname;
    /**
     * 物流费
     */
    @Column(name = "logistics_fee")
    private BigDecimal logisticsFee;

    /**
     * 物流开发
     */
    @Column(name = "open")
    private String open;
}
