package com.meta.system.domain.partner;


import com.meta.system.domain.groupkey.PartnerKey;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Entity
@Table(name = "meta_partner_asset_pool")
@Data
//@IdClass(MetaPartnerAssetPoolKey.class)
//@Accessors(chain = true)
public class MetaPartnerAssetPool implements Serializable {
//    /**
//     * 用户id
//     */
//    @Id
//    @Column(name = "partner_id")
//    @NotNull(message = "合伙人ID不能为空", groups = {IDelete.class, IUpdate.class})
//    private long partnerId;
//    /**
//     * 数字货币代码(USDT)
//     */
//    @Id
//    @Column(name = "coin_code")
//    @NotNull(message = "币种不能为空", groups = {IDelete.class, IUpdate.class})
//    private String coinCode;

    @EmbeddedId
    private PartnerKey id;

    /**
     *系统编号
     */
    @Column(name = "sys_id")
    private String sysId;




    /**
     *合伙人类型(MD10=>DAC)
     */
    @Column(name = "type")
    private String type;

    /**
     * USDT账户余额
     */
    @Column(name = "usdt_balacne")
    private BigDecimal usdtBalacne;
    /**
     * USDT账户冻结余额
     */
    @Column(name = "freeze_usdt_balacne")
    private BigDecimal freezeUstdBalacne;
    /**
     * 处理中金额
     */
    @Column(name = "processing_amt")
    private BigDecimal processingAmt;
    /**
     * 剩余卡片数
     */
    @Column(name = "card_active_num")
    private long cardActiveNum;
    /**
     * 已使用卡片数
     */
    @Column(name = "card_active_used")
    private long cardActiveUsed;
    /**
     * 卡片池子额度
     */
    @Column(name = "card_pool_limit")
    private long cardPoolLimit;
    /**
     * 卡片数量不足告警设定金额
     */
    @Column(name = "alarm_insufficient_crdnum")
    private long alarmInsufficientCrdnum;
    /**
     * 卡片数量不足告警设定比例
     */
    @Column(name = "alarm_insufficient_crdratio")
    private BigDecimal alarmInsufficientCrdratio;
    /**
     * 资金池子额度
     */
    @Column(name = "usdt_pool_limit")
    private BigDecimal usdtPoolLimit;
    /**
     * 余额不足告警设定金额
     */
    @Column(name = "alarm_insufficient_amt")
    private BigDecimal alarmInsufficientAmt;
    /**
     * 余额不足告警设定比例
     */
    @Column(name = "alarm_insufficient_ratio")
    private BigDecimal alarmInsufficientRatio;
    /**
     * 最大余额限制
     */
    @Column(name = "max_deposit_amt")
    private BigDecimal maxDepositAmt;
    /**
     * 最低余额限制
     */
    @Column(name = "min_deposit_amt")
    private BigDecimal minDepositAmt;
    /**
     * 单笔限额(0无限制)
     */
    @Column(name = "txn_single_limit")
    private BigDecimal txnSingleLimit;
    /**
     * 单日限额(0无限制)
     */
    @Column(name = "txn_day_limit")
    private BigDecimal txnDayLimit;
    /**
     * 单月限额(0无限制)
     */
    @Column(name = "txn_month_limit")
    private BigDecimal txnMonthLimit;
    /**
     * 年度限额(0无限制)
     */
    @Column(name = "txn_year_limit")
    private BigDecimal txnYearLimit;
    /**
     * 账户状态(00正常, FZ冻结)
     */
    @Column(name = "status")
    private String status;
    /**
     * 创建者
     */
    @Column(name = "create_by")
    private String createBy;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
    /**
     * 更新者
     */
    @Column(name = "update_by")
    private String updateBy;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 余额告警基数
     */
    @Column(name = "alarm_cardinality_amt")
    private BigDecimal alarmCardinalityAmt;

    /**
     * 自动/手动
     */
    @Column(name = "auto")
    private Character auto;

    /**
     * 充值低于金额不入池子
     */
    @Column(name = "auto_amt")
    private BigDecimal autoAmt;

    /**
     * 消息接收地址
     */
    @Column(name = "api_url")
    private String apiUrl;
    /**
     * 余额
     */
    @Transient
    private BigDecimal balance;


}


