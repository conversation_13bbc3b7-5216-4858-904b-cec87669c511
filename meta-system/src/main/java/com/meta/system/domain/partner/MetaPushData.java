package com.meta.system.domain.partner;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/08/22/14:25
 */
@Entity
@Table(name = "meta_push_data")
@Data
public class MetaPushData implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "sys_id")
    private String sysId;

    @Column(name ="request_no" )
    private String requestNo;

    @Column(name = "card_id")
    private String cardId;

    @Column(name = "user_email")
    private String userEmail;

    @Column(name = "api_url")
    private String apiUrl; //请求地址

    @Column(name = "request")
    private String request; // 请求体

    @Column(name = "statue")
    private String statue; //状态

    @Column(name = "response")
    private String response; // 返回体

    @Column(name = "num")
    private int num;//发送次数

    @Column(name = "create_time")
    private Date createTime; // 创建时间

    @Column(name = "api_code")
    private String apiCode; //类型

}
