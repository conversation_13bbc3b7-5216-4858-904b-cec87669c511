package com.meta.system.domain.app;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

@Entity
@Table(name = "meta_bep20_cstaddressinfo")
@AllArgsConstructor
@NoArgsConstructor
@DynamicInsert
@DynamicUpdate
public class BepCstaddress extends AbstractCstaddress{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "cst_id")
    private Long cstId;
    @Column(name = "cst_address")
    private String cstAdress;
    @Column(name = "cst_trc20private")
    private String cstTrcPrivate;
    @Column(name = "cst_hexaddress")
    private String cstHexaddress;
    @Column(name = "sys_id")
    private String sysId;

    /**
     * 二维码
     */
    @Transient
    private String qrcode;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public Long getCstId() {
        return cstId;
    }

    @Override
    public void setCstId(Long cstId) {
        this.cstId = cstId;
    }

    @Override
    public String getCstAdress() {
        return cstAdress;
    }

    @Override
    public void setCstAdress(String cstAdress) {
        this.cstAdress = cstAdress;
    }

    @Override
    public String getCstTrcPrivate() {
        return cstTrcPrivate;
    }

    @Override
    public void setCstTrcPrivate(String cstTrcPrivate) {
        this.cstTrcPrivate = cstTrcPrivate;
    }

    @Override
    public String getCstHexaddress() {
        return cstHexaddress;
    }

    @Override
    public void setCstHexaddress(String cstHexaddress) {
        this.cstHexaddress = cstHexaddress;
    }

    @Override
    public String getSysId() {
        return sysId;
    }

    @Override
    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

}
