package com.meta.system.domain.app;
/**
 * <AUTHOR>
 * @Date 2025/2/17
 */

import lombok.Data;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;


@Data
@Entity
@DynamicUpdate
@Table(name = "meta_exchange_order_info")
public class MetaExchangeOrderInfo {
    /**
     * 商户订单ID
     */
    @Id
    @Column(name = "merchant_order_id", nullable = false)
    private String merchantOrderId;
    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;
    /**
     * 交易方式
     * BUY、SELL
     */
    @Column(name = "side")
    private String side;
    /**
     * 加密货币
     */
    @Column(name = "crypto_currency")
    private String cryptoCurrency;
    /**
     * 法定货币
     */
    @Column(name = "fiat_currency")
    private String fiatCurrency;
    /**
     * 订单状态
     */
    @Column(name = "order_status")
    private String orderStatus;
    /**
     * 单价
     */
    @Column(name = "quote_price")
    private String quotePrice;
    /**
     * 用户支付的法定货币金额
     */
    @Column(name = "fiat_amount")
    private String fiatAmount;
    /**
     * 用户购买的加密货币金额
     */
    @Column(name = "crypto_amount")
    private String cryptoAmount;
    /**
     * 法定手续费（银行手续费）
     */
    @Column(name = "fiat_fee")
    private String fiatFee;
    /**
     * 交易手续费（平台收）
     */
    @Column(name = "trade_fee")
    private String tradeFee;
    /**
     * 商户手续费
     */
    @Column(name = "merchant_fee")
    private String merchantFee;
    /**
     * 网络手续费
     */
    @Column(name = "network_fee")
    private String networkFee;
    /**
     * 总手续费
     */
    @Column(name = "total")
    private String total;
    /**
     * 用户支付的方式
     */
    @Column(name = "method")
    private String method;
    /**
     * 用户支付的账号
     */
    @Column(name = "account_number")
    private String accountNumber;
    /**
     * 链上交易 ID
     */
    @Column(name = "tx_id")
    private String txId;
    /**
     * 失败代码
     */
    @Column(name = "fail_code")
    private String failCode;
    /**
     * 失败原因
     */
    @Column(name = "fail_reason")
    private String failReason;
    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private String createdAt;
    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private String updatedAt;
    /**
     * 总报价金额
     */
    @Column(name = "total_quote_amount")
    private String totalQuoteAmount;
    /**
     * 下单时间
     */
    @Column(name = "order_time")
    private String orderTime;
    /**
     * 支付时间
     */
    @Column(name = "pay_time")
    private String payTime;
    /**
     * 成交时间
     */
    @Column(name = "deal_time")
    private String dealTime;
    /**
     * 删除标识
     */
    @Column(name = "del_flag")
    private String delFlag;
    /**
     * 平台
     */
    @Column(name = "sys_id")
    private String sysId;
    /**
     * 付款地址
     */
    @Column(name = "checkout_url")
    private String checkoutUrl;
    /**
     * 网络
     */
    @Column(name = "network")
    private String network;
    /**
     * 订单状态详情
     */
    @Transient
    private String orderStatusDetail;


}
