package com.meta.system.domain.app;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 优惠券信息
 */
@Data
@Entity
@Table(name = "meta_coupon_info")
public class MetaCouponInfo  {

    /**
     * 主键
     */
    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "coupon_gen_cfg_id")
    private Integer couponGenCfgId;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 优惠券管理用户ID
     */
    @Column(name = "adm_user_id", nullable = false)
    private Long admUserId;

    /**
     * 优惠券号码
     */
    @Column(name = "coupon_nm", nullable = false)
    private String couponNm;

    /**
     * D-折扣券 M-满减券
     */
    @Column(name = "coupon_type", nullable = false)
    private String couponType;

    /**
     * NEW-开卡、TOPUP卡充值、NODE-节点购买
     */
    @Column(name = "coupon_scenarios")
    private String couponScenarios;

    /**
     * 可使用卡类型
     */
    @Column(name = "coupon_card_type")
    private String couponCardType;

    /**
     * 可使用卡等级产品
     */
    @Column(name = "coupon_cardlevel")
    private String couponCardlevel;

    /**
     * 可使用节点产品
     */
    @Column(name = "coupon_node")
    private String couponNode;

    /**
     * 折扣货币代码
     */
    @Column(name = "discount_coin")
    private String discountCoin;

    /**
     * 折扣比例
     */
    @Column(name = "discount_rate")
    private BigDecimal discountRate;

    /**
     * 折扣金额
     */
    @Column(name = "discount_amt")
    private BigDecimal discountAmt;

    /**
     * 最高抵扣金额
     */
    @Column(name = "max_discount_amt")
    private BigDecimal maxDiscountAmt;

    /**
     * 使用最低消费金额
     */
    @Column(name = "min_spend_amt")
    private BigDecimal minSpendAmt;

    /**
     * 生效日期
     */
    @Column(name = "eft_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date eftDate;

    /**
     * 失效日期
     */
    @Column(name = "exp_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expDate;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time", nullable = false)
    private Date createTime;

    /**
     * 最后更新时间
     */
    @Column(name = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 使用时间
     */
    @Column(name = "used_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date usedTime;

    /**
     * 使用用户id
     */
    @Column(name = "use_user_id")
    private Long useUserId;

    /**
     * 状态（0有效，1已使用, 2核销，3过期）
     */
    @Column(name = "status", nullable = false)
    private String status;

    /**
     * 优惠券使用次数限制(0-无限使用)
     */
    @Column(name = "coupon_usage_limit", nullable = false)
    private Integer couponUsageLimit;

    /**
     * 优惠券使用次数
     */
    @Column(name = "coupon_usage_cnt", nullable = false)
    private Integer couponUsageCnt;

    /**
     * 来源
     */
    @Column(name = "source")
    private String source;

    /**
     * 场景合集
     */
    @Transient
    private String[] couponScenariosList;

    /**
     * 可类型集合
     */
    @Transient
    private String[] couponCardTypeList;

    /**
     * 卡级别
     */
    @Transient
    private String[] couponCardlevelList;

    /**
     * 节点集合
     */
    @Transient
    private String[] couponNodeList;

    @Transient
    private String admUserName;

    @Transient
    private String userName;

    @Transient
    private  String useUserName;

    @Transient
    private String typeDetail;//类型

    @Transient
    private String scenariosDetail;//用途

    @Transient
    private String statusDetail;//状态

    /**
     * 可类型集合(tg)
     */
    @Transient
    private List cardTypeList;

    /**
     * 卡级别(tg)
     */
    @Transient
    private List cardlevelList;

    /**
     * 节点集合(tg)
     */
    @Transient
    private List nodeList;


}
