package com.meta.system.domain.apnt;

import lombok.Data;

import javax.persistence.*;
import java.math.BigInteger;
import java.util.Date;

/**
 * 用户积分记录对象
 */
@Entity
@Table(name = "tb_apnt_record")
@Data
public class TbApntRecord {


    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 积分记录类型
     */
    @Column(name = "type")
    private Long type;

    /**
     * 原始记录id
     */
    @Column(name = "original_id")
    private String originalId;

    /**
     * 积分值
     */
    @Column(name = "value")
    private BigInteger value;

    /**
     * 过期时间
     */
    @Column(name = "expire_time")
    private Date expireTime;

    /**
     * 积分增减原因
     */
    @Column(name = "reason")
    private String reason;

    /**
     * 客户ID
     */
    @Column(name = "cst_id")
    private long cstId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
