package com.meta.system.domain.kun;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.common.serializer.LocalDateTimeDeserializer;
import com.meta.common.serializer.LocalDateTimeSerializer;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/10/24/19:13
 */
@Entity
@Table(name = "meta_kun_log")
@Data
public class MetaKunLog implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "request_no")
    private String requestNo;

    @Column(name = "detail")
    private String detail;

    @Column(name = "code")
    private String code;

    @Column(name = "request")
    private String request;

    @Column(name = "response")
    private String response;

    @Column(name = "apicode")
    private String apicode;

    @Column(name = "card_id")
    private String cardId;


    @Column(name = "created_at")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createdAt;


}

