package com.meta.system.domain.app;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/07/09/17:22
 */
@Entity
@Table(name = "meta_user_favorite_address")
@Data
public class MetaUserFavoriteAddress {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "user_id")
    private Long userId;


    @Column(name = "type")
    private String type;

    @Column(name = "address")
    private String address;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private Date createTime;

}
