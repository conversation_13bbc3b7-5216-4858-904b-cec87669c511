package com.meta.system.domain.app;

import lombok.Data;

import javax.persistence.*;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @Date 2025/3/7
 */

@Entity
@Table(name = "meta_exchange_order_info_alixpay")
@Data
public class MetaExchangeOrderInfoAlixpay {
    /**
     * 订单ID
     */
    @Id
    @Column(name = "merchant_order_id", nullable = false)
    private String merchantOrderId;
    /**
     * 类型（SELL OR BUY）
     */
    @Column(name = "type")
    private String type;
    /**
     * 法定货币
     */
    @Column(name = "fiat_currency")
    private String fiatCurrency;
    /**
     * 合作伙伴支付给商家账户的金额
     */
    @Column(name = "paid_amount")
    private String paidAmount;
    /**
     * 卖出的加密货币币种 = 用户支付的加密货币币种
     */
    @Column(name = "currency")
    private String currency;
    /**
     * 单价 1 加密货币 = xx 法定货币
     */
    @Column(name = "price")
    private String price;
    /**
     * 账户持有人
     */
    @Column(name = "bank_account_name")
    private String bankAccountName;
    /**
     * 银行账户号码
     */
    @Column(name = "bank_account_number")
    private String bankAccountNumber;
    /**
     * 银行名称
     */
    @Column(name = "bank_name")
    private String bankName;
    /**
     * 银行代码
     */
    @Column(name = "bank_code")
    private String bankCode;
    /**
     * 付款备注信息
     */
    @Column(name = "content_payment")
    private String contentPayment;
    /**
     * 状态
     */
    @Column(name = "status")
    private String status;
    /**
     * 状态描述
     */
    @Column(name = "descriptions")
    private String descriptions;
    /**
     * 下单时间
     */
    @Column(name = "order_time")
    private String orderTime;
    /**
     * 成交时间
     */
    @Column(name = "deal_time")
    private String dealTime;
    /**
     * 平台
     */
    @Column(name = "sys_id")
    private String sysId;
    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;
    /**
     * 商户交易手续费 (加密货币金额)
     */
    @Column(name = "merchant_processing_fee")
    private String merchantProcessingFee;
    /**
     * 商户收取的交易费的加密货币类型
     */
    @Column(name = "merchant_coin_code")
    private String merchantCoinCode;
    /**
     * 总交易费
     */
    @Column(name = "total_processing_fee")
    private String totalProcessingFee;
    /**
     * 用户总支付的加密货币金额（含合作伙伴和商户的交易手续费）
     */
    @Column(name = "total_payment_amount")
    private String totalPaymentAmount;
    /**
     * 合作伙伴交易手续费 (加密货币金额)
     */
    @Column(name = "partner_processing_fee")
    private String partnerProcessingFee;
    /**
     * 合作伙伴
     */
    @Column(name = "partner_id")
    private String partnerId;
    /**
     * 交易类型
     */
    @Column(name = "tx_type")
    private String txType;
    /**
     * 退款金额
     */
    @Column(name = "refund_amount")
    private String refundAmount;
    /**
     * 状态详情
     */
    @Transient
    private String statusDetail;
}
