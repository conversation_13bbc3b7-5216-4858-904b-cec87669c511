package com.meta.system.domain.app;

import lombok.Data;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2024/09/03/16:32
 */
@Entity
@Table(name = "meta_card_secnario_detail")
@Data
public class MetaCardScenarioDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;


    @Column(name = "type")
    private String type;

    @Column(name = "secnairo_id")
    private long secnairoId;

    @Column(name = "valid_flag")
    private String validFlag;

    @Column(name = "card_type")
    private String cardType;


}
