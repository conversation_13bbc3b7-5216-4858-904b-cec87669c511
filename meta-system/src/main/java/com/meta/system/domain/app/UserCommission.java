package com.meta.system.domain.app;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.common.serializer.LocalDateTimeDeserializer;
import com.meta.common.serializer.LocalDateTimeSerializer;
import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 佣金表
 */
@Entity
@Table(name = "meta_user_commission")
@Data
public class UserCommission {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id; // 主键id

    @Column(name = "user_id", nullable = false)
    private Long userId; // 用户id

    @Column(name = "user_name", nullable = false)
    private String username; // 用户邮箱

    @Column(name = "commision_type", nullable = false)
    private String commissionType; // 佣金类型:1-开卡返佣,2-充值返佣，3-节点升级（usd）

    @Column(name = "commision_fee_rebate", nullable = false)
    private BigDecimal commissionFeeRebate; // 佣金费率

    @Column(name = "commision_amount", nullable = false)
    private BigDecimal commissionAmount; // 佣金：单位默认usd

    @Column(name = "commision_desc")
    private String commissionDesc; // 佣金描述

    @Column(name = "txn_id")
    private Long txnId; // 交易流水ID

    @Column(name = "txn_currency")
    private String txnCurrency; // 佣金描述


    @Column(name = "create_time", nullable = false)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTime; // 创建时间
}
