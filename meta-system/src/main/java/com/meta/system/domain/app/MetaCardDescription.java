package com.meta.system.domain.app;

import lombok.Data;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2024/07/12/11:28
 */
@Entity
@Table(name = "meta_card_description")
@Data
public class MetaCardDescription {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "card_type")
    private String cardType;

    @Column(name = "support")
    private String support;

    @Column(name = "support_tw")
    private String supportTw;

    @Column(name = "support_en")
    private String supportEn;

    @Column(name = "support_content")
    private String supportContent;

    @Column(name = "support_content_tw")
    private String supportContentTw;

    @Column(name = "support_content_en")
    private String supportContentEn;

    @Column(name = "applicable_scene")
    private String applicableScene;

    @Column(name = "applicable_scene_tw")
    private String applicableSceneTw;

    @Column(name = "applicable_scene_en")
    private String applicableSceneEn;

    @Column(name = "scene_note")
    private String sceneNote;

    @Column(name = "scene_note_tw")
    private String sceneNoteTw;

    @Column(name = "scene_note_en")
    private String sceneNoteEn;

    @Column(name = "high_risk_scene")
    private String highRiskScene;

    @Column(name = "high_risk_scene_tw")
    private String highRiskSceneTw;

    @Column(name = "high_risk_scene_en")
    private String highRiskSceneEn;

}
