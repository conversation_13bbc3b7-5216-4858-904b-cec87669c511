package com.meta.system.domain.app;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户渠道绑定表
 */
@Data
@Entity
@Table(name = "meta_user_channel_binding")
public class MetaUserChannelBinding implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 渠道名称
     */
    @Column(name = "channel", nullable = false)
    private String channel;

    /**
     * 渠道用户唯一ID
     */
    @Column(name = "channel_user_id", nullable = false)
    private String channelUserId;

    /**
     * 渠道登录名
     */
    @Column(name = "channel_user_name")
    private String channelUserName;

    /**
     * 系统用户ID
     */
    @Column(name = "sys_user_id")
    private Long sysUserId;

    /**
     * 语言
     */
    @Column(name = "language_code")
    private String languageCode;

    /**
     * 头像地址
     */
    @Column(name = "photo_url")
    private String photoUrl;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 绑定时间，默认当前时间
     */
    @Column(name = "bind_time")
    private Date bindTime;

    /**
     * 绑定状态（1：已绑定，0：未绑定/已解绑）
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 邀请码
     */
    @Column(name = "invite_code")
    private String inviteCode;

}
