package com.meta.system.domain.apnt;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 * 用户积分类型对象 tb_apnt_type
 *
 * <AUTHOR>
 * @date 2021-12-13
 */
@Entity
@Table(name = "tb_apnt_type")
@Data
public class TbApntType implements Serializable {


    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 类型
     */
    @Column(name = "type")
    private String type;
    /**
     * 积分类型名
     */
    @Column(name = "name")
    private String name;

    /**
     * 积分类型描述
     */
    @Column(name = "explain_detail")
    private String explainDetail;

    /**
     * 方向1-新增，2-扣减
     */
    @Column(name = "direct")
    private Integer direct;

    /**
     * 每天限制次数
     */
    @Column(name = "limit_count")
    private BigInteger limitCount;

    /**
     * 单次赚取/使用积分
     */
    @Column(name = "value")
    private BigInteger value;

    /**
     * 单次赚取/使用积分比例
     */
    @Column(name = "value_ratio")
    private BigDecimal valueRatio;

    /**
     * 单次最小使用积分
     */
    @Column(name = "min_value")
    private BigInteger minValue;

    /**
     * 单次最小使用比例
     */
    @Column(name = "min_ratio")
    private BigDecimal minRatio;


    /**
     * 单次最大积分
     */
    @Column(name = "max_value")
    private BigInteger maxValue;

    /**
     * 单次最大使用比例
     */
    @Column(name = "max_ratio")
    private BigDecimal maxRatio;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date updateTime;


    public void update(TbApntType tbApntType) {
        this.type = tbApntType.getType();
        this.name = tbApntType.getName();
        this.explainDetail = tbApntType.getExplainDetail();
        this.direct = tbApntType.getDirect();
        this.limitCount = tbApntType.getLimitCount();
        this.value = tbApntType.getValue();
        this.valueRatio = tbApntType.getValueRatio();
        this.minValue = tbApntType.getMinValue();
        this.minRatio = tbApntType.getMinRatio();
        this.maxValue = tbApntType.getMaxValue();
        this.maxRatio = tbApntType.getMaxRatio();


    }
}
