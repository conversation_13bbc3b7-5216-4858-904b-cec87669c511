package com.meta.system.domain.app;

import lombok.Data;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2024/09/03/16:32
 */
@Entity
@Table(name = "meta_card_scenario")
@Data
public class MetaCardScenario {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "pic_name")
    private String picName;
}
