package com.meta.system.domain.partner;

import lombok.Data;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2024/05/30/14:31
 */
@Entity
@Table(name = "meta_partner_user")
@Data
public class MetaPartnerUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "sys_id")
    private String sysId;

    @Column(name = "email")
    private String email;

    @Column(name = "mobile_prefix")
    private String mobilePrefix;

    @Column(name = "mobile_number")
    private String mobileNumber;

    @Column(name = "uid")
    private String uid;


}
