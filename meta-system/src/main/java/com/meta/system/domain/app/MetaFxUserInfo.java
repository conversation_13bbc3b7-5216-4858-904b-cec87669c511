package com.meta.system.domain.app;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "meta_fornax_user_info")
public class MetaFxUserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 持卡人名字
     */
    @Column(name = "first_name", nullable = false)
    private String firstName;

    /**
     * 持卡人姓名中的姓
     */
    @Column(name = "last_name", nullable = false)
    private String lastName;

    /**
     * 邮箱
     */
    @Column(name = "email", nullable = false)
    private String email;

    /**
     * 手机号前缀
     */
    @Column(name = "mobile_prefix", nullable = false)
    private String mobilePrefix;

    /**
     * 手机
     */
    @Column(name = "mobile", nullable = false)
    private String mobile;

    /**
     * 出生日期
     */
    @Column(name = "birth_date", nullable = false)
    private String birthDate;

    /**
     * 账单国家代 码
     */
    @Column(name = "country_code", nullable = false)
    private String countryCode;

    /**
     * 账单省/州
     */
    @Column(name = "billing_state", nullable = false)
    private String billingState;

    /**
     * 账单城市
     */
    @Column(name = "billing_city", nullable = false)
    private String billingCity;

    /**
     * 账单地址
     */
    @Column(name = "billing_address", nullable = false)
    private String billingAddress;

    /**
     * 账单邮编
     */
    @Column(name = "billing_zip_code", nullable = false)
    private String billingZipCode;

    /**
     * 在fornax系统用户id
     */
    @Column(name = "fid")
    private String fid;

}
