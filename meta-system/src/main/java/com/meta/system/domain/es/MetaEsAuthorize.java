package com.meta.system.domain.es;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "meta_es_authorize")
@ToString
public class MetaEsAuthorize {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id; // 主键id

    @Column(name = "sys_id")
    private String sysId; // 系统编号：partnerid

    @Column(name = "apicode")
    private String apiCode; // 类型：接口的apicode

    @Column(name = "public_key")
    private String publicKey; // 公钥

    @Column(name = "verision")
    private String version; // 版本号

    @Column(name = "create_time")
    private LocalDateTime createTime; // 创建时间

}
