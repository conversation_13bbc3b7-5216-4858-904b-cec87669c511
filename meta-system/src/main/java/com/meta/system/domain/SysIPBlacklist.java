package com.meta.system.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.common.serializer.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "sys_ip_blacklist")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SysIPBlacklist {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "ip_address")
    private String ipAddress;

    @Column(name = "reason")
    private String reason;

    @Column(name = "created_time",updatable = false)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createdTime;

    @Column(name = "updated_time")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime updatedTime;

    public SysIPBlacklist(String ipAddress,String reason) {
        this.ipAddress = ipAddress;
        this.reason = reason;
    }
}