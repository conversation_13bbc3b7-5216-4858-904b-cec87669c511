package com.meta.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/21/15:43
 */
@Data
@Entity
@Table(name = "meta_wallet_risk_info")
@AllArgsConstructor
@NoArgsConstructor
public class WalletRiskInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    /**
     * 钱包地址
     */
    @Column(name = "wallet_address")
    private String walletAddress;
    /**
     * 钱包地址标签
     */
    @Column(name = "address_labels")
    private String addressLabels;
    /**
     * 风险评级(Severe:91 - 100,High:71 - 90,Moderate:31 - 70,Low:0 - 30)
     */
    @Column(name = "risk_level")
    private String riskLevel;
    /**
     * 风险评分
     */
    @Column(name = "risk_score")
    private BigDecimal riskScore;
    /**
     * 币种
     */
    @Column(name = "coin")
    private String coin;
    /**
     * 网络
     */
    @Column(name = "coin_net")
    private String coinNet;
    /**
     * 数据查询平台
     */
    @Column(name = "data_platform")
    private String dataPlatform;
    /**
     * 数据查询日期
     */
    @Column(name = "data_dt")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date dataDt;
    /**
     * 记录日期
     */
    @Column(name = "create_tm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTm;

    @Transient
    private String fromaddress;

    public void update(WalletRiskInfo walletRiskInfo) {
        this.walletAddress = walletRiskInfo.getWalletAddress();
        this.addressLabels = walletRiskInfo.getAddressLabels();
        this.riskLevel = walletRiskInfo.getRiskLevel();
        this.riskScore = walletRiskInfo.getRiskScore();
        this.coinNet = walletRiskInfo.getCoinNet();
        this.coin = walletRiskInfo.getCoin();
        this.dataPlatform = walletRiskInfo.getDataPlatform();


    }
}
