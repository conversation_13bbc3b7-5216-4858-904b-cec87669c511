package com.meta.system.domain.app;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@Table(name = "meta_coin_mapping")
public class MetaCoinMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 币种
     */
    @Id
    @Column(name = "coin", nullable = false)
    private String coin;

    /**
     * 数字编码
     */
    @Column(name = "code", nullable = false)
    private String code;

}
