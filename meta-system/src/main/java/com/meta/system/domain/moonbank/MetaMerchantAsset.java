package com.meta.system.domain.moonbank;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/25/11:43
 */
@Entity
@Table(name = "meta_merchant_asset")
@Data
public class MetaMerchantAsset implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 货币
     */
    @Column(name = "currency")
    private String currency;
    /**
     * 可用账户金额
     */
    @Column(name = "available_amount")
    private BigDecimal availableAmount;

    /**
     * 账户冻结金额
     */
    @Column(name = "frozen_amount")
    private BigDecimal frozenAmount;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
}
