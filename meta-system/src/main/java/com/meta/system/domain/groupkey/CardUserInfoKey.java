package com.meta.system.domain.groupkey;

import lombok.Data;

import javax.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/14 19:54
 */
@Embeddable
@Data
public class CardUserInfo<PERSON>ey implements Serializable {

    private String uid;

    private String cardType;

    // 需要一个无参构造函数
    public CardUserInfoKey() {
    }

    public CardUserInfoKey(String uid, String cardType) {
        this.uid = uid;
        this.cardType = cardType;
    }

    // getters and setters

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof CardUserInfoKey)) return false;
        CardUserInfoKey that = (CardUserInfoKey) o;
        return Objects.equals(uid, that.uid) &&
                Objects.equals(cardType, that.cardType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(uid, cardType);
    }
}
