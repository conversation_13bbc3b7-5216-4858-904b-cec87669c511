package com.meta.system.domain.es;


import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class RespHead {

    private String sysId;//请求头原样返回系统ID标识
    private String apiCode;//请求头原样返回接⼝名称
    private String requestNo;//请求头原样返回请求流⽔号
    private String version;//请求头原样返回版本号
    private String code; //返回码
    private String detail; //描述
    private String sign;//响应报⽂签名
    private String keyEnc;//会话密钥密⽂

    public RespHead getRespHead(String code, String detail, String sign, String keyEnc, Head head){
        return new RespHead().setCode(code)
                .setDetail(detail)
                .setSysId(head.getSysId())
                .setApiCode(head.getApiCode())
                .setRequestNo(head.getRequestNo())
                .setVersion(head.getVersion())
                .setSign(sign)
                .setKeyEnc(keyEnc);
    }

}
