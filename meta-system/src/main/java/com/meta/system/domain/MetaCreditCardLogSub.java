package com.meta.system.domain;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 卡操作日志表
 */
@Data
@Entity
@Table(name = "meta_credit_card_log_sub")
public class MetaCreditCardLogSub implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "card_id", nullable = false)
    private String cardId;

    /**
     * 交易id
     */
    @Column(name = "transaction_id")
    private String transactionId;

    /**
     * 子交易id
     */
    @Column(name = "transaction_id_sub")
    private String transactionIdSub;

    /**
     * 交易时间
     */
    @Column(name = "transaction_date")
    private String transactionDate;

    /**
     * 交易币种:货币三位代码
     */
    @Column(name = "transaction_currency")
    private String transactionCurrency;

    /**
     * 交易金额
     */
    @Column(name = "transaction_amount")
    private BigDecimal transactionAmount;

    /**
     * 卡币种:货币三位代码
     */
    @Column(name = "card_currency")
    private String cardCurrency;

    /**
     * 卡交易金额
     */
    @Column(name = "card_transaction_amount")
    private BigDecimal cardTransactionAmount;

    /**
     * 手续费金额
     */
    @Column(name = "fee")
    private BigDecimal fee;

    /**
     * 手续费币种
     */
    @Column(name = "fee_currency")
    private String feeCurrency;

    /**
     * 交易类型
     */
    @Column(name = "transaction_type")
    private String transactionType;

    /**
     * 交易状态
     */
    @Column(name = "transaction_status")
    private String transactionStatus;
    @Transient
    private String transactionTypeDetail;
    @Transient
    private String transactionStatusDetail;

    @Transient
    private BigDecimal refundAmount;

}
