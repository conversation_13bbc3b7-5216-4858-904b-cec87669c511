package com.meta.system.domain.app;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.common.serializer.LocalDateTimeDeserializer;
import com.meta.common.serializer.LocalDateTimeSerializer;
import com.meta.common.utils.request.IDelete;
import com.meta.common.utils.request.IInsert;
import com.meta.common.utils.request.IUpdate;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 汇率实时转换表
 */
@Data
@Entity
@Table(name = "meta_exchange_rate")
public class ExchangeRate {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "exchange_id")
    @NotNull(message = "主键ID不能为空", groups = {IDelete.class, IUpdate.class})
    private Integer exchangeId; // 主键ID

    @Column(name = "from_currency_code", nullable = false)
    @NotEmpty(message = "本币币种编码不能为空",groups = {IInsert.class,IUpdate.class})
    private String fromCurrencyCode; // 本币币种编码

    @Column(name = "to_currency_code", nullable = false)
    @NotEmpty(message = "兑换币种编码不能为空",groups = {IInsert.class,IUpdate.class})
    private String toCurrencyCode; // 兑换币种编码

    @Column(name = "rate_val", nullable = false, precision = 18, scale = 8)
    @NotNull(message = "兑换汇率值不能为空",groups = {IInsert.class,IUpdate.class})
    private BigDecimal rateVal; // 兑换汇率值

    @Column(name = "start_time", nullable = false)
    @NotNull(message = "开始时间不能为空",groups = {IInsert.class,IUpdate.class})
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startTime; // 开始时间

    @Column(name = "end_time", nullable = false)
    @NotNull(message = "结束时间不能为空",groups = {IInsert.class,IUpdate.class})
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endTime; // 结束时间

    @Column(name = "create_by", nullable = false, length = 64)
    private String createdBy; // 创建者

    @Column(name = "create_time", nullable = false)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTime; // 创建时间

    @Column(name = "update_by", nullable = false, length = 64)
    private String updatedBy; // 更新者

    @Column(name = "update_time", nullable = false)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updateTime; // 更新时间

    @Column(name = "remark", length = 500)
    private String remark; // 备注

}