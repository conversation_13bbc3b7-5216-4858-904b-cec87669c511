package com.meta.system.domain.app;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "meta_bep20_transactions")
@AllArgsConstructor
@NoArgsConstructor
@DynamicInsert
@DynamicUpdate
public class BepTransactions extends AbstractTransactions{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "txid")
    private String txid;

    @Column(name = "blockheight")
    private Long blockHeight;


    @Column(name = "address")
    private String address;

    @Column(name = "fromaddress")
    private String fromaddress;

    @Column(name = "contract")
    private String contract;

    @Column(name = "amount")
    private BigDecimal amount;

    @Column(name = "fee")
    private BigDecimal fee;

    @Column(name = "timestamp")
    private long timestamp;

    @Column(name = "type")
    private String type;

    @Column(name = "issync")
    private int isSync;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public String getTxid() {
        return txid;
    }

    @Override
    public void setTxid(String txid) {
        this.txid = txid;
    }

    @Override
    public Long getBlockHeight() {
        return blockHeight;
    }

    @Override
    public void setBlockHeight(Long blockHeight) {
        this.blockHeight = blockHeight;
    }

    @Override
    public String getAddress() {
        return address;
    }

    @Override
    public void setAddress(String address) {
        this.address = address;
    }

    @Override
    public String getFromaddress() {
        return fromaddress;
    }

    @Override
    public void setFromaddress(String fromaddress) {
        this.fromaddress = fromaddress;
    }

    @Override
    public String getContract() {
        return contract;
    }

    @Override
    public void setContract(String contract) {
        this.contract = contract;
    }

    @Override
    public BigDecimal getAmount() {
        return amount;
    }

    @Override
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Override
    public BigDecimal getFee() {
        return fee;
    }

    @Override
    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    @Override
    public long getTimestamp() {
        return timestamp;
    }

    @Override
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String getType() {
        return type;
    }

    @Override
    public void setType(String type) {
        this.type = type;
    }

    @Override
    public int getIsSync() {
        return isSync;
    }

    @Override
    public void setIsSync(int isSync) {
        this.isSync = isSync;
    }
}
