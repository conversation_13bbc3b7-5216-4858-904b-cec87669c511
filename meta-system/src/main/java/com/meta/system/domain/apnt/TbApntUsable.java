package com.meta.system.domain.apnt;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

/**
 * 用户可用积分对象 tb_apnt_usable
 *
 * <AUTHOR>
 * @date 2021-12-13
 */
@Entity
@Table(name = "tb_apnt_usable")
@Data
public class TbApntUsable implements Serializable {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 积分记录ID
     */
    @Column(name = "record_id")
    private Long recordId;

    /**
     * 积分值
     */
    @Column(name = "value")
    private BigInteger value;

    /**
     * 过期时间
     */
    @Column(name = "expire_time")
    private Date expireTime;

    /**
     * 客户ID
     */
    @Column(name = "cst_id")
    private long cstId;

    /**
     * 删除标志(0-正常, 1-已删除)
     */
    @Column(name = "deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
