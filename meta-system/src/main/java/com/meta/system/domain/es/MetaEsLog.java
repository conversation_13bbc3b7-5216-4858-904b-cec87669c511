package com.meta.system.domain.es;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "meta_es_log")
public class MetaEsLog {
    @Id
    @Column(name = "request_no")
    private String requestNo; // 流水号

    @Column(name = "apicode")
    private String apiCode; // 请求接口

    @Column(name = "sys_id")
    private String sysId; // 系统id

    @Column(name = "version")
    private String version; // 系统编号

    @Column(name = "code")
    private String code; // 状态

    @Column(name = "detail")
    private String detail; // 返回信息


    @Column(name = "request")
    private String request; // 请求体

    @Column(name = "response")
    private String response; // 返回体


    @Column(name = "create_time")
    private LocalDateTime createTime; // 创建时间
}
