package com.meta.system.domain.app;
/**
 * <AUTHOR>
 * @Date 2025/2/27
 */

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.common.serializer.LocalDateTimeDeserializer;
import com.meta.common.serializer.LocalDateTimeSerializer;
import lombok.Data;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "meta_wello_request_log")
public class MetaWelloRequestLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(name = "request_no")
    private String requestNo;

    @Column(name = "detail")
    private String detail;

    @Column(name = "code")
    private String code;

    @Column(name = "request")
    private String request;

    @Column(name = "response")
    private String response;

    @Column(name = "apicode")
    private String apicode;

    @Column(name = "created_at")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createdAt;
}
