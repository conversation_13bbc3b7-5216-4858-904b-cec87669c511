package com.meta.system.domain.log;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.common.enums.app.TxnStatus;
import com.meta.common.serializer.LocalDateTimeDeserializer;
import com.meta.common.serializer.LocalDateTimeSerializer;
import com.meta.system.domain.Wallet;
import com.meta.system.dto.VccReqDto;
import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "meta_txn_dtl_code")
@Data
public class TxnDtlCode {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id; // 主键ID

    @Column(name = "user_id")
    private Long userId; // 用户ID

    @Column(name = "txn_type", length = 2, nullable = false)
    private String txnType; // 交易类型:见字典表code_txn_type

    @Column(name = "code_type", length = 2, nullable = false)
    private String codeType; // 卡类型：01-银卡，02-金卡，03-黑金卡


    @Column(name = "txn_product")
    private String txnProduct; // 交易商品：卡id

    @Column(name = "txn_num")
    private Integer txnNum; // 交易数量

    @Column(name = "txn_time", nullable = false, updatable = false)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime txnTime; // 交易时间

    @Column(name = "from_userid")
    private Long fromUserId; // From交易账户

    @Column(name = "to_userid")
    private Long toUserId; // To交易账户

    @Column(name = "txn_status", nullable = false)
    private Character txnStatus; // 交易状态:0-失败，1-成功

    @Column(name = "txn_desc", length = 512)
    private String txnDesc; // 交易描述

    @Column(name = "silver_code_balance")
    private Integer silverCodeBalance; // 銀卡剩余可用数量

    @Column(name = "golden_code_balance")
    private Integer goldenCodeBalance; // 金卡剩余可用数量

    @Column(name = "goldenblack_code_balance")
    private Integer goldenBlackCodeBalance; // 黑金卡剩余可用数量

    @Column(name = "white_code_balance")
    private Integer whiteCodeBalance; // 白金卡剩余可用数量
    // Constructors, getters, and setters

    @Transient
    private String txnCodeDetail;

    @Transient
    private String statueDetail;

    @Transient
    private String statueDetailShow;

    @Transient
    private String fromAddress;

    @Transient
    private String toAddress;

    public static TxnDtlCode formatCode(VccReqDto req, Wallet wallet) {
            TxnDtlCode code = new TxnDtlCode();
            code.setUserId(req.getUserId());
            code.setTxnType(req.getTxnTypeCode().getValue());
            code.setTxnDesc(req.getTxnTypeCode().getName());
            code.setFromUserId(req.getCodeFromUserId());
            code.setToUserId(req.getCodeToUserId());
            if (req.getTxnTypeCode().isSub()) {
                code.setTxnNum(0 - req.getActiveCodeNum());
            } else {
                code.setTxnNum(req.getActiveCodeNum());
            }
            code.setSilverCodeBalance(wallet.getSilverActiveCodeNum());
            code.setGoldenCodeBalance(wallet.getGoldenActiveCodeNum());
            code.setGoldenBlackCodeBalance(wallet.getGoldenblackActiveCodeNum());
            code.setWhiteCodeBalance(wallet.getWhiteActiveCodeNum());
            code.setTxnStatus(TxnStatus.SUCCESS.getValue());
            code.setCodeType(req.getCardLevel());

            return code;
    }
}
