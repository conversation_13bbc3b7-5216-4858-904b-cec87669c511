package com.meta.system.domain.app;

import lombok.Data;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @Date 2025/3/17
 */
@Entity
@Table(name = "meta_platform_state_restriction")
@Data
public class MetaPlatformStateRestriction {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private int id;

    @Column(name = "country")
    private String country;

    @Column(name = "country_code")
    private String countryCode;

    @Column(name = "open")
    private String open;

    @Column(name = "platform")
    private String platform;
}
