package com.meta.system.domain.app.node;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.common.enums.app.NodeLevel;
import com.meta.common.serializer.LocalDateTimeDeserializer;
import com.meta.common.serializer.LocalDateTimeSerializer;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Entity
@Table(name = "meta_user_node_mapping")
@DynamicInsert
@DynamicUpdate
@Data
@IdClass(UserNodeMapping.class)
public class UserNodeMapping implements Serializable {
    @Id
    @Column(name = "user_id")
    private Long userId; // 用户ID

    @Id
    @Column(name = "child_id")
    private Long childId; // 下级用户ID

    /**
     * {@link NodeLevel}
     */
    @Column(name = "node_level")
    private String nodeLevel; // 用户节点等级

    @Column(name = "child_node_level")
    private String childNodeLevel; // 下级用户节点等级

    @Column(name = "level_no")
    private Integer levelNo; // 关系级别(0为用户自己)

    @Column(name = "record_dt")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime recordDt; // 记录时间
}
