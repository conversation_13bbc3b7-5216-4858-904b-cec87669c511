package com.meta.system.domain.apnt;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户积分扣减明细对象 tb_apnt_reduce_dtl
 *
 * <AUTHOR>
 * @date 2021-12-13
 */
@Entity
@Table(name = "tb_apnt_reduce_dtl")
@Data
public class TbApntReduceDtl implements Serializable {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 扣减积分记录ID
     */
    @Column(name = "reduce_id")
    private Long reduceId;

    /**
     * 新增积分记录ID
     */
    @Column(name = "add_id")
    private Long addId;

    /**
     * 扣减积分值
     */
    @Column(name = "used_value")
    private Long usedValue;

    /**
     * 原新增积分记录过期时间
     */
    @Column(name = "expire_time")
    private Date expireTime;

    /**
     * 客户ID
     */
    @Column(name = "cst_id")
    private long cstId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;


}
