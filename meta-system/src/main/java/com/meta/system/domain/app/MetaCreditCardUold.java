package com.meta.system.domain.app;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 旧卡（不再使用）
 */
@Data
@Entity
@Table(name = "meta_credit_card_u_old")
public class MetaCreditCardUold implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 卡id
     */
    @Id
    @Column(name = "card_id", nullable = false)
    private String cardId;

    /**
     * 卡号
     */
    @Column(name = "card_no")
    private String cardNo;

    /**
     * 卡后最后四位
     */
    @Column(name = "card_last")
    private String cardLast;

    /**
     * 卡状态
     */
    @Column(name = "card_status")
    private String cardStatus;

    /**
     * 过期时间
     */
    @Column(name = "expire_date")
    private String expireDate;

    /**
     * 卡币种
     */
    @Column(name = "card_currency")
    private String cardCurrency;

    /**
     * 余额
     */
    @Column(name = "balance")
    private BigDecimal balance;

    /**
     * 邮箱
     */
    @Column(name = "user_email")
    private String userEmail;

    /**
     * 手机号前缀
     */
    @Column(name = "user_dial_code")
    private String userDialCode;

    /**
     * 手机号
     */
    @Column(name = "user_phone")
    private String userPhone;

    /**
     * 是否同步
     */
    @Column(name = "is_sync")
    private String sync;

    /**
     * 新卡的id
     */
    @Column(name = "card_id_new")
    private String cardIdNew;

}
