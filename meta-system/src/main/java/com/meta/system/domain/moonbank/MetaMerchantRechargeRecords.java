package com.meta.system.domain.moonbank;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/26/10:41
 */
@Entity
@Table(name = "meta_merchant_recharge_records")
@Data
public class MetaMerchantRechargeRecords  implements Serializable {
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 货币
     */
    @Column(name = "currency")
    private String currency;
    /**
     * 充值金额
     */
    @Column(name = "operate_amount")
    private BigDecimal operateAmount;
    /**
     * 可用金额
     */
    @Column(name = "available_amount")
    private BigDecimal availableAmount;

    /**
     * 冻结金额
     */
    @Column(name = "frozen_amount")
    private BigDecimal frozenAmount;
    /**
     * 充值时间
     */
    @Column(name = "create_at")
    private Date createAt;


}
