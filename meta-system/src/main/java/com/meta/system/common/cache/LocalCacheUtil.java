package com.meta.system.common.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 本地缓存
 *
 * <AUTHOR>
 * @date 2025/3/3 11:22
 **/
public class LocalCacheUtil {
    // 本地缓存：设置最大大小为 1000，过期时间为 1 分钟
    public static final Cache<String, Set<String>> localCache = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(60, TimeUnit.SECONDS)
            .build();

}
