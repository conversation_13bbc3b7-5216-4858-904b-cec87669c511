package com.meta.system.enums;

import com.meta.common.enums.BaseEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/4/17 15:44
 **/
@Getter
public enum CoinType implements BaseEnum<CoinType> {
    TRX("TRX", "TRX"),
    USDT("USDT", "USDT"),
    USDC("USDC", "USDC");

    private final String code;
    private final String description;

    CoinType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getOrdinal() {
        return String.valueOf(this.ordinal());
    }
}
