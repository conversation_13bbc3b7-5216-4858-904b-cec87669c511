package com.meta.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.common.enums.app.TxnStatus;
import com.meta.common.serializer.BigIntegerSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;


@Data
public class PoolTxnDtlUSDVo {

    @JsonSerialize(using = BigIntegerSerializer.class)
    private BigInteger id; // 主键ID

    private BigInteger userId; // 用户ID

    private String coinCode;
    private String currency;

    private String txnType;

    private BigDecimal txnAmount; // 交易金额

    private BigInteger txnNum; // 交易数量

    private String cardId; // 卡id
    private String txnDesc;

    private BigDecimal txnFee; // 手续费

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date txnTime; // 交易时间


    /**
     * {@link TxnStatus}
     */
    private Character txnStatus; // 交易状态:0-失败，1-成功，2-处理中

    private String txnId; // 交易id

    private BigDecimal usdBalance; // 账户当前余额

    private String cardNo;


}
