package com.meta.system.vo;

import lombok.Data;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @Date 2025/2/24
 */


@Data
public class MetaExchangeOrderInfoVo {
    /**
     * 商户订单ID
     */
    private String merchantOrderId;
    /**
     * 用户ID
     */
    private BigInteger userId;
    /**
     * 交易方式
     * BUY、SELL
     */
    private String side;
    /**
     * 加密货币
     */
    private String cryptoCurrency;
    /**
     * 法定货币
     */
    private String fiatCurrency;
    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * 报价
     */
    private String quotePrice;
    /**
     * 用户支付的法定货币金额
     */
    private String fiatAmount;
    /**
     * 用户购买的加密货币金额
     */
    private String cryptoAmount;
    /**
     * 法定手续费（银行手续费）
     */
    private String fiatFee;
    /**
     * 交易手续费（平台收）
     */
    private String tradeFee;
    /**
     * 商户手续费
     */
    private String merchantFee;
    /**
     * 网络手续费
     */
    private String networkFee;
    /**
     * 总手续费
     */
    private String total;
    /**
     * 用户支付的方式
     */
    private String method;
    /**
     * 用户支付的账号
     */
    private String accountNumber;
    /**
     * 链上交易 ID
     */
    private String txId;
    /**
     * 失败代码
     */
    private String failCode;
    /**
     * 失败原因
     */
    private String failReason;
    /**
     * 创建时间
     */
    private String createdAt;
    /**
     * 更新时间
     */
    private String updatedAt;
    /**
     * 总报价金额
     */
    private String totalQuoteAmount;
    /**
     * 下单时间
     */
    private String orderTime;
    /**
     * 支付时间
     */
    private String payTime;
    /**
     * 成交时间
     */
    private String dealTime;
    /**
     * 付款地址
     */
    private String checkoutUrl;
    /**
     * 订单状态详情
     */
    private String orderStatusDetails;
}
