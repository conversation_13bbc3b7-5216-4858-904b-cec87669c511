package com.meta.system.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2023/11/24/11:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CollectRoportVo {
    private String date;//日期
    private BigInteger count;//归集笔数
    private BigDecimal amount;//归集金额
    private BigDecimal bnbFee;//BNB归集手续费
    private BigDecimal arbEthFee;
    private BigDecimal baseEthFee;
    private BigDecimal trxFee;//TRX归集手续费
}
