package com.meta.system.vo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2024/09/04/17:55
 */
@Data
public class MetaCardScenarioDetailVo {


    private BigInteger id;

    private String type;

    private BigInteger secnairoId;

    private Character validFlag;

    private String cardType;

    private String name;

    private String picName;

}
