package com.meta.system.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/24/14:08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TradeDayTotalVo {
    private String tradeDate;//交易日期
    private BigDecimal nodePre;//节点预存
    private BigDecimal cardRecharge;//卡充值金额
    private BigDecimal cardRechargeFee;//卡充值手续费
    private BigDecimal cardWithdrawal;//卡提现金额
    private BigDecimal cardOopeningFee;//开卡费金额
    private BigDecimal usdtAmount;//USDT充值金额
    private BigDecimal usdtWithdrawalAmount;//USDT提币金额

}
