package com.meta.system.vo;

import com.meta.system.domain.MetaCreditCardLogSub;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/06/01/3:05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreditCardLogVo {
    private BigInteger id;

    private String cardID;
    /**
     * 交易id
     */
    private String transactionId;

    /**
     * 交易时间
     */
    private String transactionTime;


    /**
     * 交易币种:货币三位代码
     */
    private String transCurrency;
    /**
     * 交易金额
     */
    private BigDecimal transCurrencyAmt;
    /**
     * 卡币种:货币三位代码
     */
    private String cardCurrency;

    /**
     * 卡交易金额
     */
    private BigDecimal cardCurrencyAmt;

    /**
     * 交易类型
     */
    private String transType;

    /**
     * 交易状态：processing,success,fail
     */
    private String transStatus;


    /**
     * 商户名称
     */
    private String merchantName;
    /**
     * 商户所在城市
     */
    private String merchantCity;

    /**
     * 描述码
     */
    private String respCode;
    /**
     * 描述
     */
    private String respCodeDesc;

    /**
     * 手续费金额
     */
    private BigDecimal fee;

    /**
     * 手续费卡币种
     */
    private String feeCurrency;

    /**
     *交易详细信息
     */
    private String transactionDetail ;

    /**
     *卡可用余额
     */
    private BigDecimal cardAvailableBalance ;

    /**
     *卡可用余额币种
     */
    private String cardAvailableBalanceCurrency ;

    /**
     * 描述
     */
    private String description ;

    private  String cardNo;

    private String sysId;

    private String userName;

    private String nickName;


    private List<MetaCreditCardLogSub> list;

    private BigDecimal refumAmount;


}
