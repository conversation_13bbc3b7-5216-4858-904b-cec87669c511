package com.meta.system.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @Date 2025/3/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MetaExchangeOrderInfoAlixpayVo {
    /**
     * 订单ID
     */
    private String merchantOrderId;
    /**
     * 类型（SELL OR BUY）
     */
    private String type;
    /**
     * 卖出的加密货币币种 = 用户支付的加密货币币种
     */
    private String currency;
    /**
     * 单价 1 加密货币 = xx 法定货币
     */
    private String price;
    /**
     * 法定货币
     */
    private String fiatCurrency;
    /**
     * 支付给商家的法定货币金额
     */
    private String paidAmount;
    /**
     * 状态
     */
    private String status;
    /**
     * 状态描述
     */
    private String descriptions;
    /**
     * 下单时间
     */
    private String orderTime;
    /**
     * 成交时间
     */
    private String dealTime;
    /**
     * 总交易费
     */
    private String totalProcessingFee;
    /**
     * 用户总支付的加密货币金额（含alixpay、kazepay交易手续费）
     */
    private String totalPaymentAmount;
    /**
     * 用户id
     */
    private BigInteger userId;
    /**
     * 状态详情
     */
    private String statusDetail;
    /**
     * 付款备注信息
     */
    private String contentPayment;
    /**
     * 账户持有人
     */
    private String bankAccountName;

}
