package com.meta.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meta.system.domain.app.CoinBalance;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WalletVo {
    private BigInteger userId; //  用户id
    private String username; //  账号名称(目前与邮箱也一致)
    private String nickname; // 用户名称
    private String phonenumber; // 手机号码
    private BigDecimal usdBalacne; // usd余额
    private BigDecimal freezeUsdBalacne; // 冻结的usd余额
    private Integer silverActiveCodeNum; // 银卡激活码数量
    private Integer goldenActiveCodeNum; // 金卡激活码数量
    private Integer goldenblackActiveCodeNum; // 黑卡激活码数量
    private Character status; // 状态

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date tradeLimitExpired; // 交易受限到期时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date loginLimitExpired; // 登录受限到期时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime; // 注册时间
    /**
     * {@link com.meta.common.enums.app.NodeLevel}
     */
    private String nodeLevel; //  节点等级 默认为 00-无

    private String channelName; // 渠道商名称

    private List<CoinBalance> coinBalanceList;
}
