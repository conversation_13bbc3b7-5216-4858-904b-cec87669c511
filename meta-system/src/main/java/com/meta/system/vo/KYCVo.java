package com.meta.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meta.system.domain.app.FileSource;
import lombok.Data;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

@Data
public class KYCVo {

    private BigInteger id; // 自增主键

    private BigInteger userId; // 用户ID

    private String kycType; // kyc 认证类型

    private String nickname;// 名字

    private String email;// 邮件

    private String entName; // 企业名称

    private String certType; // 证件类型


    private String certNo; // 申请人身份证号码

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date issueDate; // 证件签发日期

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expireDate; // 证件到期日期

    private String entRegisterNumber; // 企业注册号码

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entRegisterDate; // 企业注册日期

    private String entCity; // 企业所在城市

    private String entAddress; // 企业详细地址

    /**
     * {@link com.meta.common.enums.ApprovalStatus}
     */
    private String approvalStatus; // 待审核、已通过、拒绝等

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createdAt; // 创建时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updatedAt; // 更新时间

    private String fileId; // 文件识别id
    private List<FileSource> files; // 文件


    private String remark; // 备注

}
