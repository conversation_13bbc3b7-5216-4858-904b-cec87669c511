package com.meta.system.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * 节点中心
 */
@Data
@Accessors(chain = true)
public class NodeCenter {

    @JsonIgnore
    private BigInteger userId;

    /**
     * 银卡数量
     */
    private Integer silverActiveCodeNum;


    /**
     * 金卡数量
     */
    private Integer goldenActiveCodeNum;


    /**
     * 黑金卡数量
     */
    private Integer goldenblackActiveCodeNum;

    /**
     * 白卡数量
     */
    private Integer whiteActiveCodeNum;

    /**
     * 节点等级
     */
    private String nodeLevel;

    /**
     * 佣金
     */
    private Number totalCommisionAmount;

    /**
     * 充值返佣比例
     */
    private BigDecimal  cardRechargeRebate;

    /**
     * 总推荐人数
     */
    private Number totalRecommendNum;

    /**
     * 开卡返佣比例
     */
    private BigDecimal cardOpenRebate;

    /**
     * 已开卡人数
     */
    private Number totalOpenNum;
}
