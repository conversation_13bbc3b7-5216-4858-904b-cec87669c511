package com.meta.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/11/23
 */
@Data
public class MetaPushDataVo {
    private BigInteger id;

    private String sysId;

    private String requestNo;

    private String cardId;

    private String userEmail;

    private String apiUrl; //请求地址

    private String request; // 请求体

    private String statue; //状态

    private String response; // 返回体

    private int num;//发送次数

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime; // 创建时间

    private String apiCode; //类型
}
