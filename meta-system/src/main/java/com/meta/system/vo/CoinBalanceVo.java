package com.meta.system.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2023/11/23/11:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CoinBalanceVo {

    private String userName;
    private BigInteger userId;
    private String coinCode;
    private String walletAddress;
    private String address;
    private BigDecimal coinBalance;
    private BigDecimal freezeBalance;
    private BigDecimal uncltBep20CoinBalance;
    private BigDecimal uncltErc20ArbCoinBalance;
    private BigDecimal uncltErc20BaseCoinBalance;
    private BigDecimal uncltCoinBalance;
    private String coinNet;
    private String  walletConfigKey;
    private boolean energy;




}
