package com.meta.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/04/07/11:18
 */
@Data
public class MetaGatePrePayVo {

    private Integer id;

    private BigInteger userId;

    private String merchantTradeNo;

    private String terminalType;

    private String currency;

    private String address;

    private BigDecimal orderAmount;

    private String goodsType;

    private String goodsName;

    private String goodsDetail;

    private String prepayId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    private String statue;
    private String transactionId;
}
