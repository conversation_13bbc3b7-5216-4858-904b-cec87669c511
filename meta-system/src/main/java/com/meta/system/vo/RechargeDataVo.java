package com.meta.system.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2024/08/20/15:51
 */
@Data
public class RechargeDataVo {
    private String coinNet;
    private BigInteger id;
    private String txid;
    private BigInteger blockheight;
    private String address;
    private String fromaddress;
    private String contract;
    private BigDecimal amount;
    private BigDecimal fee;
    private String date;
    private String type;
    private String txnCoin;
    private Short issync;
    private String userName;
    private String nickName;
    private String remark;
    private String sysId;
}
