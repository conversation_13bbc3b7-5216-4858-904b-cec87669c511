package com.meta.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.common.serializer.BigIntegerSerializer;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigInteger;
import java.util.Date;


@Data
public class TxnDtlCodeVo {
    @JsonSerialize(using = BigIntegerSerializer.class)
    private BigInteger id; // 主键ID

    private BigInteger userId; // 用户ID
    private String username;
    private String nickname;

    private String txnType; // 交易类型:见字典表code_txn_type

    private String codeType; // 卡类型：01-银卡，02-金卡，03-黑金卡

    @Column(name = "txn_num")
    private Integer txnNum; // 交易数量

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date txnTime; // 交易时间

    private BigInteger fromUserId; // From交易账户
    private String fromUsername; // From交易账户

    private BigInteger toUserId; // To交易账户
    private String toUsername; // To交易账户

    private Character txnStatus; // 交易状态:0-失败，1-成功

    private String txnDesc; // 交易描述

    private Integer silverCodeBalance; // 銀卡剩余可用数量

    private Integer goldenCodeBalance; // 金卡剩余可用数量

    private Integer goldenBlackCodeBalance; // 黑金卡剩余可用数量


    private String typeDetail;//交易类型
    private String codeDetail;//币种类型
    private String statusDetail;//状态详情

}
