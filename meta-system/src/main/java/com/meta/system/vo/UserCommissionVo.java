package com.meta.system.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/08/09/16:29
 */
@Data
public class UserCommissionVo {

    private BigInteger id; // 主键id

    private BigInteger userId; // 用户id

    private String username; // 用户邮箱


    private Character commissionType; // 佣金类型:1-开卡返佣,2-充值返佣，3-节点升级（usd）


    private BigDecimal commissionFeeRebate; // 佣金费率

    private BigDecimal commissionAmount; // 佣金：单位默认usd


    private String commissionDesc; // 佣金描述

    private BigInteger txnId; // 交易流水ID

    private String txnCurrency; // 佣金描述

    private String name;//
    private String nickName;
    private String invitedUid;//被邀请人uid


    private Date createTime; // 创建时间

    private String begin;
    private String end;
    private Integer pageNum;
    private Integer pageSize;
}
