package com.meta.system.dto.node;

import com.meta.common.enums.app.CardLevel;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 激活码兑换DTO
 */
@Data
public class CodeExchangeDto {
    // 源卡种
    @NotNull(message = "源卡种类型不能为空")
    private String sourceCard;
    // 兑换数量
    @NotNull(message = "兑换数量不能为空")
    private Integer sourceCardNum;

    // 目标卡种
    @NotNull(message = "目标卡种类型不能为空")
    private String targetCard;

    private Long userId;

    public CardLevel getSourceCardEnum() {
        return CardLevel.getByValue(sourceCard);
    }

    public CardLevel getTargetCardEnum() {
        return CardLevel.getByValue(targetCard);
    }
}
