package com.meta.system.dto.node;

import com.meta.common.enums.ApprovalStatus;
import com.meta.common.enums.app.NodeLevel;
import com.meta.common.enums.app.PaymentMethod;
import lombok.Data;

/**
 * 节点查询DTO
 */
@Data
public class NodeApplicationDto {

    /**
     * 申请人姓名
     */
    private String nickname;


    /**
     * 申请人邮箱
     */
    private String email;

    /**
     * 节点等级
     */
    private NodeLevel nodeLevel;

    /**
     * 支付方式
     */
    private PaymentMethod paymentMethod;

    /**
     * 审批状态
     */
    private ApprovalStatus approvalStatus;


}
