package com.meta.system.dto.alixpay;

import cn.hutool.core.date.DateUtil;
import com.meta.system.domain.app.MetaAlixpayLog;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/8 13:48
 **/
@Data
public class AlixPayOrderDto {

//    private Long id;
    private String externalOrderId;
    private String type;
    private BigDecimal fiatAmount;
    private BigDecimal paidAmount;
    private String descriptions;
    private TokenTransfer tokenTransfer;
    private BankTransfer bankTransfer;
    private Fees fees;
    private String status;
    private String createdAt;
    private String expiresAt;
    private String signature;


    public MetaAlixpayLog convertDtoToEntity() {

        AlixPayOrderDto dto = this;
        MetaAlixpayLog entity = new MetaAlixpayLog();
        entity.setMerchantOrderId(dto.getExternalOrderId());
        entity.setType(dto.getType());
        entity.setFiatAmount(dto.getFiatAmount() != null ? dto.getFiatAmount().toString() : null);
        entity.setPaidAmount(bankTransfer.getTotalPayment() != null ? bankTransfer.getTotalPayment().toString() : null);
        entity.setDescriptions(dto.getDescriptions());
        entity.setStatus(dto.getStatus());
        entity.setSignature(dto.getSignature());
        entity.setCreatedAt(LocalDateTime.parse(dto.getCreatedAt(),DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        entity.setExpiresAt(LocalDateTime.parse(dto.getExpiresAt(),DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        entity.setSysId("99");


        if (dto.getTokenTransfer() != null) {
            entity.setCurrency(dto.getTokenTransfer().getCurrency());
            entity.setPrice(dto.getTokenTransfer().getPrice() != null ? dto.getTokenTransfer().getPrice().toString() : null);
            entity.setAmount(dto.getTokenTransfer().getAmount() != null ? dto.getTokenTransfer().getAmount().toString() : null);
        }

        if (dto.getBankTransfer() != null) {
            entity.setBankAccountName(dto.getBankTransfer().getBankAccountName());
            entity.setBankAccountNumber(dto.getBankTransfer().getBankAccountNumber());
            entity.setBankName(dto.getBankTransfer().getBankName());
            entity.setBankCode(dto.getBankTransfer().getBankCode());
            entity.setContentPayment(dto.getBankTransfer().getContentPayment());
            entity.setTotalPayment(dto.getBankTransfer().getTotalPayment() != null ? dto.getBankTransfer().getTotalPayment().toString() : null);
        }

        if (dto.getFees() != null) {
            entity.setSystemFee(dto.getFees().getSystemFee() != null ? dto.getFees().getSystemFee().toString() : null);
            entity.setProcessingFee(dto.getFees().getProcessingFee() != null ? dto.getFees().getProcessingFee().toString() : null);
        }

        return entity;
    }
}
