package com.meta.system.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class CardUpdateLogDto {
    @NotBlank(message="卡id不能为空")
    private String cardId;

    @NotNull
    private Long userId;

    private String transactionStartTime; //格式: yyyy-MM-dd HH:mm:ss

    private String transactionEndTime; //格式: yyyy-MM-dd HH:mm:ss

    private Integer pageNum;//[1,100],default 1
    private Integer pageSize;//[1,100],default 100
}
