package com.meta.system.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/10/09/14:15
 */
@Data
public class CouponDto {

    private Integer id;
    private Long userId;
    private Long admUserId;
    private String couponType;

    /**
     * NEW-开卡、TOPUP卡充值、NODE-节点购买
     */
    private String couponScenarios;

    /**
     * 可使用卡类型
     */
    private String couponCardType;

    /**
     * 可使用卡等级产品
     */
    private String couponCardlevel;

    /**
     * 可使用节点产品
     */
    private String couponNode;

    /**
     * 创建开始时间
     */
    private String startDate;
    /**
     * 创建结束时间
     */
    private String endDate;

    /**
     * 优惠券
     */
    private String couponNm;

    /**
     * 失效开始时间
     */
    private String startExpDate;

    /**
     * 失效结束时间
     */
    private String endExpDate;

    private Integer couponGenCfgId;

    private String status;

    private Integer pageNum;
    private Integer pageSize;

    private BigDecimal amount;//金额
    private Long cardCfgId;//卡配置id
    private String coin;//币种
    private String cardId;//卡id

    private String nowDate;

}
