package com.meta.system.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/02/05/16:25
 */
@Data
public class TbApntRecordVo {
    private Integer id;
    private String type;
    private String typeName;
    private String originalId;
    private String direct;
    private Integer value;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;
    private String reason;
    private Integer cstId;
    private String cstName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    private Date updateTime;
}
