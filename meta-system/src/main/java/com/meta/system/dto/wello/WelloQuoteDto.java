package com.meta.system.dto.wello;

import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/2/14
 */


@Data
public class WelloQuoteDto {
    /**
     * 交易方式
     * BUY、SELL
     */
    @NotBlank(message = "交易方式不能为空")
    private String side;
    /**
     * 加密货币
     */
    @NotBlank(message = "加密货币不能为空")
    private String cryptoCurrency;
    /**
     * 法定货币
     */
    @NotBlank(message = "法定货币不能为空")
    private String fiatCurrency;
    /**
     * 请求货币
     */
    @NotBlank(message = "请求货币不能为空")
    private String requestCurrency;
    /**
     * 请求金额
     */
    @NotNull(message = "请求金额不能为空")
    @DecimalMin(value = "0.01", message = "请求金额必须大于 0")
    private BigDecimal requestAmount;
    /**
     * 付款方式类型
     * CARD、SEPA
     */
    private String paymentMethodType;
}
