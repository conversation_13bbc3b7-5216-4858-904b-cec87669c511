package com.meta.system.dto;


import com.meta.common.enums.app.AlgorithmType;
import com.meta.common.enums.app.FreezeType;
import com.meta.common.enums.app.TxnType;
import com.meta.system.domain.log.TxnDtlUSD;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class VccReqDto {

    // 必须的
    private Long userId;



    private String cardLevel;
    private Integer activeCodeNum;
    private AlgorithmType activeCodeAlgorithmType;
    private TxnType txnTypeCode;
    private Long codeFromUserId;
    private Long codeToUserId;
    private BigDecimal logisticsFee;//物流费

    private String coin;//交易币种

    private String fromCoin; // 交易对货币代码
    private BigDecimal fromAmount; // 交易货币金额
    private BigDecimal exchgRate; // 兑换汇率
    private BigDecimal amount;


    // USD
    private AlgorithmType walletAlgorithmType;
    private BigDecimal walletAmount;
    private FreezeType freezeType = FreezeType.NO;
    private TxnType txnType;
    private Long fromUserId;
    private Long toUserId;
    private String txnProduct;
    private BigDecimal fee;
    private String txnId;

    private String txnDesc;//交易描述
    private Integer recordType;

    private Long dtlUsdId;// txn_dtl_usd的主键
    private BigDecimal usdBalance;// txn_dtl_usd的当时记录的余额
    private TxnDtlUSD dtlUSD;
    // 只有兑换的时候不变
    private boolean codeUsed = true;

    //结算方式
    private String coinType;

    public VccReqDto(Long userId, AlgorithmType walletAlgorithmType, BigDecimal walletAmount, FreezeType freezeType, TxnType txnType, Long fromUserId, Long toUserId, String txnProduct, BigDecimal fee, String  txnId) {
        this.userId = userId;
        this.walletAlgorithmType = walletAlgorithmType;
        this.walletAmount = walletAmount;
        this.freezeType = freezeType;
        this.txnType = txnType;
        this.fromUserId = fromUserId;
        this.toUserId = toUserId;
        this.txnProduct = txnProduct;
        this.fee = fee;
        this.txnId = txnId;

        this.setActiveCodeNum(0);
    }
    public VccReqDto(){}

    public VccReqDto(Long userId,AlgorithmType walletAlgorithmType,BigDecimal walletAmount,FreezeType freezeType,Long dtlUsdId,BigDecimal usdBalance) {
        this.userId = userId;
        this.walletAlgorithmType = walletAlgorithmType;
        this.walletAmount  = walletAmount;
        this.freezeType = freezeType;
        this.dtlUsdId = dtlUsdId;
        this.usdBalance = usdBalance;
        this.setActiveCodeNum(0);
    }

    public VccReqDto(Long userId, AlgorithmType walletAlgorithmType, FreezeType freezeType, TxnDtlUSD dtlUsd) {
        this.userId = userId;
        this.walletAlgorithmType = walletAlgorithmType;
        this.freezeType = freezeType;
        if(dtlUsd != null){
            this.walletAmount = dtlUsd.getTxnAmount();
            this.txnType = TxnType.toType(dtlUsd.getTxnType());
            this.fromUserId = dtlUsd.getFromUserId();
            this.toUserId = dtlUsd.getToUserId();
            this.txnProduct = dtlUsd.getTxnProduct();
            this.fee = dtlUsd.getTxnFee();
            this.dtlUsdId = dtlUsd.getId();
        }
        this.dtlUSD = dtlUsd;
        this.setActiveCodeNum(0);
    }


}
