package com.meta.system.dto.node;

import com.meta.common.enums.app.ExchangeType;
import com.meta.system.dto.coin.ExchangeToCard;
import com.meta.system.dto.coin.ExchangeToWallet;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class WalletExchangeDto {
    /**
     * 兑换类型
     * {@link ExchangeType}
     */
    @NotEmpty(message = "兑换类型不能为空!" ,groups = {ExchangeToWallet.class})
    private String exchangeType;

    /**
     * 兑换数量
     *
     */
    @NotNull(message = "兑换数量不能为空" ,groups = {ExchangeToWallet.class,ExchangeToCard.class})
    private BigDecimal quantity;

    // 数字货币代码
    @NotEmpty(message = "货币代码不能为空" ,groups = {ExchangeToWallet.class,ExchangeToCard.class})
    private String coinCode;


    //卡号 充值到卡需要
    @NotEmpty(message = "卡号不能为空" ,groups = {ExchangeToCard.class})
    private String cardId;
}
