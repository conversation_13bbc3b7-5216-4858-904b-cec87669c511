package com.meta.system.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class NotifyDto {
    /**
     * 卡id
     */
    private String cardId;
    /**
     * 请求类型
     */
    private String apiCode;
    /**
     * 状态(0:失败，1：成功，2：处理中)
     */
    private String statue;

    private String sysId;
    /**
     * 开始时间
     */

    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    private Integer pageNum;
    private Integer pageSize;

}
