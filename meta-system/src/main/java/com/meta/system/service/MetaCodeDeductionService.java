package com.meta.system.service;

import com.meta.system.domain.app.MetaCodeDeduction;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/27/14:26
 */
public interface MetaCodeDeductionService {
    void save(MetaCodeDeduction metaCodeDeduction);

    List<MetaCodeDeduction> findByUserId(long userId);

    MetaCodeDeduction findByTxnId(Long txnId);

    void updateStatue(String statue, Long id);
}
