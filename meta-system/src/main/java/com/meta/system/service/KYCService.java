package com.meta.system.service;

import com.meta.system.domain.app.KYCApplication;
import com.meta.system.dto.kyc.KYCDto;
import com.meta.system.vo.KYCVo;
import org.springframework.data.domain.Page;


public interface KYCService {

    /**
     * 检测是否已经实名认证
     * @param userId 用户id
     * @return 是否已经实名
     */
    boolean checkKYC(Long userId);

    /**
     * 新增实名认证申请
     * @param application 个人认证信息
     */
    KYCApplication apply(KYCApplication application);

    /**
     * 分页查询
     * @param dto 查询条件
     * @return 分页数据
     */
    Page<KYCVo> page(KYCDto dto);

    /**
     * 根据用户ID获取实名信息
     * @param userId 用户id
     * @return 实名详情
     */
    KYCApplication findByUserId(Long userId);

    int approval(KYCDto dto);

    KYCVo findById(Long id);

    void sendMail();
}
