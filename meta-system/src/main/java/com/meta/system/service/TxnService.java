package com.meta.system.service;

import com.meta.common.core.domain.AjaxResult;
import com.meta.common.enums.app.AlgorithmType;
import com.meta.common.enums.app.FreezeType;
import com.meta.system.domain.log.TxnDtlCode;
import com.meta.system.domain.log.TxnDtlUSD;
import com.meta.system.dto.TxnDto;
import com.meta.system.dto.VccReqDto;
import com.meta.system.vo.TradeDayTotalVo;
import com.meta.system.vo.TxnDtlCodeVo;
import com.meta.system.vo.TxnDtlUSDVo;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;
import java.util.List;

public interface TxnService {

    TxnDtlCode dealTxnForCode(TxnDtlCode dtlCode);


    TxnDtlUSD findByCardIdAndTxnTypeAndRelaTransactionid(String cardId, String type,String txnId);

    List<TxnDtlUSD> findAllByCardIdAndStatusAndTransactionId(String cardId, String stauts, String transactionId, String[] txnTypes);
    /**
     * 更新交易商品
     * @param id
     * @param txnProduct
     */
     void updateTxnProductForCode(Long id,String txnProduct);

    /**
     * @param usd 交易详情
     * @param freezeType 冻结类型
     * @param type 算法类型
     * @return 交易流水
     */
    TxnDtlUSD dealTxnForUSD(TxnDtlUSD usd, FreezeType freezeType, AlgorithmType type);


    Page<TxnDtlUSDVo> pageForUSD(TxnDto txnDto);

    /**
     * 查询正在处理中的卡片交易记录
     * @param cardId
     * @param stauts
     * @return
     */
    List<TxnDtlUSD> findAllByCardIdAndStatus(String cardId, String stauts);



    /**
     *
     * @param cardId
     * @param stauts
     * @param txnTypes
     * @return
     */
    List<TxnDtlUSD> findAllByCardIdAndStatusAndTxnTypeForUsd(String cardId, String stauts,String[] txnTypes);

    /**
     *
     * @param cardId
     * @param stauts
     * @param transactionId
     * @return
     */
    List<TxnDtlUSD> findAllByCardIdAndStatusAndTransactionId(String cardId, String stauts,String transactionId);
    void updateUsdProductById(Long id,String product);


    TxnDtlUSDVo detailByUSD(Long id, Long userId);

    Page<TxnDtlCodeVo> pageForCode(TxnDto txnDto);

    TxnDtlCodeVo detailByCode(Long id, Long userId);

    TxnDtlCode findByTxnProduct(String txnProduct);

    TxnDtlCode findByTxnProductByCardId(String txnProduct);

    TxnDtlCode findByTxnProductAndStatusAndTxnTypeForCode(String txnProduct, String status, String txnType);

    TxnDtlUSD findByTxnId(String txnId);

    TxnDtlCode findByTxnIdForCode(String txnId);

    /**
     *  更新或者保存卡的数据
     * @param dtl
     */
    void merge(TxnDtlUSD dtl);

    void saveAll(List<TxnDtlUSD> list);

    void save(TxnDtlUSD txnDtlUSD);

    Page<TradeDayTotalVo> tradeReportPage(String startDate, String endDate);

    List<TxnDtlUSD> findCardIdAndTransactionId(String userBankcardId, String recordNo,String[] txnTypes);

    void updateTxnProduct(String cardId, String userBankId);


    void updateCodeProduct(String cardId, String userBankId);

    BigDecimal findRechargeAmount(String cardId, String status, String value);

    public AjaxResult update(VccReqDto req);
}
