package com.meta.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.domain.entity.SysDictData;
import com.meta.common.utils.MessageUtils;
import com.meta.system.dao.app.MetaCardScenarioDetailDao;
import com.meta.system.domain.app.MetaCardScenarioDetail;
import com.meta.system.service.ISysDictDataService;
import com.meta.system.service.MetaCardScenarioDetailService;
import com.meta.system.vo.MetaCardScenarioDetailVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/09/03/16:55
 */
@Service
public class MetaCardScenarioDetailServiceImpl implements MetaCardScenarioDetailService {

    @Autowired
    private MetaCardScenarioDetailDao metaCardScenarioDetailDao;

    @Autowired
    private ISysDictDataService sysDictDataService;

    @Override
    public List findByCardType(String cardType) {


        List<JSONObject> list = new ArrayList<>();

        //场景类型
        List<SysDictData> secnarioTypeList = sysDictDataService.selectDictDataList("secnario_type");


        List<MetaCardScenarioDetailVo> dataList = metaCardScenarioDetailDao.findByCardType(cardType);
        if (dataList.size() > 0) {
            for (SysDictData sysDictData : secnarioTypeList) {

                List<MetaCardScenarioDetailVo> resultList = dataList.stream()
                        .filter(metaCard -> sysDictData.getDictValue().equals(metaCard.getType()))
                        .collect(Collectors.toList());
                if (resultList != null && resultList.size() > 0) {
                    JSONObject jsonObject = new JSONObject();

                    jsonObject.put("type", sysDictDataService.getLabel(sysDictData));
                    jsonObject.put("order",sysDictData.getDictSort());
                    jsonObject.put("list", resultList);
                    list.add(jsonObject);
                }

            }
        }
        // 按 order 值降序排序
        list.sort((o1, o2) -> Integer.compare(o2.getIntValue("order"), o1.getIntValue("order")));
        return list;
    }
}
