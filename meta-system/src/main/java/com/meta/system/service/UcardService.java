package com.meta.system.service;

import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2025/01/14/17:22
 */
public interface UcardService {
    JSONObject setUcardKycInfo(String uid, JSONObject jsonObject, Integer bankCardId,String sysId);
    JSONObject setUcardKycInfoNew(String uid, JSONObject jsonObject, Integer bankCardId, String sysId);

    String getKycStatus(String status);
    String getMUcardIdType(String type);


    String uCardUploadFile(String fileId, String uid);

    JSONObject getUcardKycStatue(JSONObject jsonObject, String sysId);


    JSONObject getUcardKyc(String cardType,String uid);
}
