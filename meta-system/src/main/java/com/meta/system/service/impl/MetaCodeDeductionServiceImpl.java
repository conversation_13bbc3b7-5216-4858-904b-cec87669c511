package com.meta.system.service.impl;

import com.meta.system.dao.app.MetaCodeDeductionDao;
import com.meta.system.domain.app.MetaCodeDeduction;
import com.meta.system.service.MetaCodeDeductionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/04/15/18:10
 */
@Service
public class MetaCodeDeductionServiceImpl implements MetaCodeDeductionService {

    @Autowired
    private MetaCodeDeductionDao metaCodeDeductionDao;

    @Override
    public void save(MetaCodeDeduction metaCodeDeduction) {
        metaCodeDeductionDao.save(metaCodeDeduction);
    }

    @Override
    public List<MetaCodeDeduction> findByUserId(long userId) {
        return  metaCodeDeductionDao.findByUserId(userId);
    }

    @Override
    public MetaCodeDeduction findByTxnId(Long txnId) {
        return metaCodeDeductionDao.findByTxnId(txnId);
    }

    @Override
    public void updateStatue(String statue, Long id) {
        metaCodeDeductionDao.updateStatue(statue,id);
    }
}
