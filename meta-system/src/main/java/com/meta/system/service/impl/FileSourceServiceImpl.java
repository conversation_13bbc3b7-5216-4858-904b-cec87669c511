package com.meta.system.service.impl;

import com.meta.common.utils.code.BusinessBizCode;
import com.meta.common.utils.file.FileUtils;
import com.meta.system.dao.app.FileSourceDao;
import com.meta.system.domain.app.FileSource;
import com.meta.system.service.FileSourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class FileSourceServiceImpl implements FileSourceService {

    @Autowired
    private FileSourceDao fileSourceDao;


    @Override
    public List<FileSource> save(List<FileSource> file) {
        return fileSourceDao.saveAll(file);
    }

    /**
     * 文件逻辑删除
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        fileSourceDao.UpdateDelFlag(id);
        return BusinessBizCode.OPTION_SUCCESS.getCode();
    }

    /**
     * 根据ID查找文件数据
     * @param id
     * @return
     */
    @Override
    public FileSource findById(Long id) {
        return fileSourceDao.findByIdAndDelFlag(id,"0");
    }

    /**
     * 根据fileId查找文件
     * @param fileId
     * @return
     */
    @Override
    public List<FileSource> findByFileId(String fileId,List<String> fileTypes) {
        if (fileTypes != null && !fileTypes.isEmpty()){
            List<FileSource> files = fileSourceDao.findByFileIdAndDelFlagAndFileTypeIn(fileId,"0",fileTypes);
            return files;
        }else{
            List<FileSource> files = fileSourceDao.findByFileIdAndDelFlag(fileId,"0");
            return files;
        }
    }

    /**
     * 根据文件ID列表获取文件数据
     * @param ids
     * @return
     */
    @Override
    public List<FileSource> findByIds(List<Long> ids) {
        List<FileSource> files = fileSourceDao.findAllById(ids);
        return files;
    }

    /**
     * 根据fileId列表查找文件数据
     *
     * @param fileIds
     * @return
     */
    @Override
    public List<FileSource> findByFileIds(List<String> fileIds) {
        if (fileIds != null && !fileIds.isEmpty()){
            return fileSourceDao.findByFileIds(fileIds);
        }
        return null;
    }

    

    /**
     * 文件批量逻辑删除
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteAll(List<Long> ids) {
        if (ids != null && !ids.isEmpty()){
            fileSourceDao.UpdateAllDelFlag(ids);
        }
        return BusinessBizCode.OPTION_SUCCESS.getCode();
    }
    /**
     * 根据文件识别Id与文件类型获取数据(排除已删除的)
     *
     * @param fileId
     * @param fileTypes
     * @return
     */
    @Override
    public List<FileSource> findByFileIdAndFileTypes(String fileId, List<String> fileTypes) {
        return fileSourceDao.findByFileIdAndFileTypeInAndDelFlag(fileId,fileTypes,"0");
    }

    /**
     * 根据文件id与文件类型获取文件(每种取最新)
     *
     * @param fileId
     * @param fileTypes
     * @return
     */
    @Override
    public List<FileSource> findByFileIdAndFileTypeInAndNew(String fileId, List<String> fileTypes) {
        return fileSourceDao.findByFileIdAndFileTypeInAndNew(fileId,fileTypes);
    }

    @Override
    public List<FileSource> findByFileNameAndFileType(String fileName, String fileTypes) {
        return   fileSourceDao.findByFileSourceNameAndFileType(fileName,fileTypes);
    }

    /**
     * 删除文件
     *
     * @param fileId    文件id
     * @param fileTypes 文件类型
     * @param delete    是否物理删除
     */
    @Override
    public void deleteByFileIdAndTypes(String fileId, List<String> fileTypes, boolean delete) {
        if (delete){
            List<FileSource> files = fileSourceDao.findByFileIdAndFileTypeIn(fileId, fileTypes);
            for (FileSource file : files) {
                FileUtils.deleteFile(file.getAbsolutePath());
            }
            fileSourceDao.deleteAll(files);
        }else {
            fileSourceDao.updateByFileIdAndFileTypeIn(fileId,fileTypes);
        }
    }


}
