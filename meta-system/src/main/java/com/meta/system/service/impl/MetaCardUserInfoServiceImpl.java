package com.meta.system.service.impl;

import com.meta.common.utils.StringUtils;
import com.meta.system.dao.app.MetaCardUserInfoDao;
import com.meta.system.domain.app.MetaCardUserInfo;
import com.meta.system.service.MetaCardUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/06/07/15:41
 */
@Service
public class MetaCardUserInfoServiceImpl implements MetaCardUserInfoService {

    @Autowired
    private MetaCardUserInfoDao metaCardUserInfoDao;

    @Override
    public MetaCardUserInfo getInfo(String userId, String cardType) {
        MetaCardUserInfo info = metaCardUserInfoDao.getInfo(userId, cardType);
        if(info!=null){
            String address = "";
            if (StringUtils.isNotEmpty(info.getAddressLine2())) {
                address = info.getAddressLine1() + info.getAddressLine2();
            } else {
                address = info.getAddressLine1();
            }
            info.setAddress(address);
        }
        return info;
    }

    @Override
    public void save(MetaCardUserInfo metaCardUserInfo) {
        metaCardUserInfoDao.save(metaCardUserInfo);
    }

    @Override
    @Transactional
    public void updataStaue(String uid, String kycStatus, String cardType) {
        metaCardUserInfoDao.updataStaue(uid, kycStatus, cardType);
    }

    @Override
    public void updataStaueReason(String uid, String kycStatus, String kycReason, String cardType) {
        metaCardUserInfoDao.updataStaueReason(uid, kycStatus, kycReason, cardType);
    }

    @Override
    public MetaCardUserInfo findUid(String uid, String type) {
        return metaCardUserInfoDao.findUid(uid, type);
    }

    @Override
    public List<MetaCardUserInfo> getInfoList(String userId) {
        return metaCardUserInfoDao.getInfoList(userId);
    }
}
