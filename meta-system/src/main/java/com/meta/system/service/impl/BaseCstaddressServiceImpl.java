package com.meta.system.service.impl;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.utils.sign.Md5Utils;
import com.meta.system.constant.Constants;
import com.meta.system.dao.BaseCstaddressDao;
import com.meta.system.domain.MetaMainAddress;
import com.meta.system.domain.app.BaseCstaddress;
import com.meta.system.service.BaseCstaddressService;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.MetaMainAddressService;
import com.meta.system.uitls.RedisConstants;
import com.meta.system.vcc.HttpUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BaseCstaddressServiceImpl implements BaseCstaddressService {
    private final BaseCstaddressDao BaseCstaddressDao;
    private final StringRedisTemplate redisTemplate;
    private final ISysConfigService configService;
    private final MetaMainAddressService metaMainAddressService;

    /**
     * 保存
     */
    public void save(BaseCstaddress b) {
        deleteAndSaveDemoKeysWithScan(RedisConstants.BIZ_FIND_ALL_USER_ADDRESS_BASE + "*");
        BaseCstaddressDao.save(b);
    }

    @Override
    public List<BaseCstaddress> findByCstId(Long cstId, String sysId) {
        return BaseCstaddressDao.findByCstId(cstId, sysId);
    }

    @Override
    public BaseCstaddress findByAddress(String from) {
        return BaseCstaddressDao.findByAddress(from);
    }

    @Override
    public List<BaseCstaddress> findAll() {
        return BaseCstaddressDao.findAll();
    }

    @Override
    public Set<String> findAllAddress(String sysId) {
        String key;
        Set<String> allAddress;
        if (sysId == null || sysId.isEmpty()) {
            key = RedisConstants.BIZ_FIND_ALL_USER_ADDRESS_BASE + "*";
            if (hasAnyDemoKey(key)) {
                Set<String> allAddresses = new HashSet<>();
                List<String> keys = findDemoKeysWithScan(key);
                for (String redisKey : keys) {
                    String value = redisTemplate.opsForValue().get(redisKey);
                    if (value != null) {
                        List<String> addresses = JSONUtil.toBean(value, new TypeReference<List<String>>() {
                        }, true);
                        allAddresses.addAll(addresses);
                    }
                }
                return allAddresses;
            }
            List<BaseCstaddress> listAddressBySysId = BaseCstaddressDao.findAllAddressEntity();
            listAddressBySysId.forEach(address -> {
                if (address.getCstAdress() != null) {
                    address.setCstAdress(address.getCstAdress().toLowerCase());
                }
            });
            allAddress = listAddressBySysId.stream().map(BaseCstaddress::getCstAdress).collect(Collectors.toSet());
            // 按sysId保存集合到redis（只保存string列表)
            Map<String, List<String>> map = listAddressBySysId.stream()
                    .collect(Collectors.groupingBy(BaseCstaddress::getSysId,
                            Collectors.mapping(BaseCstaddress::getCstAdress, Collectors.toList())));
            // 删除所有地址(通过管道删除)
            deleteAndSaveDemoKeysWithScan(key);
            // 保存所有地址
            map.forEach((thisSysId, list) -> redisTemplate.opsForValue().set(
                    RedisConstants.BIZ_FIND_ALL_USER_ADDRESS_BASE + thisSysId, JSONUtil.toJsonStr(list), 2,
                    TimeUnit.MINUTES));
        } else {
            key = RedisConstants.BIZ_FIND_ALL_USER_ADDRESS_BASE + sysId;
            if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
                return JSONUtil.toBean(redisTemplate.opsForValue().get(key),
                        new TypeReference<HashSet<String>>() {
                        }, true);
            }
            List<BaseCstaddress> listAddressBySysId = BaseCstaddressDao.findListAddressBySysId(sysId);
            allAddress = listAddressBySysId.stream().map(BaseCstaddress::getCstAdress).collect(Collectors.toSet());
            redisTemplate.delete(RedisConstants.BIZ_FIND_ALL_USER_ADDRESS_BASE);
            redisTemplate.opsForValue().set(RedisConstants.BIZ_FIND_ALL_USER_ADDRESS_BASE,
                    JSONUtil.toJsonStr(allAddress), 2, TimeUnit.MINUTES);
        }
        return new HashSet<>(allAddress);
    }

    public void deleteAndSaveDemoKeysWithScan(String pattern) {
        // 1. 使用 SCAN 收集要删除的键
        List<String> keysToDelete = redisTemplate.execute((RedisCallback<List<String>>) connection -> {
            List<String> keys = new ArrayList<>();
            Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().match(pattern).count(100).build());
            while (cursor.hasNext()) {
                keys.add(new String(cursor.next()));
            }
            try {
                cursor.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            return keys;
        });

        // 2. 使用管道删除收集到的键
        if (keysToDelete != null && !keysToDelete.isEmpty()) {
            List<Object> results = redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                for (String key : keysToDelete) {
                    connection.del(key.getBytes());
                }
                return null;
            });
            System.out.println("Delete pipeline results: " + results);
        }
    }

    public List<String> findDemoKeysWithScan(String key) {
        List<String> matchingKeys = new ArrayList<>();

        redisTemplate.execute((RedisCallback<Void>) connection -> {
            // 使用 SCAN 迭代查找
            Cursor<byte[]> cursor = connection.scan(
                    ScanOptions.scanOptions()
                            .match(key) // 匹配模式
                            .count(100) // 每次迭代返回的数量
                            .build());

            while (cursor.hasNext()) {
                String nextKey = new String(cursor.next());
                matchingKeys.add(nextKey);
                Object value = redisTemplate.opsForValue().get(nextKey);
//                log.debug("Key: " + nextKey + ", Value: " + value);
            }

            // 关闭游标
            try {
                cursor.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null; // RedisCallback 需要返回 null
        });

        return matchingKeys;
    }

    public boolean hasAnyDemoKey(String key) {
        // 只要找到一个匹配的键就返回 true
        return Boolean.TRUE.equals(redisTemplate.execute((RedisCallback<Boolean>) connection -> {
            Cursor<byte[]> cursor = connection.scan(
                    ScanOptions.scanOptions()
                            .match(key)
                            .count(100)
                            .build());

            boolean hasKey = cursor.hasNext(); // 只要找到一个匹配的键就返回 true
            try {
                cursor.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            return hasKey;
        }));
    }

    @Override
    public void refreshAddress(String mainaddress, String sysId) {
        List<BaseCstaddress> all = BaseCstaddressDao.findBySysId(sysId);
        List<String> dataList = new ArrayList<>();
        if (!all.isEmpty()) {
            dataList = all.stream().map(BaseCstaddress::getCstAdress).collect(Collectors.toList());
        }
        // 主钱包地址
        dataList.add(mainaddress.toLowerCase());
        String key = Constants.BEP_ADDRESS + sysId;
        redisTemplate.opsForList().leftPushAll(key, dataList);

    }

    @Override
    public void refreshWallet() {
        List<MetaMainAddress> list = metaMainAddressService.getList("BEP20");
        for (MetaMainAddress metaMainAddress : list) {

            refreshAddress(metaMainAddress.getMainaddress().toLowerCase(), metaMainAddress.getSysId());
        }

    }

    @Override
    public void updateCstId(Long id, Long cstId) {
        BaseCstaddressDao.updateCstId(id, cstId);
    }

    @Override
    public String createWallet(Long userId, String coinCode, String sysId, String key) {
        // 如果找不到，需要去调用meta-web3j的接口，生成钱包地址
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        JSONObject req = new JSONObject();
        req.put("userId", userId);
        req.put("coinCode", coinCode);
        req.put("sysId", sysId);
        req.put("md5sum", Md5Utils.hash(userId + key));
        try {
            String url = configService.selectConfigByKey("meta_waller_url");
            log.info("bep创建钱包的地址：" + url);
            HttpResponse resultRep = HttpUtils.doPost(url, "/walletServe/create", "POST", headers, null,
                    req.toJSONString());
            if (resultRep.getStatusLine().getStatusCode() == 200) {
                String str = EntityUtils.toString(resultRep.getEntity());
                JSONObject resJson = JSONObject.parseObject(str);
                JSONObject dataJson = resJson.getJSONObject("data");
                return dataJson.getString("walletAddress");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public AjaxResult bepCollect(String wkey, String bepCollectUrl, String address) {
        // 进行归集
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        String url = bepCollectUrl + "meta/walletManager/sendTradeByCst";
        // 设置请求参数
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("address", address);
        params.add("type", "USDT");
        params.add("md5sum", Md5Utils.hash(address + wkey));
        log.info("发送BEP归集请求,address:" + address);
        // 发送POST请求
        ResponseEntity<String> responseEntity = new RestTemplate().exchange(url, HttpMethod.POST,
                new HttpEntity<>(params, headers), String.class);

        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            log.info("BEP归集成功");
            return AjaxResult.success("归集成功");
        } else {
            log.info("BEP归集失败");
            return AjaxResult.error("归集失败");
        }
    }
}
