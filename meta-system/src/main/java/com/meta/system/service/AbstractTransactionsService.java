package com.meta.system.service;

import com.meta.system.domain.app.AbstractTransactions;
import com.meta.system.dto.RechargeDto;
import com.meta.system.vo.CollectRoportVo;
import com.meta.system.vo.RechargeDataVo;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/23/14:49
 */
public interface AbstractTransactionsService<T extends AbstractTransactions> {
    abstract T findTime(String walletAddress);

    abstract List<String> getList(String walletAddress, int timestamp);

    abstract List<String> getList(String walletAddress);

    abstract Page<CollectRoportVo> getReportPage(String startDate, String endDate);

    abstract List<T> findByTxid(String txid);

    abstract void save(T transactions);

    abstract void updateTransactions(T transactions);

    abstract Page<RechargeDataVo> page(RechargeDto dto);

    abstract T findId(Long id);
}
