package com.meta.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.meta.common.utils.ThreadPoolUtil;
import com.meta.system.config.MoonbankConfig;
import com.meta.system.dao.MetaMerchantRechargeRecordsDao;
import com.meta.system.domain.moonbank.MetaMerchantRechargeRecords;
import com.meta.system.moonbank.models.ApiResponse;
import com.meta.system.moonbank.util.MoonbankEncryptUtil;
import com.meta.system.moonbank.util.MoonbankUtil;
import com.meta.system.service.MetaMerchantRechargeRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/26/10:50
 */
@Service
public class MetaMerchantRechargeRecordsServiceImpl implements MetaMerchantRechargeRecordsService {

    @Autowired
    private MoonbankUtil moonbankUtil;

    @Autowired
    private MetaMerchantRechargeRecordsDao metaMerchantRechargeRecordsDao;

    @Override
    public List<MetaMerchantRechargeRecords> queryMerchantRecords(int pageNum, int pageSize) throws Exception {
        ApiResponse<String> apiResponse = moonbankUtil.merchantRecharge(pageNum, pageSize);

        if (apiResponse.isSuccess()) {
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
            System.out.println("accountRecharge encode result===>" + descStr);
            List<MetaMerchantRechargeRecords> rList = JSON.parseArray(descStr, MetaMerchantRechargeRecords.class);
            asynMRData(rList);
            return rList;
        }else{
            throw new Exception("moonbank接口请求错误");
        }
    }

    /**
     * 异步处理是否保存数据
     * @param rList
     */
    public void asynMRData(List<MetaMerchantRechargeRecords> rList) {
        ThreadPoolUtil.execute(new Runnable() {
            @Override
            public void run() {
                List<MetaMerchantRechargeRecords> list = new ArrayList<>();
                for (MetaMerchantRechargeRecords metaMerchantRechargeRecords : rList) {
                    //判断数据是否存在
                    MetaMerchantRechargeRecords mr = metaMerchantRechargeRecordsDao.findById(metaMerchantRechargeRecords.getId()).orElse(null);
                    if (mr == null) {
                        list.add(metaMerchantRechargeRecords);
                    }
                }
                //保存
                if (list.size()>0){
                    metaMerchantRechargeRecordsDao.saveAll(list);
                }
            }
        });

    }
}
