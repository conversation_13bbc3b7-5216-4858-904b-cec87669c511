package com.meta.system.service;

import com.meta.system.domain.app.BepTransactions;
import com.meta.system.dto.RechargeDto;
import com.meta.system.vo.CollectRoportVo;
import com.meta.system.vo.RechargeDataVo;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/23/14:49
 */
public interface BepTransactionsService {
    BepTransactions findTime(String walletAddress);

    List<String> getList(String walletAddress, int timestamp);

    List<String> getList(String walletAddress);

    Page<CollectRoportVo> getReportPage(String startDate, String endDate);

    List<BepTransactions> findByTxid(String txid);

    void save(BepTransactions bepTransactions);

    void updateBepTransactions(BepTransactions bepTransactions);

    Page<RechargeDataVo> page(RechargeDto dto);

    BepTransactions findId(Long id);
}
