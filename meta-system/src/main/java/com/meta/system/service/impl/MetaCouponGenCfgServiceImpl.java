package com.meta.system.service.impl;

import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.StringUtils;
import com.meta.system.dao.MetaCouponGenCfgDao;
import com.meta.system.domain.app.MetaCouponGenCfg;
import com.meta.system.domain.app.MetaCouponInfo;
import com.meta.system.dto.CouponDto;
import com.meta.system.service.MetaCouponGenCfgService;
import com.meta.system.service.MetaCouponInfoService;
import com.meta.system.vo.MetaCouponGenCfgVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/09/13:49
 */
@Service
public class MetaCouponGenCfgServiceImpl implements MetaCouponGenCfgService {

    @Autowired
    private MetaCouponGenCfgDao metaCouponGenCfgDao;

    @Autowired
    private MetaCouponInfoService metaCouponInfoService;

    @Override
    public Page<MetaCouponGenCfgVo> getCouponConfigList(CouponDto couponDto) {
        Page<MetaCouponGenCfgVo> page = metaCouponGenCfgDao.getCouponConfigList(couponDto);
        List<MetaCouponGenCfgVo> list = page.getContent();
        for (MetaCouponGenCfgVo vo : list) {
//            vo.setCouponScenariosList(getResult(vo.getCouponScenarios()));
            vo.setCouponCardTypeList(getResult(vo.getCouponCardType()));
            vo.setCouponCardlevelList(getResult(vo.getCouponCardlevel()));
            vo.setCouponNodeList(getResult(vo.getCouponNode()));
            if (vo.getDiscountRate() != null) {
                vo.setDiscountRate(vo.getDiscountRate().multiply(new BigDecimal("100")));
            }
            if(vo.getCouponUsageLimit()==0){
                List<MetaCouponInfo> data = metaCouponInfoService.findListByCfgId(vo.getId());
                if(data.size()>0){

                    vo.setCouponNm(data.get(0).getCouponNm());
                }

            }
        }

        return page;
    }

    private String[] getResult(String str) {
        String[] result = null;
        if (StringUtils.isNotEmpty(str)) {

            if (str.contains(",")) {
                result = str.split(",");
            } else {
                result = new String[1]; // 初始化数组
                result[0] = str; // 将单个元素赋值给数组
            }

        }
        return result;

    }


    /**
     * 保存
     *
     * @param metaCouponGenCfg
     */
    @Override
    public void insertCouponConfig(MetaCouponGenCfg metaCouponGenCfg) {
        if (metaCouponGenCfg.getAdmUserId() == null) {
            metaCouponGenCfg.setAdmUserId(SecurityUtils.getLoginUser().getUserId());
        }
//        if(metaCouponGenCfg.getCouponScenariosList()!=null){
//            metaCouponGenCfg.setCouponScenarios(String.join(",", metaCouponGenCfg.getCouponScenariosList()));
//        }
        if (metaCouponGenCfg.getCouponCardTypeList() != null) {
            metaCouponGenCfg.setCouponCardType(String.join(",", metaCouponGenCfg.getCouponCardTypeList()));
        }
        if (metaCouponGenCfg.getCouponCardlevelList() != null) {
            metaCouponGenCfg.setCouponCardlevel(String.join(",", metaCouponGenCfg.getCouponCardlevelList()));
        }
        if (metaCouponGenCfg.getCouponNodeList() != null) {
            metaCouponGenCfg.setCouponNode(String.join(",", metaCouponGenCfg.getCouponNodeList()));
        }

        metaCouponGenCfg.setCouponAva(metaCouponGenCfg.getCouponTot());
        metaCouponGenCfg.setCouponUsed(0);
        metaCouponGenCfg.setCreateTime(new Date());
        metaCouponGenCfg.setUpdateTime(new Date());
        metaCouponGenCfgDao.save(metaCouponGenCfg);

        metaCouponInfoService.saveForCfg(metaCouponGenCfg);

    }

    @Override
    public void updateCouponConfig(MetaCouponGenCfg metaCouponGenCfg) {

        if (metaCouponGenCfg.getCouponCardTypeList() != null) {
            metaCouponGenCfg.setCouponCardType(String.join(",", metaCouponGenCfg.getCouponCardTypeList()));
        }
        if (metaCouponGenCfg.getCouponCardlevelList() != null) {
            metaCouponGenCfg.setCouponCardlevel(String.join(",", metaCouponGenCfg.getCouponCardlevelList()));
        }
        if (metaCouponGenCfg.getCouponNodeList() != null) {
            metaCouponGenCfg.setCouponNode(String.join(",", metaCouponGenCfg.getCouponNodeList()));
        }
        metaCouponGenCfg.setUpdateTime(new Date());
        metaCouponGenCfgDao.save(metaCouponGenCfg);
        metaCouponInfoService.updateForCfg(metaCouponGenCfg);

    }

    @Override
    public void deleteCouponConfig(Integer id) {
        metaCouponGenCfgDao.deleteById(id);
        metaCouponInfoService.deleteByCfg(id);
    }
}
