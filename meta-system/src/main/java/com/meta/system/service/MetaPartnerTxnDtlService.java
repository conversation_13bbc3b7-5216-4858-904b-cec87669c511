package com.meta.system.service;

import com.meta.common.enums.app.AlgorithmType;
import com.meta.common.enums.app.FreezeType;
import com.meta.common.enums.app.PartnerTxnType;
import com.meta.system.domain.groupkey.MetaPartnerAssetPoolKey;
import com.meta.system.domain.log.TxnDtlUSD;
import com.meta.system.domain.partner.MetaPartnerAssetPool;
import com.meta.system.domain.partner.MetaPartnerTxnDtl;
import com.meta.system.dto.PoolTxnDto;
import com.meta.system.vo.PoolTxnDtlUSDVo;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/06/20/17:00
 */
public interface MetaPartnerTxnDtlService {
    void save(MetaPartnerTxnDtl txnDtl);

    Page<PoolTxnDtlUSDVo> pageForUSD_B(PoolTxnDto poolTxnDto);

    List<MetaPartnerTxnDtl> findCardIdAndTransactionId(String userBankcardId, String recordNo, String[] txnTypes);

    List<MetaPartnerTxnDtl> findCardIdAndTxnIdAndStatus(String cardId, String stauts, String[] txnTypes);

    List<MetaPartnerTxnDtl> findCardRechare(String cardId, String stauts, String txnId);

    void updateStatus(String cardId, String stauts, String txnId);

    void saveUSD(MetaPartnerAssetPool metaPartnerAssetPool, PartnerTxnType txnType, String cardId, String currency, Integer num, BigDecimal txnAmount, BigDecimal fee, Character txnStatus, String txnId) ;

    void dealPool(BigDecimal amount, String sysId, String userBankcardId, FreezeType freezeType, AlgorithmType algorithmType, String txnId);

    void updateDtl(MetaPartnerTxnDtl metaPartnerTxnDtl, String orderTransactionType);

    void updateDtl(List<MetaPartnerTxnDtl> list, String orderTransactionType);

    List<MetaPartnerTxnDtl> findPaddingKunData(String type, String status);
}
