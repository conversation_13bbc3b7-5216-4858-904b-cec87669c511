package com.meta.system.service;


import com.meta.common.core.domain.AjaxResult;
import com.meta.system.domain.app.ArbCstaddress;

import java.util.List;
import java.util.Set;

public interface ArbCstaddressService {


    /**
     * 保存
     * @param b
     */
    void save(ArbCstaddress b);

    List<ArbCstaddress> findByCstId(Long cstId,String sysId);

    ArbCstaddress findByAddress(String from);

    List<ArbCstaddress> findAll();

    Set<String> findAllAddress(String sysId);

    void refreshAddress(String mainAddress, String sysId);

    void refreshWallet();

    void updateCstId(Long id, Long cstId);

    String createWallet(Long userId, String coinCode, String sysId, String key);

    AjaxResult bepCollect(String wkey, String bepCollectUrl, String address) ;
}
