package com.meta.system.service.impl;

import com.meta.common.enums.ApprovalStatus;
import com.meta.common.utils.DateUtils;
import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.code.BusinessBizCode;
import com.meta.system.dao.SysUserDao;
import com.meta.system.dao.app.KYCDao;
import com.meta.system.domain.app.KYCApplication;
import com.meta.system.dto.kyc.KYCDto;
import com.meta.system.email.Mail;
import com.meta.system.email.SendEmail;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.KYCService;
import com.meta.system.vo.KYCVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional(readOnly = true)
public class KYCServiceImpl implements KYCService {

    @Resource
    private KYCDao kycDao;

    @Resource
    private SysUserDao userDao;

    @Autowired
    private ISysConfigService configService;


    @Autowired
    private SendEmail sendEmail;

    /**
     * 检测是否已经实名认证
     *
     * @param userId 用户id
     * @return 是否已经实名
     */
    @Override
    public boolean checkKYC(Long userId) {
        KYCApplication application = kycDao.findByUserId(userId);
        return application != null && ApprovalStatus.PASS.getOrdinal().equals(application.getApprovalStatus());
    }

    /**
     * 新增实名认证申请
     *
     * @param application 个人认证信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public KYCApplication apply(KYCApplication application) {
        application.trim();// 去除字符串前后空格
        application.setUserId(SecurityUtils.getLoginUser().getUserId());
        application.setApprovalStatus(ApprovalStatus.PENDING.getOrdinal());
        KYCApplication kyc = kycDao.findByUserId(SecurityUtils.getLoginUser().getUserId());
        if (kyc != null) {
            application.setId(kyc.getId());
            application.setCreatedAt(kyc.getCreatedAt());
            application.setFileId(kyc.getFileId());
        } else {
            application.setCreatedAt(DateUtils.getNowDate());
        }
        application.setUpdatedAt(DateUtils.getNowDate());

        return kycDao.save(application);
    }

    /**
     * 分页查询
     *
     * @param dto 查询条件
     * @return 查询结果
     */
    @Override
    public Page<KYCVo> page(KYCDto dto) {
        return kycDao.page(dto);
    }

    /**
     * 根据用户ID获取实名信息
     *
     * @param userId 用户id
     * @return 实名详情
     */
    @Override
    public KYCApplication findByUserId(Long userId) {
        return kycDao.findByUserId(userId);
    }

    @Override
    public int approval(KYCDto dto) {
        Optional<KYCApplication> kyc = kycDao.findById(dto.getId());
        if (!kyc.isPresent()) {
            return BusinessBizCode.OPTION_ERROR.getCode();
        }
        KYCApplication kycApplication = kyc.get();
        kycApplication.setApprovalStatus(dto.getApprovalStatus());
        if (StringUtils.isNotEmpty(dto.getRemark())) {
            kycApplication.setRemark(dto.getRemark());
        }
        String nickname = "";
        // 将nick_name或者ent_name 保存到 sys-user
        if (StringUtils.isNotEmpty(kycApplication.getNickname())) {
            nickname = kycApplication.getNickname();
        } else {
            nickname = kycApplication.getEntName();
        }
        kycDao.save(kycApplication);
      //  userDao.updateNickname(kycApplication.getUserId(), nickname);
        return BusinessBizCode.OPTION_SUCCESS.getCode();
    }

    @Override
    public KYCVo findById(Long id) {
        return kycDao.findVoById(id);
    }

    @Override
    public void sendMail() {
        //发送邮件
        String auditEmail = configService.selectConfigByKey("audit_email");
        if(StringUtils.isNotEmpty(auditEmail)){
            if(auditEmail.contains(",")){
                String[] result=auditEmail.split(",");
                for (String  address:result){
                    Mail mail = new Mail();
                    mail.setEmail(address);
                    mail.setContent("KYC审核:" + SecurityUtils.getLoginUser().getUserId());
                    mail.setSubject("KYC审核");
                    sendEmail.sendCheckEmail(mail);

                }
            }else {
                Mail mail = new Mail();
                mail.setEmail(auditEmail);
                mail.setContent("KYC审核:" + SecurityUtils.getLoginUser().getUserId());
                mail.setSubject("KYC审核");
                sendEmail.sendCheckEmail(mail);
            }

        }


    }

    public static void main(String[] args) {
        System.out.println(UUID.randomUUID().toString().replace("-", ""));
    }

}
