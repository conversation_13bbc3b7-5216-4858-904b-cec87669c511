package com.meta.system.service;

import com.meta.common.core.domain.AjaxResult;
import com.meta.common.enums.app.AlgorithmType;
import com.meta.common.enums.app.FreezeType;
import com.meta.system.domain.app.CoinTxnDtl;
import com.meta.system.dto.RechargeDto;
import com.meta.system.dto.VccReqDto;
import com.meta.system.vo.CoinOperationDto;
import com.meta.system.vo.CoinTxnDtlVo;
import org.springframework.data.domain.Page;

import java.util.List;

public interface CoinTxnDtlService {
    /**
     * 保存
     * @param dtl
     */
    void save(CoinTxnDtl dtl);

    /**
     *
     * @param receiptNo 链上交易号
     * @return
     */
    CoinTxnDtl findByReceiptNo(String receiptNo);

    /**
     * 获取充提币记录
     * @param userId 用户id
     * @param recordType
     * @param coinNet 充值链
     * @param txnCode 类型
     * @return
     */
    Page<CoinTxnDtlVo> coinTxnRecord(Long userId, String recordType, String coinNet, String txnCode);


    Page<CoinTxnDtlVo> coinTxnRecord3(CoinTxnDtlVo vo);

    CoinTxnDtlVo getCoinTxnDetail(Long txnId);

    /**
     * 操作个人账户
     * @param req
     * @return
     */
    AjaxResult update(VccReqDto req,String  fromCoin);

    /**
     * 销卡处理
     * @param req
     * @param coin
     * @return
     */
    AjaxResult updateCloseCard(VccReqDto req, String coin);

    /**
     * 操作个人账户
     * @param req
     * @return
     */
    AjaxResult update2(VccReqDto req,String  fromCoin);

    AjaxResult updateLogisticsFee(VccReqDto req,String  fromCoin);

    AjaxResult updateDtlStatue(CoinTxnDtl dtl, FreezeType freezeType, AlgorithmType walletAlgorithmType);


    AjaxResult transferCoin(VccReqDto source, VccReqDto target, String fromCoin);

    List<CoinTxnDtl> findAllByCardIdAndStatusAndTxnTypeForCoin(String cardId, String valueOf, String[] txnTypes);

    void updateTxnProduct(String cardId, String userBankId);

    Page<CoinTxnDtl> list(CoinTxnDtl coinTxnDtl);

    void withdraw(CoinOperationDto withdraw);

    Page<CoinTxnDtlVo> page(CoinTxnDtl coinTxnDtl);

    CoinTxnDtlVo selectById(Long id);

    void recharge(CoinOperationDto recharge);

    List<CoinTxnDtl> findAllByCardIdAndStatusAndTxnId(String cardId, String stauts, String txnId);

    List<CoinTxnDtl> findAllByCardIdAndStatusAndTxnId(String cardId, String stauts, String txnId, String[] txnTypes) ;
}
