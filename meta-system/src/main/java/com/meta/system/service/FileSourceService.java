package com.meta.system.service;
import com.meta.system.domain.app.FileSource;

import java.util.List;

public interface FileSourceService {
    /**
     * 保存文件
     * @param file
     * @return
     */
    List<FileSource> save(List<FileSource> file);

    /**
     * 文件逻辑删除
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 根据od查找文件数据
     * @param id
     * @return
     */
    FileSource findById(Long id);

    /***
     * 根据fileId查找文件
     * @param fileId
     * @return
     */
    List<FileSource> findByFileId(String fileId,List<String> fileTypes);

    /**
     * 根据文件id列表查找文件数据
     * @param ids
     * @return
     */
    List<FileSource> findByIds(List<Long> ids);


    /**
     * 根据fileId列表查找文件数据
     * @param fileIds
     * @return
     */
    List<FileSource> findByFileIds(List<String> fileIds);

    /**
     * 文件批量逻辑删除
     * @param ids
     * @return
     */
    int deleteAll(List<Long> ids);


    /**
     * 根据文件识别Id与文件类型获取数据(排除已删除的)
     * @param fileId
     * @param fileTypes
     * @return
     */
    List<FileSource> findByFileIdAndFileTypes(String fileId, List<String> fileTypes);

    /**
     * 根据文件id与文件类型获取文件(每种取最新)
     * @return
     */
    List<FileSource> findByFileIdAndFileTypeInAndNew(String fileId, List<String> fileTypes);

    List<FileSource> findByFileNameAndFileType(String fileName, String fileTypes);

    /**
     * 删除文件
     * @param fileId 文件id
     * @param fileTypes 文件类型
     * @param delete 是否物理删除
     *
     */
    void deleteByFileIdAndTypes(String fileId,List<String> fileTypes,boolean delete);
}
