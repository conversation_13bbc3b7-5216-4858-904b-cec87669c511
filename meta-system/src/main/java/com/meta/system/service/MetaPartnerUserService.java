package com.meta.system.service;

import com.meta.system.domain.partner.MetaPartnerUser;

/**
 * <AUTHOR>
 * @date 2024/05/30/14:46
 */
public interface MetaPartnerUserService {
    MetaPartnerUser findEmailOrPhone(String mobileNumber, String email, String mobilePrefix,String sysId);

    void save(MetaPartnerUser metaPartnerUser);

    MetaPartnerUser findEmailAndSysId(String cardHolder, String sysId);

    MetaPartnerUser findUid(String uid);

    void updatePartnerUser(Long id, String uid);

    MetaPartnerUser findData();
}
