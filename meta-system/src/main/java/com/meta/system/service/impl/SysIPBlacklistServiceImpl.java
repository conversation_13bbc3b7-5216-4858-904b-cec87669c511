package com.meta.system.service.impl;

import com.meta.common.constant.Constants;
import com.meta.common.core.redis.RedisCache;
import com.meta.system.dao.SysIPBlacklistDao;
import com.meta.system.domain.SysIPBlacklist;
import com.meta.system.service.SysIPBlacklistService;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@Service
public class SysIPBlacklistServiceImpl implements SysIPBlacklistService {
    @Resource
    private SysIPBlacklistDao sysIPBlacklistDao;
    @Resource
    private RedisCache redisCache;


    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        updateCache();
    }

    /**
     * 新增黑名单
     *
     * @param sysIPBlacklist 黑名单
     */
    @Override
    public void insert(SysIPBlacklist sysIPBlacklist) {
        sysIPBlacklistDao.save(sysIPBlacklist);
        // 更新redis缓存
        redisCache.deleteObject(Constants.IP_BLACK_KEY);
        redisCache.setCacheObject(Constants.IP_BLACK_KEY, sysIPBlacklistDao.findAllIPs());
    }

    /**
     * 根据id删除黑名单
     *
     * @param id 主键
     */
    @Override
    public void deleteById(Long id) {
        sysIPBlacklistDao.deleteById(id);
        updateCache();
    }

    /**
     * 根据ip删除黑名单
     *
     * @param ip ip地址
     */
    @Override
    public void deleteByIP(String ip) {
        sysIPBlacklistDao.deleteByIP(ip);
        updateCache();
    }

    /**
     * 判断ip是否在黑名单中
     *
     * @param ipAddr ip
     * @return 是否在黑名单中
     */
    @Override
    public boolean checkIP(String ipAddr) {
        List<SysIPBlacklist> sysIPBlacklists = sysIPBlacklistDao.findByIpAddress(ipAddr);
        return sysIPBlacklists != null && !sysIPBlacklists.isEmpty();
    }

    /**
     * 批量删除黑名单
     * @param ips
     */
    @Override
    public void deleteByIps(List<String> ips) {
        List<SysIPBlacklist> all = sysIPBlacklistDao.findAll();
        List<SysIPBlacklist> needDel = new ArrayList<>();
        for (SysIPBlacklist sysIPBlacklist : all) {
            if (ips.contains(sysIPBlacklist.getIpAddress())) {
                needDel.add(sysIPBlacklist);
            }
        }
        sysIPBlacklistDao.deleteAll(needDel);
        updateCache();
    }

    /**
     * 更新redis缓存
     */
    public void updateCache() {
        // 更新redis缓存
        redisCache.deleteObject(Constants.IP_BLACK_KEY);
        List<String> allIPs = sysIPBlacklistDao.findAllIPs();
        if (allIPs != null && !allIPs.isEmpty()) {
            redisCache.setCacheObject(Constants.IP_BLACK_KEY,allIPs);
        }

    }
}
