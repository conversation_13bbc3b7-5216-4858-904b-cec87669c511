package com.meta.system.service.impl;

import com.meta.system.dao.MetaNotifyLogDao;
import com.meta.system.domain.es.MetaNotifyLog;
import com.meta.system.service.MetaNotifyLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/09/03/10:36
 */
@Service
public class MetaNotifyLogServiceImpl implements MetaNotifyLogService {
    @Autowired
    private MetaNotifyLogDao metaNotifyLogDao;


    @Override
    public void save(MetaNotifyLog metaNotifyLog) {
        metaNotifyLogDao.save(metaNotifyLog);
    }
}
