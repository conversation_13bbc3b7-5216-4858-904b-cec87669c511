package com.meta.system.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meta.common.config.LockCfg;
import com.meta.common.constant.Constants;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.enums.PhysicalCardType;
import com.meta.common.enums.VccTxnType;
import com.meta.common.enums.app.*;
import com.meta.common.enums.vcc.CardStatus;
import com.meta.common.exception.CustomException;
import com.meta.common.utils.*;
import com.meta.common.utils.uuid.UniqueIDGenerator;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.common.utils.vcc.StringUtil;
import com.meta.system.config.MoonbankConfig;
import com.meta.system.constant.ChannelType;
import com.meta.system.constant.KzOrderStatus;
import com.meta.system.dao.CreditCardDao;
import com.meta.system.dao.TxnDtlCodeDao;
import com.meta.system.dao.app.CardConfigDao;
import com.meta.system.dao.app.CoinBalanceDao;
import com.meta.system.domain.CreditCard;
import com.meta.system.domain.CreditCardLog;
import com.meta.system.domain.Wallet;
import com.meta.system.domain.app.*;
import com.meta.system.domain.groupkey.MetaPartnerAssetPoolKey;
import com.meta.system.domain.log.TxnDtlCode;
import com.meta.system.domain.log.TxnDtlUSD;
import com.meta.system.domain.partner.MetaPartnerAssetPool;
import com.meta.system.domain.partner.MetaPartnerTxnDtl;
import com.meta.system.domain.partner.MetaPartnerUser;
import com.meta.system.domain.partner.MetaPushData;
import com.meta.system.dto.VccReqDto;
import com.meta.system.email.SendEmail;
import com.meta.system.fornax.service.FornaxService;
import com.meta.system.kun.constants.KunCardStatus;
import com.meta.system.kun.constants.KunPriceSide;
import com.meta.system.kun.service.KunService;
import com.meta.system.models.CardInfo;
import com.meta.system.models.CardRechargeResult;
import com.meta.system.models.CardResult;
import com.meta.system.moonbank.models.ActivatecardRequest;
import com.meta.system.moonbank.models.ApiResponse;
import com.meta.system.moonbank.util.MoonbankEncryptUtil;
import com.meta.system.moonbank.util.MoonbankUtil;
import com.meta.system.service.*;
import com.meta.system.uitls.ApntUtil;
import com.meta.system.uitls.SendEmailDataUtils;
import com.meta.system.vo.CardConfigVo;
import com.meta.system.vo.CardVo;
import com.meta.system.vo.CreditCardDtl;
import com.meta.system.vo.CreditCardVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Service
public class CreditCardServiceImpl implements CreditCardService {
    private final Logger logger = LoggerFactory.getLogger(CreditCardServiceImpl.class);
    @Autowired
    private CreditCardDao creditCardDao;
    @Autowired
    private ApntUtil apntUtil;

    @Autowired
    private CardConfigDao cardConfigDao;
    @Autowired
    private MoonbankUtil moonbankUtil;

    @Autowired
    private WalletService walletService;
    @Autowired
    private SendEmail sendEmail;

    @Autowired
    private TxnService txnService;

    @Autowired
    private ProcedureUtils procedureUtils;

    @Autowired
    private CreditCardLogService creditCardLogService;

    @Autowired
    @Lazy
    private MetaPartnerTxnDtlService metaPartnerTxnDtlService;

    @Autowired
    private MetaPartnerAssetPoolService metaPartnerAssetPoolService;

    @Autowired
    private CoinTxnDtlService coinTxnDtlService;

    @Autowired
    private ExchangeRateService exchangeRateService;

    @Autowired
    private ISysDictDataService sysDictDataService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    @Lazy
    private MetaCouponInfoService metaCouponInfoService;

    @Autowired
    @Lazy
    private KunService kunService;

    @Autowired
    private MetaPartnerUserService metaPartnerUserService;

    @Autowired
    private LockCfg lockCfg;
    @Autowired
    private MetaUserInvitesService metaUserInvitesService;

    @Autowired
    @Lazy
    private SendEmailDataUtils sendEmailDataUtils;

    @Autowired
    private MetaPhysicalCardService metaPhysicalCardService;

    @Autowired
    private McardService mcardService;

    @Autowired
    private MetaPushDataService metaPushDataService;

    @Autowired
    @Lazy
    private FornaxService fornaxService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private CoinBalanceDao coinBalanceDao;


    @Override
    public void save(CreditCard c) {
        creditCardDao.save(c);
    }

    @Override
    public CreditCard findByCardNo(String cardNo) {
        return creditCardDao.findByCardNo(cardNo);
    }

    @Override
    public CreditCard findByCardId(String cardId) {
        Optional<CreditCard> op = creditCardDao.findById(cardId);
        if (op.isPresent()) {
            return op.get();
        }
        return null;
    }

    /**
     * 通过上游卡id进行查询
     *
     * @param source     渠道
     * @param bankCardId 卡id
     * @return
     */
    @Override
    public CreditCard findByBankCardId(String source, String bankCardId) {
        CreditCard card = creditCardDao.findByBankCardId(source, bankCardId);
        return card;
    }

    @Override
    public List<CreditCard> listByUserId(Long userId) {
        return creditCardDao.findByUserIdOrderByBalanceDesc(userId);
    }

    @Override
    public List<CreditCard> listAllByEncrypt(String encrypt) {
        return creditCardDao.findByEncrypt(encrypt);
    }

    @Override
    public AjaxResult recharge(String cardId, BigDecimal balance) {
        creditCardDao.updateAmount(cardId, balance);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateStatus(String cardId, String status) {
        creditCardDao.updateStatus(cardId, status);
        return AjaxResult.success();
    }

    @Override
    public void updateActiveStatus(String cardId) {
        creditCardDao.updateActiveStatus(cardId);
    }

    @Override
    public AjaxResult update(CreditCard c) {
        creditCardDao.save(c);
        return AjaxResult.success();
    }

    @Override
    public void updateAmount(String cardId, BigDecimal amount) {
//        CreditCard c = creditCardDao.findByCardId(cardId);
        creditCardDao.updateAmount(cardId, amount);
    }

    @Override
    public void update(String cardId, String status, BigDecimal amount) {
        creditCardDao.update(cardId, status, amount);
    }

    @Override
    @Transactional
    public void saveAll(List<CreditCard> list) {
        creditCardDao.saveAll(list);
    }

    @Override
    public List<CardConfigVo> getValidCard() {
        List<CardConfigVo> validCard = cardConfigDao.findValidCard();
        if (validCard != null && !validCard.isEmpty()) {
            // List<CardConfigVo> vo = new ArrayList<>();
            for (CardConfigVo card : validCard) {

//                String display=MessageUtils.message("credit_card_type_" + card.getCardType());
                String display = sysDictDataService.getLabel("credit_card_type", card.getCardType());
                card.setDisplay(display);

            }
            return validCard;
        }
        return new ArrayList<>();
    }

    @Override
    public List<CardConfigVo> findPhysicalCard() {
        List<String> validCard = cardConfigDao.findPhysicalCard();
        if (validCard != null && !validCard.isEmpty()) {
            List<CardConfigVo> vo = new ArrayList<>();
            for (String cardType : validCard) {
                CardConfigVo cardConfigVo = new CardConfigVo();
                cardConfigVo.setCardType(cardType);
                String display = sysDictDataService.getLabel("credit_card_type", cardType);
                cardConfigVo.setDisplay(display);
                vo.add(cardConfigVo);
            }
            return vo;
        }
        return new ArrayList<>();
    }

    @Override
    public Page<CreditCardVo> page(CreditCard card) {

        return creditCardDao.page(card);
    }

    @Override
    public Page<CreditCardVo> pageB(CreditCard card) {
        return creditCardDao.pageB(card);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public void updateCardMoonbank(String cardId, Long userId, String moonbankUid) {
        CreditCard c = creditCardDao.getOne(cardId);
        //更新卡信息
        updateCardData(c.getSource(), c, moonbankUid);
        //更新卡信息
//        ApiResponse<String> apiResponse = moonbankUtil.queryBankcardInfo(moonbankUid, Integer.valueOf(c.getCardId()));
//        if (apiResponse.isSuccess()) {
//            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
//
//            JSONObject j = JSONObject.parseObject(descStr);
//            String cardStatus = j.getString("userBankCardStatus");
//            String cvv2 = j.getString("cardCvv");
//            String cardNo = j.getString("cardNo");
//            if (StringUtils.isNotEmpty(cvv2)) {
//                c.setCvv2(AESUtils.aesEncrypt(Constants.card_key, cvv2));
//            }
//            if (StringUtils.isNotEmpty(cardNo)) {
//                c.setCardNo(AESUtils.aesEncrypt(Constants.card_key, cardNo));
//            }
//            JSONObject vccCardHolderVo = j.getJSONObject("vccCardHolderVo");
//            if (vccCardHolderVo != null) {
//                c.setBillingCountry(vccCardHolderVo.getString("countryCode"));
//                c.setProvince(vccCardHolderVo.getString("billingState"));
//                c.setCity(vccCardHolderVo.getString("billingCity"));
//                c.setAddressLine1(vccCardHolderVo.getString("billingAddress"));
//            }
//            c.setSearchTime(DateUtils.getNowDate());
//            c.setCardStatus(cardStatus);
//            String expiryDate = j.getString("expiryDate");
//            if (StringUtils.isNotEmpty(expiryDate) && expiryDate.length() >= 7) {
//                c.setExpirationTime(expiryDate.substring(5, 7) + expiryDate.substring(0, 2));
//            } else if (StringUtils.isNotEmpty(expiryDate) && expiryDate.length() == 5 && expiryDate.contains("/")) {
//                c.setExpirationTime(expiryDate.substring(3, 5) + expiryDate.substring(0, 2));
//            }
//
//
//            c.setSearchTime(DateUtils.getNowDate());
//            c.setRechargeFee(j.getString("rechargeFee"));
//            c.setMonthFee(j.getString("monthFee"));
//            c.setHashHolderInfo(j.getString("hashHolderInfo"));
//
//
//            ApiResponse<String> apiResponse2 = moonbankUtil.queryBankcardBalance(moonbankUid, Integer.valueOf(c.getCardId()));
//            if (apiResponse.isSuccess()) {
//                String descStr2 = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse2.getResult());
//                JSONObject j2 = JSONObject.parseObject(descStr2);
//                c.setBalance(j2.getBigDecimal("balance"));
//                c.setCurrency(j2.getString("currency"));
//            }
//            creditCardDao.save(c);

//        }
    }


    @Transactional
    public void updateCardMoonbankInfo(String cardId, String bankCardId, String moonbankUid) {
        CreditCard c = creditCardDao.getOne(cardId);

        //更新卡信息
        ApiResponse<String> apiResponse = moonbankUtil.queryBankcardInfo(moonbankUid, Integer.valueOf(bankCardId));
        if (apiResponse.isSuccess()) {
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

            JSONObject j = JSONObject.parseObject(descStr);
            String cardStatus = j.getString("userBankCardStatus");
            String cvv2 = j.getString("cardCvv");
            String cardNo = j.getString("cardNo");
            if (StringUtils.isNotEmpty(cvv2)) {
                c.setCvv2(AESUtils.aesEncrypt(Constants.card_key, cvv2));
            }
            if (StringUtils.isNotEmpty(cardNo)) {
                c.setCardNo(AESUtils.aesEncrypt(Constants.card_key, cardNo));
            }
            c.setSearchTime(DateUtils.getNowDate());
            String status = c.getCardStatus();
            if (StringUtils.isNotEmpty(cardStatus) && !cardStatus.equals(status)) {
                c.setStatusUpdateTime(new Date());
            }
            c.setCardStatus(cardStatus);
            String expiryDate = j.getString("expiryDate");
            if (StringUtils.isNotEmpty(expiryDate) && expiryDate.length() >= 7) {
                c.setExpirationTime(expiryDate.substring(5, 7) + expiryDate.substring(0, 2));
            } else if (StringUtils.isNotEmpty(expiryDate) && expiryDate.length() == 5 && expiryDate.contains("/")) {
                c.setExpirationTime(expiryDate.substring(3, 5) + expiryDate.substring(0, 2));
            }

            c.setSearchTime(DateUtils.getNowDate());
            ApiResponse<String> apiResponse2 = moonbankUtil.queryBankcardBalance(moonbankUid, Integer.valueOf(c.getBankCardId()));
            if (apiResponse.isSuccess()) {
                String descStr2 = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse2.getResult());
                JSONObject j2 = JSONObject.parseObject(descStr2);
                c.setBalance(j2.getBigDecimal("balance"));
                c.setCurrency(j2.getString("currency"));
            }
            creditCardDao.save(c);

        }
    }

    TxnDtlCodeDao txnDtlCodeDao;

    /**
     * 充值成功
     *
     * @param cardId
     * @param txnId
     * @param moonbankUid
     * @param cardStatus
     */
    @Override
    public void dealCoinAfterMoonbookRecharge(String cardId, String txnId, String moonbankUid, String cardStatus) {

        boolean b = lockCfg.tryLock(txnId, txnId, 5, TimeUnit.SECONDS);

        try {
            if (!b) {
                Thread.sleep(5000);
            }
            CreditCard c = creditCardDao.getOne(cardId);
            //2.处理CoinTxnDtl
            List<CoinTxnDtl> dtlCoinlist = coinTxnDtlService.findAllByCardIdAndStatusAndTxnId(cardId, String.valueOf(TxnStatus.PADDING.getValue()), txnId);
            CoinTxnDtl dtlCoin = null;
            CoinTxnDtl dtlCoinFee = null;
//        CoinTxnDtl dtlCoinComm = null;
//        CoinTxnDtl dtlCoinCommFee = null;
            if (dtlCoinlist != null && !dtlCoinlist.isEmpty()) {

                BigDecimal totAmount = BigDecimal.ZERO;//卡充值金额
                for (CoinTxnDtl d : dtlCoinlist) {
                    if (d.getTxnCode().equals(TxnType.COIN_CARD_RECHARGE_OUT.getValue())) {
                        dtlCoin = d;
                        totAmount = totAmount.add(dtlCoin.getTxnAmount());
                    }
                    if (d.getTxnCode().equals(TxnType.COIN_CARD_RECHARGE_FEE_OUT.getValue())) {
                        dtlCoinFee = d;
                        totAmount = totAmount.add(dtlCoinFee.getTxnAmount());
                    }

                }


                if (dtlCoin != null) {
                    //A.个人充值

                    coinTxnDtlService.updateDtlStatue(dtlCoin, FreezeType.FREE_FREEZE, AlgorithmType.SUB);
                }
                if (dtlCoinFee != null) {
                    //B.个人手续费

                    coinTxnDtlService.updateDtlStatue(dtlCoinFee, FreezeType.FREE_FREEZE, AlgorithmType.SUB);
                }

                //充值成功积分处理
                apntUtil.dealApnt(c.getUserId(), "recharge", txnId, totAmount, null);

                logger.info("充值成功，触及返佣");
                // 充值成功，才触及返佣
                String flag = procedureUtils.callStoredProcedure(dtlCoinFee.getTxnId(), Constants.procedure.p_rel_card_recharge_benefit_coin);

                if ("-1".equals(flag)) {
                    // 发邮件通知管理员
                    sendEmail.errorMail("充值返佣返佣失败,交易ID:" + dtlCoin.getTxnId());
                }
                if (c.getSource().startsWith("moonbank")) {
                    // moonbank充值才算激活人数邀请成功，尝试更新邀请信息(meta_user_invites)

                    TxnDtlCode byTxnProduct = txnService.findByTxnProductByCardId(cardId);
                    //不存在说明不是激活币开卡
                    if (byTxnProduct == null) {
                        metaUserInvitesService.updateActiveStatus(c.getUserId());
                    }
                }
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            // 释放锁
            lockCfg.unlock(txnId, txnId);
            logger.info("Lock released.");
        }
    }


    /**
     * 充值成功
     * todo 尝试更新邀请信息(meta_user_invites)
     *
     * @param cardId
     * @param txnId
     * @param moonbankUid
     * @param cardStatus
     */
    @Override
    public void dealAfterMoonbookRecharge(String cardId, String txnId, String moonbankUid, String cardStatus) {

        boolean b = lockCfg.tryLock(txnId, txnId, 5, TimeUnit.SECONDS);
        try {
            if (!b) {
                Thread.sleep(5000);
            }
            CreditCard c = creditCardDao.getOne(cardId);
            //2.处理TxnDtlUSD
            List<TxnDtlUSD> dtlUsdlist = txnService.findAllByCardIdAndStatusAndTransactionId(cardId, String.valueOf(TxnStatus.PADDING.getValue()), txnId);
            TxnDtlUSD dtlUSD = null;
            TxnDtlUSD dtlUSDFee = null;
            TxnDtlUSD dtlUSDComm = null;
            TxnDtlUSD dtlUSDCommFee = null;
            if (dtlUsdlist != null && !dtlUsdlist.isEmpty()) {

                BigDecimal totAmount = BigDecimal.ZERO;//卡充值金额
                for (TxnDtlUSD d : dtlUsdlist) {
                    if (d.getTxnType().equals(TxnType.CARD_RECHARGE_C.getValue())) {
                        dtlUSD = d;
                        totAmount = totAmount.add(dtlUSD.getTxnAmount());
                    }
                    if (d.getTxnType().equals(TxnType.CARD_RECHARGE_FEE_C.getValue())) {
                        dtlUSDFee = d;
                        totAmount = totAmount.add(dtlUSDFee.getTxnAmount());
                    }
                    if (d.getTxnType().equals(TxnType.CARD_RECHARGE_D.getValue())) {
                        dtlUSDComm = d;
                    }
                    if (d.getTxnType().equals(TxnType.CARD_RECHARGE_FEE_D.getValue())) {
                        dtlUSDCommFee = d;
                    }
                }


                if (dtlUSD != null) {
                    //A.个人充值
                    VccReqDto req = new VccReqDto(dtlUSD.getUserId(), AlgorithmType.SUB, dtlUSD.getTxnAmount(), FreezeType.FREE_FREEZE, dtlUSD.getId(), dtlUSD.getUsdBalance());
                    walletService.update(req);
                }
                if (dtlUSDFee != null) {
                    //B.个人手续费
                    VccReqDto reqFee = new VccReqDto(dtlUSDFee.getUserId(), AlgorithmType.SUB, dtlUSDFee.getTxnAmount(), FreezeType.FREE_FREEZE, dtlUSDFee.getId(), dtlUSDFee.getUsdBalance());
                    walletService.update(reqFee);
                }
                if (dtlUSDComm != null) {
                    //C.公司充值
                    VccReqDto reqComm = new VccReqDto(null, AlgorithmType.ADD, FreezeType.NO, dtlUSDComm);
                    reqComm.setUsdBalance(dtlUSDComm.getUsdBalance().add(dtlUSDComm.getTxnAmount()));
                    reqComm.setTxnId(dtlUSDComm.getRelaTransactionid());
                    walletService.updateComAcc(reqComm);
                }
                if (dtlUSDCommFee != null) {
                    //D.公司手续费
                    VccReqDto reqCommFEE = new VccReqDto(null, AlgorithmType.ADD, FreezeType.NO, dtlUSDCommFee);
                    reqCommFEE.setUsdBalance(dtlUSDCommFee.getUsdBalance().add(dtlUSDCommFee.getTxnAmount()));
                    reqCommFEE.setTxnId(dtlUSDCommFee.getRelaTransactionid());
                    walletService.updateComAcc(reqCommFEE);
                }
                //充值成功积分处理
                apntUtil.dealApnt(c.getUserId(), "recharge", txnId, totAmount, null);

                logger.info("充值成功，触及返佣");
                // 充值成功，才触及返佣
                String flag = procedureUtils.callStoredProcedure(dtlUSDFee.getId(), Constants.procedure.p_rel_card_recharge_benefit);
                if ("-1".equals(flag)) {
                    // 发邮件通知管理员
                    sendEmail.errorMail("充值返佣返佣失败,交易ID:" + dtlUSD.getId());
                }
                if (c.getSource().startsWith("moonbank")) {
                    // moonbank充值才算激活人数邀请成功，尝试更新邀请信息(meta_user_invites)
                    TxnDtlCode byTxnProduct = txnService.findByTxnProductByCardId(cardId);
                    //不存在说明不是激活币开卡
                    if (byTxnProduct == null) {
                        metaUserInvitesService.updateActiveStatus(c.getUserId());
                    }

                }
                getCardInfo(c);


            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            // 释放锁
            lockCfg.unlock(txnId, txnId);
            logger.info("Lock released.");
        }

    }

    @Override
    public void updateMcardInfo(String cardId, String moonbankUid, CreditCard c) {
        //判断cvv2是否存在，不存在更新卡信息
        if (StringUtils.isEmpty(c.getCvv2())) {
            ApiResponse<String> apiResponse3 = moonbankUtil.queryBankcardInfo(moonbankUid, Integer.valueOf(c.getBankCardId()));
            if (apiResponse3.isSuccess()) {
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse3.getResult());

                JSONObject j = JSONObject.parseObject(descStr);
                String cardStatus = j.getString("userBankCardStatus");
                String cvv2 = j.getString("cardCvv");
                String cardNo = j.getString("cardNo");
                if (StringUtils.isNotEmpty(cvv2)) {
                    c.setCvv2(AESUtils.aesEncrypt(Constants.card_key, cvv2));
                }
                if (StringUtils.isNotEmpty(cardNo)) {
                    c.setCardNo(AESUtils.aesEncrypt(Constants.card_key, cardNo));
                }
                JSONObject vccCardHolderVo = j.getJSONObject("vccCardHolderVo");
                if (vccCardHolderVo != null) {
                    c.setBillingCountry(vccCardHolderVo.getString("countryCode"));
                    c.setProvince(vccCardHolderVo.getString("billingState"));
                    c.setCity(vccCardHolderVo.getString("billingCity"));
                    c.setAddressLine1(vccCardHolderVo.getString("billingAddress"));
                }
                String status = c.getCardStatus();
                if (StringUtils.isNotEmpty(cardStatus) && !cardStatus.equals(status)) {
                    c.setStatusUpdateTime(new Date());
                }
                c.setCardStatus(cardStatus);
                String expiryDate = j.getString("expiryDate");
                if (StringUtils.isNotEmpty(expiryDate) && expiryDate.length() >= 7) {
                    c.setExpirationTime(expiryDate.substring(5, 7) + expiryDate.substring(0, 2));
                } else if (StringUtils.isNotEmpty(expiryDate) && expiryDate.length() == 5 && expiryDate.contains("/")) {
                    c.setExpirationTime(expiryDate.substring(3, 5) + expiryDate.substring(0, 2));
                }
                creditCardDao.updateCardData(c.getCvv2(), c.getCardNo(), c.getCardStatus(), c.getExpirationTime(), c.getCardId());
            }
        }
        //更新卡的余额
        //激活更新卡片的时候已经更新余额了
        ApiResponse<String> apiResponse2 = moonbankUtil.queryBankcardBalance(moonbankUid, Integer.valueOf(c.getBankCardId()));
        if (apiResponse2.isSuccess()) {
            String descStr2 = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse2.getResult());
            JSONObject j2 = JSONObject.parseObject(descStr2);

            c.setBalance(j2.getBigDecimal("balance"));
            c.setCurrency(j2.getString("currency"));
            creditCardDao.updateAmountAndCurrency(cardId, j2.getBigDecimal("balance"), j2.getString("currency"));
        }
    }

    /**
     * 充值成功（商户-B端）
     *
     * @param cardId
     * @param txnId
     * @param moonbankUid
     * @param cardStatus
     */
    @Override
    public void dealAfterMoonbookRechargeMerchants(String cardId, String txnId, String moonbankUid, String cardStatus) {


        CreditCard c = creditCardDao.getOne(cardId);
        //2.处理
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectByUserIdForUpdate(c.getUserId());
        List<MetaPartnerTxnDtl> dtlUsdlist = metaPartnerTxnDtlService.findCardRechare(cardId, String.valueOf(TxnStatus.PADDING.getValue()), txnId);


        if (dtlUsdlist != null && !dtlUsdlist.isEmpty()) {

            BigDecimal totAmount = BigDecimal.ZERO;//卡充值金额
            for (MetaPartnerTxnDtl d : dtlUsdlist) {
                if (d.getTxnType().equals(PartnerTxnType.CardTopup.getValue())) {
                    totAmount = totAmount.add(d.getTxnAmount().abs()).add(d.getTxnFee().abs());
                }
            }

            if (totAmount.compareTo(BigDecimal.ZERO) > 0) {
                //更新冻结金额
                metaPartnerAssetPool.setFreezeUstdBalacne(metaPartnerAssetPool.getFreezeUstdBalacne().subtract(totAmount));
                metaPartnerAssetPoolService.updateBalance(metaPartnerAssetPool.getId().getPartnerId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());

                //更新充值记录
                metaPartnerTxnDtlService.updateStatus(cardId, String.valueOf(TxnStatus.SUCCESS.getValue()), txnId);

            }
            //更新卡片最新信息
            getCardInfo(c);
        }


    }

    /**
     * 充值失败（商户-B端）
     *
     * @param cardId
     * @param txnId
     * @param moonbankUid
     * @param cardStatus
     */
    @Override
    public void dealAfterMoonbookRechargeFailMerchants(String cardId, String txnId, String moonbankUid, String cardStatus) {
        CreditCard c = creditCardDao.getOne(cardId);
        //2.处理
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectByUserIdForUpdate(c.getUserId());
        List<MetaPartnerTxnDtl> dtlUsdlist = metaPartnerTxnDtlService.findCardRechare(cardId, String.valueOf(TxnStatus.PADDING.getValue()), txnId);


        if (dtlUsdlist != null && !dtlUsdlist.isEmpty()) {

            BigDecimal totAmount = BigDecimal.ZERO;//卡充值金额
            for (MetaPartnerTxnDtl d : dtlUsdlist) {
                if (d.getTxnType().equals(PartnerTxnType.CardTopup.getValue())) {
                    totAmount = totAmount.add(d.getTxnAmount().abs()).add(d.getTxnFee().abs());
                }
            }

            if (totAmount.compareTo(BigDecimal.ZERO) > 0) {
                //更新冻结金额
                metaPartnerAssetPool.setUsdtBalacne(metaPartnerAssetPool.getUsdtBalacne().add(totAmount));
                metaPartnerAssetPool.setFreezeUstdBalacne(metaPartnerAssetPool.getFreezeUstdBalacne().subtract(totAmount));
                metaPartnerAssetPoolService.updateBalance(metaPartnerAssetPool.getId().getPartnerId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());

                //更新充值记录
                metaPartnerTxnDtlService.updateStatus(cardId, String.valueOf(TxnStatus.FAIL.getValue()), txnId);

            }
        }
    }

    /**
     * 充值失败
     *
     * @param cardId
     * @param txnId
     * @param moonbankUid
     * @param cardStatus
     */
    @Override
    public void dealAfterMoonbookRechargeFail(String cardId, String txnId, String moonbankUid, String cardStatus) {
        boolean b = lockCfg.tryLock(txnId, txnId, 5, TimeUnit.SECONDS);

        try {
            if (!b) {
                Thread.sleep(5000);
            }
            CreditCard c = creditCardDao.getOne(cardId);
            //2.处理TxnDtlUSD

            List<TxnDtlUSD> dtlUsdlist = txnService.findAllByCardIdAndStatusAndTransactionId(cardId, String.valueOf(TxnStatus.PADDING.getValue()), txnId);
            TxnDtlUSD dtlUSD = null;
            TxnDtlUSD dtlUSDFee = null;
            TxnDtlUSD dtlUSDComm = null;
            TxnDtlUSD dtlUSDCommFee = null;
            if (dtlUsdlist != null && !dtlUsdlist.isEmpty()) {

                BigDecimal totAmount = BigDecimal.ZERO;//卡充值金额
                for (TxnDtlUSD d : dtlUsdlist) {
                    if (d.getTxnType().equals(TxnType.CARD_RECHARGE_C.getValue())) {
                        dtlUSD = d;
                        totAmount = totAmount.add(dtlUSD.getTxnAmount());
                    }
                    if (d.getTxnType().equals(TxnType.CARD_RECHARGE_FEE_C.getValue())) {
                        dtlUSDFee = d;
                        totAmount = totAmount.add(dtlUSDFee.getTxnAmount());
                    }
                    if (d.getTxnType().equals(TxnType.CARD_RECHARGE_D.getValue())) {
                        dtlUSDComm = d;
                    }
                    if (d.getTxnType().equals(TxnType.CARD_RECHARGE_FEE_D.getValue())) {
                        dtlUSDCommFee = d;
                    }
                }
                if (dtlUSD != null) {
                    //A.个人充值
                    VccReqDto req = new VccReqDto(dtlUSD.getUserId(), AlgorithmType.ADD, FreezeType.FALLBACK, dtlUSD);
                    walletService.update(req);
                }
                if (dtlUSDFee != null) {
                    //B.个人手续费
                    VccReqDto reqFee = new VccReqDto(dtlUSDFee.getUserId(), AlgorithmType.ADD, FreezeType.FALLBACK, dtlUSDFee);
                    walletService.update(reqFee);
                }
                if (dtlUSDComm != null) {
                    //C.公司充值
                    VccReqDto reqComm = new VccReqDto(null, AlgorithmType.SUB, FreezeType.NORMAL_FALLBACK, dtlUSDComm);
                    walletService.updateComAcc(reqComm);
                }
                if (dtlUSDCommFee != null) {
                    //D.公司充值
                    VccReqDto reqCommFEE = new VccReqDto(null, AlgorithmType.SUB, FreezeType.NORMAL_FALLBACK, dtlUSDCommFee);
                    walletService.updateComAcc(reqCommFEE);
                }


            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            // 释放锁
            lockCfg.unlock(txnId, txnId);
            logger.info("Lock released.");
        }
    }

    /**
     * 充值失败处理
     *
     * @param cardId
     * @param txnId
     * @param moonbankUid
     * @param cardStatus
     */
    @Override
    public void dealCoinAfterMoonbookRechargeFail(String cardId, String txnId, String moonbankUid, String cardStatus) {
        boolean b = lockCfg.tryLock(txnId, txnId, 5, TimeUnit.SECONDS);

        try {
            if (!b) {
                Thread.sleep(5000);
            }
            CreditCard c = creditCardDao.getOne(cardId);
            //2.处理CoinTxnDtl
            List<CoinTxnDtl> dtlCoinlist = coinTxnDtlService.findAllByCardIdAndStatusAndTxnId(cardId, String.valueOf(TxnStatus.PADDING.getValue()), txnId);
            CoinTxnDtl dtlCoin = null;
            CoinTxnDtl dtlCoinFee = null;
//        CoinTxnDtl dtlCoinComm = null;
//        CoinTxnDtl dtlCoinCommFee = null;
            if (dtlCoinlist != null && !dtlCoinlist.isEmpty()) {

                BigDecimal totAmount = BigDecimal.ZERO;//卡充值金额
                for (CoinTxnDtl d : dtlCoinlist) {
                    if (d.getTxnCode().equals(TxnType.COIN_CARD_RECHARGE_OUT.getValue())) {
                        dtlCoin = d;
                        totAmount = totAmount.add(dtlCoin.getTxnAmount());
                    }
                    if (d.getTxnCode().equals(TxnType.COIN_CARD_RECHARGE_FEE_OUT.getValue())) {
                        dtlCoinFee = d;
                        totAmount = totAmount.add(dtlCoinFee.getTxnAmount());
                    }

                }


                if (dtlCoin != null) {
                    //A.个人充值

                    coinTxnDtlService.updateDtlStatue(dtlCoin, FreezeType.FALLBACK, AlgorithmType.ADD);
                }
                if (dtlCoinFee != null) {
                    //B.个人手续费

                    coinTxnDtlService.updateDtlStatue(dtlCoinFee, FreezeType.FALLBACK, AlgorithmType.ADD);
                }

            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            // 释放锁
            lockCfg.unlock(txnId, txnId);
            logger.info("Lock released.");
        }

    }

    @Override
    public AjaxResult oldReplaceNew() {
        String source = "moonbank";
        List<CreditCard> list = creditCardDao.findData(source, CardStatus.ACTIVE.getValue());
        for (CreditCard card : list) {
            Wallet w = walletService.findByUserId(card.getUserId());
            updateCardData(source, card, w.getMoonbankUid());

        }

        return AjaxResult.success();
    }

    /**
     * 更新卡片信息
     *
     * @param source
     * @param card
     * @param uid
     */
    private void updateCardData(String source, CreditCard card, String uid) {
        ApiResponse<String> apiResponse = moonbankUtil.queryBankcardInfo(uid, Integer.valueOf(card.getBankCardId()));
        logger.info("queryBankcardInfo response Object:  " + apiResponse);
        if (apiResponse.isSuccess()) {

            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
            logger.info("queryBankcardInfo encode result===>" + descStr);
            JSONObject j = JSONObject.parseObject(descStr);
            // String bankCardId = j.getString("bankCardId");
            String cardCvv = j.getString("cardCvv");
            String cardNo = j.getString("cardNo");
            String expiryDate = j.getString("expiryDate");
            String cardStatus = j.getString("userBankCardStatus");
            String currency = j.getString("currency");
            String rechargeFee = j.getString("rechargeFee");
            String monthFee = j.getString("monthFee");
            String hashHolderInfo = j.getString("hashHolderInfo");

            if (StringUtils.isNotEmpty(cardCvv)) {
                card.setCvv2(AESUtils.aesEncrypt(Constants.card_key, cardCvv));
            }
            if (StringUtils.isNotEmpty(cardNo)) {
                card.setCardNo(AESUtils.aesEncrypt(Constants.card_key, cardNo));
            }

            if (StringUtils.isNotEmpty(expiryDate) && expiryDate.length() >= 7) {
                card.setExpirationTime(expiryDate.substring(5, 7) + expiryDate.substring(0, 2));
            } else if (StringUtils.isNotEmpty(expiryDate) && expiryDate.length() == 5 && expiryDate.contains("/")) {
                card.setExpirationTime(expiryDate.substring(3, 5) + expiryDate.substring(0, 2));
            }
            JSONObject vccCardHolderVo = j.getJSONObject("vccCardHolderVo");
            if (vccCardHolderVo != null) {
                card.setBillingCountry(vccCardHolderVo.getString("countryCode"));
                card.setProvince(vccCardHolderVo.getString("billingState"));
                card.setCity(vccCardHolderVo.getString("billingCity"));
                card.setAddressLine1(vccCardHolderVo.getString("billingAddress"));
            }

            ApiResponse<String> apiResponse2 = moonbankUtil.queryBankcardBalance(uid, Integer.valueOf(card.getBankCardId()));
            if (apiResponse2.isSuccess()) {
                String descStr2 = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse2.getResult());
                logger.info("queryBankcardInfo encode result===>" + descStr2);
                JSONObject j2 = JSONObject.parseObject(descStr2);
                card.setBalance(j2.getBigDecimal("balance"));

            }
            String status = card.getCardStatus();
            if (StringUtils.isNotEmpty(cardStatus) && !cardStatus.equals(status)) {
                card.setStatusUpdateTime(new Date());
            }
            card.setCardStatus(cardStatus);
            card.setSource(source);
            card.setCurrency(currency);
            card.setMonthFee(monthFee);
            card.setRechargeFee(rechargeFee);
            card.setHashHolderInfo(hashHolderInfo);
//            CardConfig cardConfig = cardConfigDao.findCardConfigByCardTypeAndCardLevelAndChannel(card.getCardType(), card.getCardLevel(), source);
//            card.setCardCfgId(cardConfig.getCardCfgId());
            card.setSearchTime(DateUtils.getNowDate());
            //更新卡信息
            save(card);

        }
    }

    @Override
    public CreditCard findFiat() {
        long userId = SecurityUtils.getLoginUser().getUserId();
        List<CreditCard> list = creditCardDao.findFiat(userId);
        if (list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public void updateCardHolder(String cardId, String cardHolder, String cardNumber, String status, BigDecimal availableUsd) {
        creditCardDao.updateCardHolder(cardId, cardHolder, cardNumber, status, availableUsd);
    }

    @Override
    public CreditCard findByCardIdAndSysId(String cardId, String sysId) {
        return creditCardDao.findByCardIdAndSysId(cardId, sysId);
    }

    @Override
    public void updateCard(String cardId, String userBankId, String cardStatus) {
        creditCardDao.updateCard(cardId, userBankId, cardStatus);
    }

    /**
     * @param cardId     旧卡id
     * @param userBankId 卡id
     * @param cardStatus 卡状态
     * @param cardLabel  卡标签
     * @param cardType   卡类型
     * @param currency   币种
     * @param cardCfgId  卡配置id
     */
    @Override
    public void updateCardSG(String cardId, String userBankId, String cardStatus, String cardLabel, String cardType, String currency, Long cardCfgId) {
        creditCardDao.updateCardSG(cardId, userBankId, cardStatus, cardLabel, cardType, currency, cardCfgId);
    }

    @Override
    public void updateCardId(String cardId, String userBankId) {
        creditCardDao.updateCardId(cardId, userBankId);
    }

    @Override
    public void updateCardPin(String cardId, String pin) {
        creditCardDao.updateCardPin(cardId, pin);
    }

    /**
     * 更新卡状态
     *
     * @param c
     * @param uid
     * @param enable
     * @return
     */
    @Override
    public AjaxResult updCardStatus(CreditCard c, String uid, boolean enable) {


        ApiResponse<String> apiResponse = moonbankUtil.updateBankcardStatus(uid, Integer.valueOf(c.getBankCardId()), enable);
        logger.info("updCardStatus response Object:  " + apiResponse);
        if (apiResponse.isSuccess()) {
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
            logger.info("updCardStatus encode result===>" + descStr);
            JSONObject j = JSONObject.parseObject(descStr);
            //更新卡信息
            updateCardData(c.getSource(), c, uid);
            return AjaxResult.success();
        } else {
            return AjaxResult.error(apiResponse.getMessage());
        }

    }


    @Override
    public BigDecimal getEURAmount(BigDecimal amount, String currency) {

        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        Long userId = SecurityUtils.getLoginUser().getUserId();
        Wallet wallet = walletService.findByUserId(userId);
        BigDecimal amt = null;
        if ("USD".equals(currency)) {
            amt = amount;
        } else {
            ExchangeRate rate = exchangeRateService.findByFromCurrencyCodeAndToCurrencyCode(currency, "USD");
            amt = amount.multiply(rate.getRateVal()).setScale(2, RoundingMode.CEILING);
        }
        return getEUR(wallet.getMoonbankUid(), amt);
    }


    /**
     * 获取转换金额
     *
     * @param uid
     * @param amt
     * @return
     */
    @Override
    public BigDecimal getEUR(String uid, BigDecimal amt) {
        ApiResponse<String> apiResponse = moonbankUtil.getEURAmount(amt, uid);
        if (apiResponse.isSuccess()) {
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

            System.out.println("getEURAmount encode result===>  true");
            JSONObject j = JSON.parseObject(descStr);
            return j.getBigDecimal("amount");
        } else {

            return null;
        }
    }

    @Override
    public BigDecimal getCurrenyAmount(BigDecimal amount, String currency, String toCurrency) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        Long userId = SecurityUtils.getLoginUser().getUserId();
        Wallet wallet = walletService.findByUserId(userId);


        if ("EUR".equals(toCurrency)) {

            return getEURAmount(amount, currency);
        } else if ("HKD".equals(toCurrency)) {
            BigDecimal price = kunService.getPrice("USDT", toCurrency, KunPriceSide.SELL.getValue(), amount);
            BigDecimal amt = amount.multiply(price).setScale(2, RoundingMode.CEILING);
            return amt;
        }
        return amount;
    }

    @Override
    public List<CreditCard> findUserAndCardType(Long userId, String cardType) {
        return creditCardDao.findUserAndCardType(userId, cardType);
    }

    @Override
    public CreditCard updateAlias(CreditCard c, String cardAlias) {
        //更新卡信息
        c.setCardAlias(cardAlias);
        save(c);
        return c;
    }

    @Override
    public CreditCard updateShowCurrency(CreditCard c, String showCurrency) {
        //更新展示的币种
        c.setShowCurrency(showCurrency);
        save(c);
        return c;
    }

    @Override
    public CreditCard updateShowAmt(CreditCard c, String showAmt) {
        //更新展示的余额
        c.setShowAmt(showAmt);
        save(c);
        return c;
    }

    @Override
    public CreditCard deleteCard(CreditCard c) {
        //更新展示的余额
        c.setCardStatus("DELETE");
        c.setStatusUpdateTime(new Date());
        save(c);
        return c;
    }

    @Override
    public List<CreditCard> findBySource(String source) {

        return creditCardDao.findBySource(source);
    }

    @Override
    public List<CreditCard> findBySource2(String source, String sysId) {
        return creditCardDao.findBySource2(source,sysId);
    }

    @Override
    public AjaxResult dealCoinWhenRecharge(String coin, BigDecimal amount, BigDecimal fee, String cardId, String txnId, BigDecimal rateVale, BigDecimal toUsd, BigDecimal toUsdFee) {
        //4.操作个人账户,余额扣减， 冻结增加
        Long userId = SecurityUtils.getLoginUser().getUserId();
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));

        VccReqDto dto = new VccReqDto(
                userId,
                AlgorithmType.SUB,
                amount,
                FreezeType.FREEZE,
                TxnType.COIN_CARD_RECHARGE_OUT,
                userId,
                accountId,
                cardId,
                fee,
                txnId);

        dto.setRecordType(0);//支出
        dto.setCoin(coin);
        dto.setFromCoin("USD");
        dto.setFromAmount(toUsd);
        dto.setExchgRate(rateVale);
        AjaxResult ar = coinTxnDtlService.update(dto, coin);
        ar.put("dtlCoin", (CoinTxnDtl) ar.get("dtlCoin"));

        //4.1 操作个人产生的手续费

        VccReqDto dtoFee = new VccReqDto(
                userId,
                AlgorithmType.SUB,
                fee,
                FreezeType.FREEZE,
                TxnType.COIN_CARD_RECHARGE_FEE_OUT,
                userId,
                accountId,
                cardId,
                BigDecimal.ZERO,
                txnId);

        dtoFee.setRecordType(0);//支出
        dtoFee.setCoin(coin);
        dtoFee.setFromCoin("USD");
        dtoFee.setFromAmount(toUsdFee);
        dtoFee.setExchgRate(rateVale);
        AjaxResult arFee = coinTxnDtlService.update(dtoFee, coin);
        ar.put("dtlCoinFee", (CoinTxnDtl) arFee.get("dtlCoin"));
        return ar;
    }


    /**
     * 成功充值
     *
     * @param req
     * @param c
     * @param cc
     * @param userId
     * @param rate
     * @param coinAmount
     * @param toUsdFee
     * @param amount
     * @param wallet
     * @param discountAmount
     * @param metaCouponInfo
     * @param cardStatus
     * @param txnId
     */
    @Override
    public void rechargeSuccess(VccReq req, CreditCard c, CardConfig cc, Long userId, ExchangeRate rate, BigDecimal coinAmount, BigDecimal toUsdFee, BigDecimal amount, Wallet wallet, BigDecimal discountAmount, MetaCouponInfo metaCouponInfo, String cardStatus, String txnId) {
        AjaxResult ares;
        if (!"USD".equals(req.getCoin())) {
            saveMoonbankCreditCardLogNew(VccTxnType.card_deposit.getValue(), txnId, amount, c.getCardId(), "SUCCESS", req.getCoin(), req.getSendToCardCurrency(), req.getSendToCardAmount(), req.getFeeAmount(), req.getCoin());

            if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {

                ares = dealCoinWhenRecharge(req.getCoin(), req.getDepositAmount().subtract(req.getFeeAmount()).subtract(discountAmount), req.getFeeAmount(), c.getCardId(), txnId, rate.getRateVal(), req.getRechargeAmount(), toUsdFee);
                CoinTxnDtl coinTxnDtl = (CoinTxnDtl) ares.get("dtlCoin");
                coinTxnDtl.setTotalPrice(coinAmount);
                coinTxnDtl.setDiscountAmt(discountAmount);
                coinTxnDtl.setCouponId(metaCouponInfo.getId());
                metaCouponInfoService.updateUse(metaCouponInfo.getId(), userId);

            } else {
                ares = dealCoinWhenRecharge(req.getCoin(), req.getDepositAmount().subtract(req.getFeeAmount()), req.getFeeAmount(), c.getCardId(), txnId, rate.getRateVal(), req.getRechargeAmount(), toUsdFee);
            }


            //成功充值后的流程
            dealCoinAfterMoonbookRecharge(c.getCardId(), txnId, wallet.getMoonbankUid(), cardStatus);
        } else {
            saveMoonbankCreditCardLogNew(VccTxnType.card_deposit.getValue(), txnId, req.getRechargeAmount(), c.getCardId(), "SUCCESS", "USD", req.getSendToCardCurrency(), req.getSendToCardAmount(), req.getFeeAmount(), req.getCoin());

            if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {

                ares = dealWalletWhenCardRecharge(req.getRechargeAmount().subtract(discountAmount), req.getFeeAmount(), c.getCardId(), txnId, null, new BigDecimal("1"), "USD");
                TxnDtlUSD dtlUSD = (TxnDtlUSD) ares.get("dtlUSD");
                TxnDtlUSD dtlUSDCom = (TxnDtlUSD) ares.get("dtlUSDComm");
                dtlUSD.setTotalPrice(req.getDepositAmount());
                dtlUSD.setDiscountAmt(discountAmount);
                dtlUSD.setCouponId(metaCouponInfo.getId());

                dtlUSDCom.setTotalPrice(req.getDepositAmount());
                dtlUSDCom.setDiscountAmt(discountAmount);
                dtlUSDCom.setCouponId(metaCouponInfo.getId());
                metaCouponInfoService.updateUse(metaCouponInfo.getId(), userId);

            } else {
                ares = dealWalletWhenCardRecharge(req.getRechargeAmount(), req.getFeeAmount(), c.getCardId(), txnId, null, new BigDecimal("1"), "USD");

            }

            //成功充值后的流程
            dealAfterMoonbookRecharge(c.getCardId(), txnId, wallet.getMoonbankUid(), cardStatus);
        }
    }

    /**
     * 充值处理中
     *
     * @param req            参数
     * @param c              卡
     * @param cc             卡配置
     * @param userId         用户id
     * @param rate           币种转换率
     * @param coinAmount
     * @param toUsdFee
     * @param amount
     * @param discountAmount
     * @param metaCouponInfo
     * @param txnId
     */
    public void rechargeProcess(VccReq req, CreditCard c, CardConfig cc, Long userId, ExchangeRate rate, BigDecimal coinAmount, BigDecimal toUsdFee, BigDecimal amount, BigDecimal discountAmount, MetaCouponInfo metaCouponInfo, String txnId) {
        AjaxResult ares;//保存充值记录
        if (!"USD".equals(req.getCoin())) {
            saveMoonbankCreditCardLogNew(VccTxnType.card_deposit.getValue(), txnId, amount, c.getCardId(), "processing", req.getCoin(), req.getSendToCardCurrency(), req.getSendToCardAmount(), req.getFeeAmount(), req.getCoin());

            if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {

                ares = dealCoinWhenRecharge(req.getCoin(), req.getDepositAmount().subtract(req.getFeeAmount()).subtract(discountAmount), req.getFeeAmount(), c.getCardId(), txnId, rate.getRateVal(), req.getRechargeAmount(), toUsdFee);
                CoinTxnDtl coinTxnDtl = (CoinTxnDtl) ares.get("dtlCoin");
                coinTxnDtl.setTotalPrice(coinAmount);
                coinTxnDtl.setDiscountAmt(discountAmount);
                coinTxnDtl.setCouponId(metaCouponInfo.getId());
                metaCouponInfoService.updateUse(metaCouponInfo.getId(), userId);

            } else {
                ares = dealCoinWhenRecharge(req.getCoin(), req.getDepositAmount().subtract(req.getFeeAmount()), req.getFeeAmount(), c.getCardId(), txnId, rate.getRateVal(), req.getRechargeAmount(), toUsdFee);
            }
        } else {
            saveMoonbankCreditCardLogNew(VccTxnType.card_deposit.getValue(), txnId, req.getRechargeAmount(), c.getCardId(), "processing", "USD", req.getSendToCardCurrency(), req.getSendToCardAmount(), req.getFeeAmount(), req.getCoin());
            if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {

                ares = dealWalletWhenCardRecharge(req.getRechargeAmount().subtract(discountAmount), req.getFeeAmount(), c.getCardId(), txnId, null, new BigDecimal("1"), "USD");
                TxnDtlUSD dtlUSD = (TxnDtlUSD) ares.get("dtlUSD");
                TxnDtlUSD dtlUSDCom = (TxnDtlUSD) ares.get("dtlUSDComm");
                dtlUSD.setTotalPrice(req.getDepositAmount());
                dtlUSD.setDiscountAmt(discountAmount);
                dtlUSD.setCouponId(metaCouponInfo.getId());

                dtlUSDCom.setTotalPrice(req.getDepositAmount());
                dtlUSDCom.setDiscountAmt(discountAmount);
                dtlUSDCom.setCouponId(metaCouponInfo.getId());
                metaCouponInfoService.updateUse(metaCouponInfo.getId(), userId);

            } else {
                ares = dealWalletWhenCardRecharge(req.getRechargeAmount(), req.getFeeAmount(), c.getCardId(), txnId, null, new BigDecimal("1"), "USD");
            }


        }
    }

    @Override
    public void saveMoonbankCreditCardLogNew(String txnType, String txnId, BigDecimal amount, String cardId, String status, String transactionCurrency, String cardCurrency, String sendAmount, BigDecimal fee, String feeCurrency) {
        CreditCardLog log = new CreditCardLog();
        log.setTransactionId(txnId);
        log.setTransactionAmount(amount);
        log.setTransactionCurrency(transactionCurrency);
        if (StringUtils.isNotEmpty(sendAmount)) {
            log.setCardTransactionAmount(new BigDecimal(sendAmount));
            log.setCardCurrency(cardCurrency);
        } else {
            log.setCardTransactionAmount(amount);
            log.setCardCurrency(transactionCurrency);
        }

        log.setTransactionType(txnType);
        log.setTransactionStatus(status);
        log.setFee(fee);
        log.setFeeCurrency(feeCurrency);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
//        calendar.add(Calendar.HOUR_OF_DAY, -8);
        Date newDate = calendar.getTime();
        log.setTransactionDate(simpleDateFormat.format(newDate));
        log.setCardId(cardId);
        creditCardLogService.save(log);
    }

    @Override
    public void oldCardTransferNewCard(String txnType, String txnId, String transactionCurrency, BigDecimal transactionCurrencyAmount, String cardCurrency, BigDecimal cardAmount, String cardId, String status, BigDecimal fee, String feeCurrency) {
        CreditCardLog log = new CreditCardLog();
        log.setCardId(cardId);
        log.setTransactionId(txnId);
        log.setTransactionCurrency(transactionCurrency);
        log.setTransactionAmount(transactionCurrencyAmount);
        log.setCardTransactionAmount(cardAmount);
        log.setCardCurrency(cardCurrency);
        log.setTransactionType(txnType);
        log.setTransactionStatus(status);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        Date newDate = calendar.getTime();
        log.setTransactionDate(simpleDateFormat.format(newDate));
        log.setFee(fee);
        log.setFeeCurrency(feeCurrency);
        log.setDescription("Transfer the balance from old card to new card");
        creditCardLogService.save(log);
    }

    @Override
    public AjaxResult dealWalletWhenCardRecharge(BigDecimal amount, BigDecimal fee, String cardId, String txnId, BigDecimal amountWallet, BigDecimal rate, String coin) {
        //4.操作个人账户,余额扣减， 冻结增加
        Long userId = SecurityUtils.getLoginUser().getUserId();
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        VccReqDto dto = setReqDto(null, 0, FreezeType.FREEZE, userId
                , userId, accountId, null, AlgorithmType.SUB
                , amount, fee, TxnType.CARD_RECHARGE_C, cardId, txnId);
        dto.setFromCoin(coin);
        dto.setExchgRate(rate);
        dto.setFromAmount(amountWallet);
        AjaxResult ar = walletService.update(dto);
        //4.1 操作个人产生的手续费
        VccReqDto dtoFee = setReqDto(null, 0, FreezeType.FREEZE, userId
                , userId, accountId, null, AlgorithmType.SUB
                , fee, BigDecimal.ZERO, TxnType.CARD_RECHARGE_FEE_C, cardId, txnId);
        dtoFee.setFromCoin(coin);
        dtoFee.setExchgRate(rate);
        dtoFee.setFromAmount(amountWallet);
        AjaxResult arFee = walletService.update(dtoFee);
        //5.操作公司账户： 余额不动,冻结增加
        VccReqDto dtoCom = setReqDto(null, 0, FreezeType.NORMAL, accountId
                , userId, accountId, null, AlgorithmType.ADD
                , amount, fee, TxnType.CARD_RECHARGE_D, cardId, txnId);
        dtoCom.setFromCoin(coin);
        dtoCom.setExchgRate(rate);
        dtoCom.setFromAmount(amountWallet);
        AjaxResult arComm = walletService.updateComAcc(dtoCom);
        //5.1 操作公司账号产生的手续费
        VccReqDto dtoComFee = setReqDto(null, 0, FreezeType.NORMAL, accountId
                , userId, accountId, null, AlgorithmType.ADD
                , fee, BigDecimal.ZERO, TxnType.CARD_RECHARGE_FEE_D, cardId, txnId);
        dtoComFee.setFromCoin(coin);
        dtoComFee.setExchgRate(rate);
        dtoComFee.setFromAmount(amountWallet);
        AjaxResult arCommFee = walletService.updateComAcc(dtoComFee);
        ar.put("dtlUSD", (TxnDtlUSD) ar.get("dtlUSD"));
        ar.put("dtlUSDFee", (TxnDtlUSD) arFee.get("dtlUSD"));
        ar.put("dtlUSDComm", (TxnDtlUSD) arComm.get("dtlUSD"));
        ar.put("dtlUSDCommFee", (TxnDtlUSD) arCommFee.get("dtlUSD"));
        return ar;
    }

    public VccReqDto setReqDto(String cardLevel, Integer activeCodeNum, FreezeType freezeType
            , Long userId, Long fromUserId, Long toUserId, AlgorithmType activeCodeAlgorithmType, AlgorithmType walletAlgorithmType
            , BigDecimal walletAmount, BigDecimal fee, TxnType txnType, String txnProduct, String txnId) {
        return new VccReqDto().setCardLevel(cardLevel)
                .setActiveCodeNum(activeCodeNum)
                .setFreezeType(freezeType)
                .setUserId(userId)
                .setFromUserId(fromUserId)
                .setToUserId(toUserId)
                .setActiveCodeAlgorithmType(activeCodeAlgorithmType)
                .setWalletAlgorithmType(walletAlgorithmType)
                .setWalletAmount(walletAmount)
                .setFee(fee)
                .setTxnType(txnType)
                .setTxnProduct(txnProduct)
                .setTxnId(txnId);
    }

    /**
     * 查询开卡中的卡（kun）
     *
     * @param source    渠道
     * @param carStatus 卡状态
     * @param txnStatus 交易状态
     * @param coinCode  coin表的类型
     * @param usdCode   usd表的类型
     * @param code      code标的类型
     * @return
     */
    @Override
    public List<CreditCardDtl> findOpening(String source, String carStatus, String txnStatus, String coinCode, String usdCode, String code) {
        return creditCardDao.findOpening(source, carStatus, txnStatus, coinCode, usdCode, code);
    }

    /**
     * 查询充值中的卡（kun）
     *
     * @param source    渠道
     * @param carStatus 卡状态
     * @param txnStatus 交易状态
     * @param coinCode  coin表的类型
     * @param usdCode   usd表的类型
     * @return
     */
    @Override
    public List<CreditCardDtl> findRecharge(String source, String carStatus, String txnStatus, String coinCode, String usdCode) {
        return creditCardDao.findRecharge(source, carStatus, txnStatus, coinCode, usdCode);
    }

    /**
     * 开卡结果的处理
     *
     * @param cardId               卡id
     * @param orderTransactionType 交易状态
     * @param type                 表类型
     */

    @Override
    public void openCardDeal(String cardId, String orderTransactionType, String type, String txnId) {
        CreditCard card = findByCardId(cardId);
        if (TxnStatus.APPROVED.getName().equals(orderTransactionType)) {
            Wallet wallet = walletService.findByUserId(card.getUserId());
            if ("code".equals(type)) {
                // todo 判断是否有充值金额
                // todo 充值流水处理
                // todo 改变卡充值日志的状态

                dealAfterMoonbookRecharge(card.getCardId(), txnId, "", card.getCardStatus());
                dealCoinAfterMoonbookRecharge(card.getCardId(), txnId, "", card.getCardStatus());
                //更新充值流水为成功
                creditCardLogService.updateTxnStatue(txnId, "SUCCESS");


            } else if ("usd".equals(type)) {
                usdOpenDeal(card, orderTransactionType);
                usdopenChargeDeal(txnId, card, TxnStatus.APPROVED.getValue());

            } else if ("coin".equals(type)) {
                coinOpenCardDeal(card);
                openChargeDeal(txnId, card, TxnStatus.APPROVED.getValue());

            }
            //开卡推荐人积分
            if (wallet.getReferrerId() != null) {
                //新增积分
                apntUtil.dealApnt(wallet.getReferrerId(), "share_card", card.getCardId(), null, null);
            }

            getCardInfo(card);
            //更新卡片信息
            if ("kun".equals(card.getSource())) {
                //开卡成功发送邮件提醒
                sendEmailDataUtils.sendOpenCardResult(TxnStatus.SUCCESS.getName(), card.getCardId());
            }

        } else if (TxnStatus.DECLINED.getName().equals(orderTransactionType)) {
            if ("code".equals(type)) {

                dealAfterMoonbookRechargeFail(card.getCardId(), txnId, "", "");
                dealCoinAfterMoonbookRechargeFail(card.getCardId(), txnId, "", "");
                //退回激活币
                walletService.dealAfterCode(card.getUserId(), card.getCardId(), TxnStatus.DECLINED.getValue());

                //更新充值流水为成功
                creditCardLogService.updateTxnStatue(txnId, "FAIL");

            } else if ("usd".equals(type)) {
                //  退回开卡费
                usdOpenDeal(card, orderTransactionType);
                usdopenChargeDeal(txnId, card, TxnStatus.DECLINED.getValue());

            } else if ("coin".equals(type)) {
                //  退回开卡费
                String[] txnTypes = new String[]{TxnType.COIN_OPEN_CARD_OUT.getValue()};
                List<CoinTxnDtl> dtlCoinlist = coinTxnDtlService.findAllByCardIdAndStatusAndTxnTypeForCoin(cardId, String.valueOf(TxnStatus.PADDING.getValue()), txnTypes);
                for (CoinTxnDtl coinTxnDtl : dtlCoinlist) {
                    coinTxnDtlService.updateDtlStatue(coinTxnDtl, FreezeType.FALLBACK, AlgorithmType.ADD);
                }
                openChargeDeal(txnId, card, TxnStatus.DECLINED.getValue());
            }
            //更新卡片状态
            //开卡失败
            creditCardDao.updateStatus(card.getCardId(), kunService.getCardStatus(KunCardStatus.FAIL.getValue()));

        }

    }

    public void usdopenChargeDeal(String txnId, CreditCard card, Character status) {
        if (ChannelType.FORNAX.getValue().equals(card.getSource())) {
            List<TxnDtlUSD> list = txnService.findAllByCardIdAndStatusAndTransactionId(card.getCardId(), TxnStatus.PADDING.getValue().toString(), txnId
                    , new String[]{TxnType.CARD_RECHARGE_D.getValue(), TxnType.CARD_RECHARGE_C.getValue()});
            if (list != null && !list.isEmpty()) {
                TxnDtlUSD dtlUSD = null;
                TxnDtlUSD dtlUSDCom = null;
                for (TxnDtlUSD d : list) {
                    if (d.getTxnType().equals(TxnType.CARD_RECHARGE_C.getValue()))
                        dtlUSD = d;
                    if (d.getTxnType().equals(TxnType.CARD_RECHARGE_D.getValue()))
                        dtlUSDCom = d;
                }

                if (TxnStatus.APPROVED.getValue() == status) {//成功
                    VccReqDto req = new VccReqDto(card.getUserId(), AlgorithmType.SUB, FreezeType.FREE_FREEZE, dtlUSD);
                    walletService.update(req);
                    VccReqDto reqComm = new VccReqDto(null, AlgorithmType.ADD, FreezeType.NO, dtlUSDCom);
                    reqComm.setUsdBalance(dtlUSDCom.getUsdBalance().add(dtlUSDCom.getTxnAmount().abs()));
                    //更新公司流水余额
                    AjaxResult arCom = walletService.updateComAcc(reqComm);
                    TxnDtlUSD dtlCom = (TxnDtlUSD) arCom.get("dtlUSD");
                    dtlCom.setUsdBalance(dtlCom.getUsdBalance().add(dtlUSDCom.getTxnAmount().abs()));
                    txnService.save(dtlCom);

                } else if (TxnStatus.DECLINED.getValue() == status) {//失败
                    VccReqDto req = new VccReqDto(card.getUserId(), AlgorithmType.ADD, FreezeType.FALLBACK, dtlUSD);
                    req.setTxnProduct(card.getCardId());
                    walletService.update(req);
                    VccReqDto reqComm = new VccReqDto(null, AlgorithmType.SUB, FreezeType.NORMAL_FALLBACK, dtlUSDCom);
                    reqComm.setTxnProduct(card.getCardId());
                    walletService.updateComAcc(reqComm);

                }
            }
        }
    }

    /**
     * 开卡时充值处理
     *
     * @param txnId
     * @param card
     * @param status
     */
    public void openChargeDeal(String txnId, CreditCard card, Character status) {
        //开卡时的充值
        if (ChannelType.FORNAX.getValue().equals(card.getSource())) {
            List<CoinTxnDtl> rlist = coinTxnDtlService.findAllByCardIdAndStatusAndTxnId(card.getCardId(), TxnStatus.PADDING.getValue().toString(), txnId, new String[]{TxnType.COIN_CARD_RECHARGE_OUT.getValue()});

            if (rlist != null && !rlist.isEmpty()) {
                CoinTxnDtl dtlCoin = null;

                for (CoinTxnDtl d : rlist) {
                    if (d.getTxnCode().equals(TxnType.COIN_CARD_RECHARGE_OUT.getValue()))
                        dtlCoin = d;
                }
                //成功
                if (dtlCoin != null) {
                    if (status == TxnStatus.APPROVED.getValue()) {
                        coinTxnDtlService.updateDtlStatue(dtlCoin, FreezeType.FREE_FREEZE, AlgorithmType.SUB);
                    } else if (status == TxnStatus.DECLINED.getValue()) {
                        coinTxnDtlService.updateDtlStatue(dtlCoin, FreezeType.FALLBACK, AlgorithmType.ADD);
                    }
                }


            }
        }
    }

    /**
     * USD开卡成功处理
     *
     * @param card                 卡
     * @param orderTransactionType 交易状态
     */
    @Override
    public void usdOpenDeal(CreditCard card, String orderTransactionType) {
        List<TxnDtlUSD> list = txnService.findAllByCardIdAndStatusAndTxnTypeForUsd(card.getCardId(), TxnStatus.PADDING.getValue().toString()
                , new String[]{TxnType.CARD_ACTIVATION_FEE_D.getValue(), TxnType.CARD_ACTIVATION_FEE_C.getValue()});
        if (list != null && !list.isEmpty()) {
            TxnDtlUSD dtlUSD = null;
            TxnDtlUSD dtlUSDCom = null;
            for (TxnDtlUSD d : list) {
                if (d.getTxnType().equals(TxnType.CARD_ACTIVATION_FEE_C.getValue()))
                    dtlUSD = d;
                if (d.getTxnType().equals(TxnType.CARD_ACTIVATION_FEE_D.getValue()))
                    dtlUSDCom = d;
            }
            //成功
            if (TxnStatus.APPROVED.getName().equals(orderTransactionType)) {
                VccReqDto req = new VccReqDto(card.getUserId(), AlgorithmType.SUB, FreezeType.FREE_FREEZE, dtlUSD);
                walletService.update(req);
                VccReqDto reqComm = new VccReqDto(null, AlgorithmType.ADD, FreezeType.NO, dtlUSDCom);
                reqComm.setUsdBalance(dtlUSDCom.getUsdBalance().add(dtlUSDCom.getTxnAmount().abs()));
                //更新公司流水余额
                AjaxResult arCom = walletService.updateComAcc(reqComm);
                TxnDtlUSD dtlCom = (TxnDtlUSD) arCom.get("dtlUSD");
                dtlCom.setUsdBalance(dtlCom.getUsdBalance().add(dtlUSDCom.getTxnAmount().abs()));
                txnService.save(dtlCom);

                // 开卡返佣
                if (dtlUSD != null) {
                    String flag = procedureUtils.callStoredProcedure(dtlUSD.getId(), Constants.procedure.p_rel_card_open_benefit);

                    // kun开卡即算激活人数邀请成功，尝试更新邀请信息(meta_user_invites)
                    if (card.getSource().equals("kun") || card.getSource().equals(ChannelType.FORNAX.getValue())) {
                        metaUserInvitesService.updateCardStatus(card.getUserId());
                    }
                    if ("-1".equals(flag)) {
                        // 发邮件通知管理员
                        sendEmail.errorMail("开卡返佣返佣失败,交易ID:" + dtlUSD.getId());
                    }
                }
            }
            //失败
            else if (TxnStatus.DECLINED.getName().equals(orderTransactionType)) {
                VccReqDto req = new VccReqDto(card.getUserId(), AlgorithmType.ADD, FreezeType.FALLBACK, dtlUSD);
                req.setTxnProduct(card.getCardId());
                walletService.update(req);
                VccReqDto reqComm = new VccReqDto(null, AlgorithmType.SUB, FreezeType.NORMAL_FALLBACK, dtlUSDCom);
                reqComm.setTxnProduct(card.getCardId());
                walletService.updateComAcc(reqComm);
            }
        }
    }

    /**
     * @param sysId
     * @param bodyJson   返回参数
     * @param c          卡
     * @param cardConfig 卡配置
     * @return
     */
    @Override
    public JSONObject closeCardB(String sysId, JSONObject bodyJson, CreditCard c, CardConfig cardConfig) {
        String cardID = c.getCardId();
        MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(c.getCardHolder(), sysId);
        ApiResponse<String> apiResponseBl = moonbankUtil.queryBankcardBalance(partnerUser.getUid(), Integer.valueOf(c.getBankCardId()));
        if (apiResponseBl.isSuccess()) {
            String descStrBl = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponseBl.getResult());
            logger.info("queryBankcardBalance encode result===>" + descStrBl);
            JSONObject jBl = JSONObject.parseObject(descStrBl);
            BigDecimal b = jBl.getBigDecimal("balance");
            BigDecimal refundAmt = jBl.getBigDecimal("refundAmt");
            logger.info("查询balance===>" + b);
            logger.info("返回balance===>" + refundAmt);


            ApiResponse<String> apiResponse = moonbankUtil.close(partnerUser.getUid(), Integer.valueOf(c.getBankCardId()));
            logger.info("closeCard response Object:  " + apiResponse);
            if (apiResponse.isSuccess()) {
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
                logger.info("closeCard encode result===>" + descStr);
                JSONObject j = JSONObject.parseObject(descStr);
                String requestOrderId = j.getString("requestOrderId");
                bodyJson.put("txnId", requestOrderId);
                bodyJson.put("cardID", cardID);
                BigDecimal balance = j.getBigDecimal("refundAmt");

                bodyJson.put("currency", "USD");
                logger.info("销卡返回余额balance===>" + balance);
                if (balance == null) {
                    balance = refundAmt;
                }
                logger.info("销卡返回余额balance===>" + balance);
                bodyJson.put("refundAmt", balance);
                String cardStatus = "";
                ApiResponse<String> apiResponse2 = moonbankUtil.queryBankcardInfo(partnerUser.getUid(), Integer.valueOf(c.getBankCardId()));
                logger.info("closeCard response Object:  " + apiResponse2);
                if (apiResponse.isSuccess()) {
                    String descStr2 = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse2.getResult());
                    logger.info("closeCard encode result===>" + descStr2);
                    JSONObject j2 = JSONObject.parseObject(descStr2);
                    cardStatus = j2.getString("userBankCardStatus");
                }
                bodyJson.put("status", "PENDING");

                updateStatus(cardID, cardStatus);

                dealProcessingBalanceB(sysId, c, cardConfig, requestOrderId, balance);


                return bodyJson;
            } else {
                //错误
                String message = apiResponse.getMessage();
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
        } else {
            //错误
            String message = apiResponseBl.getMessage();
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
    }

    /**
     * 余额处理中（B）
     *
     * @param sysId
     * @param c
     * @param cardConfig
     * @param requestOrderId
     * @param balance
     */
    @Override
    public void dealProcessingBalanceB(String sysId, CreditCard c, CardConfig cardConfig, String requestOrderId, BigDecimal balance) {
        String cardID = c.getCardId();
        //余额处理
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectBySysIdForUpdate(sysId);
        //保存卡记录
        saveMoonbankCreditCardLog(VccTxnType.card_withdraw.getValue(), requestOrderId, balance, c.getCardId(), "processing", "USD", cardConfig.getCurrency(), String.valueOf(balance));
        //余额提现流水
        metaPartnerAssetPool.setFreezeUstdBalacne(metaPartnerAssetPool.getFreezeUstdBalacne().add(balance));
        metaPartnerTxnDtlService.saveUSD(metaPartnerAssetPool, PartnerTxnType.CardWithdrawal, cardID, c.getCurrency(), 0, balance, BigDecimal.ZERO, TxnStatus.PADDING.getValue(), requestOrderId);
        metaPartnerAssetPoolService.updateBalance(metaPartnerAssetPool.getId().getPartnerId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());
    }

    /**
     * 余额处理（B）
     *
     * @param sysId
     * @param c
     * @param cardConfig
     * @param requestOrderId
     * @param balance
     */
    @Override
    public void dealBalanceB(String sysId, CreditCard c, CardConfig cardConfig, String requestOrderId, BigDecimal
            balance) {
        String cardID = c.getCardId();
        //余额处理
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectBySysIdForUpdate(sysId);
        //保存卡记录
        saveMoonbankCreditCardLog(VccTxnType.card_withdraw.getValue(), requestOrderId, balance, c.getCardId(), "SUCCESS", "USD", cardConfig.getCurrency(), String.valueOf(balance));
        //余额提现流水
        metaPartnerAssetPool.setUsdtBalacne(metaPartnerAssetPool.getUsdtBalacne().add(balance));
        metaPartnerTxnDtlService.saveUSD(metaPartnerAssetPool, PartnerTxnType.CardWithdrawal, cardID, c.getCurrency(), 0, balance, BigDecimal.ZERO, TxnStatus.SUCCESS.getValue(), requestOrderId);
        metaPartnerAssetPoolService.updateBalance(metaPartnerAssetPool.getId().getPartnerId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());
    }

    @Override
    public void saveMoonbankCreditCardLog(String txnType, String txnId, BigDecimal amount, String cardId, String
            status, String transactionCurrency, String cardCurrency, String sendAmount) {
        CreditCardLog log = new CreditCardLog();
        log.setTransactionId(txnId);
        log.setTransactionAmount(amount);
        log.setTransactionCurrency(transactionCurrency);
        if (StringUtils.isNotEmpty(sendAmount)) {
            log.setCardTransactionAmount(new BigDecimal(sendAmount));
            log.setCardCurrency(cardCurrency);
        } else {
            log.setCardTransactionAmount(amount);
            log.setCardCurrency(transactionCurrency);
        }

        log.setTransactionType(txnType);
        log.setTransactionStatus(status);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
//        calendar.add(Calendar.HOUR_OF_DAY, -8);
        Date newDate = calendar.getTime();
        log.setTransactionDate(simpleDateFormat.format(newDate));
        log.setCardId(cardId);
        creditCardLogService.save(log);
    }

    /**
     * coin开卡成功流水处理
     *
     * @param card
     */
    @Override
    public void coinOpenCardDeal(CreditCard card) {
        //b. 如果是开卡，需要更新dtl_coin的数据
        //如果是 coin开卡
        List<CoinTxnDtl> list = coinTxnDtlService.findAllByCardIdAndStatusAndTxnTypeForCoin(card.getCardId(), TxnStatus.PADDING.getValue().toString(), new String[]{TxnType.COIN_OPEN_CARD_OUT.getValue(), TxnType.COIN_LOGISTICS_OUT.getValue()});

        if (list != null && !list.isEmpty()) {
            CoinTxnDtl dtlCoin = null;

            CoinTxnDtl dtlCoinLogistics = null;

            for (CoinTxnDtl d : list) {
                if (d.getTxnCode().equals(TxnType.COIN_OPEN_CARD_OUT.getValue()))
                    dtlCoin = d;

                if (d.getTxnCode().equals(TxnType.COIN_LOGISTICS_OUT.getValue()))
                    dtlCoinLogistics = d;

            }
            //成功
            if (dtlCoin != null) {
                coinTxnDtlService.updateDtlStatue(dtlCoin, FreezeType.FREE_FREEZE, AlgorithmType.SUB);
            }
            //更新物流费
            if (dtlCoinLogistics != null) {
                coinTxnDtlService.updateDtlStatue(dtlCoinLogistics, FreezeType.FREE_FREEZE, AlgorithmType.SUB);
            }

//             开卡返佣
            if (dtlCoin != null && dtlCoin.getTxnAmount() != null && dtlCoin.getTxnAmount().abs().compareTo(BigDecimal.ZERO) > 0) {
                String flag = procedureUtils.callStoredProcedure(dtlCoin.getTxnId(), Constants.procedure.p_rel_card_open_benefit_coin);
                // kun开卡即算激活人数邀请成功，尝试更新邀请信息(meta_user_invites)
                if (card.getSource().equals("kun") || card.getSource().equals(ChannelType.FORNAX.getValue())) {
                    metaUserInvitesService.updateCardStatus(card.getUserId());
                }
                if ("-1".equals(flag)) {
                    // 发邮件通知管理员
                    sendEmail.errorMail("开卡返佣返佣失败,交易ID:" + dtlCoin.getTxnId());
                }

            }


        }
    }

    /**
     * 充值流水处理
     *
     * @param cardId
     * @param orderTransactionType
     * @param type
     */
    @Override
    public void dealRecharge(String cardId, String txnId, String orderTransactionType, String type) {
        CreditCard card = findByCardId(cardId);

        if (TxnStatus.APPROVED.getName().equals(orderTransactionType)) {
            if ("usd".equals(type)) {
                dealAfterMoonbookRecharge(card.getCardId(), txnId, "", card.getCardStatus());

            } else if ("coin".equals(type)) {
                dealCoinAfterMoonbookRecharge(card.getCardId(), txnId, "", card.getCardStatus());

            }
            //更新充值流水为成功
            creditCardLogService.updateTxnStatue(txnId, "SUCCESS");

        } else if (TxnStatus.DECLINED.getName().equals(orderTransactionType)) {
            if ("usd".equals(type)) {
                dealAfterMoonbookRechargeFail(card.getCardId(), txnId, "", "");

            } else if ("coin".equals(type)) {
                //  退回开卡费
                dealCoinAfterMoonbookRechargeFail(card.getCardId(), txnId, "", "");
            }
            //更新充值流水为失败
            creditCardLogService.updateTxnStatue(txnId, "FAIL");

        }

    }

    @Override
    public AjaxResult activeCard(Wallet wallet, CreditCard card, String cardNo, CardConfig cardConfig) {

        AjaxResult ajax = AjaxResult.success();
        if (PhysicalCardType.EuroCard.getType().equals(cardConfig.getCardType())) {
            ActivatecardRequest activatecardRequest = new ActivatecardRequest();
            activatecardRequest.setCardNumber(cardNo);
            activatecardRequest.setTemplateId(cardConfig.getBankCardId());
            ApiResponse<String> apiResponse = moonbankUtil.activatecard(wallet.getMoonbankUid(), activatecardRequest);
            if (apiResponse.isSuccess()) {
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

                System.out.println("activatecard encode result===>" + descStr);
                JSONObject j = JSON.parseObject(descStr);
                String cardId = j.getString("cardId");
                String userBankId = j.getString("userBankId");
                String cardStatus = j.getString("cardStatus");
                //更新卡ID和卡信息
                updateCardSG(card.getCardId(), userBankId, cardStatus, cardConfig.getCardLabel(), cardConfig.getCardType(), cardConfig.getCurrency(), cardConfig.getCardCfgId());
                //把之前临时卡id替换掉(流水)
                txnService.updateTxnProduct(card.getCardId(), userBankId);
                txnService.updateCodeProduct(card.getCardId(), userBankId);
                coinTxnDtlService.updateTxnProduct(card.getCardId(), userBankId);

                //更新入库的卡为激活
                metaPhysicalCardService.updateActive(cardNo);
                //更新卡信息
                card.setBankCardId(userBankId);
                updateCardMoonbankInfo(card.getCardId(), userBankId, wallet.getMoonbankUid());
                return ajax;
            } else {

                String message = apiResponse.getMessage();
                return AjaxResult.error(message);
            }
        } else if (PhysicalCardType.SingaporeUCard_01.getType().equals(cardConfig.getCardType()) || PhysicalCardType.SingaporeUCard_10.getType().equals(cardConfig.getCardType())) {
            ApiResponse<String> apiResponse = moonbankUtil.ucardAssign(wallet.getMoonbankUid(), cardNo, cardConfig.getBankCardId(), true);

            if (apiResponse.isSuccess()) {
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

                logger.info("ucardAssign encode result===>" + descStr);
                JSONObject j = JSON.parseObject(descStr);

                String userBankId = j.getString("userBankcardId");
                String cardStatus = "ACTIVE_PROCESSING";
                //更新卡ID和卡信息
                updateCardSG(card.getCardId(), userBankId, cardStatus, cardConfig.getCardLabel(), cardConfig.getCardType(), cardConfig.getCurrency(), cardConfig.getCardCfgId());
                //把之前临时卡id替换掉(流水)
                txnService.updateTxnProduct(card.getCardId(), userBankId);
                txnService.updateCodeProduct(card.getCardId(), userBankId);
                coinTxnDtlService.updateTxnProduct(card.getCardId(), userBankId);

                //更新入库的卡为激活
                metaPhysicalCardService.updateActive(cardNo);
                //更新卡信息
                card.setBankCardId(userBankId);
                updateCardMoonbankInfo(card.getCardId(), userBankId, wallet.getMoonbankUid());
                return ajax;

            } else {
                String message = apiResponse.getMessage();
                return AjaxResult.error(message);
            }

        } else {
            return AjaxResult.error();
        }
    }

    /**
     * 查询卡片信息
     *
     * @return
     */
    @Override
    public JSONObject getCardStutus(String uid, String bankCardId) {
        ApiResponse<String> apiResponse = moonbankUtil.queryBankcardInfo(uid, Integer.valueOf(bankCardId));
        logger.info("queryBankcardInfo response Object:  " + apiResponse);
        String cardNo = "";
        String cvv = "";
        if (apiResponse.isSuccess()) {
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
            logger.info("queryBankcardInfo encode result===>" + descStr);
            JSONObject j = JSONObject.parseObject(descStr);
            return j;
        }
        return null;
    }

    @Override
    public List<CardVo> findExchangeList() {
        return creditCardDao.findExchangeList();
    }

    /**
     * 检查是否允许充值
     *
     * @param cardType
     * @return
     */
    @Override
    public boolean checkRecharge(String cardType) {
        String old_kun_close_recharge_time = configService.selectConfigByKey("old_kun_close_recharge_time");
        if ("87".equals(cardType) && StringUtils.isNotEmpty(old_kun_close_recharge_time)) {

            Date date = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, old_kun_close_recharge_time);
            logger.info("旧kun卡的关闭时间为：");
            boolean compare = DateUtils.compare(date);
            logger.info("时间比较：" + compare);
            if (!compare) {
                return false;
            }
        }
        return true;
    }

    @Override
    public List<CreditCard> findUserAndCardLevel(Long userId, String cardLevel) {
        return creditCardDao.findUserAndCardLevel(userId, cardLevel);
    }

    @Override
    public List<CreditCard> findCardHolderAndCardType(String cardHolder, String cardType) {
        return creditCardDao.findCardHolderAndCardType(cardHolder, cardType);
    }

    @Override
    public void updateOldCard(String cardId, BigDecimal balance, String statue) {
        creditCardDao.updateOldCard(cardId, balance, statue);
    }

    @Override
    public boolean checkShowExchange(String cardId) {
        if ("1242362514924609536".equals(cardId) || "1294167895052886016".equals(cardId)) {
            return true;
        }
        String show_old_change = configService.selectConfigByKey("show_old_change");
        Date date = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, show_old_change);
        logger.info("旧卡转移按钮展示时间：" + date);
        boolean compare = DateUtils.compare(date);
        logger.info("时间比较：" + compare);
        if (!compare) {
            return true;
        }
        return false;
    }

    /**
     * 异步冻结卡片
     *
     * @param cardId
     */
    @Async
    @Override
    public CompletableFuture<Void> lockCardAsync(String cardId) {
        CompletableFuture<Void> future = new CompletableFuture<>();
        try {
            CreditCard c = findByCardId(cardId);
            if (c.getSource().startsWith("moonbank")) {
                Wallet w = walletService.findByUserId(c.getUserId());
                updCardStatus(c, w.getMoonbankUid(), false);
            } else if ("kun".equals(c.getSource())) {
                if ("89".equals(c.getCardType())) {
                    kunService.cardfreeze(c, false, new JSONObject());
                    kunService.upCardInfo(c.getCardId());
                }
            } else if (ChannelType.FORNAX.getValue().equals(c.getSource())) {
                fornaxService.updateCard(c, "INACTIVE");
            }
            future.complete(null);
        } catch (Exception e) {
            future.completeExceptionally(e);
        }
        return future;
    }

    /**
     * 获取卡片的id
     *
     * @return
     */
    @Override
    public String generateCardId() {
        String cardId = UniqueIDGenerator.generateID();
        return "KP" + cardId;
    }


    /**
     * 调用开卡
     *
     * @param metaOrder 订单
     * @return
     */
    @Override
    @Transactional
    public CardResult openCard(MetaOrder metaOrder) {
        String cardId = metaOrder.getCardId();
        CardResult cardResult = null;
        if (StringUtils.isEmpty(cardId)) {
            logger.error("卡id不存在：" + cardId);
            return null;
        }
        CreditCard card = findByCardId(cardId);
        if (card == null) {
            logger.error("卡片不存在：" + cardId);
            return null;
        }
        if (StringUtils.isNotEmpty(card.getBankCardId())) {
            logger.info("已经开过卡了：" + card.getBankCardId());
            return null;
        }

        String source = card.getSource();
        logger.info("卡片渠道：" + source);
        if (StringUtils.isEmpty(source)) {
            logger.info("卡片渠道不能为空");
            return null;
        }

        CardConfig cardConfig = cardConfigDao.findById(card.getCardCfgId()).orElse(null);
        if (cardConfig == null) {
            logger.info("卡片配置找不到数据:" + card.getCardCfgId());
            return null;
        }
        //是否是B端
        boolean isB = false;
        if (StringUtils.isNotEmpty(card.getSysId())) {
            isB = true;
        }


        if (source.startsWith("moonbank")) {
            logger.info("moonbank开卡");
            String uid = "";

            if (isB) {
                MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(card.getCardHolder(), card.getSysId());
                uid = partnerUser.getUid();

            } else {
                Wallet w = walletService.findByUserId(card.getUserId());
                uid = w.getMoonbankUid();
            }

            cardResult = mcardService.openCard(uid, cardConfig.getBankCardId());
            if (cardResult == null) {
                logger.error("开卡失败!");
                metaOrder.setStatus(KzOrderStatus.fail.getValue());
            } else {
                logger.info("开卡成功!");
                metaOrder.setStatus(KzOrderStatus.success.getValue());

            }

        } else if ("kun".equals(source)) {
            //查询开卡流水,如果存在就不处理
            JSONObject result = kunService.queryOpenCard(card, metaOrder.getRequestNo());
            if (result != null) {
                logger.info("开卡流水已经存在：" + metaOrder.getRequestNo());
                return null;
            }

            cardResult = kunService.openCard(card, cardConfig, metaOrder);
            if (cardResult == null) {
                logger.error("开卡失败!");
            } else {
                String cardStatue = cardResult.getCardStatus();//卡片状态
                logger.info("开卡成功!");

                if ("ACTIVE".equals(cardStatue)) {
                    //开卡成功
                    metaOrder.setStatus(KzOrderStatus.success.getValue());
                } else if ("AUDITING".equals(cardStatue)) {
                    //开卡中
                    metaOrder.setStatus(KzOrderStatus.process.getValue());
                } else if ("AUDIT_NOT_PASS".equals(cardStatue)) {
                    //开卡失败
                    metaOrder.setStatus(KzOrderStatus.fail.getValue());
                }
            }
        } else if (ChannelType.FORNAX.getValue().equals(source)) {
            //查询开卡流水,如果存在就不处理
            JSONObject jsonObject = fornaxService.queryOrderOpenCard(cardId);
            if (jsonObject != null) {
                logger.info("开卡流水已经存在：" + metaOrder.getRequestNo());
                return null;
            }
            cardResult = fornaxService.applyCard(cardConfig, metaOrder);
            //开卡中
            metaOrder.setStatus(KzOrderStatus.process.getValue());
            if (cardResult == null) {
                logger.error("开卡失败!");
            } else {
                String cardStatue = cardResult.getCardStatus();//卡片状态


                if ("ACTIVE".equals(cardStatue)) {
                    logger.info("开卡成功!");
                    //开卡成功
                    metaOrder.setStatus(KzOrderStatus.success.getValue());
                } else if ("AUDITING".equals(cardStatue)) {
                    logger.info("开卡进行中!");
                    //开卡中
                    metaOrder.setStatus(KzOrderStatus.process.getValue());
                } else if ("AUDIT_NOT_PASS".equals(cardStatue)) {
                    logger.info("开卡失败!");
                    //开卡失败
                    metaOrder.setStatus(KzOrderStatus.fail.getValue());
                }
            }

        }

        if (cardResult != null) {
            card.setBankCardId(cardResult.getCardId());
            card.setCardStatus(cardResult.getCardStatus());
        }
        creditCardDao.save(card);
        openCardOrderDeal(metaOrder, card, isB);
        return cardResult;
    }

    /**
     * 开卡订单处理
     *
     * @param metaOrder
     * @param card
     * @param isB
     */
    public void openCardOrderDeal(MetaOrder metaOrder, CreditCard card, boolean isB) {
        String lockKey = com.meta.system.constant.Constants.order_lock_deal + metaOrder.getId();
        boolean locked = false;
        try {
            // 尝试获取锁，设置过期时间防止死锁
            locked = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 30, TimeUnit.SECONDS);
            if (!locked) {
                logger.info("订单{}结果处理中，跳过重复处理", metaOrder.getId());
                return;
            }
            if (KzOrderStatus.success.getValue().equals(metaOrder.getStatus())) {
                //开卡成功处理
                if (isB) {

                    //B端
                    MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartnerBySysId(card.getSysId());
                    List<MetaPartnerTxnDtl> metaPartnerTxnDtlList = metaPartnerTxnDtlService.findCardIdAndTxnIdAndStatus(card.getCardId(), String.valueOf(TxnStatus.PADDING.getValue()), new String[]{PartnerTxnType.CardApplication.getValue()});

                    if (metaPartnerTxnDtlList.size() > 0) {
                        Character orderStatus = TxnStatus.APPROVED.getValue();
                        logger.info("orderStatus: " + orderStatus);
                        String orderTransactionType = TxnStatus.APPROVED.getName();
                        logger.info("orderTransactionType: " + orderTransactionType);

                        MetaPartnerTxnDtl dtl = metaPartnerTxnDtlList.get(0);
                        logger.info("开卡流水: " + dtl.getId());
                        metaPartnerTxnDtlService.updateDtl(dtl, orderTransactionType);

                        JSONObject jsonData = new JSONObject();
                        jsonData.put("cardID", card.getCardId());
                        jsonData.put("reason", "");
                        if (card.getSource().startsWith("moonbank")) {
                            jsonData.put("status", "TBA");
                        } else {
                            jsonData.put("status", kunService.getCardStatus(KunCardStatus.NORMAL.getValue()));
                        }
                        pushData("CARD_STATUS_CHANGE", metaPartnerAssetPool.getSysId(), card.getCardId(), card.getCardHolder(), metaPartnerAssetPool.getApiUrl(), jsonData);
                    }
                    getCardInfo(card);
                } else {
                    openCardDeal(card.getCardId(), TxnStatus.APPROVED.getName(), metaOrder.getSettlementType(), metaOrder.getRequestNo());
                }
            } else if (KzOrderStatus.fail.getValue().equals(metaOrder.getStatus())) {
                //开卡失败处理
                if (isB) {
                    //B端
                    MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartnerBySysId(card.getSysId());
                    List<MetaPartnerTxnDtl> metaPartnerTxnDtlList = metaPartnerTxnDtlService.findCardIdAndTxnIdAndStatus(card.getCardId(), String.valueOf(TxnStatus.PADDING.getValue()), new String[]{PartnerTxnType.CardApplication.getValue()});
                    if (metaPartnerTxnDtlList.size() > 0) {
                        Character orderStatus = TxnStatus.DECLINED.getValue();
                        logger.info("orderStatus: " + orderStatus);
                        String orderTransactionType = TxnStatus.DECLINED.getName();
                        logger.info("orderTransactionType: " + orderTransactionType);

                        MetaPartnerTxnDtl dtl = metaPartnerTxnDtlList.get(0);
                        logger.info("开卡流水: " + dtl.getId());
                        metaPartnerTxnDtlService.updateDtl(dtl, orderTransactionType);

                        JSONObject jsonData = new JSONObject();
                        jsonData.put("cardID", card.getCardId());
                        jsonData.put("reason", "");
                        jsonData.put("status", kunService.getCardStatus(KunCardStatus.FAIL.getValue()));
                        pushData("CARD_STATUS_CHANGE", metaPartnerAssetPool.getSysId(), card.getCardId(), card.getCardHolder(), metaPartnerAssetPool.getApiUrl(), jsonData);

                    }
                } else {
                    openCardDeal(card.getCardId(), TxnStatus.DECLINED.getName(), metaOrder.getSettlementType(), metaOrder.getRequestNo());
                }

            }
        } finally {
            if (locked) {
                redisTemplate.delete(lockKey); // 释放锁
            }
        }
    }

    /**
     * 调用充值
     *
     * @param metaOrder 订单
     * @return
     */
    @Override
    @Transactional
    public CardRechargeResult rechargeCard(MetaOrder metaOrder) {
        CardRechargeResult cardRechargeResult = null;
        String cardId = metaOrder.getCardId();
        CreditCard card = findByCardId(cardId);
        String uid = "";
        //是否是B端
        boolean isB = false;
        if (StringUtils.isNotEmpty(card.getSysId())) {
            isB = true;
        }
        if (isB) {
            MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(card.getCardHolder(), card.getSysId());
            uid = partnerUser.getUid();

        } else {
            Wallet w = walletService.findByUserId(card.getUserId());
            uid = w.getMoonbankUid();
        }


        if ("kun".equals(card.getSource())) {
            JSONObject jsonObject = kunService.queryCharge(metaOrder.getRequestNo(), card);
            if (jsonObject != null) {
                logger.info("充值流水已经存在：" + metaOrder.getRequestNo());
                return null;
            }
            cardRechargeResult = kunService.rechargeCard(card, metaOrder);
        } else if (card.getSource().startsWith("moonbank")) {
            //判断是否流水是否已经存在
            JSONObject jsonObject = mcardService.rechargeQuery(metaOrder.getRequestNo(), card);
            if (jsonObject != null) {
                logger.error("充值流水已经存在：" + metaOrder.getRequestNo());
                return null;
            }
            cardRechargeResult = mcardService.rechargeCard(uid, card, metaOrder);
        } else if (ChannelType.FORNAX.getValue().equals(card.getSource())) {
            //查询充值流水,如果存在就不处理
            JSONObject result = fornaxService.queryOrderRechargeCard(metaOrder.getRequestNo());
            if (result != null) {
                logger.info("充值流水已经存在：" + metaOrder.getRequestNo());
                return null;
            }
            cardRechargeResult = fornaxService.rechargeCard(card, metaOrder);
        }

        if (cardRechargeResult == null || StringUtil.isEmpty(cardRechargeResult.getStatus())) {
            return null;
        }
        if (TxnStatus.APPROVED.getName().equals(cardRechargeResult.getStatus())) {
            //成功
            metaOrder.setStatus(KzOrderStatus.success.getValue());

        } else if (TxnStatus.DECLINED.getName().equals(cardRechargeResult.getStatus())) {
            //失败
            metaOrder.setStatus(KzOrderStatus.fail.getValue());
        }

        orderRechageDeal(metaOrder, cardRechargeResult, card);


        return cardRechargeResult;
    }

    /**
     * 充值处理
     *
     * @param metaOrder
     * @param cardRechargeResult
     * @param card
     */
    public void orderRechageDeal(MetaOrder metaOrder, CardRechargeResult cardRechargeResult, CreditCard card) {
        String cardId = card.getCardId();
        if (!TxnStatus.PENDING.getName().equals(cardRechargeResult.getStatus())) {
            getCardInfo(card);
            if (StringUtils.isEmpty(card.getSysId())) {
                dealRecharge(card.getCardId(), metaOrder.getRequestNo(), cardRechargeResult.getStatus(), metaOrder.getSettlementType());
            } else {
                //B端的数据处理
                String requestNo = metaOrder.getRequestNo();
                List<MetaPartnerTxnDtl> dtlUsdlist = metaPartnerTxnDtlService.findCardRechare(cardId, String.valueOf(TxnStatus.PADDING.getValue()), requestNo);
                if (dtlUsdlist.size() > 0) {
                    MetaPartnerTxnDtl dtl = dtlUsdlist.get(0);
                    logger.info("充值流水: " + requestNo);
                    logger.info("orderTransactionType: " + cardRechargeResult.getStatus());
                    metaPartnerTxnDtlService.updateDtl(dtl, cardRechargeResult.getStatus());
                    getCardInfo(card);

                    MetaPartnerAssetPoolKey metaPartnerAssetPool = metaPartnerAssetPoolService.selectBySysId(card.getSysId());
                    CreditCardLog creditCardLog = creditCardLogService.findByTransactionId(dtl.getTxnId());

                    JSONObject jsonData = new JSONObject();
                    jsonData.put("cardID", cardId);

                    Date currentDate = new Date();

                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                    // 将日期时间格式化为指定格式的字符串
                    String formattedDate = formatter.format(currentDate);
                    jsonData.put("time", formattedDate);
                    jsonData.put("currency", dtl.getCurrency());
                    jsonData.put("amount", dtl.getTxnAmount().abs());//扣减的资产
                    jsonData.put("sendAmount", creditCardLog.getCardTransactionAmount());//充值到卡的金额
                    jsonData.put("sendCurrency", card.getCurrency());
                    if (TxnStatus.APPROVED.getName().equals(cardRechargeResult.getStatus())) {
                        jsonData.put("status", "SUCCESS");
                        jsonData.put("transactionId", requestNo);
                        pushData("CARD_RECHARGE_RESULT", metaPartnerAssetPool.getSysId(), card.getCardId(), card.getCardHolder(), metaPartnerAssetPool.getApiUrl(), jsonData);

                    } else if (TxnStatus.DECLINED.getName().equals(cardRechargeResult.getStatus())) {
                        jsonData.put("status", "FAIL");
                        jsonData.put("transactionId", requestNo);
                        pushData("CARD_RECHARGE_RESULT", metaPartnerAssetPool.getSysId(), card.getCardId(), card.getCardHolder(), metaPartnerAssetPool.getApiUrl(), jsonData);

                    }

                }
            }

        }
    }

    /**
     * 更新卡片信息
     *
     * @param c 卡片
     */
    public CreditCard getCardInfo(CreditCard c) {
        //判断cvv2是否存在，不存在更新卡信息


        String cardStatus = c.getCardStatus();


        CardInfo cardInfo = null;
        if (c.getSource().startsWith("moonbank")) {

            String moonbankUid = "";
            if (StringUtils.isNotEmpty(c.getSysId())) {
                MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(c.getCardHolder(), c.getSysId());
                moonbankUid = partnerUser.getUid();
            } else {
                Wallet w = walletService.findByUserId(c.getUserId());
                moonbankUid = w.getMoonbankUid();
            }
            cardInfo = mcardService.getCardInfo(moonbankUid, c);

        } else if ("kun".equals(c.getSource())) {
            cardInfo = kunService.getCardInfo(c);
        } else if (ChannelType.FORNAX.getValue().equals(c.getSource())) {
            cardInfo = fornaxService.getCardInfo(c);
        }


        if (cardInfo != null) {
            String newStatus = cardInfo.getCardStatus();
            String cvv2 = cardInfo.getCvv2();
            String cardNo = cardInfo.getCardNo();
            if (StringUtils.isEmpty(c.getCvv2())) {
                if (StringUtils.isNotEmpty(cvv2)) {
                    c.setCvv2(AESUtils.aesEncrypt(Constants.card_key, cvv2));
                }
                if (StringUtils.isNotEmpty(cardNo) && !cardNo.contains("*")) {
                    c.setCardNo(AESUtils.aesEncrypt(Constants.card_key, cardNo));
                }
            }
            String status = c.getCardStatus();
            if (StringUtils.isNotEmpty(newStatus) && !cardStatus.equals(status)) {
                c.setStatusUpdateTime(new Date());
            }
            c.setCardStatus(newStatus);
            c.setExpirationTime(cardInfo.getExpirationTime());
            c.setBillingCountry(cardInfo.getCountryCode());
            c.setProvince(cardInfo.getBillingState());
            c.setCity(cardInfo.getBillingCity());
            c.setAddressLine1(cardInfo.getBillingAddress());
            c.setPostalCode(cardInfo.getBillingZipCode());
            c.setBalance(cardInfo.getBalance());

            creditCardDao.save(c);
        }

        return c;

    }

    /**
     * 新增卡状态推送数据
     *
     * @param type
     * @param sysId
     * @param cardId
     * @param userEmail
     * @param apiUrl
     * @param json
     */
    @Transactional
    public void pushData(String type, String sysId, String cardId, String userEmail, String apiUrl, JSONObject json) {

        MetaPushData metaPushData = metaPushDataService.creatPashData(sysId, cardId, userEmail, apiUrl, JSON.toJSONString(json), type);
        if (StringUtils.isNotEmpty(apiUrl)) {
            metaPushData.setStatue("3");//表示还未推送到mq
        } else {
            metaPushData.setStatue("0");//推送失败
        }
        metaPushDataService.save(metaPushData);

    }

    @Override
    public void openRecharge(Long userId, BigDecimal rechargeAmount, String coin, String cardId, String txnId) {

        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        if ("USD".equals(coin)) {
            Wallet wallet = walletService.findByUserId(userId);

            if (wallet.getUsdBalance().compareTo(rechargeAmount) < 0) {
                //账户余额不足
                throw new CustomException(MessageUtils.message("wallet.account.insufficient.balance"));
            }

            VccReqDto dto = setReqDto(null, 0, FreezeType.FREEZE, userId
                    , userId, accountId, null, AlgorithmType.SUB
                    , rechargeAmount, BigDecimal.ZERO, TxnType.CARD_RECHARGE_C, cardId, txnId);


            dto.setFromCoin(coin);
            dto.setExchgRate(new BigDecimal("1"));
            dto.setFromAmount(rechargeAmount);
            AjaxResult ar = walletService.update(dto);

            VccReqDto dtoCom = setReqDto(null, 0, FreezeType.NORMAL, accountId
                    , userId, accountId, null, AlgorithmType.ADD
                    , rechargeAmount, BigDecimal.ZERO, TxnType.CARD_RECHARGE_D, cardId, txnId);
            dtoCom.setFromCoin(coin);
            dtoCom.setExchgRate(new BigDecimal("1"));
            dtoCom.setFromAmount(rechargeAmount);
            AjaxResult arComm = walletService.updateComAcc(dtoCom);

        } else {

            ExchangeRate rate = exchangeRateService.findByFromCurrencyCodeAndToCurrencyCode(coin, "USD");
            BigDecimal amount = rechargeAmount.multiply(rate.getRateVal());
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                logger.error("开卡充值金额异常");
                throw new CustomException("error");
            }
            CoinBalance c = coinBalanceDao.findByUserIdAndCoinCode(userId, coin);
            if (c.getCoinBalance().compareTo(amount) < 0 && c.getUserId() != accountId) {
                //账户余额不足
                throw new CustomException(MessageUtils.message("wallet.account.insufficient.coinbalance", c.getCoinCode()));
            }

            VccReqDto dto = new VccReqDto(
                    userId,
                    AlgorithmType.SUB,
                    amount,
                    FreezeType.FREEZE,
                    TxnType.COIN_CARD_RECHARGE_OUT,
                    userId,
                    accountId,
                    cardId,
                    BigDecimal.ZERO,
                    txnId);

            dto.setRecordType(0);//支出
            dto.setCoin(coin);
            dto.setFromCoin("USD");
            dto.setFromAmount(amount);
            dto.setExchgRate(rate.getRateVal());
            AjaxResult ar = coinTxnDtlService.update(dto, coin);
        }
    }

    /**
     * 卡交易信息处理
     *
     * @param str
     * @return
     */
    @Override
    public String dealAesCardInfo(String str) {
        if (StringUtils.isNotEmpty(str) && !str.contains("*")) {
            return AESUtils.aesDecrypt(Constants.card_key, str);
        }
        return str;
    }
}
