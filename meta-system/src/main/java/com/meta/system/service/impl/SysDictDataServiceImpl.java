package com.meta.system.service.impl;

import com.meta.common.core.domain.entity.SysDictData;
import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableSupport;
import com.meta.common.utils.DictUtils;
import com.meta.common.utils.ServletUtils;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.code.BusinessBizCode;
import com.meta.common.utils.sql.SqlUtil;
import com.meta.system.dao.SysDictDataDao;
import com.meta.system.service.ISysDictDataService;
import com.meta.system.service.ISysDictTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.*;

/**
 * 字典 业务层处理
 *
 * <AUTHOR>
 * @since 1.0  2020-12-11
 */
@Transactional(readOnly = true)
@Service
public class SysDictDataServiceImpl implements ISysDictDataService {

    @Autowired
    private SysDictDataDao dictDataDao;

    @Autowired
    private ISysDictTypeService dictTypeService;

    /**
     * 根据条件分页查询字典数据
     *
     * @param req 字典数据信息
     * @return 字典数据集合信息
     */
    @Override
    public Page<SysDictData> selectDictDataList(SysDictData req) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        if (StringUtils.isNotNull(pageDomain.getPageNum()) && StringUtils.isNotNull(pageDomain.getPageSize())) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        }
        Specification<SysDictData> example = new Specification<SysDictData>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<SysDictData> root,
                                         CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<>();
                if (StringUtils.isNoneBlank(req.getDictLabel())) {
                    Predicate pre = cb.like(root.get("dictLabel").as(String.class), "%" + req.getDictLabel() + "%");
                    list.add(pre);
                }
                if (StringUtils.isNoneBlank(req.getDictType())) {
                    Predicate pre = cb.equal(root.get("dictType").as(String.class), req.getDictType());
                    list.add(pre);
                }
                if (StringUtils.isNoneBlank(req.getStatus())) {
                    Predicate pre = cb.equal(root.get("status").as(String.class), req.getStatus());
                    list.add(pre);
                }
                if (list.isEmpty()) {
                    return null;
                }
                return cb.and(list.toArray(new Predicate[0]));
            }
        };
        Pageable pageable = PageRequest.of(pageDomain.getPageNo(), pageDomain.getPageSize(), Sort.Direction.DESC, Optional.ofNullable(pageDomain.getOrderByColumn()).orElse("createTime"));
        Page<SysDictData> page = dictDataDao.findAll(example, pageable);
        return page;
    }

    /**
     * 根据字典类型和字典键值查询字典数据信息
     *
     * @param dictType  字典类型
     * @param dictValue 字典键值
     * @return 字典标签
     */
    @Override
    public String selectDictLabel(String dictType, String dictValue) {
        SysDictData sysDictData = dictDataDao.findByDictTypeAndDictValue(dictType, dictValue).orElse(new SysDictData());
        return sysDictData.getDictLabel();
    }

    /**
     * 根据字典数据ID查询信息
     *
     * @param dictCode 字典数据ID
     * @return 字典数据
     */
    @Override
    public SysDictData selectDictDataById(Long dictCode) {
        SysDictData sysDictData = dictDataDao.findById(dictCode).orElse(new SysDictData());
        return sysDictData;
    }

    /**
     * 批量删除字典数据信息
     *
     * @param dictCodes 需要删除的字典数据ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteDictDataByIds(Long[] dictCodes) {
        List<SysDictData> sysDictDataList = dictDataDao.findByDictCodeIn(Arrays.asList(dictCodes));
        dictDataDao.deleteAll(sysDictDataList);
        DictUtils.clearDictCache();
        return BusinessBizCode.OPTION_SUCCESS.getCode();
    }

    /**
     * 新增保存字典数据信息
     *
     * @param dictData 字典数据信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertDictData(SysDictData dictData) {
        dictData.setCreateTime(new Date());
        SysDictData save = dictDataDao.save(dictData);
        if (null != save) {
            DictUtils.clearDictCache();
        }
        return BusinessBizCode.OPTION_SUCCESS.getCode();
    }

    /**
     * 修改保存字典数据信息
     *
     * @param dictData 字典数据信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateDictData(SysDictData dictData) {
        SysDictData save = dictDataDao.save(dictData);
        if (null != save) {
            DictUtils.clearDictCache();
            dictTypeService.reload();
        }
        return BusinessBizCode.OPTION_SUCCESS.getCode();
    }

    /**
     * 获取app广告图片列表
     *
     * @param dList
     * @return
     */
    @Override
    public Page<SysDictData> selectAppImageList(List<String> dList) {

        PageDomain pageDomain = TableSupport.buildPageRequest();
        if (StringUtils.isNotNull(pageDomain.getPageNum()) && StringUtils.isNotNull(pageDomain.getPageSize())) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        }
        Specification<SysDictData> example = new Specification<SysDictData>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<SysDictData> root,
                                         CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<>();

                if (dList.size() > 0) {
                    CriteriaBuilder.In<String> in = cb.in(root.get("dictType"));
                    dList.forEach(in::value);
                    list.add(in);
                }

                Predicate pre = cb.equal(root.get("status").as(String.class), "0");
                list.add(pre);

                if (list.isEmpty()) {
                    return null;
                }
                return cb.and(list.toArray(new Predicate[0]));
            }
        };
        Pageable pageable = PageRequest.of(pageDomain.getPageNo(), pageDomain.getPageSize(), Sort.Direction.ASC, Optional.ofNullable(pageDomain.getOrderByColumn()).orElse("dictCode"));
        Page<SysDictData> page = dictDataDao.findAll(example, pageable);
        return page;
    }

    @Override
    public List<SysDictData> selectDictDataList(String dictType) {
        return dictDataDao.selectDictDataList(dictType);
    }

    @Override
    public String getLabel(SysDictData sysDictData) {
        String acceptLanguage = ServletUtils.getRequest().getHeader("Accept-Language");
        String str = "";
        if (StringUtils.isNotEmpty(acceptLanguage)) {
            if ("en".equals(acceptLanguage) || "en-US".equals(acceptLanguage)) {
                str = sysDictData.getDictLabelEn();
            } else {
                str = sysDictData.getDictLabel();
            }
        } else {
            str = sysDictData.getDictLabel();
        }
        return str;
    }

    @Override
    public String getLabel(String type, String value) {
        String acceptLanguage = ServletUtils.getRequest().getHeader("Accept-Language");
        String str = "";
        SysDictData sysDictData = dictDataDao.findByDictTypeAndDictValue(type, value).orElse(new SysDictData());
        if (StringUtils.isNotEmpty(acceptLanguage)) {
            if ("en".equals(acceptLanguage) || "en-US".equals(acceptLanguage)) {
                str = sysDictData.getDictLabelEn();
            } else {
                str = sysDictData.getDictLabel();
            }
        } else {
            str = sysDictData.getDictLabel();
        }
        return str;
    }


    @Override
    public String getLabelLanguage(String acceptLanguage, String type, String value) {
        String str = "";
        SysDictData sysDictData = dictDataDao.findByDictTypeAndDictValue(type, value).orElse(new SysDictData());
        if (StringUtils.isNotEmpty(acceptLanguage)) {
            if ("en".equals(acceptLanguage) || "en-US".equals(acceptLanguage)) {
                str = sysDictData.getDictLabelEn();
            } else {
                str = sysDictData.getDictLabel();
            }
        } else {
            str = sysDictData.getDictLabel();
        }
        return str;
    }

    @Override
    public String getDataValue(String type, String label) {
        if(StringUtils.isEmpty(label)){
            return "";
        }
        List<SysDictData> list = dictDataDao.findByDictType(type);
        String str = "";
        for (SysDictData data : list) {
            if (data.getDictLabel().equals(label.trim())) {
                str = data.getDictValue();
                break;
            }
        }
        return str;
    }

}
