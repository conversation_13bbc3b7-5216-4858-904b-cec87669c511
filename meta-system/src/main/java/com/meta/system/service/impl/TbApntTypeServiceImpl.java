package com.meta.system.service.impl;

import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableSupport;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.code.BusinessBizCode;
import com.meta.system.dao.TbApntTypeDao;
import com.meta.system.domain.apnt.TbApntType;
import com.meta.system.service.TbApntTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/02/02/13:42
 */
@Service
public class TbApntTypeServiceImpl implements TbApntTypeService {

    @Autowired
    private TbApntTypeDao tbApntTypeDao;

    @Override
    public TbApntType findByType(String type) {
        return tbApntTypeDao.findByType(type);
    }

    @Override
    public Page<TbApntType> selectList(String type) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(pageReq.getPageNo(), pageReq.getPageSize(), sort);
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("type", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withIgnoreNullValues();
        TbApntType tbApntType = new TbApntType();
        if (StringUtils.isNotEmpty(type)) {
            tbApntType.setType(type);
        }
        Example<TbApntType> example = Example.of(tbApntType, matcher);
        return tbApntTypeDao.findAll(example, pageable);
    }

    @Override
    public TbApntType save(TbApntType tbApntType) {
        return tbApntTypeDao.save(tbApntType);
    }

    @Override
    public int delete(long id) {
        tbApntTypeDao.deleteById(id);
        return BusinessBizCode.OPTION_SUCCESS.getCode();
    }

    @Override
    public TbApntType update(TbApntType tbApntType) {
        TbApntType type = tbApntTypeDao.findById(tbApntType.getId()).orElse(null);
        if (type == null) {
            return null;
        }
        type.update(tbApntType);
        type.setUpdateTime(new Date());
        return tbApntTypeDao.save(type);
    }
}
