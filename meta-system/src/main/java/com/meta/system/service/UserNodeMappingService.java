package com.meta.system.service;

import com.meta.system.domain.app.node.UserNodeMapping;

public interface UserNodeMappingService {

    /**
     *  插入
     * @param mapping 数据
     */
    void insert(UserNodeMapping mapping);

    /**
     * 2.将新注册用户的信息维护至推荐人关系中，找到referrer_id如111，
     * 通过查询sql获取关系数据后将level_no关系+1后，新增mapping；如新注册用户没有推荐人，则这步可忽略。
     *
     * @param userId     注册人id
     * @param nodeLevel  注册人节点等级
     * @param referrerId 推荐人id
     */
    void maintenance(Long userId,String nodeLevel, Long referrerId);

    /**
     * 节点升级时维护更新mapping
     * @param userId 升级的节点用户id
     * @param nodeLevel 新的节点等级代码
     */
    void upgrade(Long userId, String nodeLevel);
}
