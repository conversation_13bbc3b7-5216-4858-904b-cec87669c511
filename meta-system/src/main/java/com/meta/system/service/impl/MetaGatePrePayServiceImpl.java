package com.meta.system.service.impl;


import com.meta.system.dao.app.MetaGatePrePayDao;
import com.meta.system.domain.app.MetaGatePrePay;
import com.meta.system.service.MetaGatePrePayService;
import com.meta.system.vo.MetaGatePrePayVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/27/14:27
 */
@Service
public class MetaGatePrePayServiceImpl implements MetaGatePrePayService {

    @Autowired
    private MetaGatePrePayDao metaGatePrePayDao;

    @Override
    public void save(MetaGatePrePay metaGatePrePay) {
        metaGatePrePayDao.save(metaGatePrePay);
    }

    @Override
    public MetaGatePrePay findData(String merchantTradeNo) {
        return  metaGatePrePayDao.findData(merchantTradeNo);
    }

    @Override
    public void updateData(String merchantTradeNo, String bizStatus, String transactionId) {
          metaGatePrePayDao.updateData(merchantTradeNo,bizStatus,transactionId);
    }

    @Override
    public BigDecimal sumData(String email) {
        return metaGatePrePayDao.sumData(email);
    }

    @Override
    public Page<MetaGatePrePayVo> tradeList(String email) {
        return metaGatePrePayDao.tradeList(email);
    }
}
