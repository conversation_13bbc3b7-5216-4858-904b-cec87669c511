package com.meta.system.service.impl;

import com.meta.common.core.domain.AjaxResult;
import com.meta.common.enums.app.*;
import com.meta.common.exception.CustomException;
import com.meta.common.utils.DateUtils;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.StringUtils;
import com.meta.system.constant.ExceptionInfo;
import com.meta.system.dao.TxnDtlCodeDao;
import com.meta.system.dao.TxnDtlUSDDao;
import com.meta.system.dao.WalletDao;
import com.meta.system.domain.Wallet;
import com.meta.system.domain.log.TxnDtlCode;
import com.meta.system.domain.log.TxnDtlUSD;
import com.meta.system.dto.TxnDto;
import com.meta.system.dto.VccReqDto;
import com.meta.system.service.ISysDictDataService;
import com.meta.system.service.TxnService;
import com.meta.system.vo.TradeDayTotalVo;
import com.meta.system.vo.TxnDtlCodeVo;
import com.meta.system.vo.TxnDtlUSDVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

@Service
public class TxnServiceImpl implements TxnService {

    @Autowired
    private TxnDtlUSDDao dtlUSDDao;

    @Autowired
    private TxnDtlCodeDao dtlCodeDao;

    @Autowired
    private WalletDao walletDao;

    @Autowired
    private ISysDictDataService sysDictDataService;

    @Override
    public TxnDtlCode dealTxnForCode(TxnDtlCode dtlCode) {
        dtlCode.setTxnTime(DateUtils.getNowDate());
        return dtlCodeDao.save(dtlCode);
    }

    @Override
    public void updateTxnProductForCode(Long id, String txnProduct) {
        dtlCodeDao.updateTxnProductById(id, txnProduct);
    }

    @Override
    public Page<TxnDtlUSDVo> pageForUSD(TxnDto txnDto) {
        return dtlUSDDao.page(txnDto);
    }

    @Override
    public List<TxnDtlUSD> findAllByCardIdAndStatus(String cardId, String stauts) {
        return dtlUSDDao.findAllByCardIdAndStatus(cardId, stauts);
    }


    @Override
    public TxnDtlUSD findByCardIdAndTxnTypeAndRelaTransactionid(String cardId, String type, String txnId) {
        return dtlUSDDao.findByCardIdAndTxnTypeAndRelaTransactionid(cardId, type, txnId);
    }
    @Override
    public List<TxnDtlUSD> findAllByCardIdAndStatusAndTransactionId(String cardId, String stauts, String transactionId, String[] txnTypes) {
        return dtlUSDDao.findAllByCardIdAndStatusAndTransactionId(cardId, stauts, transactionId,txnTypes);
    }

    @Override
    public List<TxnDtlUSD> findAllByCardIdAndStatusAndTxnTypeForUsd(String cardId, String stauts, String[] txnTypes) {
        return dtlUSDDao.findAllByCardIdAndStatusAndTxnType(cardId, stauts, txnTypes);
    }

    public List<TxnDtlUSD> findAllByCardIdAndStatusAndTransactionId(String cardId, String stauts, String transactionId) {
        return dtlUSDDao.findAllByCardIdAndStatusAndTransactionId(cardId, stauts, transactionId);
    }

    @Override
    public void updateUsdProductById(Long id, String product) {
        dtlUSDDao.updateTxnProductById(id, product);
    }

    @Override
    public TxnDtlUSDVo detailByUSD(Long id, Long userId) {
        return dtlUSDDao.selectVoById(id, userId);
    }

    @Override
    public Page<TxnDtlCodeVo> pageForCode(TxnDto txnDto) {
        Page<TxnDtlCodeVo> page = dtlCodeDao.page(txnDto);
        List<TxnDtlCodeVo> list = page.getContent();
        for (TxnDtlCodeVo vo : list) {

            if (StringUtils.isNotEmpty(vo.getCodeType())) {
                vo.setCodeDetail(sysDictDataService.getLabel("credit_card_level", vo.getCodeType()));
            }
            if (StringUtils.isNotEmpty(vo.getTxnType())) {
                if (TxnType.CARD_ACTIVATION.getValue().equals(vo.getTxnType())) {
                    vo.setToUsername("");
                    vo.setFromUsername("");
                }
                vo.setTypeDetail(sysDictDataService.getLabel("code_txn_type", vo.getTxnType()));
            }
            if (vo.getTxnStatus() != null) {
                vo.setStatusDetail(sysDictDataService.getLabel("coin_txn_status", vo.getTxnStatus().toString()));
            }


        }

        return page;
    }

    @Override
    public TxnDtlCodeVo detailByCode(Long id, Long userId) {
        return dtlCodeDao.selectVoById(id, userId);
    }

    @Override
    public TxnDtlCode findByTxnProduct(String txnProduct) {
        return dtlCodeDao.findByTxnProduct(SecurityUtils.getLoginUser().getUserId(), txnProduct, TxnType.CARD_ACTIVATION.getValue());
    }

    @Override
    public TxnDtlCode findByTxnProductByCardId(String txnProduct) {
        return dtlCodeDao.findByTxnProduct(txnProduct, TxnType.CARD_ACTIVATION.getValue());
    }

    @Override
    public TxnDtlCode findByTxnProductAndStatusAndTxnTypeForCode(String txnProduct, String status, String txnType) {
        return dtlCodeDao.findByTxnProductAndTxnType(txnProduct, status, txnType);
    }

    @Override
    public TxnDtlUSD findByTxnId(String txnId) {
        TxnDtlUSD txnDtlUSD = dtlUSDDao.findById(txnId);
        String txnType = txnDtlUSD.getTxnType();
        if (StringUtils.isNotEmpty(txnType) && txnType.length() > 3) {
            String s = txnType.substring(2, 3);
            if ("1".equals(s)) {
                //vcc的不做处理
            } else if ("0".equals(s)) {
//                txnDtlUSD.setTxnDesc(MessageUtils.message("usd_txn_type_"+txnType));
            }
        }
        return txnDtlUSD;
    }

    @Override
    public TxnDtlCode findByTxnIdForCode(String txnId) {
        TxnDtlCode txnDtlCode = dtlCodeDao.findById(txnId);
        txnDtlCode.setTxnDesc(MessageUtils.message("code_txn_type_" + txnDtlCode.getTxnType()));
        return txnDtlCode;
    }

    @Override
    @Transactional
    public void merge(TxnDtlUSD dtl) {
        dtlUSDDao.updateTxnStatusForCard(dtl.getId(), dtl.getTxnStatus(), dtl.getMerchantName(), dtl.getMerchantCity());
    }

    @Override
    public void saveAll(List<TxnDtlUSD> list) {
        dtlUSDDao.saveAll(list);
    }

    @Override
    public void save(TxnDtlUSD txnDtlUSD) {
        dtlUSDDao.save(txnDtlUSD);
    }


    @Override
    public TxnDtlUSD dealTxnForUSD(TxnDtlUSD usd, FreezeType freezeType, AlgorithmType algorithmType) {
        switch (freezeType) {
            case FREEZE:
            case NO:
            case NORMAL:
                // 添加交易日志
                if (usd.getId() != null) {
                    TxnDtlUSD oldusd = dtlUSDDao.findById(usd.getId().toString());
                    if (oldusd.getTxnAmount() == null)
                        oldusd.setTxnAmount(usd.getTxnAmount());
                    oldusd.setTxnStatus(usd.getTxnStatus());
                    usd = dtlUSDDao.save(oldusd);
                } else {
                    usd.setTxnTime(DateUtils.getNowDate());
                    usd = dtlUSDDao.save(usd);
                }

                break;
            case FREE_FREEZE:
                // 交易成功
                if (algorithmType.equals(AlgorithmType.ADD)) {
                    dtlUSDDao.updateTxnStatusAndUsdBalanceById(usd.getId(), TxnStatus.SUCCESS.getValue(), usd.getTxnAmount().abs(), usd.getTxnProduct());
                } else {
                    dtlUSDDao.updateTxnStatusById(usd.getId(), TxnStatus.SUCCESS.getValue());
                }
                break;
            case FALLBACK:
                // 交易失败[未处理]
                if (algorithmType.equals(AlgorithmType.ADD)) {
                    dtlUSDDao.updateTxnStatusAndUsdBalanceById(usd.getId(), TxnStatus.FAIL.getValue(), usd.getTxnAmount().abs(), usd.getTxnProduct());
                } else {
                    dtlUSDDao.updateTxnStatusById(usd.getId(), TxnStatus.FAIL.getValue());
                }
                break;
            case NORMAL_FALLBACK:
                dtlUSDDao.updateTxnStatusById(usd.getId(), TxnStatus.FAIL.getValue());
                break;
            default:
                break;
        }
        return usd;
    }

    @Override
    public Page<TradeDayTotalVo> tradeReportPage(String startDate, String endDate) {
        return dtlUSDDao.tradeReportPage(startDate, endDate);
    }

    @Override
    public List<TxnDtlUSD> findCardIdAndTransactionId(String userBankcardId, String recordNo, String[] txnTypes) {
        return dtlUSDDao.findCardIdAndTransactionId(userBankcardId, recordNo, txnTypes);
    }

    @Override
    public void updateTxnProduct(String cardId, String userBankId) {
        dtlUSDDao.updateTxnProduct(cardId, userBankId);
    }

    @Override
    public void updateCodeProduct(String cardId, String userBankId) {
        dtlCodeDao.updateTxnProduct(cardId, userBankId);
    }

    @Override
    public BigDecimal findRechargeAmount(String cardId, String status, String txnType) {
        return dtlUSDDao.findRechargeAmount(cardId, status, txnType);
    }

    @Override
    public AjaxResult update(VccReqDto req) {
        //1.锁住行数据
        Wallet w = walletDao.findOneForUpdate(req.getUserId());
        AjaxResult res = AjaxResult.success();
        if (w != null) {
            if (req.getActiveCodeNum() != 0) {
                //3.激活码处理
                if (req.getCardLevel().equals(CardLevel.SILVER.getOrdinal())) {
                    if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.ADD)) {
                        w.setSilverActiveCodeNum(w.getSilverActiveCodeNum() + req.getActiveCodeNum());
                    } else if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.SUB)) {
                        if (w.getSilverActiveCodeNum() < req.getActiveCodeNum()) {
                            throw new CustomException(ExceptionInfo.SILVER_ACTIVITI_CODE_NUM_NO_ENOUGH);
                        }
                        w.setSilverActiveCodeNum(w.getSilverActiveCodeNum() - req.getActiveCodeNum());
                        if (req.isCodeUsed()) {
                            w.setSilverActiveCodeUsed(w.getSilverActiveCodeUsed() + req.getActiveCodeNum());
                        }
                    }
                } else if (req.getCardLevel().equals(CardLevel.GOLD.getOrdinal())) {
                    if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.ADD)) {
                        w.setGoldenActiveCodeNum(w.getGoldenActiveCodeNum() + req.getActiveCodeNum());
                    } else if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.SUB)) {
                        if (w.getGoldenActiveCodeNum() < req.getActiveCodeNum()) {
                            throw new CustomException(ExceptionInfo.GOLDEN_ACTIVITI_CODE_NUM_NO_ENOUGH);
                        }
                        w.setGoldenActiveCodeNum(w.getGoldenActiveCodeNum() - req.getActiveCodeNum());
                        if (req.isCodeUsed()) {
                            w.setGoldenActiveCodeUsed(w.getGoldenActiveCodeUsed() + req.getActiveCodeNum());
                        }
                    }
                }
                // 激活码 交易明细
                TxnDtlCode dtlCode = dealTxnForCode(TxnDtlCode.formatCode(req, w));
                res.put("dtlCode", dtlCode);
            }

            walletDao.update(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                    , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                    , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                    , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());
        }
        res.put("wallet", w);
        return res;
    }
}
