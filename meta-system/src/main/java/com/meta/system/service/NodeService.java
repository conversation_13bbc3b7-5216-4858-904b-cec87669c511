package com.meta.system.service;

import com.meta.common.core.domain.AjaxResult;
import com.meta.common.enums.app.NodeLevel;
import com.meta.system.domain.app.NodeConfig;
import com.meta.system.dto.node.NodeApplicationDto;
import com.meta.system.domain.app.node.NodeApplication;
import com.meta.system.vo.NodeApplicationVo;
import com.meta.system.vo.NodeCenter;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 节点申请
 */
public interface NodeService {

    /**
     *
     * @param nodeApplication 节点信息
     * @return 保存的节点信息
     */
    NodeApplication applyNode(NodeApplication nodeApplication);

    /**
     * 分页查询
     * @param apply 查询条件
     * @return 分页查询结果
     */
    Page<NodeApplicationVo> list(NodeApplicationDto apply);

    /**
     *  节点审批
     * @param nodeApplication 审批结论
     * @return 操作结果
     */
    int approval(NodeApplication nodeApplication) throws Exception;

    /**
     * 判断余额是否充足
     * @param userId 用户id
     * @param application 申请信息
     * @return 是否充足
     */
    boolean checkBalance(Long userId, NodeApplication application);

    /**
     * 判断该用户是否存在申请中的节点
     * @param userId 用户id
     * @return 是否存在申请中的节点
     */
    boolean checkApprovalStatus(Long userId);

    /**
     * 检测节点是否达到自动升级条件,返回以目前的充值额度所能达到的节点等级
     * @param userId 用户id
     * @return 当前可以升等级
     */
    NodeLevel checkUpgrade(Long userId);

    /**
     * 获取所有的节点配置
     * @return 全部的节点配置
     */
    List<NodeConfig> findNodeConfigAll();

    /**
     *
     * @param nodeLevel 节点等级
     * @return 跟据节点等级获取节点配置
     */
    NodeConfig findNodeConfigByNodeLevel(String nodeLevel);

    /**
     * 统计节点数据
     *
     * @param userId 用户id
     * @return 节点中心内容
     */
    NodeCenter findNodeCenter(Long userId);

    /**
     * 节点自动升级
     *
     * @param userId 用户
     * @param nodeLevel 可升节点等级
     */
    void autoUpgrade(Long userId, NodeLevel nodeLevel);

    /**
     * 查找上一节点信息
     * @param nodeLevel
     * @return
     */
    NodeConfig findPreviousNode(String  nodeLevel);

    AjaxResult apply(NodeApplication apply) throws Exception;
}
