package com.meta.system.service.impl;

import com.meta.system.dao.app.MetaCreditCardUoldDao;
import com.meta.system.domain.app.MetaCreditCardUold;
import com.meta.system.service.MetaCreditCardUoldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/03/18/19:31
 */
@Service
public class MetaCreditCardUoldServiceImpl implements MetaCreditCardUoldService {

    @Autowired
    private MetaCreditCardUoldDao metaCreditCardUoldDao;

    /**
     * 列表
     * @param cardStatus 卡状态
     * @param sync 是否同步
     * @return
     */
    @Override
    public List<MetaCreditCardUold> findCardByStatus(String cardStatus, String sync) {
        return metaCreditCardUoldDao.findCardByStatus(cardStatus, sync);
    }

    @Override
    public void updateData(String cardId, String sync, String cardIdNew) {
         metaCreditCardUoldDao.updateData( cardId,  sync,  cardIdNew);
    }

    @Override
    public MetaCreditCardUold findByOldId(String cardId) {
        return metaCreditCardUoldDao.findByOldId(cardId);
    }
}
