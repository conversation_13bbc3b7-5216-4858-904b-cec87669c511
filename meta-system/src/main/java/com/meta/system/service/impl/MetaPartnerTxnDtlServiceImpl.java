package com.meta.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.meta.common.constant.Constants;
import com.meta.common.enums.app.AlgorithmType;
import com.meta.common.enums.app.FreezeType;
import com.meta.common.enums.app.PartnerTxnType;
import com.meta.common.enums.app.TxnStatus;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.system.dao.MetaPartnerTxnDtlDao;
import com.meta.system.domain.CreditCard;
import com.meta.system.domain.partner.MetaPartnerAssetPool;
import com.meta.system.domain.partner.MetaPartnerTxnDtl;
import com.meta.system.dto.PoolTxnDto;
import com.meta.system.kun.constants.KunCardStatus;
import com.meta.system.kun.service.KunService;
import com.meta.system.service.CreditCardService;
import com.meta.system.service.MetaPartnerAssetPoolService;
import com.meta.system.service.MetaPartnerTxnDtlService;
import com.meta.system.vo.PoolTxnDtlUSDVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/06/20/17:01
 */
@Service
public class MetaPartnerTxnDtlServiceImpl implements MetaPartnerTxnDtlService {

    @Autowired
    private MetaPartnerTxnDtlDao metaPartnerTxnDtlDao;


    @Autowired
    private MetaPartnerAssetPoolService metaPartnerAssetPoolService;

    @Autowired
    @Lazy
    private CreditCardService creditCardService;

    @Autowired
    private KunService kunService;

    @Override
    public void save(MetaPartnerTxnDtl txnDtl) {
        metaPartnerTxnDtlDao.save(txnDtl);
    }

    @Override
    public Page<PoolTxnDtlUSDVo> pageForUSD_B(PoolTxnDto poolTxnDto) {
        if (StringUtils.isNotEmpty(poolTxnDto.getCardNo()) && !poolTxnDto.getCardNo().contains("*")) {
            poolTxnDto.setCardNo(AESUtils.aesEncrypt(Constants.card_key, poolTxnDto.getCardNo()));
        }
        return metaPartnerTxnDtlDao.pageForUSD_B(poolTxnDto);
    }

    @Override
    public List<MetaPartnerTxnDtl> findCardIdAndTransactionId(String userBankcardId, String recordNo, String[] txnTypes) {
        return metaPartnerTxnDtlDao.findCardIdAndTransactionId(userBankcardId, recordNo, txnTypes);
    }

    @Override
    public List<MetaPartnerTxnDtl> findCardIdAndTxnIdAndStatus(String cardId, String stauts, String[] txnTypes) {
        return metaPartnerTxnDtlDao.findCardIdAndTxnIdAndStatus(cardId, stauts, txnTypes);
    }

    @Override
    public List<MetaPartnerTxnDtl> findCardRechare(String cardId, String stauts, String txnId) {
        return metaPartnerTxnDtlDao.findCardRechare(cardId, stauts, txnId);
    }

    @Override
    public void updateStatus(String cardId, String stauts, String txnId) {
        metaPartnerTxnDtlDao.updateStatus(cardId, stauts, txnId);
    }

    /**
     * @param metaPartnerAssetPool 商户
     * @param txnType              交易类型
     * @param cardId               卡id
     * @param currency             币种
     * @param num                  交易数量
     * @param txnAmount            金额
     * @param fee                  手续费
     * @param txnStatus            状态
     * @param txnId                交易流水
     */
    @Override
    public void saveUSD(MetaPartnerAssetPool metaPartnerAssetPool, PartnerTxnType txnType, String cardId, String currency, Integer num, BigDecimal txnAmount, BigDecimal fee, Character txnStatus, String txnId) {

        MetaPartnerTxnDtl txnDtl = new MetaPartnerTxnDtl();
        txnDtl.setPartnerId(metaPartnerAssetPool.getId().getPartnerId());
        txnDtl.setCoinCode(metaPartnerAssetPool.getId().getCoinCode());
        txnDtl.setCardId(cardId);
        txnDtl.setCurrency(currency);
        txnDtl.setTxnType(txnType.getValue());
        txnDtl.setTxnAmount(txnAmount);
        txnDtl.setTxnNum(num);
        txnDtl.setTxnFee(fee);
        txnDtl.setTxnTime(new Date());
        txnDtl.setTxnStatus(txnStatus);
        txnDtl.setTxnDesc(txnType.getEname());
        txnDtl.setAssetBalance(metaPartnerAssetPool.getUsdtBalacne());
        txnDtl.setTxnId(txnId);
        metaPartnerTxnDtlDao.save(txnDtl);

    }

    @Override
    public void dealPool(BigDecimal amount, String sysId, String userBankcardId, FreezeType freezeType, AlgorithmType algorithmType, String txnId) {
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectBySysIdForUpdate(sysId);
        if (amount.compareTo(BigDecimal.ZERO) != 0) {
            if (algorithmType.equals(AlgorithmType.SUB)) {
                switch (freezeType) {
                    case FREEZE:
                        //金额冻结
                        metaPartnerAssetPool.setUsdtBalacne(metaPartnerAssetPool.getUsdtBalacne().subtract(amount));
                        metaPartnerAssetPool.setFreezeUstdBalacne(metaPartnerAssetPool.getFreezeUstdBalacne().add(amount));

                        metaPartnerAssetPoolService.updateBalance(metaPartnerAssetPool.getId().getPartnerId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());
                        saveUSD(metaPartnerAssetPool, PartnerTxnType.CardApplication, userBankcardId, "USD", 0, amount.negate(), BigDecimal.ZERO, TxnStatus.PADDING.getValue(), txnId);
                        break;
                    case FREE_FREEZE:
                        // 交易成功
                        metaPartnerAssetPool.setUsdtBalacne(metaPartnerAssetPool.getUsdtBalacne().subtract(amount));
                        metaPartnerAssetPoolService.updateBalance(metaPartnerAssetPool.getId().getPartnerId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());
                        saveUSD(metaPartnerAssetPool, PartnerTxnType.CardApplication, userBankcardId, "USD", 0, amount.negate(), BigDecimal.ZERO, TxnStatus.SUCCESS.getValue(), txnId);

                    default:
                        break;
                }
            } else if (algorithmType.equals(AlgorithmType.ADD)) {

            }
        }
    }


    @Override
    @Transactional
    public void updateDtl(MetaPartnerTxnDtl metaPartnerTxnDtl, String orderTransactionType) {
        if (TxnStatus.PADDING.getValue().equals(metaPartnerTxnDtl.getTxnStatus())) {
            BigDecimal totAmount = metaPartnerTxnDtl.getTxnAmount().abs();
            if (metaPartnerTxnDtl.getTxnFee() != null) {
                totAmount = totAmount.add(metaPartnerTxnDtl.getTxnFee().abs());
            }
            updateBalance(metaPartnerTxnDtl.getTxnId(), metaPartnerTxnDtl.getCardId(), metaPartnerTxnDtl.getPartnerId(), orderTransactionType, totAmount);
        }
    }

    @Override
    @Transactional
    public void updateDtl(List<MetaPartnerTxnDtl> list, String orderTransactionType) {
        BigDecimal totAmount = BigDecimal.ZERO;
        if (list.size() > 0) {
            for (MetaPartnerTxnDtl metaPartnerTxnDtl : list) {
                if (TxnStatus.PADDING.getValue().equals(metaPartnerTxnDtl.getTxnStatus())) {

                    totAmount = totAmount.add(metaPartnerTxnDtl.getTxnAmount().abs());
                    if (metaPartnerTxnDtl.getTxnFee() != null) {
                        totAmount = totAmount.add(metaPartnerTxnDtl.getTxnFee().abs());
                    }
                }
            }
            updateBalance(list.get(0).getTxnId(), list.get(0).getCardId(), list.get(0).getPartnerId(), orderTransactionType, totAmount);

        }

    }

    /**
     * 更新余额
     *
     * @param txnId                交易id
     * @param cardId               卡id
     * @param partnerId            合作者
     * @param orderTransactionType 状态
     * @param totAmount            金额
     */
    private void updateBalance(String txnId, String cardId, Long partnerId, String orderTransactionType, BigDecimal totAmount) {
        if (totAmount.compareTo(BigDecimal.ZERO) > 0) {
            MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectByUserIdForUpdate(partnerId);
            if (metaPartnerAssetPool != null) {

                if (TxnStatus.APPROVED.getName().equals(orderTransactionType)) {
                    //开卡成功
                    metaPartnerAssetPool.setFreezeUstdBalacne(metaPartnerAssetPool.getFreezeUstdBalacne().subtract(totAmount));
                    metaPartnerAssetPoolService.updateBalance(partnerId, metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());
                    updateStatus(cardId, String.valueOf(TxnStatus.SUCCESS.getValue()), txnId);


                    CreditCard card = creditCardService.findByCardId(cardId);
                    creditCardService.getCardInfo(card);


                } else if (TxnStatus.DECLINED.getName().equals(orderTransactionType)) {
                    //开卡失败
                    metaPartnerAssetPool.setUsdtBalacne(metaPartnerAssetPool.getUsdtBalacne().add(totAmount));
                    metaPartnerAssetPool.setFreezeUstdBalacne(metaPartnerAssetPool.getFreezeUstdBalacne().subtract(totAmount));
                    metaPartnerAssetPoolService.updateBalance(partnerId, metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());
                    updateStatus(cardId, String.valueOf(TxnStatus.FAIL.getValue()), txnId);

                    creditCardService.updateStatus(cardId, kunService.getCardStatus(KunCardStatus.FAIL.getValue()));

                }
            }
        }
    }


    @Override
    public List<MetaPartnerTxnDtl> findPaddingKunData(String type, String status) {
        return metaPartnerTxnDtlDao.findPaddingData(type, status);
    }
}
