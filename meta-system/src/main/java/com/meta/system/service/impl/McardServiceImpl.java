package com.meta.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meta.common.enums.app.TxnStatus;
import com.meta.common.utils.StringUtils;
import com.meta.system.config.MoonbankConfig;
import com.meta.system.domain.CreditCard;
import com.meta.system.domain.Wallet;
import com.meta.system.domain.app.MetaOrder;
import com.meta.system.domain.partner.MetaPartnerUser;
import com.meta.system.email.SendEmail;
import com.meta.system.models.CardInfo;
import com.meta.system.models.CardRechargeResult;
import com.meta.system.models.CardResult;
import com.meta.system.moonbank.models.ApiResponse;
import com.meta.system.moonbank.util.MoonbankEncryptUtil;
import com.meta.system.moonbank.util.MoonbankUtil;
import com.meta.system.service.McardService;
import com.meta.system.service.MetaPartnerUserService;
import com.meta.system.service.WalletService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/05/13/17:22
 */
@Slf4j
@Service
public class McardServiceImpl implements McardService {

    @Autowired
    private MoonbankUtil moonbankUtil;

    @Autowired
    private SendEmail sendEmail;

    @Autowired
    private MetaPartnerUserService metaPartnerUserService;

    @Autowired
    private WalletService walletService;

    /**
     * 申请卡
     *
     * @param uid        用户uid
     * @param bankCardId 模板id
     * @return
     */
    @Override
    public CardResult openCard(String uid, String bankCardId) {

        CardResult cardResult = null;
        ApiResponse<String> applyBankcardRes = moonbankUtil.applyBankcard(uid, Integer.valueOf(bankCardId), null, null);
        if (applyBankcardRes.isSuccess()) {
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, applyBankcardRes.getResult());
            System.out.println("userRegister encode result===>" + descStr);
            JSONObject j = JSON.parseObject(descStr);

            String userBankcardId = j.getString("userBankcardId");
            String status = j.getString("status");
            cardResult = new CardResult();
            cardResult.setCardId(userBankcardId);
            cardResult.setCardStatus(status);
            return cardResult;
        } else {
            log.error("开卡失败：" + applyBankcardRes.getMessage());
            sendEmail.remindMail(applyBankcardRes.getMessage(), "moonbank的开卡失败");

        }

        return cardResult;

    }

    @Override
    public CardRechargeResult rechargeCard(String uid, CreditCard c, MetaOrder metaOrder) {
        log.info("调用moonbank接口");
        BigDecimal rechargeAmount = metaOrder.getOrderAmount();
        ApiResponse<String> applyBankcardRes = moonbankUtil.rechargeBankcardByRequestNo(metaOrder.getRequestNo(), uid, Integer.valueOf(c.getBankCardId()), rechargeAmount);
        CardRechargeResult cardRechargeResult = null;
        if (applyBankcardRes.isSuccess()) {

            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, applyBankcardRes.getResult());
            JSONObject j = JSON.parseObject(descStr);
            String txnId = j.getString("requestOrderId");
            cardRechargeResult = new CardRechargeResult();
            cardRechargeResult.setStatus(TxnStatus.APPROVED.getName());
            cardRechargeResult.setTxnId(txnId);

        } else {
            if (applyBankcardRes.getCode() == 99997) {
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, applyBankcardRes.getResult());
                System.out.println("userRegister encode result===>" + descStr);
                JSONObject j = JSON.parseObject(descStr);
                String txnId = j.getString("requestOrderId");
                cardRechargeResult = new CardRechargeResult();
                cardRechargeResult.setStatus(TxnStatus.PENDING.getName());
                cardRechargeResult.setTxnId(txnId);
            }
        }

        return cardRechargeResult;
    }

    /**
     * 更新卡片信息
     *
     * @param
     * @param moonbankUid 用户uid
     * @param card        卡片
     */
    public CardInfo getCardInfo(String moonbankUid, CreditCard card) {
        CardInfo cardInfo = null;
        //判断cvv2是否存在，不存在更新卡信息

        ApiResponse<String> apiResponse = moonbankUtil.queryBankcardInfo(moonbankUid, Integer.valueOf(card.getBankCardId()));
        if (apiResponse.isSuccess()) {
            cardInfo = new CardInfo();
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

            JSONObject j = JSONObject.parseObject(descStr);
            String cardStatus = j.getString("userBankCardStatus");
            String cvv2 = j.getString("cardCvv");
            String cardNo = j.getString("cardNo");

            cardInfo.setCardStatus(cardStatus);
            cardInfo.setCardNo(cardNo);
            cardInfo.setCvv2(cvv2);

            String expiryDate = j.getString("expiryDate");
            if (StringUtils.isNotEmpty(expiryDate) && expiryDate.length() >= 7) {
                cardInfo.setExpirationTime(expiryDate.substring(5, 7) + expiryDate.substring(0, 2));
            } else if (StringUtils.isNotEmpty(expiryDate) && expiryDate.length() == 5 && expiryDate.contains("/")) {
                cardInfo.setExpirationTime(expiryDate.substring(3, 5) + expiryDate.substring(0, 2));
            }

            JSONObject vccCardHolderVo = j.getJSONObject("vccCardHolderVo");
            if (vccCardHolderVo != null) {
                cardInfo.setCountryCode(vccCardHolderVo.getString("countryCode"));
                cardInfo.setBillingState(vccCardHolderVo.getString("billingState"));
                cardInfo.setBillingCity(vccCardHolderVo.getString("billingCity"));
                cardInfo.setBillingAddress(vccCardHolderVo.getString("billingAddress"));
                cardInfo.setBillingZipCode(vccCardHolderVo.getString("billingZipCode"));
            }

            BigDecimal balance = getBalance(moonbankUid, card);
            if (balance != null) {
                cardInfo.setBalance(balance);
            }


        }
        return cardInfo;

    }

    /**
     * 获取卡片余额
     *
     * @param moonbankUid 用户uid
     * @param card        卡片
     * @return
     */
    public BigDecimal getBalance(String moonbankUid, CreditCard card) {
        //更新卡的余额

        ApiResponse<String> apiResponse = moonbankUtil.queryBankcardBalance(moonbankUid, Integer.valueOf(card.getBankCardId()));
        if (apiResponse.isSuccess()) {
            String descStr2 = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
            JSONObject j2 = JSONObject.parseObject(descStr2);

            BigDecimal balance = j2.getBigDecimal("balance");
            return balance;
        }
        return null;
    }

    @Override
    public JSONObject rechargeQuery(String transactionId, CreditCard c) {
        JSONObject bodyJson = null;
        String moonbankUid = "";
        if (StringUtils.isNotEmpty(c.getSysId())) {
            MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(c.getCardHolder(), c.getSysId());
            moonbankUid = partnerUser.getUid();
        } else {
            Wallet w = walletService.findByUserId(c.getUserId());
            moonbankUid = w.getMoonbankUid();
        }

        ApiResponse<String> apiResponse = moonbankUtil.cardOrderInfo(moonbankUid, Integer.valueOf(c.getBankCardId()), transactionId);

        if (apiResponse.isSuccess()) {
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
            bodyJson = new JSONObject();
            System.out.println("cardOrderInfo encode result===>" + descStr);
            JSONObject j = JSON.parseObject(descStr);
            String status = j.getString("status");

            if ("ACCEPT".equals(status)) {
                bodyJson.put("transactionStatus", "PENDING");
            } else if ("SUCCESS".equals(status)) {
                bodyJson.put("transactionStatus", "APPROVED");
            } else if ("FAILED".equals(status)) {
                bodyJson.put("transactionStatus", "DECLINED");
            } else if ("UNKNOWN".equals(status)) {
                bodyJson.put("transactionStatus", "OTHER");
            }

            bodyJson.put("cardID", c.getCardId());
            bodyJson.put("transactionId", transactionId);


        }

        return bodyJson;
    }
}
