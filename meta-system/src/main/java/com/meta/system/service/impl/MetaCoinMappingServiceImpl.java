package com.meta.system.service.impl;

import com.meta.common.constant.Constants;
import com.meta.system.dao.app.MetaCoinMappingDao;
import com.meta.system.domain.app.MetaCoinMapping;
import com.meta.system.service.MetaCoinMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/02/26/14:09
 */
@Service
public class MetaCoinMappingServiceImpl implements MetaCoinMappingService {

    @Autowired
    private MetaCoinMappingDao metaCoinMappingDao;
    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 把币种数据写到redis缓存中
     */
    @PostConstruct
    public void init() {
        String pre = Constants.TRADE_COIN;
        deleteTradeCoinKeys(pre);
        List<MetaCoinMapping> all = metaCoinMappingDao.findAll();
        for (MetaCoinMapping metaCoinMapping : all) {
            redisTemplate.opsForValue().set(pre + metaCoinMapping.getCode(), metaCoinMapping.getCoin());
        }


    }

    /**
     * 删除所有以 `xx:` 开头的键
     */
    public void deleteTradeCoinKeys(String prefix) {

        Set<String> keys = new HashSet<>();

        // 使用 SCAN 命令分批扫描匹配的键
        ScanOptions scanOptions = ScanOptions.scanOptions()
                .match(prefix + "*") // 匹配模式：TRADE_COIN:*
                .count(100) // 每次扫描的键数量（根据数据量调整）
                .build();

        try (Cursor<byte[]> cursor = redisTemplate.executeWithStickyConnection(conn ->
                conn.scan(scanOptions))) {
            while (cursor.hasNext()) {
                keys.add(new String(cursor.next()));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 批量删除键
        if (!keys.isEmpty()) {
            redisTemplate.delete(keys);
            System.out.println("Deleted keys: " + keys);
        }
    }
}
