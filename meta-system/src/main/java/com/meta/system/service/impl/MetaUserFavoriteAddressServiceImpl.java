package com.meta.system.service.impl;


import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableSupport;
import com.meta.common.enums.app.TxnType;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.sql.SqlUtil;
import com.meta.system.dao.app.MetaUserFavoriteAddressDao;
import com.meta.system.domain.app.MetaUserFavoriteAddress;
import com.meta.system.service.MetaUserFavoriteAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/07/09/17:32
 */
@Service
public class MetaUserFavoriteAddressServiceImpl implements MetaUserFavoriteAddressService {

    @Autowired
    private MetaUserFavoriteAddressDao metaUserFavoriteAddressDao;

    @Override
    public void save(MetaUserFavoriteAddress metaUserFavoriteAddress) {
        metaUserFavoriteAddressDao.save(metaUserFavoriteAddress);
    }

    @Override
    public MetaUserFavoriteAddress findAddress(String address, long userId) {
        return metaUserFavoriteAddressDao.findAddree(address, userId);
    }

    @Override
    public void delAddress(long id) {
        metaUserFavoriteAddressDao.deleteById(id);
    }

    @Override
    public Page<MetaUserFavoriteAddress> page(MetaUserFavoriteAddress metaUserFavoriteAddress) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        if (StringUtils.isNotNull(pageDomain.getPageNum()) && StringUtils.isNotNull(pageDomain.getPageSize())) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        }
        Specification<MetaUserFavoriteAddress> example = new Specification<MetaUserFavoriteAddress>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<MetaUserFavoriteAddress> root,
                                         CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<>();

                if (metaUserFavoriteAddress.getUserId() != null) {
                    Predicate pre = cb.equal(root.get("userId").as(Long.class), metaUserFavoriteAddress.getUserId());
                    list.add(pre);
                }
                if (StringUtils.isNoneBlank(metaUserFavoriteAddress.getType())) {
                    Predicate pre = cb.equal(root.get("type").as(String.class), metaUserFavoriteAddress.getType());
                    list.add(pre);
                }

                if (list.isEmpty()) {
                    return null;
                }
                return cb.and(list.toArray(new Predicate[0]));
            }
        };
        Pageable pageable = PageRequest.of(pageDomain.getPageNo(),
                Optional.ofNullable(pageDomain.getPageSize()).orElse(PageDomain.DEFAULT_PAGE_SIZE),
                Sort.Direction.DESC,
                Optional.ofNullable(pageDomain.getOrderByColumn()).orElse("createTime"));
        Page<MetaUserFavoriteAddress> page = metaUserFavoriteAddressDao.findAll(example, pageable);
        return page;
    }

    @Override
    public List<MetaUserFavoriteAddress> findTypeList(long userId,String type) {
        String[] types;
        if(StringUtils.isNotEmpty(type)){
            types=new String[]{type};
        }else{
            types=new String[]{"", ""};
        }

        return  metaUserFavoriteAddressDao.findList( userId, types);
    }
}
