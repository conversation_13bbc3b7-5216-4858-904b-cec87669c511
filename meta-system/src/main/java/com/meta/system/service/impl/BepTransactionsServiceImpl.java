package com.meta.system.service.impl;


import com.meta.common.utils.StringUtils;
import com.meta.system.dao.BepTransactionsDao;
import com.meta.system.domain.app.BepTransactions;
import com.meta.system.dto.RechargeDto;
import com.meta.system.service.BepTransactionsService;
import com.meta.system.vo.CollectRoportVo;
import com.meta.system.vo.RechargeDataVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
public class BepTransactionsServiceImpl implements BepTransactionsService {
    @Autowired
    BepTransactionsDao bepTransactionsDao;

    @Override
    public BepTransactions findTime(String walletAddress) {
        return bepTransactionsDao.findTime(walletAddress);
    }

    @Override
    public List<String> getList(String walletAddress, int timestamp) {
        return bepTransactionsDao.getList( walletAddress,timestamp) ;
    }

    @Override
    public List<String> getList(String walletAddress) {
        return bepTransactionsDao.getList(walletAddress) ;
    }

    @Override
    public Page<CollectRoportVo> getReportPage(String startDate, String endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        long startTimes=0;
        long endTimes=0;
        try {
            // 创建一个Calendar对象
            Calendar calendar = Calendar.getInstance();
            if(StringUtils.isNotEmpty(startDate)){
                // 解析字符串日期为Date对象
                Date date = sdf.parse(startDate);
                // 设置日期为解析得到的Date对象
                calendar.setTime(date);
                // 获取时间戳
                startTimes = calendar.getTimeInMillis()/1000;
            }

            if(StringUtils.isNotEmpty(endDate)){
                // 解析字符串日期为Date对象
                Date date = sdf.parse(endDate);
                // 设置日期为解析得到的Date对象
                calendar.setTime(date);
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                // 获取时间戳
                endTimes = calendar.getTimeInMillis()/1000;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }


        return bepTransactionsDao.getReportPage(startTimes, endTimes);
    }
    @Override
    public List<BepTransactions> findByTxid(String txid) {
        return bepTransactionsDao.findByTxid(txid);
    }

    @Override
    public void save(BepTransactions bepTransactions) {
        bepTransactionsDao.save(bepTransactions);
    }

    @Override
    public void updateBepTransactions(BepTransactions bepTransactions) {
        bepTransactionsDao.updateBepTransactions(bepTransactions.getIsSync(), bepTransactions.getTxid());
    }

    @Override
    public Page<RechargeDataVo> page(RechargeDto dto) {
        return bepTransactionsDao.page( dto);
    }

    @Override
    public BepTransactions findId(Long id) {
        Optional<BepTransactions> op = bepTransactionsDao.findById(id);
        if(op.isPresent())
            return op.get();
        return null;
    }
}
