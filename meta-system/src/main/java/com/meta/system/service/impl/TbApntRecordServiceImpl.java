package com.meta.system.service.impl;

import com.meta.system.dao.TbApntRecordDao;
import com.meta.system.domain.apnt.TbApntRecord;
import com.meta.system.dto.TbApntRecordVo;
import com.meta.system.service.TbApntRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

/**
 * 用户积分记录Service业务层处理
 *
 */
@Service
public class TbApntRecordServiceImpl implements TbApntRecordService {

    @Autowired
    private TbApntRecordDao tbApntRecordDao;


    @Override
    public TbApntRecord save(TbApntRecord tbApntRecord) {
        return tbApntRecordDao.save(tbApntRecord);
    }

    @Override
    public Page<TbApntRecordVo> selectList(String type, String cstName) {
        return tbApntRecordDao.selectList( type,cstName);
    }
    @Override
    public Page<TbApntRecordVo> userIntegralList(String userId) {
        return tbApntRecordDao.userIntegralList( userId);
    }

}
