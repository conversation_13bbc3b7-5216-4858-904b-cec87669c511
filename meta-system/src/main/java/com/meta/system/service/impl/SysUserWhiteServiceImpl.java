package com.meta.system.service.impl;

import com.meta.common.annotation.DataScope;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableSupport;
import com.meta.common.utils.SecurityUtils;
import com.meta.system.dao.app.SysUserWhiteDao;
import com.meta.system.domain.app.SysUserWhite;
import com.meta.system.service.SysUserWhiteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/05/11/11:37
 */
@Service
public class SysUserWhiteServiceImpl implements SysUserWhiteService {

    @Autowired
    private SysUserWhiteDao sysUserWhiteDao;

    @Override
    public SysUserWhite findByUserId(long userId) {
        return sysUserWhiteDao.findByuserId(userId);
    }

    @Override
    public Page<SysUserWhite> selectUserWhiteList(SysUserWhite sysUserWhite) {
        return sysUserWhiteDao.selectUserWhiteList(sysUserWhite);
    }

    /**
     * 新增白名单
     */
    @Override
    public AjaxResult addUserWhiteList(SysUserWhite sysUserWhite) {
        if (sysUserWhite.getUserList().isEmpty()){
            return AjaxResult.error("用户列表不能为空");
        }
        List<SysUserWhite> userList = new ArrayList<>();
        for (BigInteger userId : sysUserWhite.getUserList()) {
            SysUserWhite userWhite = new SysUserWhite();
            Date date = new Date();
            userWhite.setUserId(userId);
            userWhite.setCreateBy(SecurityUtils.getUsername());
            userWhite.setCreateTime(date);
            userWhite.setUpdateBy(SecurityUtils.getUsername());
            userWhite.setUpdateTime(date);
            userWhite.setDelFlag("0");
            userList.add(userWhite);
        }
        sysUserWhiteDao.saveAll(userList);
        return AjaxResult.success();
    }

    /**
     * 删除白名单
     * @param sysUserWhite
     * @return
     */
    @Override
    public AjaxResult delUserWhiteList(SysUserWhite sysUserWhite) {
        if (sysUserWhite.getUserList().isEmpty()){
            return AjaxResult.error("用户列表不能为空");
        }
        List<SysUserWhite> userList = new ArrayList<>();
        for (BigInteger userId : sysUserWhite.getUserList()) {
            SysUserWhite userWhite = new SysUserWhite();
            userWhite.setUserId(userId);
            userWhite.setDelFlag("2");
            userList.add(userWhite);
        }
        sysUserWhiteDao.saveAll(userList);
        return AjaxResult.success();
    }
}
