package com.meta.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.StringUtils;
import com.meta.system.config.MoonbankConfig;
import com.meta.system.dao.MetaMerchantAssetDao;
import com.meta.system.domain.moonbank.MetaMerchantAsset;
import com.meta.system.email.Mail;
import com.meta.system.email.SendEmail;
import com.meta.system.moonbank.models.ApiResponse;
import com.meta.system.moonbank.util.MoonbankEncryptUtil;
import com.meta.system.moonbank.util.MoonbankUtil;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.MetaMerchantAssetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/12/25/16:19
 */
@Service
public class MetaMerchantAssetServiceImpl implements MetaMerchantAssetService {

    @Autowired
    private MoonbankUtil moonbankUtil;

    @Autowired
    private MetaMerchantAssetDao metaMerchantAssetDao;

    @Autowired
    private SendEmail sendEmail;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private RedisTemplate redisTemplate;


    @Override
    public MetaMerchantAsset queryMerchantAsset() throws Exception {

        MetaMerchantAsset metaMerchantAsset = metaMerchantAssetDao.getNewData();
        ApiResponse<String> apiResponse = moonbankUtil.merchantAsset();
        if (apiResponse.isSuccess()) {
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
            System.out.println("accountRecharge encode result===>" + descStr);

            JSONObject j = JSON.parseObject(descStr);
            MetaMerchantAsset metaMerchantAssetNew = new MetaMerchantAsset();

            BigDecimal availableAmount = j.getBigDecimal("availableAmount");
            String frozenAmount = j.getString("frozenAmount");
            BigDecimal famount = null;
            if (frozenAmount != null && frozenAmount.equals("0E-18")) {
                famount = BigDecimal.ZERO;
            } else if (frozenAmount == null) {
                famount = BigDecimal.ZERO;
            } else {
                famount = new BigDecimal(frozenAmount);
            }
            String currency = j.getString("currency");
            if (metaMerchantAsset != null) {
                if (currency.equals(metaMerchantAsset.getCurrency()) && availableAmount.compareTo(metaMerchantAsset.getAvailableAmount()) == 0 && famount.compareTo(metaMerchantAsset.getFrozenAmount()) == 0) {
                    return metaMerchantAsset;
                }
            }

            metaMerchantAssetNew.setAvailableAmount(availableAmount);
            metaMerchantAssetNew.setFrozenAmount(famount);
            metaMerchantAssetNew.setCurrency(currency);
            metaMerchantAssetNew.setCreateTime(new Date());
            metaMerchantAssetDao.save(metaMerchantAssetNew);
            return metaMerchantAssetNew;
        } else {
            throw new Exception("moonbank接口请求错误");
        }

//        else {
//            return metaMerchantAsset;
//        }

    }

    @Override
    public void queryMerchantTask() {
        MetaMerchantAsset metaMerchantAsset = metaMerchantAssetDao.getNewData();
        ApiResponse<String> apiResponse = moonbankUtil.merchantAsset();
        if (apiResponse.isSuccess()) {
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
            System.out.println("accountRecharge encode result===>" + descStr);

            JSONObject j = JSON.parseObject(descStr);
            MetaMerchantAsset metaMerchantAssetNew = new MetaMerchantAsset();

            BigDecimal availableAmount = j.getBigDecimal("availableAmount");
            //判断金额不足的时候发送邮件
            String remindAmount = configService.selectConfigByKey("moonbank_merchantAsset_remind");
            if (availableAmount != null && availableAmount.compareTo(new BigDecimal(remindAmount)) < 0) {
                //一天发送一次
                String sendRemindMailKey = "sendMetaMerchantAssetRemindMail";
                if (!redisTemplate.hasKey(sendRemindMailKey)) {
                    redisTemplate.opsForValue().set(sendRemindMailKey, "1", 24, TimeUnit.HOURS);
                    //发送邮件
                    String auditEmail = configService.selectConfigByKey("audit_email");

                    if (StringUtils.isNotEmpty(auditEmail)) {
                        if (auditEmail.contains(",")) {
                            String[] result = auditEmail.split(",");
                            for (String address : result) {
                                Mail mail = new Mail();
                                mail.setEmail(address);
                                mail.setContent("kazepay在moonbank的银行资金池不足" + remindAmount+"，当前为"+availableAmount);
                                mail.setSubject("银行资金池不足");
                                sendEmail.sendCheckEmail(mail);

                            }
                        } else {
                            Mail mail = new Mail();
                            mail.setEmail(auditEmail);
                            mail.setContent("kazepay在moonbank的银行资金池不足" + remindAmount+"，当前为"+availableAmount);
                            mail.setSubject("银行资金池不足");
                            sendEmail.sendCheckEmail(mail);
                        }

                    }
                }
            }


            String frozenAmount = j.getString("frozenAmount");
            BigDecimal famount = null;
            if (frozenAmount != null && frozenAmount.equals("0E-18")) {
                famount = BigDecimal.ZERO;
            } else if (frozenAmount == null) {
                famount = BigDecimal.ZERO;
            } else {
                famount = new BigDecimal(frozenAmount);
            }
            String currency = j.getString("currency");
            if (metaMerchantAsset != null) {
                if (!(currency.equals(metaMerchantAsset.getCurrency()) && availableAmount.compareTo(metaMerchantAsset.getAvailableAmount()) == 0 && famount.compareTo(metaMerchantAsset.getFrozenAmount()) == 0)) {
                    metaMerchantAssetNew.setAvailableAmount(availableAmount);
                    metaMerchantAssetNew.setFrozenAmount(famount);
                    metaMerchantAssetNew.setCurrency(currency);
                    metaMerchantAssetNew.setCreateTime(new Date());
                    metaMerchantAssetDao.save(metaMerchantAssetNew);
                }
            }


        }
    }

    @Override
    public JSONArray queryRechargeInfo() {
        JSONArray jsonArray=new JSONArray();
//        String descStr="[{\"addressInfo\":[{\"address\":\"TBKyL9PYXcERkuWaUkpPpt3HMYSKkutrhn\",\"name\":\"TRC20\"}],\"confirmBlockCount\":10,\"image\":\"https://www.asinx.io/static-res/currency/usdt.png\",\"rechargeMinLimit\":1.000000000000000000,\"subAccountId\":31136,\"symbol\":\"USDT\"}]";
//         jsonArray = JSON.parseArray(descStr);
        ApiResponse<String> apiResponse = moonbankUtil.merchantRechargeinfo();
        if (apiResponse.isSuccess()) {
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
            System.out.println("merchantRechargeinfo encode result===>" + descStr);

              jsonArray = JSON.parseArray(descStr);
        }

        return jsonArray;
    }
}
