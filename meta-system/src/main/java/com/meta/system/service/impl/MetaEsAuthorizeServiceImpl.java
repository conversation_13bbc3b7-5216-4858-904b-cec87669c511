package com.meta.system.service.impl;



import com.meta.system.dao.MetaEsAuthorizeDao;
import com.meta.system.domain.es.MetaEsAuthorize;
import com.meta.system.service.MetaEsAuthorizeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(readOnly = true)
@Service
public class MetaEsAuthorizeServiceImpl implements MetaEsAuthorizeService {
    @Autowired
    private MetaEsAuthorizeDao esAuthorizeDao;


    @Override
    public void save(MetaEsAuthorize au) {
        esAuthorizeDao.save(au);
    }

    @Override
    public List<MetaEsAuthorize> findBySysId(String sysId) {
        return esAuthorizeDao.findBySysId(sysId);
    }

    @Override
    public MetaEsAuthorize findBySysIdAndApiCode(String sysId, String apiCode) {
        return esAuthorizeDao.findBySysIdAndApiCode(sysId,apiCode);
    }
}
