package com.meta.system.service;

import com.meta.common.core.domain.AjaxResult;
import com.meta.system.domain.app.node.UserNodeInfo;
import com.meta.system.vo.PartnerNodeInfoVo;
import com.meta.system.vo.UserInfoVo;
import com.meta.system.vo.UserNodeInfoVo;
import com.meta.system.vo.UserTeamCommissionVo;
import org.springframework.data.domain.Page;

import java.util.List;

public interface UserNodeInfoService {

    /**
     * 判断是否可以新增个人节点配置
     * @param userNodeInfo 配置信息
     * @return
     */
    AjaxResult checkNodeLevel(UserNodeInfo userNodeInfo);

    /**
     * 保存数据
     * @param userNodeInfo 配置信息
     */
    void save(UserNodeInfo userNodeInfo);

    /**
     * 跟据用户id获取节点返佣信息
     * @param userId
     * @return
     */
    UserNodeInfo findByUserId(Long userId);

    /**
     * 获取节点自定义返佣的分页数据
     *
     * @param userId 合伙人用户id
     * @param email
     * @return 分页数据
     */
    Page<UserNodeInfoVo> getChildNodeInfos(Long userId, String email);

    /**
     * 查询未自定义返佣的用户数据
     *
     * @param userId 合伙人id
     * @param email
     * @return 分页数据
     */
    Page<UserInfoVo> getNotCustomChildUsers(Long userId, String email);


    /**
     * 费率范围限制
     */
    AjaxResult rateLimit(UserNodeInfo userNodeInfo);

    /**
     * 获取所有的LV4的username
     * @param userId 用户id
     * @return 用户姓名列表
     */
    List<String> getNotCustomChildUsername(Long userId);

    List<UserInfoVo> getAllChildNodeInfos(Long userId, String email);

    Page<PartnerNodeInfoVo> getChildInfos(Long userId, String uuid);
}
