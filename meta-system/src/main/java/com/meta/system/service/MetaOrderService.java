package com.meta.system.service;

import com.meta.system.domain.CreditCard;
import com.meta.system.domain.app.MetaOrder;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/05/12/13:40
 */
public interface MetaOrderService {

    /**
     *
     * 创建订单
     * @param type          类型
     * @param requestNo     流水号
     * @param cardId        卡id
     * @param status        状态
     * @param orderAmount   订单金额
     * @param orderCurrency 订单币种
     * @param userId        用户id
     * @param sysId         系统id
     * @param closeAmount   销卡的卡金额
     * @param closeCurrency 销卡的卡币种
     * @param createId      创建人
     * @param remark        备注
     * @param settlementType  结算的方式
     * @param openEmail 开卡邮箱
     * @param openMobileprefix  开卡手机号前缀
     * @param openMobilenumber 开卡手机号码
     * @return
     */
    MetaOrder insert(String type, String requestNo,
                     String cardId, String status,
                     BigDecimal orderAmount, String orderCurrency,
                     Long userId, String sysId,
                     BigDecimal closeAmount, String closeCurrency,
                     Long createId, String remark,String settlementType,
                     String openEmail, String openMobileprefix, String openMobilenumber
    );

    /**
     * 消费数据
     * @param id 数据id
     * @return
     */
    boolean deal(Long id);


    /**
     * 查询数据
     * @param id 数据id
     * @return
     */
    MetaOrder findById(Long id);

    /**
     * 通过类型和卡id查找数据
     *
     * @param type   类型
     * @param cardId 卡id
     * @return
     */
    MetaOrder findByCardId(String type, String cardId);

    /**
     * 通过流水号查询订单
     * @param userReqNo 订单
     * @return
     */
    MetaOrder findRequestNo(String userReqNo);

    void save(MetaOrder metaOrder);

}
