package com.meta.system.service.impl;

import com.meta.system.dao.TbApntUsableDao;
import com.meta.system.domain.apnt.TbApntUsable;
import com.meta.system.dto.TbApntUsableVo;
import com.meta.system.service.TbApntUsableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.List;

/**
 * 用户可用积分Service业务层处理
 */
@Service
public class TbApntUsableServiceImpl implements TbApntUsableService {

    @Autowired
    private TbApntUsableDao tbApntUsableDao;

    @Override
    public TbApntUsable save(TbApntUsable tbApntUsable) {
        return tbApntUsableDao.save(tbApntUsable);
    }

    @Override
    public TbApntUsable findByUserId(Long userId) {
        return tbApntUsableDao.findByUserId(userId);
    }

    @Override
    public void updateValue(Long id, BigInteger value) {
        tbApntUsableDao.updateValue(id, value);
    }

    @Override
    public BigInteger selectIntegralByCstId(Long userId) {
        return tbApntUsableDao.selectIntegralByCstId(userId);
    }

    @Override
    public List<TbApntUsable> selectIntegralList(Long userId) {
        return tbApntUsableDao.selectIntegralList(userId);
    }

    @Override
    public void updateTbApntUsable(Long id, BigInteger value, Integer deleted) {
        tbApntUsableDao.updateTbApntUsable(id, value, deleted);
    }

    @Override
    public Page<TbApntUsableVo> selectList(String cstName) {
        return  tbApntUsableDao.selectList(cstName);
    }


}
