package com.meta.system.service.impl;


import com.meta.common.constant.HttpStatus;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableDataInfo;
import com.meta.common.core.page.TableSupport;
import com.meta.common.enums.app.*;
import com.meta.common.exception.CustomException;
import com.meta.common.utils.DateUtils;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.StringUtils;
import com.meta.system.dao.SysUserDao;
import com.meta.system.dao.app.CoinBalanceDao;
import com.meta.system.dao.app.CoinTxnDtlDao;
import com.meta.system.domain.Wallet;
import com.meta.system.domain.app.CoinBalance;
import com.meta.system.domain.app.CoinTxnDtl;
import com.meta.system.domain.app.MetaCodeDeduction;
import com.meta.system.domain.log.TxnDtlCode;
import com.meta.system.dto.VccReqDto;
import com.meta.system.service.*;
import com.meta.system.vo.CoinOperationDto;
import com.meta.system.vo.CoinTxnDtlVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class CoinTxnDtlServiceImpl implements CoinTxnDtlService {
    @Autowired
    private CoinTxnDtlDao coinTxnDtlDao;

    @Autowired
    private CoinBalanceDao coinBalanceDao;


    @Autowired
    private MetaCodeDeductionService metaCodeDeductionService;

    @Autowired
    private WalletService walletService;

    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private TxnService txnService;

    @Autowired
    private ISysDictDataService iSysDictDataService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private SysUserDao userDao;

    @Override
    @Transactional
    public void save(CoinTxnDtl dtl) {
        coinTxnDtlDao.save(dtl);
    }

    @Override
    public CoinTxnDtl findByReceiptNo(String receiptNo) {
        return coinTxnDtlDao.findByReceiptNo(receiptNo);
    }

    /**
     * 获取充提币记录
     *
     * @param userId     用户id
     * @param recordType
     * @param coinNet    充值链
     * @param txnCode    类型
     * @return
     */
    @Override
    public Page<CoinTxnDtlVo> coinTxnRecord(Long userId, String recordType, String coinNet, String txnCode) {
        return coinTxnDtlDao.coinTxnRecord(userId, recordType, coinNet, txnCode);
    }

    @Override
    public Page<CoinTxnDtlVo> coinTxnRecord3(CoinTxnDtlVo vo) {
        return coinTxnDtlDao.coinTxnRecord3(vo);
    }

    /**
     * 获取充提币记录详情
     *
     * @param txnId
     * @return
     */
    @Override
    public CoinTxnDtlVo getCoinTxnDetail(Long txnId) {
        CoinTxnDtlVo coinTxnDtlVo = coinTxnDtlDao.selectById(txnId);
        if (StringUtils.isNotEmpty(coinTxnDtlVo.getTxnCode())) {
            String display = iSysDictDataService.getLabel("coin_txn_type", coinTxnDtlVo.getTxnCode());
            coinTxnDtlVo.setTxnCodeDetail(display);
        }
        if (coinTxnDtlVo.getTxnCode().equals("b1010") || coinTxnDtlVo.getTxnCode().equals("s1010")){
            coinTxnDtlVo.setReceiptNo(null);
            coinTxnDtlVo.setFromAddress(null);
            coinTxnDtlVo.setToAddress(null);
            coinTxnDtlVo.setTxnFee(null);
            coinTxnDtlVo.setCoinNet(null);
        }
        return coinTxnDtlVo;
    }


    public CoinTxnDtl formatCoin(VccReqDto req, BigDecimal balance) {
        {
            CoinTxnDtl coin = new CoinTxnDtl();
            coin.setTxnId(req.getDtlUsdId());
            coin.setUserId(req.getUserId());
            coin.setRecordType(req.getRecordType());
            coin.setTxnCode(req.getTxnType() != null ? req.getTxnType().getValue() : null);
            if (StringUtils.isNotEmpty(req.getTxnDesc())) {
                coin.setTxnDesc(req.getTxnDesc());
            }
            coin.setReceiptNo(req.getTxnId());
            coin.setFromAddress(req.getFromUserId().toString());
            coin.setToAddress(req.getToUserId().toString());
            coin.setTxnCoin(req.getCoin());


            coin.setToCoin(req.getFromCoin());
            coin.setToAmount(req.getFromAmount());
            coin.setExchgRate(req.getExchgRate());
            coin.setTxnProduct(req.getTxnProduct());
            coin.setTxnTime(new Date());

            if (req.getTxnType() != null) {
                if (req.getTxnType().isSub()) {// 向自己账号转账 金额是正的
                    coin.setUserBalance(req.getUsdBalance() == null ? balance : req.getUsdBalance());
                    coin.setTxnAmount(BigDecimal.ZERO.subtract(req.getWalletAmount()));
                    coin.setTxnFee(BigDecimal.ZERO.subtract(req.getFee()));
                } else {// 自己账号转出 金额是负的(支出类型)
                    coin.setUserBalance(req.getUsdBalance() == null ? balance : req.getUsdBalance());
                    coin.setTxnAmount(req.getWalletAmount());
                    coin.setTxnFee(req.getFee());
                }
            }
            // 设置交易状态
            switch (req.getFreezeType()) {
                case NO:
                case FREE_FREEZE:
                    coin.setTxnStatus(1);
                    break;
                case FALLBACK:
                case NORMAL_FALLBACK:
                    coin.setTxnStatus(0);
                    break;
                case FREEZE:
                case NORMAL:
                    coin.setTxnStatus(2);
                    break;
                default:
                    break;
            }
            return coin;
        }
    }

    @Override
    public AjaxResult update(VccReqDto req, String coin) {
        //1.锁住行数据
        CoinBalance coinBalance = coinBalanceDao.findOneForUpdate(req.getUserId(), coin);
        AjaxResult res = AjaxResult.success();
        if (coinBalance != null) {
            //2.处理账户:
            if (req.getWalletAmount() != null && req.getWalletAmount().compareTo(BigDecimal.ZERO) != 0 || req.getFreezeType().equals(FreezeType.FREE_FREEZE)) {
                dealCoin(req.getWalletAmount(), req.getWalletAlgorithmType(), req.getFreezeType(), coinBalance);
                CoinTxnDtl dtlCoin = dealTxnForCoin(formatCoin(req, coinBalance.getCoinBalance()), req.getFreezeType(), req.getWalletAlgorithmType());
                res.put("dtlCoin", dtlCoin);
            }
            coinBalanceDao.updateCoinBalance(coinBalance.getUserId(), coinBalance.getCoinCode(), coinBalance.getCoinBalance());
        }
        res.put("coinbalance", coinBalance);
        return res;
    }

    /**
     * 销卡
     *
     * @param req
     * @param coin
     * @return
     */
    @Override
    public AjaxResult updateCloseCard(VccReqDto req, String coin) {
        //1.锁住行数据
        CoinBalance coinBalance = coinBalanceDao.findOneForUpdate(req.getUserId(), coin);
        AjaxResult res = AjaxResult.success();
        if (coinBalance != null) {
            //2.处理账户:
            if (req.getWalletAmount() != null) {
                dealCoinCloseCard(req.getWalletAmount(), req.getWalletAlgorithmType(), req.getFreezeType(), coinBalance);
                CoinTxnDtl dtlCoin = dealTxnForCoin(formatCoin(req, coinBalance.getCoinBalance()), req.getFreezeType(), req.getWalletAlgorithmType());
                res.put("dtlCoin", dtlCoin);
                coinBalanceDao.updateCoinBalance(coinBalance.getUserId(), coinBalance.getCoinCode(), coinBalance.getCoinBalance());
            }

        }
        res.put("coinbalance", coinBalance);
        return res;
    }

    @Override
    public AjaxResult update2(VccReqDto req, String coin) {
        //1.锁住行数据
        CoinBalance coinBalance = coinBalanceDao.findOneForUpdate(req.getUserId(), coin);
        AjaxResult res = AjaxResult.success();
        if (coinBalance != null) {
            //2.处理账户:
            if (req.getWalletAmount() != null || req.getFreezeType().equals(FreezeType.FREE_FREEZE)) {
                dealCoin(req.getWalletAmount(), req.getWalletAlgorithmType(), req.getFreezeType(), coinBalance);
                CoinTxnDtl dtlCoin = dealTxnForCoin(formatCoin(req, coinBalance.getCoinBalance()), req.getFreezeType(), req.getWalletAlgorithmType());
                res.put("dtlCoin", dtlCoin);
            }
            coinBalanceDao.updateCoinBalance(coinBalance.getUserId(), coinBalance.getCoinCode(), coinBalance.getCoinBalance());
        }
        res.put("coinbalance", coinBalance);
        return res;
    }

    @Override
    public AjaxResult updateLogisticsFee(VccReqDto req, String coin) {
        CoinBalance coinBalance = coinBalanceDao.findOneForUpdate(req.getUserId(), coin);
        AjaxResult res = AjaxResult.success();
        if (coinBalance != null) {
            //2.处理账户:
            if (req.getWalletAmount() != null && req.getWalletAmount().compareTo(BigDecimal.ZERO) != 0 || req.getFreezeType().equals(FreezeType.FREE_FREEZE)) {
                dealCoin(req.getWalletAmount(), req.getWalletAlgorithmType(), req.getFreezeType(), coinBalance);
                CoinTxnDtl dtlCoin = dealTxnForCoin(formatCoin(req, coinBalance.getCoinBalance()), req.getFreezeType(), req.getWalletAlgorithmType());
                res.put("dtlLogisticsCoin", dtlCoin);
            }
            coinBalanceDao.updateCoinBalance(coinBalance.getUserId(), coinBalance.getCoinCode(), coinBalance.getCoinBalance());
        }
        res.put("coinbalance", coinBalance);
        return res;


    }

    @Override
    public AjaxResult updateDtlStatue(CoinTxnDtl dtl, FreezeType freezeType, AlgorithmType walletAlgorithmType) {
        //1.锁住行数据
        CoinBalance coinBalance = coinBalanceDao.findOneForUpdate(dtl.getUserId(), dtl.getTxnCoin());
        AjaxResult res = AjaxResult.success();
        if (coinBalance != null) {
            //2.处理账户:
            if (dtl.getTxnAmount() != null && dtl.getTxnAmount().compareTo(BigDecimal.ZERO) != 0 || freezeType.equals(FreezeType.FREE_FREEZE)) {
                dealCoin(dtl.getTxnAmount(), walletAlgorithmType, freezeType, coinBalance);
                CoinTxnDtl dtlCoin = dealTxnForCoin(dtl, freezeType, walletAlgorithmType);
                res.put("dtlCoin", dtlCoin);
            }
            coinBalanceDao.updateCoinBalance(coinBalance.getUserId(), coinBalance.getCoinCode(), coinBalance.getCoinBalance());
        }
        res.put("coinbalance", coinBalance);
        return res;
    }

    /**
     * 处理美元账户
     */

    public void dealCoin(BigDecimal walletAmount, AlgorithmType type, FreezeType freezeType, CoinBalance c) {
        if (walletAmount.compareTo(BigDecimal.ZERO) != 0) {
            if (type.equals(AlgorithmType.SUB)) {
                Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
                if (c.getCoinBalance().compareTo(walletAmount) < 0 && c.getUserId() != accountId) {
                    //账户余额不足
                    throw new CustomException(MessageUtils.message("wallet.account.insufficient.coinbalance", c.getCoinCode()));
                }
                //处理冻结金额
                switch (freezeType) {
                    case FREEZE:
                        BigDecimal fr = c.getFreezeBalance().add(walletAmount.abs());
                        c.setFreezeBalance(fr);
                        c.setCoinBalance(c.getCoinBalance().subtract(walletAmount.abs()));
                        break;
                    case FREE_FREEZE:
                        BigDecimal free = c.getFreezeBalance().subtract(walletAmount.abs());
                        c.setFreezeBalance(free);
                        break;
                    case FALLBACK:
                        // 回退的的时候，冻结的金额扣去； @{AlgorithmType.ADD} 的时候才加，其他时候不加
                        c.setFreezeBalance(c.getFreezeBalance().subtract(walletAmount.abs()));
                        break;
                    case NO:
                        BigDecimal balance = c.getCoinBalance().subtract(walletAmount.abs());
                        c.setCoinBalance(balance);
                        break;
                    default:
                        break;
                }

            } else if (type.equals(AlgorithmType.ADD)) {
                //2.加法的时候：加上余额
                //处理冻结金额
                switch (freezeType) {
                    case FREEZE:
                        BigDecimal fr = c.getFreezeBalance().add(walletAmount.abs());
                        c.setFreezeBalance(fr);
                        break;
                    case FREE_FREEZE:
                        BigDecimal free = c.getFreezeBalance().subtract(walletAmount.abs());
                        c.setFreezeBalance(free);
                        c.setCoinBalance(c.getCoinBalance().add(walletAmount.abs()));
                        break;
                    case FALLBACK:
                        // 回退的的时候，冻结的金额扣去； @{AlgorithmType.ADD} 的时候才加，其他时候不加
                        c.setFreezeBalance(c.getFreezeBalance().subtract(walletAmount.abs()));
                        c.setCoinBalance(c.getCoinBalance().add(walletAmount.abs()));
                        break;
                    case NO:
                        BigDecimal balance = c.getCoinBalance().add(walletAmount.abs());
                        c.setCoinBalance(balance);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    /**
     * 销卡处理
     */

    public void dealCoinCloseCard(BigDecimal walletAmount, AlgorithmType type, FreezeType freezeType, CoinBalance c) {
        if (walletAmount.compareTo(BigDecimal.ZERO) != 0) {
            if (type.equals(AlgorithmType.SUB)) {
                //处理冻结金额
                switch (freezeType) {
                    case FREEZE:
                        BigDecimal fr = c.getFreezeBalance().add(walletAmount.abs());
                        c.setFreezeBalance(fr);
                        c.setCoinBalance(c.getCoinBalance().subtract(walletAmount.abs()));
                        break;
                    case FREE_FREEZE:
                        BigDecimal free = c.getFreezeBalance().subtract(walletAmount.abs());
                        c.setFreezeBalance(free);
                        break;
                    case FALLBACK:
                        // 回退的的时候，冻结的金额扣去； @{AlgorithmType.ADD} 的时候才加，其他时候不加
                        c.setFreezeBalance(c.getFreezeBalance().subtract(walletAmount.abs()));
                        break;
                    case NO:
                        BigDecimal balance = c.getCoinBalance().subtract(walletAmount.abs());
                        c.setCoinBalance(balance);
                        break;
                    default:
                        break;
                }

            } else if (type.equals(AlgorithmType.ADD)) {
                //2.加法的时候：加上余额
                //处理冻结金额
                switch (freezeType) {
                    case FREEZE:
                        BigDecimal fr = c.getFreezeBalance().add(walletAmount.abs());
                        c.setFreezeBalance(fr);
                        break;
                    case FREE_FREEZE:
                        BigDecimal free = c.getFreezeBalance().subtract(walletAmount.abs());
                        c.setFreezeBalance(free);
                        c.setCoinBalance(c.getCoinBalance().add(walletAmount.abs()));
                        break;
                    case FALLBACK:
                        // 回退的的时候，冻结的金额扣去； @{AlgorithmType.ADD} 的时候才加，其他时候不加
                        c.setFreezeBalance(c.getFreezeBalance().subtract(walletAmount.abs()));
                        c.setCoinBalance(c.getCoinBalance().add(walletAmount.abs()));
                        break;
                    case NO:
                        BigDecimal balance = c.getCoinBalance().add(walletAmount.abs());
                        c.setCoinBalance(balance);
                        break;
                    default:
                        break;
                }
            }
        }
    }


    /**
     * 币种资源划转
     *
     * @param source 源
     * @param target 目标
     * @param coin   币种
     * @return
     */
    @Override
    public AjaxResult transferCoin(VccReqDto source, VccReqDto target, String coin) {
        AjaxResult res = update(source, coin);// 付款
        if (res.get("code").equals(200)) {
            Object or = res.get("dtlCoin");
            res = update(target, coin);// 收款
            if (or != null) {
                res.put("resultData", or);
            }

            return res;
        } else {
            return res;
        }
    }


    public CoinTxnDtl dealTxnForCoin(CoinTxnDtl coinTxnDtl, FreezeType freezeType, AlgorithmType algorithmType) {
        switch (freezeType) {
            case FREEZE:
            case NO:
            case NORMAL:
                // 添加交易日志
                if (coinTxnDtl.getTxnId() != null) {
                    CoinTxnDtl olCoinTxnDtl = coinTxnDtlDao.findByTxnId(coinTxnDtl.getTxnId());
                    if (olCoinTxnDtl.getTxnAmount() == null)
                        olCoinTxnDtl.setTxnAmount(coinTxnDtl.getTxnAmount());
                    olCoinTxnDtl.setTxnStatus(coinTxnDtl.getTxnStatus());
                    coinTxnDtl = coinTxnDtlDao.save(olCoinTxnDtl);
                } else {
                    coinTxnDtl.setTxnTime(new Date());
                    coinTxnDtl.setCreateTime(new Date());
                    coinTxnDtl = coinTxnDtlDao.save(coinTxnDtl);
                }

                break;
            case FREE_FREEZE:
                // 交易成功
                if (algorithmType.equals(AlgorithmType.ADD)) {
                    coinTxnDtlDao.updateTxnStatusAndUsdBalanceById(coinTxnDtl.getTxnId(), TxnStatus.SUCCESS.getValue(), coinTxnDtl.getTxnAmount().abs(), coinTxnDtl.getTxnProduct());
                } else {
                    coinTxnDtlDao.updateTxnStatusById(coinTxnDtl.getTxnId(), TxnStatus.SUCCESS.getValue());
                }
                break;
            case FALLBACK:
                // 交易失败[未处理]
                if (algorithmType.equals(AlgorithmType.ADD)) {
                    coinTxnDtlDao.updateTxnStatusAndUsdBalanceById(coinTxnDtl.getTxnId(), TxnStatus.FAIL.getValue(), coinTxnDtl.getTxnAmount().abs(), coinTxnDtl.getTxnProduct());
                } else {
                    coinTxnDtlDao.updateTxnStatusById(coinTxnDtl.getTxnId(), TxnStatus.FAIL.getValue());
                }
                break;
            case NORMAL_FALLBACK:
                coinTxnDtlDao.updateTxnStatusById(coinTxnDtl.getTxnId(), TxnStatus.FAIL.getValue());
                break;
            default:
                break;
        }
        return coinTxnDtl;
    }

    @Override
    public List<CoinTxnDtl> findAllByCardIdAndStatusAndTxnTypeForCoin(String cardId, String stauts, String[] txnTypes) {
        return coinTxnDtlDao.findAllByCardIdAndStatusAndTxnType(cardId, stauts, txnTypes);
    }

    @Override
    public void updateTxnProduct(String cardId, String userBankId) {
        coinTxnDtlDao.updateTxnProduct(cardId, userBankId);
    }

    @Override
    public Page<CoinTxnDtl> list(CoinTxnDtl coinTxnDtl) {
        Example<CoinTxnDtl> example = new Example<CoinTxnDtl>() {
            @Override
            public CoinTxnDtl getProbe() {
                return coinTxnDtl;
            }

            @Override
            public ExampleMatcher getMatcher() {
                ExampleMatcher matcher = ExampleMatcher.matching()
                        .withMatcher("userId", ExampleMatcher.GenericPropertyMatchers.exact())
                        .withMatcher("txnCode", ExampleMatcher.GenericPropertyMatchers.exact())
                        .withMatcher("fromAddress", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                        .withMatcher("toAddress", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                        .withMatcher("txnCoin", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                        .withMatcher("txnStatus", ExampleMatcher.GenericPropertyMatchers.exact())
                        .withIgnoreNullValues();
                return matcher;
            }
        };

        PageDomain pageReq = TableSupport.buildPageRequest();
        Sort sort = Sort.by(Sort.Direction.DESC, "txnTime");
        Pageable pageable = PageRequest.of(pageReq.getPageNo(), pageReq.getPageSize(), sort);
        return coinTxnDtlDao.findAll(example, pageable);
    }

    @Override
    public void withdraw(CoinOperationDto withdraw) {
        CoinTxnDtl coinTxnDtl = coinTxnDtlDao.findById(withdraw.getId()).orElse(null);
        if (coinTxnDtl != null && CoinOperationStatus.PADDING.getValue().equals(coinTxnDtl.getTxnStatus().toString())) {
            BigDecimal num = coinTxnDtl.getTxnAmount().add(coinTxnDtl.getTxnFee());
            coinTxnDtl.setTxnStatus(Integer.valueOf(withdraw.getTxnStatus()));
            coinTxnDtl.setReceiptNo(withdraw.getReceiptNo());
            coinTxnDtl.setTxnTime(DateUtils.getNowDateOld());
            if (StringUtils.isNotBlank(withdraw.getTxnDesc())) {
                coinTxnDtl.setTxnDesc(withdraw.getTxnDesc());
            }
            if (CoinOperationStatus.COMPLETED.getValue().equals(withdraw.getTxnStatus())) { // 完成  将冻结的钱包数量 扣去提现的数量
                CoinBalance balance = coinBalanceDao.findByUserIdAndCoinCode(coinTxnDtl.getUserId(), coinTxnDtl.getTxnCoin());
                balance.setFreezeBalance(balance.getFreezeBalance().subtract(num)); // 原本冻结的余额 减去本次提现数量
                coinBalanceDao.save(balance);

                //判断是否有gate用户提币是否扣钱卡码费用
                MetaCodeDeduction metaCodeDeduction = metaCodeDeductionService.findByTxnId(coinTxnDtl.getTxnId());
                if (metaCodeDeduction != null) {
                    metaCodeDeductionService.updateStatue("1", metaCodeDeduction.getId());
                    //新增撤销卡码的流水
                    Wallet w = walletService.findByUserId(metaCodeDeduction.getUserId());
                    //流水
                    Long accountId = Long.valueOf(sysConfigService.selectConfigByKey("company_account_id"));
                    TxnDtlCode code = new TxnDtlCode();
                    code.setUserId(metaCodeDeduction.getUserId());
                    code.setTxnType(TxnType.SYSTEM_REVOKED.getValue());
                    code.setTxnDesc(TxnType.SYSTEM_REVOKED.getName());
                    code.setFromUserId(metaCodeDeduction.getUserId());
                    code.setToUserId(accountId);

                    code.setSilverCodeBalance(w.getSilverActiveCodeNum());
                    code.setGoldenCodeBalance(w.getGoldenActiveCodeNum());
                    code.setGoldenBlackCodeBalance(w.getGoldenblackActiveCodeNum());
                    code.setWhiteCodeBalance(w.getWhiteActiveCodeNum());
                    code.setTxnStatus(TxnStatus.SUCCESS.getValue());

                    if (balance.getGateRecharge().compareTo(new BigDecimal("500")) == 0) {
                        //扣除银卡
                        code.setTxnNum(1);
                        code.setCodeType("01");
                    } else if (balance.getGateRecharge().compareTo(new BigDecimal("2000")) == 0) {
                        //扣除金卡
                        code.setTxnNum(1);
                        code.setCodeType("02");
                    } else if (balance.getGateRecharge().compareTo(new BigDecimal("5000")) == 0) {
                        code.setTxnNum(2);
                        code.setCodeType("02");
                    }
                    txnService.dealTxnForCode(code);
                }


            } else if (CoinOperationStatus.REJECT.getValue().equals(withdraw.getTxnStatus())) { // 拒绝  将冻结的 钱包数量返还至用户钱包
                CoinBalance balance = coinBalanceDao.findByUserIdAndCoinCode(coinTxnDtl.getUserId(), coinTxnDtl.getTxnCoin());
                balance.setFreezeBalance(balance.getFreezeBalance().subtract(num)); // 原本冻结的余额 减去本次提现数量
                balance.setCoinBalance(balance.getCoinBalance().add(num));  // 原本的余额加上本次提现数量
                coinBalanceDao.save(balance);

                //判断是否有gate用户提币是否扣钱卡码费用
                MetaCodeDeduction metaCodeDeduction = metaCodeDeductionService.findByTxnId(coinTxnDtl.getTxnId());
                if (metaCodeDeduction != null) {
                    metaCodeDeductionService.updateStatue("2", metaCodeDeduction.getId());
                    Wallet w = walletService.findByUserId(metaCodeDeduction.getUserId());
                    if ("code".equals(metaCodeDeduction.getType())) {

                        if ("01".equals(metaCodeDeduction.getCodeType())) {
                            w.setSilverActiveCodeNum(w.getSilverActiveCodeNum() + 1);
                        } else if ("02".equals(metaCodeDeduction.getCodeType())) {
                            if (balance.getGateRecharge().compareTo(new BigDecimal("5000")) == 0) {
                                w.setGoldenActiveCodeNum(w.getGoldenActiveCodeNum() + 2);

                            } else {
                                w.setGoldenActiveCodeNum(w.getGoldenActiveCodeNum() + 1);
                            }

                        }

                    } else {
                        if (balance.getGateRecharge().compareTo(new BigDecimal("5000")) == 0) {
                            if (metaCodeDeduction.getNum() == 1) {
                                w.setGoldenActiveCodeNum(w.getGoldenActiveCodeNum() + 1);
                            }
                        }
                    }
                    walletService.updateCode(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                            , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                            , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                            , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());
                }

            }
            coinTxnDtlDao.save(coinTxnDtl);
        } else {
            String msg = "";
            if (coinTxnDtl == null) {
                msg = "不存在txn_id为" + withdraw.getId() + "的提现记录";
            } else if (!CoinOperationStatus.PADDING.getValue().equals(coinTxnDtl.getTxnStatus().toString())) {
                msg = "不可重复操作该提现记录";
            }

            throw new RuntimeException("提现失败:" + msg);
        }

    }

    @Override
    public Page<CoinTxnDtlVo> page(CoinTxnDtl coinTxnDtl) {
        Page<CoinTxnDtlVo> rawPage = coinTxnDtlDao.page(coinTxnDtl);
        List<CoinTxnDtlVo> content = rawPage.getContent();

        List<SysUser> list = userDao.findAll();

        Map<String, String> userIdToEmail = list.stream()
                .collect(Collectors.toMap(u -> u.getUserId().toString(), SysUser::getEmail));
        for (CoinTxnDtlVo vo : content) {
            if (userIdToEmail.containsKey(vo.getFromAddress())) {
                vo.setFromAddress(userIdToEmail.get(vo.getFromAddress()));
            }
            if (userIdToEmail.containsKey(vo.getToAddress())) {
                vo.setToAddress(userIdToEmail.get(vo.getToAddress()));
            }
        }

        return new PageImpl<>(content, rawPage.getPageable(), rawPage.getTotalElements());
    }

    /**
     * 响应请求分页数据
     */
    protected TableDataInfo getDataTable(Page<?> page) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(page.getContent());
        rspData.setTotal(page.getTotalElements());
        rspData.setSize(pageReq.getPageSize());
        rspData.setPages(page.getTotalPages());
        rspData.setCurrent(pageReq.getPageNo() + 1);
        return rspData;
    }

    @Override
    public CoinTxnDtlVo selectById(Long id) {
        return coinTxnDtlDao.selectById(id);
    }

    @Override
    public void recharge(CoinOperationDto recharge) {
        CoinTxnDtl coinTxnDtl = coinTxnDtlDao.findById(recharge.getId()).orElse(null);
        if (coinTxnDtl != null && CoinOperationStatus.PADDING.getValue().equals(coinTxnDtl.getTxnStatus().toString())) {
            coinTxnDtl.setTxnStatus(Integer.valueOf(recharge.getTxnStatus()));
            if (StringUtils.isNotBlank(recharge.getTxnDesc())) {
                coinTxnDtl.setTxnDesc(recharge.getTxnDesc());
            }
            if (CoinOperationStatus.COMPLETED.getValue().equals(recharge.getTxnStatus())) { // 完成  已完成状态时，需要将充值金额 入账到该用户对应数字货币的coin_balance可用余额中，并且扣减freeze_balance冻结金额；最后更新meta_coin_txn_dtl信息
                CoinBalance balance = coinBalanceDao.findByUserIdAndCoinCode(coinTxnDtl.getUserId(), coinTxnDtl.getTxnCoin());
                balance.setFreezeBalance(balance.getFreezeBalance().subtract(coinTxnDtl.getTxnAmount())); // 扣减freeze_balance冻结金额 (充值金额与手续费)
                balance.setCoinBalance(balance.getCoinBalance().add(coinTxnDtl.getTxnAmount())); // 入账到该用户对应数字货币的coin_balance可用余额中
                coinBalanceDao.save(balance);
            } else if (CoinOperationStatus.REJECT.getValue().equals(recharge.getTxnStatus())) { // 已拒绝状态时，更新meta_coin_txn_dtl信息 TODO 暂时不操作
//                CoinBalance balance = coinBalanceDao.findByUserIdAndCoinCode(coinTxnDtl.getUserId(), coinTxnDtl.getTxnCoin());
//                balance.setFreezeBalance(balance.getFreezeBalance().subtract(num)); // 原本冻结的余额 减去本次提现数量
//                coinBalanceDao.save(balance);
            }
            coinTxnDtlDao.save(coinTxnDtl);
        } else {
            String msg = "";
            if (coinTxnDtl == null) {
                msg = "不存在txn_id为" + recharge.getId() + "的充值记录";
            } else if (!CoinOperationStatus.PADDING.getValue().equals(coinTxnDtl.getTxnStatus().toString())) {
                msg = "不可重复操作该充值记录";
            }

            throw new RuntimeException("充值失败:" + msg);
        }

    }

    @Override
    public List<CoinTxnDtl> findAllByCardIdAndStatusAndTxnId(String cardId, String stauts, String txnId) {
        return coinTxnDtlDao.findAllByCardIdAndStatusAndTxnId(cardId, stauts, txnId);
    }

    @Override
    public List<CoinTxnDtl> findAllByCardIdAndStatusAndTxnId(String cardId, String stauts, String txnId, String[] txnTypes) {
        return coinTxnDtlDao.findAllByCardIdAndStatusAndTxnId(cardId, stauts, txnId, txnTypes);
    }
}
