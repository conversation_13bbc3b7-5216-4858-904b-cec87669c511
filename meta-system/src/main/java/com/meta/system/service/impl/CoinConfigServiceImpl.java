package com.meta.system.service.impl;

import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableSupport;
import com.meta.common.utils.DateUtils;
import com.meta.common.utils.SecurityUtils;
import com.meta.system.dao.CoinConfigDao;
import com.meta.system.domain.app.CoinConfig;
import com.meta.system.service.CoinConfigService;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CoinConfigServiceImpl implements CoinConfigService {
    @Resource
    private CoinConfigDao coinConfigDao;

    @Override
    public boolean existsBySymbol(String symbol, Integer id) {
        List<Integer> ids = coinConfigDao.findBySymbol(symbol);
        if (ids.isEmpty()){
            return false;
        }else if(ids.size() == 1 && ids.contains(id)){
            return false;
        }
        return true;
    }

    @Override
    public CoinConfig save(CoinConfig coinConfig) {
        return coinConfigDao.save(coinConfig);
    }

    @Override
    public Page<CoinConfig> list(CoinConfig coinConfig) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        Pageable pageable = PageRequest.of(pageReq.getPageNo(), pageReq.getPageSize(),Sort.by(Sort.Direction.ASC, "sortNo"));
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("symbol", ExampleMatcher.GenericPropertyMatchers.contains())
                .withMatcher("validFlag", ExampleMatcher.GenericPropertyMatchers.exact())
                .withIgnoreNullValues();
        Example<CoinConfig> example = Example.of(coinConfig, matcher);
        return coinConfigDao.findAll(example,pageable);
    }

    @Override
    public CoinConfig changeValidFlag(CoinConfig coinConfig) {
        CoinConfig config = coinConfigDao.findById(coinConfig.getCoinId()).orElse(null);
        if(config == null){
            return null;
        }
        config.setValidFlag(coinConfig.getValidFlag());
        config.setUpdateBy(SecurityUtils.getUsername());
        config.setUpdateTime(DateUtils.getNowDate());
        return coinConfigDao.save(config);
    }

    @Override
    public CoinConfig update(CoinConfig coinConfig) {
        CoinConfig config = coinConfigDao.findById(coinConfig.getCoinId()).orElse(null);
        if(config == null){
            return null;
        }
        config.update(coinConfig);
        return coinConfigDao.save(config);
    }
}
