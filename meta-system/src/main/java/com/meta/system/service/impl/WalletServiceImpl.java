package com.meta.system.service.impl;


import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.enums.TradeAuthType;
import com.meta.common.enums.app.*;
import com.meta.common.exception.CustomException;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.StringUtils;
import com.meta.system.constant.Constants;
import com.meta.system.constant.ExceptionInfo;
import com.meta.system.dao.SysUserDao;
import com.meta.system.dao.TxnDtlUSDDao;
import com.meta.system.dao.WalletDao;
import com.meta.system.dao.app.CoinBalanceDao;
import com.meta.system.dao.app.CoinTxnDtlDao;
import com.meta.system.domain.MetaUserInvites;
import com.meta.system.domain.Wallet;
import com.meta.system.domain.app.CoinBalance;
import com.meta.system.domain.app.CoinTxnDtl;
import com.meta.system.domain.log.TxnDtlCode;
import com.meta.system.domain.log.TxnDtlUSD;
import com.meta.system.dto.UsdExchangeOutDto;
import com.meta.system.dto.VccReqDto;
import com.meta.system.dto.node.CodeExchangeDto;
import com.meta.system.dto.node.CodeTransferDto;
import com.meta.system.dto.node.NodeTransferDto;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.MetaUserInvitesService;
import com.meta.system.service.TxnService;
import com.meta.system.service.WalletService;
import com.meta.system.vo.WalletVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Transactional(readOnly = true)
@Service
@Slf4j
public class WalletServiceImpl implements WalletService {
    @Autowired
    private WalletDao walletDao;

    @Autowired
    private SysUserDao userDao;


    @Autowired
    private ISysConfigService configService;

    @Autowired
    private TxnService txnService;

    @Autowired
    private CoinBalanceDao coinBalanceDao;

    @Autowired
    private CoinTxnDtlDao coinTxnDtlDao;

    @Autowired
    private MetaUserInvitesService metaUserInvitesService;
    @Autowired
    private MetaCouponInfoServiceImpl metaCouponInfoServiceImpl;

    @Autowired
    private TxnDtlUSDDao txnDtlUSDDao;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(Wallet w) {

        walletDao.save(w);
    }

    @Override
    public Wallet findByUserId(Long userId) {
        return walletDao.getOne(userId);
    }

    @Override
    public AjaxResult updateCou(VccReqDto req) {
        //1.锁住行数据
        Wallet w = walletDao.findOneForUpdate(req.getUserId());
        AjaxResult res = AjaxResult.success();
        if (w != null) {
            //2.处理美元账户:
            if ((req.getWalletAmount() != null && req.getWalletAmount().compareTo(BigDecimal.ZERO) >= 0) || req.getFreezeType().equals(FreezeType.FREE_FREEZE)) {
                dealUSD(req.getWalletAmount(), req.getWalletAlgorithmType(), req.getFreezeType(), w);
                TxnDtlUSD dtlUSD = txnService.dealTxnForUSD(TxnDtlUSD.formatUSD(req, w.getUsdBalance()), req.getFreezeType(), req.getWalletAlgorithmType());
                res.put("dtlUSD", dtlUSD);
            }

            if (req.getActiveCodeNum() != 0) {
                //3.激活码处理
                if (req.getCardLevel().equals(CardLevel.SILVER.getOrdinal())) {
                    if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.ADD)) {
                        w.setSilverActiveCodeNum(w.getSilverActiveCodeNum() + req.getActiveCodeNum());
                    } else if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.SUB)) {
                        if (w.getSilverActiveCodeNum() < req.getActiveCodeNum()) {
                            throw new CustomException(ExceptionInfo.SILVER_ACTIVITI_CODE_NUM_NO_ENOUGH);
                        }
                        w.setSilverActiveCodeNum(w.getSilverActiveCodeNum() - req.getActiveCodeNum());
                        if (req.isCodeUsed()) {
                            w.setSilverActiveCodeUsed(w.getSilverActiveCodeUsed() + req.getActiveCodeNum());
                        }
                    }
                } else if (req.getCardLevel().equals(CardLevel.GOLD.getOrdinal())) {
                    if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.ADD)) {
                        w.setGoldenActiveCodeNum(w.getGoldenActiveCodeNum() + req.getActiveCodeNum());
                    } else if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.SUB)) {
                        if (w.getGoldenActiveCodeNum() < req.getActiveCodeNum()) {
                            throw new CustomException(ExceptionInfo.GOLDEN_ACTIVITI_CODE_NUM_NO_ENOUGH);
                        }
                        w.setGoldenActiveCodeNum(w.getGoldenActiveCodeNum() - req.getActiveCodeNum());
                        if (req.isCodeUsed()) {
                            w.setGoldenActiveCodeUsed(w.getGoldenActiveCodeUsed() + req.getActiveCodeNum());
                        }
                    }
                } else if (req.getCardLevel().equals(CardLevel.BLACK_GOLD.getOrdinal())) {
                    if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.ADD)) {
                        w.setGoldenblackActiveCodeNum(w.getGoldenblackActiveCodeNum() + req.getActiveCodeNum());
                    } else if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.SUB)) {
                        if (w.getGoldenblackActiveCodeNum() < req.getActiveCodeNum()) {
                            throw new CustomException(ExceptionInfo.BLACKGOLDEN_ACTIVITI_CODE_NUM_NO_ENOUGH);
                        }
                        w.setGoldenblackActiveCodeNum(w.getGoldenblackActiveCodeNum() - req.getActiveCodeNum());
                        if (req.isCodeUsed()) {
                            w.setGoldenblackActiveCodeUsed(w.getGoldenblackActiveCodeUsed() + req.getActiveCodeNum());
                        }
                    }
                }

                // 激活码 交易明细
                TxnDtlCode dtlCode = txnService.dealTxnForCode(TxnDtlCode.formatCode(req, w));
                res.put("dtlCode", dtlCode);
            }

            walletDao.update(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                    , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                    , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                    , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());
        }
        res.put("wallet", w);
        return res;
    }

    @Override
    public AjaxResult updateComAccCou(VccReqDto req) {
        //1.锁住行数据
        // 公司账户id
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        req.setUserId(accountId);
        Wallet w = walletDao.findOneForUpdate(accountId);
        if (w != null && req.getWalletAmount().compareTo(BigDecimal.ZERO) >= 0) {
            AjaxResult res = AjaxResult.success();

            //2.减法的时候：扣减美元余额 + 增加冻结美元余额
            if (req.getFreezeType() != null && !FreezeType.NORMAL.equals(req.getFreezeType()) && !FreezeType.NORMAL_FALLBACK.equals(req.getFreezeType())) {
                dealUSD(req.getWalletAmount(), req.getWalletAlgorithmType(), req.getFreezeType(), w);
                walletDao.update(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                        , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                        , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                        , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());
            }
            res.put("wallet", w);
            TxnDtlUSD dtlUSD = txnService.dealTxnForUSD(TxnDtlUSD.formatUSD(req, w.getUsdBalance()), req.getFreezeType(), req.getWalletAlgorithmType());
            res.put("dtlUSD", dtlUSD);
            return res;
        }
        return AjaxResult.error();
    }

    /**
     * usd兑换 其他加密货币
     * @param usdExchangeOutDto
     * @return
     */
    @Override
    @Transactional
    public AjaxResult usdExchangeOut(UsdExchangeOutDto usdExchangeOutDto) {
        List<String> Currencies = Constants.kazepay_currency_gather;
        if (!Currencies.contains(usdExchangeOutDto.getCurrency())) return AjaxResult.error(MessageUtils.message("common.currency.not.supported", usdExchangeOutDto.getCurrency()));
        SysUser user = userDao.getOne(usdExchangeOutDto.getUserId());
        Wallet wallet = walletDao.findOneForUpdate(user.getUserId());
        CoinBalance coinBalance = coinBalanceDao.findOneForUpdate(user.getUserId(), usdExchangeOutDto.getCurrency());
        if (coinBalance == null) {
            return AjaxResult.error(MessageUtils.message("wallet.not.wallet", usdExchangeOutDto.getCurrency()));
        }
        if (wallet.getUsdBalance().compareTo(usdExchangeOutDto.getAmount()) < 0) {
            return AjaxResult.error(MessageUtils.message("wallet.account.insufficient.balance"));
        }

        TxnDtlUSD txnDtlUSD = new TxnDtlUSD();
        txnDtlUSD.setUserId(user.getUserId());
        txnDtlUSD.setTxnType("c1020");
        txnDtlUSD.setTxnAmount(usdExchangeOutDto.getAmount());
        txnDtlUSD.setTxnNum(0L);
        txnDtlUSD.setTxnFee(BigDecimal.ZERO);
        txnDtlUSD.setTxnTime(LocalDateTime.now());
        txnDtlUSD.setFromUserId(user.getUserId());
        txnDtlUSD.setToUserId(99L);
        txnDtlUSD.setTxnStatus('1');
        txnDtlUSD.setTxnDesc("兑换" + usdExchangeOutDto.getCurrency() + "资产");
        txnDtlUSD.setUsdBalance(wallet.getUsdBalance().subtract(usdExchangeOutDto.getAmount()));
        txnDtlUSD.setFromCoin("USD");
        txnDtlUSD.setFromAmount(usdExchangeOutDto.getAmount());
        txnDtlUSD.setExchgRate(new BigDecimal(1));
        txnDtlUSDDao.save(txnDtlUSD);


        CoinTxnDtl coinTxnDtl = new CoinTxnDtl();
        coinTxnDtl.setUserId(user.getUserId());
        coinTxnDtl.setRecordType(1);
        coinTxnDtl.setTxnCode("e1010");
        coinTxnDtl.setFromAddress("99");
        coinTxnDtl.setToAddress(user.getUserId().toString());
        coinTxnDtl.setTxnCoin(usdExchangeOutDto.getCurrency());
        coinTxnDtl.setTxnStatus(1);
        coinTxnDtl.setTxnDesc("兑换" + usdExchangeOutDto.getCurrency() + "资产");
        coinTxnDtl.setTxnAmount(usdExchangeOutDto.getAmount());
        coinTxnDtl.setTxnFee(new BigDecimal(0));
        coinTxnDtl.setTxnTime(new Date());
        coinTxnDtl.setFromCoin("USD");
        coinTxnDtl.setFromAmount(usdExchangeOutDto.getAmount());
        coinTxnDtl.setToCoin(usdExchangeOutDto.getCurrency());
        coinTxnDtl.setToAmount(usdExchangeOutDto.getAmount());
        coinTxnDtl.setExchgRate(new BigDecimal(1));
        coinTxnDtl.setUserBalance(coinBalance.getCoinBalance().add(usdExchangeOutDto.getAmount()));
        coinTxnDtl.setCreateTime(new Date());
        coinTxnDtlDao.save(coinTxnDtl);

        // 入账
        coinBalance.setCoinBalance(coinBalance.getCoinBalance().add(usdExchangeOutDto.getAmount()));
        wallet.setUsdBalance(wallet.getUsdBalance().subtract(usdExchangeOutDto.getAmount()));
        coinBalanceDao.save(coinBalance);
        walletDao.save(wallet);


        return AjaxResult.success();
    }

    @Override
    public AjaxResult update(VccReqDto req) {
        //1.锁住行数据
        Wallet w = walletDao.findOneForUpdate(req.getUserId());
        AjaxResult res = AjaxResult.success();
        if (w != null) {
            //2.处理美元账户:
            if (req.getWalletAmount() != null && req.getWalletAmount().compareTo(BigDecimal.ZERO) != 0 || req.getFreezeType().equals(FreezeType.FREE_FREEZE)) {
                dealUSD(req.getWalletAmount(), req.getWalletAlgorithmType(), req.getFreezeType(), w);
                TxnDtlUSD dtlUSD = txnService.dealTxnForUSD(TxnDtlUSD.formatUSD(req, w.getUsdBalance()), req.getFreezeType(), req.getWalletAlgorithmType());
                res.put("dtlUSD", dtlUSD);
            }

            if (req.getActiveCodeNum() != 0) {
                //3.激活码处理
                if (req.getCardLevel().equals(CardLevel.SILVER.getOrdinal())) {
                    if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.ADD)) {
                        w.setSilverActiveCodeNum(w.getSilverActiveCodeNum() + req.getActiveCodeNum());
                    } else if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.SUB)) {
                        if (w.getSilverActiveCodeNum() < req.getActiveCodeNum()) {
                            throw new CustomException(ExceptionInfo.SILVER_ACTIVITI_CODE_NUM_NO_ENOUGH);
                        }
                        w.setSilverActiveCodeNum(w.getSilverActiveCodeNum() - req.getActiveCodeNum());
                        if (req.isCodeUsed()) {
                            w.setSilverActiveCodeUsed(w.getSilverActiveCodeUsed() + req.getActiveCodeNum());
                        }
                    }
                } else if (req.getCardLevel().equals(CardLevel.GOLD.getOrdinal())) {
                    if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.ADD)) {
                        w.setGoldenActiveCodeNum(w.getGoldenActiveCodeNum() + req.getActiveCodeNum());
                    } else if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.SUB)) {
                        if (w.getGoldenActiveCodeNum() < req.getActiveCodeNum()) {
                            throw new CustomException(ExceptionInfo.GOLDEN_ACTIVITI_CODE_NUM_NO_ENOUGH);
                        }
                        w.setGoldenActiveCodeNum(w.getGoldenActiveCodeNum() - req.getActiveCodeNum());
                        if (req.isCodeUsed()) {
                            w.setGoldenActiveCodeUsed(w.getGoldenActiveCodeUsed() + req.getActiveCodeNum());
                        }
                    }
                } else if (req.getCardLevel().equals(CardLevel.BLACK_GOLD.getOrdinal())) {
                    if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.ADD)) {
                        w.setGoldenblackActiveCodeNum(w.getGoldenblackActiveCodeNum() + req.getActiveCodeNum());
                    } else if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.SUB)) {
                        if (w.getGoldenblackActiveCodeNum() < req.getActiveCodeNum()) {
                            throw new CustomException(ExceptionInfo.BLACKGOLDEN_ACTIVITI_CODE_NUM_NO_ENOUGH);
                        }
                        w.setGoldenblackActiveCodeNum(w.getGoldenblackActiveCodeNum() - req.getActiveCodeNum());
                        if (req.isCodeUsed()) {
                            w.setGoldenblackActiveCodeUsed(w.getGoldenblackActiveCodeUsed() + req.getActiveCodeNum());
                        }
                    }
                }

                // 激活码 交易明细
                TxnDtlCode dtlCode = txnService.dealTxnForCode(TxnDtlCode.formatCode(req, w));
                res.put("dtlCode", dtlCode);
            }

            walletDao.update(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                    , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                    , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                    , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());
        }
        res.put("wallet", w);
        return res;
    }

    @Override
    public AjaxResult updateComAcc(VccReqDto req) {
        //1.锁住行数据
        // 公司账户id
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        req.setUserId(accountId);
        Wallet w = walletDao.findOneForUpdate(accountId);
        if (w != null && req.getWalletAmount().compareTo(BigDecimal.ZERO) != 0) {
            AjaxResult res = AjaxResult.success();

            //2.减法的时候：扣减美元余额 + 增加冻结美元余额
            if (req.getFreezeType() != null && !FreezeType.NORMAL.equals(req.getFreezeType()) && !FreezeType.NORMAL_FALLBACK.equals(req.getFreezeType())) {
                dealUSD(req.getWalletAmount(), req.getWalletAlgorithmType(), req.getFreezeType(), w);
                walletDao.update(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                        , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                        , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                        , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());
            }
            res.put("wallet", w);
            TxnDtlUSD dtlUSD = txnService.dealTxnForUSD(TxnDtlUSD.formatUSD(req, w.getUsdBalance()), req.getFreezeType(), req.getWalletAlgorithmType());
            res.put("dtlUSD", dtlUSD);
            return res;
        }
        return AjaxResult.error();
    }

    /**
     * 用户的激活币处理
     * @param userId
     * @param cardId
     * @param value
     */
    @Override
    public void dealAfterCode(Long userId, String cardId, Character value) {
        TxnDtlCode txnDtlCode=txnService.findByTxnProductByCardId(cardId);
        if(txnDtlCode!=null){
            //失败退回激活币
            if(TxnStatus.DECLINED.getValue().equals(value)){
                if(TxnStatus.SUCCESS.getValue().equals(txnDtlCode.getTxnStatus())){
                    //退回用户的开发费
                    Wallet w = walletDao.findOneForUpdate(userId);
                    if (w != null){

                        //3.激活码处理
                        if (CardLevel.SILVER.getOrdinal().equals(txnDtlCode.getCodeType())) {
                            w.setSilverActiveCodeNum(w.getSilverActiveCodeNum() + txnDtlCode.getTxnNum());
                            w.setSilverActiveCodeUsed(w.getSilverActiveCodeUsed() - txnDtlCode.getTxnNum());
                        } else if (CardLevel.GOLD.getOrdinal().equals(txnDtlCode.getCodeType())) {
                            w.setGoldenActiveCodeNum(w.getGoldenblackActiveCodeNum() + txnDtlCode.getTxnNum());
                            w.setGoldenActiveCodeUsed(w.getGoldenActiveCodeUsed() - txnDtlCode.getTxnNum());
                        }
                        walletDao.update(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                                , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                                , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                                , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());
                    }
                    txnDtlCode.setTxnStatus(TxnStatus.FAIL.getValue());
                    txnDtlCode.setSilverCodeBalance(w.getSilverActiveCodeNum());
                    txnDtlCode.setGoldenCodeBalance(w.getGoldenActiveCodeNum());
                    txnService.dealTxnForCode(txnDtlCode);
                }
            }
        }

    }

    /**
     * 针对开卡USDT结算
     *
     * @param req
     * @return
     */
    @Override
    public AjaxResult update2(VccReqDto req) {
        //1.锁住行数据
        Wallet w = walletDao.findOneForUpdate(req.getUserId());
        AjaxResult res = AjaxResult.success();
        if (w != null) {
            //2.处理美元账户:
            if (req.getActiveCodeNum() == 0 || req.getFreezeType().equals(FreezeType.FREE_FREEZE)) {
                if (req.getFromCoin() != null && req.getFromCoin().equals("USDT")) {
                    CoinBalance c = coinBalanceDao.findOneForUpdate(req.getUserId(), req.getFromCoin());
                    dealUSDT(req.getAmount(), req.getWalletAlgorithmType(), req.getFreezeType(), c);
                    //2.USDT兑换USD 用户兑换记录
                    req.setTxnType(TxnType.WALLET_WITHDRAW_D);// 交易类型
                    TxnDtlUSD dtl = txnService.dealTxnForUSD(TxnDtlUSD.formatUSD(req, w.getUsdBalance().add(req.getWalletAmount())), req.getFreezeType(), req.getWalletAlgorithmType());
                    dtl.setTxnStatus(TxnStatus.SUCCESS.getValue());
                    dtl.setFromAmount(req.getFromAmount().negate());
                    //扣去USDT
                    coinBalanceDao.updateCoinBalance(w.getUserId(), req.getFromCoin(), c.getCoinBalance());
                    //增加冻结USD
                    w.setFreezeUsdBalacne(w.getFreezeUsdBalacne().add(req.getWalletAmount()));
                    walletDao.update(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                            , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                            , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                            , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());
                } else {
                    dealUSD(req.getWalletAmount(), req.getWalletAlgorithmType(), req.getFreezeType(), w);
                }
                req.setTxnType(TxnType.CARD_ACTIVATION_FEE_C);
                TxnDtlUSD dtlUSD = txnService.dealTxnForUSD(TxnDtlUSD.formatUSD(req, w.getUsdBalance()), req.getFreezeType(), req.getWalletAlgorithmType());
                dtlUSD.setFromAmount(req.getFromAmount().negate());
                res.put("dtlUSD", dtlUSD);
            }

            if (req.getActiveCodeNum() != 0) {
                //3.激活码处理
                if (req.getCardLevel().equals(CardLevel.SILVER.getOrdinal())) {
                    if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.ADD)) {
                        w.setSilverActiveCodeNum(w.getSilverActiveCodeNum() + req.getActiveCodeNum());
                    } else if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.SUB)) {
                        if (w.getSilverActiveCodeNum() < req.getActiveCodeNum()) {
                            throw new CustomException(ExceptionInfo.SILVER_ACTIVITI_CODE_NUM_NO_ENOUGH);
                        }
                        w.setSilverActiveCodeNum(w.getSilverActiveCodeNum() - req.getActiveCodeNum());
                        if (req.isCodeUsed()) {
                            w.setSilverActiveCodeUsed(w.getSilverActiveCodeUsed() + req.getActiveCodeNum());
                        }
                    }
                } else if (req.getCardLevel().equals(CardLevel.GOLD.getOrdinal())) {
                    if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.ADD)) {
                        w.setGoldenActiveCodeNum(w.getGoldenActiveCodeNum() + req.getActiveCodeNum());
                    } else if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.SUB)) {
                        if (w.getGoldenActiveCodeNum() < req.getActiveCodeNum()) {
                            throw new CustomException(ExceptionInfo.GOLDEN_ACTIVITI_CODE_NUM_NO_ENOUGH);
                        }
                        w.setGoldenActiveCodeNum(w.getGoldenActiveCodeNum() - req.getActiveCodeNum());
                        if (req.isCodeUsed()) {
                            w.setGoldenActiveCodeUsed(w.getGoldenActiveCodeUsed() + req.getActiveCodeNum());
                        }
                    }
                } else if (req.getCardLevel().equals(CardLevel.BLACK_GOLD.getOrdinal())) {
                    if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.ADD)) {
                        w.setGoldenblackActiveCodeNum(w.getGoldenblackActiveCodeNum() + req.getActiveCodeNum());
                    } else if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.SUB)) {
                        if (w.getGoldenblackActiveCodeNum() < req.getActiveCodeNum()) {
                            throw new CustomException(ExceptionInfo.BLACKGOLDEN_ACTIVITI_CODE_NUM_NO_ENOUGH);
                        }
                        w.setGoldenblackActiveCodeNum(w.getGoldenblackActiveCodeNum() - req.getActiveCodeNum());
                        if (req.isCodeUsed()) {
                            w.setGoldenblackActiveCodeUsed(w.getGoldenblackActiveCodeUsed() + req.getActiveCodeNum());
                        }
                    }
                } else if (req.getCardLevel().equals(CardLevel.WHITE.getOrdinal())) {
                    if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.ADD)) {
                        w.setWhiteActiveCodeNum(w.getWhiteActiveCodeNum() + req.getActiveCodeNum());
                    } else if (req.getActiveCodeAlgorithmType().equals(AlgorithmType.SUB)) {
                        if (w.getWhiteActiveCodeNum() < req.getActiveCodeNum()) {
                            throw new CustomException(ExceptionInfo.WHITE_ACTIVITI_CODE_NUM_NO_ENOUGH);
                        }
                        w.setWhiteActiveCodeNum(w.getWhiteActiveCodeNum() - req.getActiveCodeNum());
                        if (req.isCodeUsed()) {
                            w.setWhiteActiveCodeUsed(w.getWhiteActiveCodeUsed() + req.getActiveCodeNum());
                        }
                    }
                }

                // 激活码 交易明细
                TxnDtlCode dtlCode = txnService.dealTxnForCode(TxnDtlCode.formatCode(req, w));
                res.put("dtlCode", dtlCode);
            }
            if (!(req.getFromCoin() != null && req.getFromCoin().equals("USDT"))) {
                walletDao.update(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                        , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                        , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                        , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());

            }

        }
        res.put("wallet", w);
        return res;
    }

    /**
     * 针对开卡USDT结算
     *
     * @param req
     * @return
     */
    @Override
    public AjaxResult updateComAcc2(VccReqDto req) {
        //1.锁住行数据
        // 公司账户id
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        req.setUserId(accountId);
        Wallet w = walletDao.findOneForUpdate(accountId);
        if (w != null && req.getActiveCodeNum() == 0) {
            AjaxResult res = AjaxResult.success();


            //2.减法的时候：扣减美元余额 + 增加冻结美元余额
            if (req.getFreezeType() != null && !FreezeType.NORMAL.equals(req.getFreezeType()) && !FreezeType.NORMAL_FALLBACK.equals(req.getFreezeType())) {
                dealUSD(req.getWalletAmount(), req.getWalletAlgorithmType(), req.getFreezeType(), w);
                walletDao.update(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                        , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                        , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                        , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());
            }
            res.put("wallet", w);
            TxnDtlUSD dtlUSD = txnService.dealTxnForUSD(TxnDtlUSD.formatUSD(req, w.getUsdBalance()), req.getFreezeType(), req.getWalletAlgorithmType());

            if (req.getFromCoin() != null && req.getFromCoin().equals("USDT")) {
                w.setUsdBalance(w.getUsdBalance().subtract(req.getWalletAmount()));
                dtlUSD.setUsdBalance(w.getUsdBalance());
                updateUSDTComAcc(req);
            }
            res.put("dtlUSD", dtlUSD);
            return res;
        }
        return AjaxResult.error();
    }


    /**
     * 处理美元账户
     */
    public void dealUSD(BigDecimal walletAmount, AlgorithmType type, FreezeType freezeType, Wallet w) {
        if (walletAmount.compareTo(BigDecimal.ZERO) != 0) {
            if (type.equals(AlgorithmType.SUB)) {
                Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
                if (w.getUsdBalance().compareTo(walletAmount) < 0 && w.getUserId() != accountId) {
                    //账户余额不足
                    throw new CustomException(MessageUtils.message("wallet.account.insufficient.balance"));
                }
                //处理冻结金额
                switch (freezeType) {
                    case FREEZE:
                        BigDecimal fr = w.getFreezeUsdBalacne().add(walletAmount.abs());
                        w.setFreezeUsdBalacne(fr);
                        w.setUsdBalance(w.getUsdBalance().subtract(walletAmount.abs()));
                        break;
                    case FREE_FREEZE:
                        BigDecimal free = w.getFreezeUsdBalacne().subtract(walletAmount.abs());
                        w.setFreezeUsdBalacne(free);
                        break;
                    case FALLBACK:
                        // 回退的的时候，冻结的金额扣去； @{AlgorithmType.ADD} 的时候才加，其他时候不加
                        w.setFreezeUsdBalacne(w.getFreezeUsdBalacne().subtract(walletAmount.abs()));
                        break;
                    case NO:
                        BigDecimal balance = w.getUsdBalance().subtract(walletAmount.abs());
                        w.setUsdBalance(balance);
                        break;
                    default:
                        break;
                }

            } else if (type.equals(AlgorithmType.ADD)) {
                //2.加法的时候：加上美元余额
                //处理冻结金额
                switch (freezeType) {
                    case FREEZE:
                        BigDecimal fr = w.getFreezeUsdBalacne().add(walletAmount.abs());
                        w.setFreezeUsdBalacne(fr);
                        break;
                    case FREE_FREEZE:
                        BigDecimal free = w.getFreezeUsdBalacne().subtract(walletAmount.abs());
                        w.setFreezeUsdBalacne(free);
                        w.setUsdBalance(w.getUsdBalance().add(walletAmount.abs()));
                        break;
                    case FALLBACK:
                        // 回退的的时候，冻结的金额扣去； @{AlgorithmType.ADD} 的时候才加，其他时候不加
                        w.setFreezeUsdBalacne(w.getFreezeUsdBalacne().subtract(walletAmount.abs()));
                        w.setUsdBalance(w.getUsdBalance().add(walletAmount.abs()));
                        break;
                    case NO:
                        BigDecimal balance = w.getUsdBalance().add(walletAmount.abs());
                        w.setUsdBalance(balance);
                        break;
                    default:
                        break;
                }
            }
        }
    }


    public void updateUSDTComAcc(VccReqDto req) {
        //1.锁住行数据
        // 公司账户id
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        Wallet w = walletDao.findOneForUpdate(accountId);
        req.setUserId(accountId);
        CoinBalance c = coinBalanceDao.findOneForUpdate(accountId, req.getFromCoin());
        if (c != null && req.getWalletAmount().compareTo(BigDecimal.ZERO) != 0) {
            AjaxResult res = AjaxResult.success();

            //2.减法的时候：扣减美元余额 + 增加冻结美元余额
            req.setTxnType(TxnType.WALLET_WITHDRAW_C);

            TxnDtlUSD dtlc = txnService.dealTxnForUSD(TxnDtlUSD.formatUSD(req, w.getUsdBalance().subtract(req.getWalletAmount())), req.getFreezeType(), req.getWalletAlgorithmType());
            dtlc.setTxnStatus(TxnStatus.SUCCESS.getValue());

            c.setCoinBalance(c.getCoinBalance().add(req.getAmount()));
            //增加USDT
            coinBalanceDao.updateCoinBalance(w.getUserId(), req.getFromCoin(), c.getCoinBalance());
            //减少USD
            w.setUsdBalance(w.getUsdBalance().subtract(req.getWalletAmount()));
            walletDao.update(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                    , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                    , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                    , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());

        }
    }

    /**
     * 处理USDT账户
     */
    public void dealUSDT(BigDecimal walletAmount, AlgorithmType type, FreezeType freezeType, CoinBalance c) {
//        if (walletAmount.compareTo(BigDecimal.ZERO) != 0) {
        if (type.equals(AlgorithmType.SUB)) {
            if (c.getCoinBalance().compareTo(walletAmount) < 0) {
                //账户余额不足
                throw new CustomException(MessageUtils.message("wallet.coin.insufficient.balance"));
            }
            //处理冻结金额
            switch (freezeType) {
                case FREEZE:
                    BigDecimal fr = c.getFreezeBalance().add(walletAmount.abs());
                    c.setCoinBalance(c.getCoinBalance().subtract(walletAmount.abs()));
                    break;
                case FREE_FREEZE:
                    BigDecimal free = c.getFreezeBalance().subtract(walletAmount.abs());
                    c.setFreezeBalance(free);
                    break;
                case FALLBACK:
                    // 回退的的时候，冻结的金额扣去； @{AlgorithmType.ADD} 的时候才加，其他时候不加
                    c.setFreezeBalance(c.getFreezeBalance().subtract(walletAmount.abs()));
                    break;
                case NO:
                    BigDecimal balance = c.getCoinBalance().subtract(walletAmount.abs());
                    c.setCoinBalance(balance);
                    break;
                default:
                    break;
            }

        } else if (type.equals(AlgorithmType.ADD)) {
            //2.加法的时候：加上USDT余额
            //处理冻结金额
            switch (freezeType) {
                case FREEZE:
                    BigDecimal fr = c.getFreezeBalance().add(walletAmount.abs());
                    c.setCoinBalance(fr);
                    break;
                case FREE_FREEZE:
                    BigDecimal free = c.getFreezeBalance().subtract(walletAmount.abs());
                    c.setFreezeBalance(free);
                    c.setCoinBalance(c.getCoinBalance().add(walletAmount.abs()));
                    break;
                case FALLBACK:
                    // 回退的的时候，冻结的金额扣去； @{AlgorithmType.ADD} 的时候才加，其他时候不加
                    c.setFreezeBalance(c.getFreezeBalance().subtract(walletAmount.abs()));
                    c.setCoinBalance(c.getCoinBalance().add(walletAmount.abs()));
                    break;
                case NO:
                    BigDecimal balance = c.getCoinBalance().add(walletAmount.abs());
                    c.setCoinBalance(balance);
                    break;
                default:
                    break;
            }
        }
//        }
    }

    /**
     * 注册用户 初始化账户信息
     *
     * @param userId     用户id
     * @param referrerId 推荐人ID
     */
    @Override
    public Wallet register(Long userId, Long referrerId) {
        Wallet wallet = new Wallet();
        if (referrerId != null) {
            Wallet referrer = walletDao.findByUserId(referrerId);
            wallet.setChannelUserId(referrer.getChannelUserId());

            //添加邀请记录
            MetaUserInvites metaUserInvites = new MetaUserInvites();
            metaUserInvites.setInviterUserId(referrerId);
            metaUserInvites.setInviteeUserId(userId);
            metaUserInvitesService.save(metaUserInvites);
            //发放优惠券
            metaCouponInfoServiceImpl.sendCoupon(userId);
        }
        String tradeType = configService.selectConfigByKey("default.trade.type");
        if (StringUtils.isEmpty(tradeType)) {
            tradeType = TradeAuthType.TRADE_PASSWORD.getOrdinal();
        }
        wallet
                .setUserId(userId)//用户id
                .setReferrerId(referrerId)//usd美元账户余额
                .setUsdBalance(BigDecimal.ZERO)//冻结的usd美元账户余额
                .setFreezeUsdBalacne(BigDecimal.ZERO)//节点级别:00,01,02,03,04 - 00表示暂无节点
                .setNodeLevel(NodeLevel.NONE.getOrdinal())//推荐人
                .setTradePasswd("")//交易密码
                .setTradeAuthType(tradeType) // 0:交易密码,1:人脸 交易认证方式
                .setSilverActiveCodeNum(0)//银卡激活码数量余额
                .setSilverActiveCodeUsed(0)//银卡已用激活码数量
                .setGoldenActiveCodeNum(0)//金卡激活码数量余额
                .setGoldenActiveCodeUsed(0)//金卡已用激活码数量
                .setGoldenblackActiveCodeNum(0)//黑金卡激活码数量余额
                .setGoldenblackActiveCodeUsed(0)//黑金卡已用激活码数量
                .setWhiteActiveCodeNum(0)//白卡激活码数量余额
                .setWhiteActiveCodeUsed(0);//白金卡已用激活码数量

        walletDao.save(wallet);
        return wallet;
    }

    /**
     * 节点划转
     *
     * @param source 付款方
     * @param target 收款方
     */
    @Override
    public AjaxResult transfer(VccReqDto source, VccReqDto target) {
        AjaxResult res = update(source);// 付款
        if (res.get("code").equals(200)) {
            Object or = res.get("dtlUSD");
            res = update(target);// 收款
            if (or != null) {
                res.put("resultData", or);
            }
            return res;
        } else {
            return res;
        }
    }

    /**
     * 节点划转
     *
     * @param source 付款方
     * @param target 收款方
     */
    @Override
    public AjaxResult transferCode(VccReqDto source, VccReqDto target) {
        AjaxResult res = update(source);// 付款
        if (res.get("code").equals(200)) {
            TxnDtlCode dtlCode = (TxnDtlCode) res.get("dtlCode");
            res = update(target);// 收款
            res.put("codeDetail", dtlCode);
            return res;
        } else {
            return res;
        }
    }

    /**
     * 检测激活码余额是否足够
     *
     * @param transfer 划转信息
     * @return 是否足够
     */
    @Override
    public boolean checkCardBalance(NodeTransferDto transfer) {
        // 获取账户详情
        Wallet wallet = walletDao.findOneForUpdate(SecurityUtils.getLoginUser().getUserId());
        if (wallet == null) {
            return false;
        }
        boolean res = false;
        switch (transfer.getCardLevel()) {
            case "02":
                if (wallet.getGoldenActiveCodeNum() >= transfer.getActiveCodeNum()) {
                    res = true;
                }
                break;
            case "01":
                if (wallet.getSilverActiveCodeNum() >= transfer.getActiveCodeNum()) {
                    res = true;
                }
                break;
            case "03":
                if (wallet.getGoldenblackActiveCodeNum() >= transfer.getActiveCodeNum()) {
                    res = true;
                }
        }
        return res;
    }

    /**
     * 检测激活码余额是否足够
     *
     * @param transfer
     * @return 是否足够
     */
    @Override
    public boolean checkCardBalance(CodeTransferDto transfer) {
        // 获取账户详情
        Wallet wallet = walletDao.findOneForUpdate(transfer.getFromUserId());
        if (wallet == null) {
            return false;
        }
        boolean res = false;
        switch (transfer.getCardLevel()) {
            case "02":
                if (wallet.getGoldenActiveCodeNum() >= transfer.getActiveCodeNum()) {
                    res = true;
                }
                break;
            case "01":
                if (wallet.getSilverActiveCodeNum() >= transfer.getActiveCodeNum()) {
                    res = true;
                }
                break;
            case "03":
                if (wallet.getGoldenblackActiveCodeNum() >= transfer.getActiveCodeNum()) {
                    res = true;
                }
        }
        return res;
    }


    /**
     * 卡兑换
     *
     * @param exchange 兑换数据
     * @return 兑换结果
     */
    @Override
    public AjaxResult exchangeCards(CodeExchangeDto exchange) {
        //
        if (exchange.getSourceCardNum() <= 0) {
            return AjaxResult.error(MessageUtils.message("node.transfer.failed"));

        }

        // 收款
        VccReqDto target = new VccReqDto();
        // 计算转入多少钱
        Integer income = CardLevel.exchange(exchange.getSourceCardEnum(), exchange.getSourceCardNum(), exchange.getTargetCardEnum());
        target
                .setCardLevel(exchange.getTargetCard()) // 激活码的类型
                .setActiveCodeNum(income) //需要处理的激活码数量
                .setActiveCodeAlgorithmType(AlgorithmType.ADD)
                .setTxnTypeCode(TxnType.EXCHANGE_RECHARGE)
                .setCodeFromUserId(exchange.getUserId())
                .setCodeToUserId(exchange.getUserId())
                .setUserId(exchange.getUserId()) // 用户id
                .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                .setWalletAmount(BigDecimal.ZERO); // 预存金额
        //扣款
        // 计算转出多少钱
        Integer expense = CardLevel.exchange(exchange.getTargetCardEnum(), income, exchange.getSourceCardEnum());
        VccReqDto source = new VccReqDto();
        source
                .setCardLevel(exchange.getSourceCard()) // 激活码的类型
                .setCodeUsed(false) // 是否加入已使用的字段
                .setActiveCodeNum(expense) //需要处理的激活码数量
                .setActiveCodeAlgorithmType(AlgorithmType.SUB)
                .setTxnTypeCode(TxnType.EXCHANGE_EXPENDITURE)
                .setCodeFromUserId(exchange.getUserId())
                .setCodeToUserId(exchange.getUserId())
                .setUserId(exchange.getUserId()) // 用户id
                .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                .setWalletAmount(BigDecimal.ZERO); // 预存金额
        return transfer(source, target);
    }

    /**
     * 节点升级返佣
     *
     * @param userId 用户id
     * @param level  节点等级
     */
    @Override
    public void upgradeNodeLevel(Long userId, String level) {
        walletDao.upgradeNodeLevel(userId, level);
    }

    /**
     * 节点升级返佣
     *
     * @param inviterUserId 邀请者id
     * @param level         节点等级
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public void upgradeNodeLevel(Long inviterUserId, String level, String oldLevel) {
        if (walletDao.upgradeNodeLevel(inviterUserId, level, oldLevel) != 1) {
            log.error("邀请者 {} 升级节点失败，当前节点等级为 {}，升级节点等级为 {}", inviterUserId, oldLevel, level);
        } else {
            log.info("邀请者 {} 升级节点成功，当前节点等级为 {}，升级节点等级为 {}", inviterUserId, oldLevel, level);
        }
    }

//    /**
//     * 尝试升级邀请者的节点等级
//     */
////    @Override
//    public void tryUpgradeReferrerNodeLevel(Long userId) {
//        Wallet wallet = walletDao.findByUserId(userId);
//        if (wallet == null) {
//            throw new CustomException(MessageUtils.message("wallet.not.found"));
//        }
//
//        // 获取邀请者的节点等级
//        String nodeLevel = wallet.getNodeLevel();
//        if (!nodeLevel.equals(NodeLevel.NONE.getName())) {
//            log.info("用户 {} 节点等级为 {}，不为{}，不需要进行邀请升级", userId, nodeLevel, NodeLevel.NONE.getName());
//            return;
//        }
//
//        // 查询邀请者的被邀请人开卡的数量是否大于10，大于10则升级
//
////        walletDao.countInvitedAndCardActivatedUser(wallet.getReferrerId());
//
//
//        // 升级邀请者的节点等级
//        walletDao.upgradeNodeLevel(wallet.getReferrerId(), NodeLevel.FIRST.getOrdinal(), NodeLevel.NONE.getName());
//    }

    /**
     * 重置交易密码
     *
     * @param userId 用户id
     * @param newPwd 新密码
     */
    @Override
    public void resetTradePwd(Long userId, String newPwd) {
        walletDao.resetTradePwd(userId, SecurityUtils.encryptPassword(newPwd));
    }

    /**
     * 判断交易密码是否正确
     *
     * @param password 交易密码
     * @return 交易密码是否正确
     */
    @Override
    public boolean checkTradePwd(String password) {
        Wallet wallet = walletDao.findByUserId(SecurityUtils.getLoginUser().getUserId());
        return SecurityUtils.matchesPassword(password, wallet.getTradePasswd());
    }

    @Override
    public void updateAmount(Long userId, BigDecimal amount, BigDecimal freeze) {

    }

    /**
     * 修改交易认证方式
     *
     * @param tradeAuth 认证方式
     */
    @Override
    public void setTradeAuth(String tradeAuth) {
        walletDao.updateTradeAuthType(SecurityUtils.getLoginUser().getUserId(), tradeAuth);
    }

    /**
     * 根据推荐人id 获取所有的LV4的username
     *
     * @param userId
     * @return
     */
    @Override
    public List<String> findByReferrerIdAndLv4(Long userId) {
        List<Long> userIds = walletDao.findByReferrerIdAndLv4(userId);
        if (userIds == null) {
            return new ArrayList<>();
        }
        List<String> usernames = userDao.findByIds(userIds);
        return usernames;
    }

    /**
     * 账号管理列表
     *
     * @param username    用户名
     * @param status      状态
     * @param startDate   注册开始时间
     * @param endDate     注册结束日期
     * @param nodeLevel
     * @param channelName
     * @param limitFlag
     * @return
     */
    @Override
    public Page<WalletVo> selectUserList(String nickName, String username, String status, String startDate, String endDate, String nodeLevel, String channelName, String limitFlag) {
        Page<WalletVo> walletVos = walletDao.selectUserList(nickName, username, status, startDate, endDate, nodeLevel, channelName, limitFlag);
        walletVos.getContent().forEach(walletVo -> {
//            walletVo.set(walletDao.findCreditCardCount(walletVo.getUserId()));
            walletVo.setCoinBalanceList(coinBalanceDao.findAllByUserId(walletVo.getUserId().longValue()));
        });

        return walletVos;
    }

    @Override
    public void upgrade(Long userId, String nodeLevel) {
        walletDao.upgrade(userId, nodeLevel);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void upUid(Long userId, String uid) {
        walletDao.upUid(userId, uid);
    }

    /**
     * 即时转账USDT转到卡的数据处理
     *
     * @param dto
     * @param usdtAmount USDT金额
     */
    public void wallerRecharge(VccReqDto dto, BigDecimal usdtAmount) {
        //公司账户处理
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        CoinBalance balanceC = coinBalanceDao.findByUserIdAndCoinCode(accountId, dto.getFromCoin());
        coinBalanceDao.updateCoinBalance(accountId, dto.getFromCoin(), balanceC.getCoinBalance().add(dto.getFromAmount()));
        VccReqDto vccReqP = new VccReqDto();
        vccReqP
                .setActiveCodeNum(0) //需要处理的激活码数量
                .setActiveCodeAlgorithmType(AlgorithmType.ADD)
                .setUserId(accountId) // 公司总帐
                .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                .setWalletAmount(dto.getWalletAmount()) //钱包需要处理的金额
                .setFreezeType(FreezeType.NO) // 冻结类型
                .setTxnType(TxnType.WALLET_WITHDRAW_C) // 交易类型
                .setFromUserId(accountId) // 支出账户id
                .setToUserId(SecurityUtils.getLoginUser().getUserId()) // 收入账户id
                .setFromCoin(dto.getFromCoin())
                .setFromAmount(dto.getFromAmount())
                .setExchgRate(dto.getExchgRate())
                .setFee(BigDecimal.ZERO); // 手续费
        updateComAcc(vccReqP);

        //个人用户处理
        CoinBalance balance = coinBalanceDao.findByUserIdAndCoinCode(SecurityUtils.getLoginUser().getUserId(), dto.getFromCoin());
        coinBalanceDao.updateCoinBalance(SecurityUtils.getLoginUser().getUserId(), dto.getFromCoin(), balance.getCoinBalance().subtract(dto.getFromAmount()));
        VccReqDto vccReqC = new VccReqDto();
        vccReqC
                .setActiveCodeNum(0) //需要处理的激活码数量
                .setActiveCodeAlgorithmType(AlgorithmType.ADD)
                .setUserId(SecurityUtils.getLoginUser().getUserId()) // 用户id
                .setWalletAlgorithmType(AlgorithmType.ADD) //钱包需要处理的类型：加法或者减法
                .setWalletAmount(dto.getWalletAmount()) //钱包需要处理的金额
                .setFreezeType(FreezeType.NO) // 冻结类型
                .setTxnType(TxnType.WALLET_WITHDRAW_D) // 交易类型
                .setFromUserId(accountId) // 支出账户id
                .setToUserId(SecurityUtils.getLoginUser().getUserId()) // 收入账户id
                .setFromCoin(dto.getFromCoin())
                .setFromAmount(dto.getFromAmount())
                .setExchgRate(dto.getExchgRate())
                .setFee(BigDecimal.ZERO); // 手续费
        update(vccReqC);

    }

    @Override
    public Wallet findByGateUid(String uid) {
        return walletDao.findByGateUid(uid);
    }

    @Override
    public void updateCode(Long userId, BigDecimal usdBalance, BigDecimal freezeUsdBalacne, Integer silverActiveCodeNum, Integer silverActiveCodeUsed, Integer goldenActiveCodeNum, Integer goldenActiveCodeUsed, Integer goldenblackActiveCodeNum, Integer goldenblackActiveCodeUsed) {
        walletDao.update(userId, usdBalance, freezeUsdBalacne
                , silverActiveCodeNum, silverActiveCodeUsed
                , goldenActiveCodeNum, goldenActiveCodeUsed
                , goldenblackActiveCodeNum, goldenblackActiveCodeUsed);
    }

    /**
     * 扣去物流费
     *
     * @param req
     */
    @Override
    public AjaxResult updateLogisticsFee(VccReqDto req) {
        //1.锁住行数据
        Wallet w = walletDao.findOneForUpdate(req.getUserId());
        AjaxResult res = AjaxResult.success();
        if (w != null) {
            //2.处理美元账户:
            if (req.getLogisticsFee().compareTo(BigDecimal.ZERO) != 0 || req.getFreezeType().equals(FreezeType.FREE_FREEZE)) {
                if (req.getFromCoin() != null && req.getFromCoin().equals("USDT")) {
                    CoinBalance c = coinBalanceDao.findOneForUpdate(req.getUserId(), req.getFromCoin());
                    dealUSDT(req.getAmount(), req.getWalletAlgorithmType(), req.getFreezeType(), c);
                    //2.USDT兑换USD 用户兑换记录
                    req.setTxnType(TxnType.WALLET_WITHDRAW_D);// 交易类型
                    TxnDtlUSD dtl = txnService.dealTxnForUSD(TxnDtlUSD.formatUSD(req, w.getUsdBalance().add(req.getLogisticsFee())), req.getFreezeType(), req.getWalletAlgorithmType());
                    dtl.setTxnStatus(TxnStatus.SUCCESS.getValue());
                    dtl.setFromAmount(req.getFromAmount().negate());
                    //扣去USDT
                    coinBalanceDao.updateCoinBalance(w.getUserId(), req.getFromCoin(), c.getCoinBalance());
                    //增加冻结USD
                    w.setFreezeUsdBalacne(w.getFreezeUsdBalacne().add(req.getLogisticsFee()));
                    walletDao.update(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                            , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                            , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                            , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());
                } else {
                    dealUSD(req.getLogisticsFee(), req.getWalletAlgorithmType(), req.getFreezeType(), w);
                }
                req.setTxnType(TxnType.CARD_LOGISTICS_FEE_C);
                TxnDtlUSD dtlUSD = txnService.dealTxnForUSD(TxnDtlUSD.formatUSD(req, w.getUsdBalance()), req.getFreezeType(), req.getWalletAlgorithmType());
                dtlUSD.setFromAmount(req.getFromAmount().negate());
                res.put("dtlUSDLogisticsFee", dtlUSD);
            }

            if (!(req.getFromCoin() != null && req.getFromCoin().equals("USDT"))) {
                walletDao.update(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                        , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                        , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                        , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());
            }

        }
        return res;
    }

    /**
     * 操作公司账户-物流费
     *
     * @param req
     */
    @Override
    public AjaxResult updateComLogisticsFee(VccReqDto req) {
        //1.锁住行数据
        // 公司账户id
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        req.setUserId(accountId);
        req.setTxnType(TxnType.CARD_LOGISTICS_FEE_D);
        Wallet w = walletDao.findOneForUpdate(accountId);
        if (w != null && req.getLogisticsFee().compareTo(BigDecimal.ZERO) != 0) {
            AjaxResult res = AjaxResult.success();
            //2.减法的时候：扣减美元余额 + 增加冻结美元余额
            if (req.getFreezeType() != null && !FreezeType.NORMAL.equals(req.getFreezeType()) && !FreezeType.NORMAL_FALLBACK.equals(req.getFreezeType())) {
                dealUSD(req.getLogisticsFee(), req.getWalletAlgorithmType(), req.getFreezeType(), w);
                walletDao.update(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                        , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                        , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                        , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());
            }
            TxnDtlUSD dtlUSD = txnService.dealTxnForUSD(TxnDtlUSD.formatUSD(req, w.getUsdBalance()), req.getFreezeType(), req.getWalletAlgorithmType());
            if (req.getFromCoin() != null && req.getFromCoin().equals("USDT")) {
                w.setUsdBalance(w.getUsdBalance().subtract(req.getLogisticsFee()));
                dtlUSD.setUsdBalance(w.getUsdBalance());
                updateUSDTComAcc(req);
            }
            res.put("dtlUSDLogisticsFee", dtlUSD);
            return res;
        }
        return AjaxResult.error();
    }

    /**
     * 系统赠送银卡
     *
     * @param userId
     */
    @Override
    public void sendSliver(Long userId, String cardId) {
        Wallet w = walletDao.findOneForUpdate(userId);
        w.setSilverActiveCodeNum(w.getSilverActiveCodeNum() + 1);
        //流水
        // 公司账户id
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        TxnDtlCode code = new TxnDtlCode();
        code.setUserId(userId);
        code.setTxnType(TxnType.SYSTEM_SEND.getValue());
        code.setTxnDesc(TxnType.SYSTEM_SEND.getName());
        code.setFromUserId(accountId);
        code.setToUserId(userId);
        code.setTxnProduct(cardId);
        code.setTxnNum(1);
        code.setSilverCodeBalance(w.getSilverActiveCodeNum());
        code.setGoldenCodeBalance(w.getGoldenActiveCodeNum());
        code.setGoldenBlackCodeBalance(w.getGoldenblackActiveCodeNum());
        code.setWhiteCodeBalance(w.getWhiteActiveCodeNum());
        code.setTxnStatus(TxnStatus.SUCCESS.getValue());
        code.setCodeType("01");
        txnService.dealTxnForCode(code);

        //新增卡
        walletDao.update(w.getUserId(), w.getUsdBalance(), w.getFreezeUsdBalacne()
                , w.getSilverActiveCodeNum(), w.getSilverActiveCodeUsed()
                , w.getGoldenActiveCodeNum(), w.getGoldenActiveCodeUsed()
                , w.getGoldenblackActiveCodeNum(), w.getGoldenblackActiveCodeUsed());

    }
}
