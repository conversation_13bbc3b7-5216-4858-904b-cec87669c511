package com.meta.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.meta.common.utils.StringUtils;
import com.meta.system.dao.app.MetaFxUserInfoDao;
import com.meta.system.domain.app.MetaFxUserInfo;
import com.meta.system.fornax.service.FornaxService;
import com.meta.system.service.MetaFxUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/05/08/17:35
 */
@Service
public class MetaFxUserInfoServiceImpl implements MetaFxUserInfoService {

    @Autowired
    private MetaFxUserInfoDao metaFxUserInfoDao;

    @Autowired
    private FornaxService fornaxService;


    @Override
    public MetaFxUserInfo findByEmail(String email) {
        if (StringUtils.isEmpty(email)) {
            return null;
        }
        MetaFxUserInfo metaFxUserInfo = metaFxUserInfoDao.findByEmail(email);
        return metaFxUserInfo;

    }

    @Override
    public boolean save(MetaFxUserInfo metaFxUserInfo) {

        MetaFxUserInfo fxUserInfo = findByEmail(metaFxUserInfo.getEmail());
        if (fxUserInfo == null) {
            JSONObject result = fornaxService.cardholder(metaFxUserInfo);
            if (result != null && result.getBoolean("success")) {
                JSONObject object = result.getJSONObject("result");
                String cardUserId = object.getString("cardUserId");
                metaFxUserInfo.setFid(cardUserId);
                metaFxUserInfoDao.save(metaFxUserInfo);
                return true;
            }
        } else {
            metaFxUserInfo.setId(fxUserInfo.getId());
            metaFxUserInfo.setFid(fxUserInfo.getFid());
            if (StringUtils.isEmpty(metaFxUserInfo.getFid())) {
                JSONObject result = fornaxService.cardholder(metaFxUserInfo);
                if (result != null && result.getBoolean("success")) {
                    JSONObject object = result.getJSONObject("result");
                    String cardUserId = object.getString("cardUserId");
                    metaFxUserInfo.setFid(cardUserId);
                    metaFxUserInfoDao.save(metaFxUserInfo);
                    return true;
                }
            } else {
                //先删除再新增
                JSONObject result2 = fornaxService.cardholderDrop(metaFxUserInfo.getFid());
                if (result2 != null && result2.getBoolean("success")) {
                    JSONObject result = fornaxService.cardholder(metaFxUserInfo);
                    if (result != null && result.getBoolean("success")) {
                        JSONObject object = result.getJSONObject("result");
                        String cardUserId = object.getString("cardUserId");
                        metaFxUserInfo.setFid(cardUserId);
                        metaFxUserInfoDao.save(metaFxUserInfo);
                        return true;
                    }
                }

            }
        }
        return false;

    }
}
