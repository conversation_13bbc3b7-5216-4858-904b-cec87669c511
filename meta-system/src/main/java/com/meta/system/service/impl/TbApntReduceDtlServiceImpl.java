package com.meta.system.service.impl;

import com.meta.system.dao.TbApntReduceDtlDao;
import com.meta.system.domain.apnt.TbApntReduceDtl;
import com.meta.system.service.TbApntReduceDtlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户积分扣减明细Service业务层处理
 */
@Service
public class TbApntReduceDtlServiceImpl implements TbApntReduceDtlService {


    @Autowired
    private TbApntReduceDtlDao tbApntReduceDtlDao;

    @Override
    public void save(TbApntReduceDtl tbApntReduceDtl) {
        tbApntReduceDtlDao.save(tbApntReduceDtl);
    }
}
