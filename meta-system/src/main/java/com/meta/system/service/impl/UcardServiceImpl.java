package com.meta.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meta.common.constant.Constants;
import com.meta.common.enums.PhysicalCardType;
import com.meta.common.utils.StringUtils;
import com.meta.system.config.MoonbankConfig;
import com.meta.system.domain.app.FileSource;
import com.meta.system.domain.app.MetaCardUserInfo;
import com.meta.system.domain.groupkey.CardUserInfoKey;
import com.meta.system.domain.partner.MetaPartnerUser;
import com.meta.system.moonbank.constants.MucardIdType;
import com.meta.system.moonbank.constants.MucardKycStatus;
import com.meta.system.moonbank.models.*;
import com.meta.system.moonbank.util.Base64ImgUtil;
import com.meta.system.moonbank.util.MoonbankEncryptUtil;
import com.meta.system.moonbank.util.MoonbankUtil;
import com.meta.system.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/01/14/17:22
 */
@Slf4j
@Service
public class UcardServiceImpl implements UcardService {

    @Autowired
    private MetaCardUserInfoService metaCardUserInfoService;

    @Autowired
    private MoonbankUtil moonbankUtil;

    @Resource
    private FileSourceService fileSourceService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private MetaPartnerUserService metaPartnerUserService;

    @Override
    public String getKycStatus(String status) {
        if (MucardKycStatus.NOT_SUB.getValue().equals(status)) {
            return "";
        } else if (MucardKycStatus.PENDING.getValue().equals(status)) {
            return "WAITING_KYC";
        } else if (MucardKycStatus.PASS.getValue().equals(status)) {
            return "PASSED";
        } else if (MucardKycStatus.REJECT.getValue().equals(status)) {
            return "REJECTED";
        }
        return null;
    }

    @Override
    public String getMUcardIdType(String type) {
        if ("PASSPORT".equals(type)) {
            return MucardIdType.PASSPORT.getValue();
        } else if ("ID".equals(type)) {
            return MucardIdType.NATIONALID.getValue();
        } else if ("ID_CARD".equals(type)) {
            return MucardIdType.NATIONALID.getValue();
        }
        return null;
    }


    @Override
    public JSONObject setUcardKycInfo(String uid, JSONObject jsonObject, Integer bankCardId, String sysId) {
        JSONObject json = new JSONObject();
        String email = jsonObject.getString("email");
        try {
            MetaCardUserInfo metaCardUserInfo = metaCardUserInfoService.findUid(uid, PhysicalCardType.SingaporeUCard_10.getType());
            if (metaCardUserInfo == null) {
                metaCardUserInfo = new MetaCardUserInfo();

                metaCardUserInfo.setId(new CardUserInfoKey(uid, PhysicalCardType.SingaporeUCard_10.getType()));
            } else {
                //判断kyc是否审核通过
                if ("PASSED".equals(metaCardUserInfo.getKycStatue())) {
                    String message = "KYC already PASSED!";
                    json.put("error_message", message);
                    log.info("======调用错误===== " + message);
                    return json;
                } else {
                    JSONObject js = new JSONObject();
                    js.put("email", email);
                    JSONObject kycJson = getUcardKycStatue(js, sysId);
                    if (kycJson != null) {
                        String kycStatus = kycJson.getString("kycStatus");
                        if ("PASSED".equals(kycStatus)) {
                            String message = "KYC already PASSED!";
                            json.put("error_message", message);
                            log.info("======调用错误===== " + message);
                            return json;
                        } else if ("WAITING_KYC".equals(kycStatus)) {
                            String message = "kyc has been submitted!";
                            json.put("error_message", message);
                            log.info("======调用错误===== " + message);
                            return json;
                        }

                    }
                }


            }

            String firstName = jsonObject.getString("firstName");
            String lastName = jsonObject.getString("lastName");
            String dateOfBirth = jsonObject.getString("dateOfBirth");
            String mobilePrefixCountry = jsonObject.getString("mobilePrefixCountry");
            String mobileNumber = jsonObject.getString("mobileNumber");

            metaCardUserInfo.setFirstName(firstName);
            metaCardUserInfo.setFirstNameEnglish("-");
            metaCardUserInfo.setLastName(lastName);
            metaCardUserInfo.setLastNameEnglish("-");
            metaCardUserInfo.setNationality(mobilePrefixCountry);
            metaCardUserInfo.setMobilePrefixCountry(mobilePrefixCountry);
            metaCardUserInfo.setDateOfBirth(dateOfBirth);


            UcardApplyRequest ucardApplyRequest = new UcardApplyRequest();
            HolderInfo holderInfo = new HolderInfo();
            holderInfo.setEmail(email);
            holderInfo.setFirst_name(firstName);
            holderInfo.setLast_name(lastName);
            holderInfo.setDate_of_birth(dateOfBirth);
            holderInfo.setCountry_code(mobilePrefixCountry);
            holderInfo.setPhone_number(mobileNumber);
            ucardApplyRequest.setHolderInfo(holderInfo);

            String countryCode = jsonObject.getString("countryCode");
            String city = jsonObject.getString("city");
            String state = jsonObject.getString("state");
            String postCode = jsonObject.getString("postCode");
            String addressLine = jsonObject.getString("addressLine");

            DeliveryAddress deliveryAddress = new DeliveryAddress();
            deliveryAddress.setCountry(countryCode);
            deliveryAddress.setCity(city);
            deliveryAddress.setState(state);
            deliveryAddress.setPostal_code(postCode);

            metaCardUserInfo.setCity(city);
            metaCardUserInfo.setState(state);
            metaCardUserInfo.setPostCode(postCode);
            metaCardUserInfo.setCountryCode(countryCode);

            if (addressLine.length() > 70) {
                String firstPart = addressLine.substring(0, 70);
                String secondPart = addressLine.substring(70);
                log.info("第一部分（70个字符）：\n" + firstPart);
                log.info("第二部分（超过70个字符）：\n" + secondPart);
                deliveryAddress.setLine1(firstPart);
                deliveryAddress.setLine2(secondPart);
                metaCardUserInfo.setAddressLine1(firstPart);
                metaCardUserInfo.setAddressLine2(secondPart);
            } else {
                deliveryAddress.setLine1(addressLine);
                metaCardUserInfo.setAddressLine1(addressLine);
            }
            ucardApplyRequest.setDeliveryAddress(deliveryAddress);

            Identification identification = new Identification();

            String identificationType = jsonObject.getString("identificationType");
            String identificationNumber = jsonObject.getString("identificationNumber");
            String identificationExpiryDate = jsonObject.getString("identificationExpiryDate");

            metaCardUserInfo.setIdentificationType(identificationType);
            metaCardUserInfo.setIdentificationNumber(identificationNumber);
            metaCardUserInfo.setIdentificationExpiryDate(identificationExpiryDate);
            String idType = getMUcardIdType(identificationType);
            if (StringUtils.isEmpty(idType)) {
                String message = "IdentificationType Error";
                json.put("error_message", message);
                log.info("======调用错误===== " + message);
                return json;
            }
            identification.setIdentificationType(idType);
            identification.setIdentificationNumber(identificationNumber);
            identification.setIdentificationExpiryDate(identificationExpiryDate);

            String picName = "";
            String frontImgBase64 = jsonObject.getString("frontImgBase64");
            if (StringUtils.isEmpty(frontImgBase64) || !Base64ImgUtil.isValidBase64Image(frontImgBase64)) {
                String message = "Front image  format error";
                json.put("error_message", message);
                log.info("======调用错误===== " + message);
                return json;
            }
            picName = email + "_frontImg.jpg";
            String frontImgId = uploadImg(uid, frontImgBase64, picName);
            if (StringUtils.isEmpty(frontImgId)) {
                String message = "Front Image Error";
                json.put("error_message", message);
                log.info("======调用错误===== " + message);
                return json;
            }
            identification.setFrontImgFileId(frontImgId);
            metaCardUserInfo.setFrontImgFileId(frontImgId);

            String backImgBase64 = jsonObject.getString("backImgBase64");
            if (StringUtils.isNotEmpty(backImgBase64)) {
                if (!Base64ImgUtil.isValidBase64Image(backImgBase64)) {
                    String message = "Back image  format error";
                    json.put("error_message", message);
                    log.info("======调用错误===== " + message);
                    return json;
                }
                picName = email + "_backImg.jpg";
                String backImgId = uploadImg(uid, backImgBase64, picName);
                if (StringUtils.isEmpty(backImgId)) {
                    String message = "Back Image Error";
                    json.put("error_message", message);
                    log.info("======调用错误===== " + message);
                    return json;
                }
                identification.setBackImgFileId(backImgId);
                metaCardUserInfo.setBackImgFileId(backImgId);
            }

            String handheldImgBase64 = jsonObject.getString("handheldImgBase64");
            if (StringUtils.isEmpty(handheldImgBase64) || !Base64ImgUtil.isValidBase64Image(handheldImgBase64)) {
                String message = "Handeld image  format error";
                json.put("error_message", message);
                log.info("======调用错误===== " + message);
                return json;
            }
            picName = email + "_handheldImg.jpg";
            String handheldImgId = uploadImg(uid, handheldImgBase64, picName);
            if (StringUtils.isEmpty(handheldImgId)) {
                String message = "Handeld Image Error";
                json.put("error_message", message);
                log.info("======调用错误===== " + message);
                return json;
            }

            identification.setHandheldImgFileId(handheldImgId);
            metaCardUserInfo.setHandheldImgFileId(handheldImgId);

            ucardApplyRequest.setIdentification(identification);
            ucardApplyRequest.setAttemptBankcardId(bankCardId);
            ApiResponse<String> apiResponse = moonbankUtil.setUcardKycApply(uid, ucardApplyRequest);
            if (apiResponse.isSuccess()) {
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
                log.info("ucardKycApply encode result===>" + descStr);
                JSONObject r = JSONObject.parseObject(descStr);
                String status = r.getString("status");


                json.put("status", getKycStatus(status));
                metaCardUserInfo.setKycStatue(getKycStatus(status));

            } else {
                String message = apiResponse.getMessage();
                json.put("error_message", message);
                log.info("======调用错误===== " + message);

            }
            metaCardUserInfoService.save(metaCardUserInfo);

            //设置kyc审核时间
            String kyc_check_time = sysConfigService.selectConfigByKey("kyc_check_time");
            redisTemplate.opsForValue().set(Constants.KYC_CHECK_TIME + "B-" + uid, "1",
                    Long.valueOf(kyc_check_time.replaceAll(" ", "")), TimeUnit.MINUTES);

            String waitKey = Constants.KYC_WAIT_COUNT + uid;
            redisTemplate.opsForValue().increment(waitKey);
            redisTemplate.expire(waitKey, 6, TimeUnit.HOURS);
        } finally {
            // 显式清除大对象引用
            jsonObject.clear();
        }
        return json;
    }

    @Override
    public JSONObject setUcardKycInfoNew(String uid, JSONObject jsonObject, Integer bankCardId, String sysId) {
        JSONObject json = new JSONObject();
        String email = jsonObject.getString("email");
        MetaCardUserInfo metaCardUserInfo = metaCardUserInfoService.findUid(uid, PhysicalCardType.SingaporeUCard_10.getType());
        if (metaCardUserInfo == null) {
            metaCardUserInfo = new MetaCardUserInfo();

            metaCardUserInfo.setId(new CardUserInfoKey(uid, PhysicalCardType.SingaporeUCard_10.getType()));
        } else {
            //判断kyc是否审核通过
            if ("PASSED".equals(metaCardUserInfo.getKycStatue())) {
                String message = "KYC already PASSED!";
                json.put("error_message", message);
                log.info("======调用错误===== " + message);
                return json;
            } else {
                JSONObject js = new JSONObject();
                js.put("email", email);
                JSONObject kycJson = getUcardKycStatue(js, sysId);
                if (kycJson != null) {
                    String kycStatus = kycJson.getString("kycStatus");
                    if ("PASSED".equals(kycStatus)) {
                        String message = "KYC already PASSED!";
                        json.put("error_message", message);
                        log.info("======调用错误===== " + message);
                        return json;
                    } else if ("WAITING_KYC".equals(kycStatus)) {
                        String message = "kyc has been submitted!";
                        json.put("error_message", message);
                        log.info("======调用错误===== " + message);
                        return json;
                    }

                }
            }


        }

        String firstName = jsonObject.getString("firstName");
        String lastName = jsonObject.getString("lastName");
        String dateOfBirth = jsonObject.getString("dateOfBirth");
        String mobilePrefixCountry = jsonObject.getString("mobilePrefixCountry");
        String mobileNumber = jsonObject.getString("mobileNumber");

        metaCardUserInfo.setFirstName(firstName);
        metaCardUserInfo.setFirstNameEnglish("-");
        metaCardUserInfo.setLastName(lastName);
        metaCardUserInfo.setLastNameEnglish("-");
        metaCardUserInfo.setNationality(mobilePrefixCountry);
        metaCardUserInfo.setMobilePrefixCountry(mobilePrefixCountry);
        metaCardUserInfo.setDateOfBirth(dateOfBirth);


        UcardApplyRequest ucardApplyRequest = new UcardApplyRequest();
        HolderInfo holderInfo = new HolderInfo();
        holderInfo.setEmail(email);
        holderInfo.setFirst_name(firstName);
        holderInfo.setLast_name(lastName);
        holderInfo.setDate_of_birth(dateOfBirth);
        holderInfo.setCountry_code(mobilePrefixCountry);
        holderInfo.setPhone_number(mobileNumber);
        ucardApplyRequest.setHolderInfo(holderInfo);

        String countryCode = jsonObject.getString("countryCode");
        String city = jsonObject.getString("city");
        String state = jsonObject.getString("state");
        String postCode = jsonObject.getString("postCode");
        String addressLine = jsonObject.getString("addressLine");

        DeliveryAddress deliveryAddress = new DeliveryAddress();
        deliveryAddress.setCountry(countryCode);
        deliveryAddress.setCity(city);
        deliveryAddress.setState(state);
        deliveryAddress.setPostal_code(postCode);

        metaCardUserInfo.setCity(city);
        metaCardUserInfo.setState(state);
        metaCardUserInfo.setPostCode(postCode);
        metaCardUserInfo.setCountryCode(countryCode);

        if (addressLine.length() > 70) {
            String firstPart = addressLine.substring(0, 70);
            String secondPart = addressLine.substring(70);
            log.info("第一部分（70个字符）：\n" + firstPart);
            log.info("第二部分（超过70个字符）：\n" + secondPart);
            deliveryAddress.setLine1(firstPart);
            deliveryAddress.setLine2(secondPart);
            metaCardUserInfo.setAddressLine1(firstPart);
            metaCardUserInfo.setAddressLine2(secondPart);
        } else {
            deliveryAddress.setLine1(addressLine);
            metaCardUserInfo.setAddressLine1(addressLine);
        }
        ucardApplyRequest.setDeliveryAddress(deliveryAddress);

        Identification identification = new Identification();

        String identificationType = jsonObject.getString("identificationType");
        String identificationNumber = jsonObject.getString("identificationNumber");
        String identificationExpiryDate = jsonObject.getString("identificationExpiryDate");

        metaCardUserInfo.setIdentificationType(identificationType);
        metaCardUserInfo.setIdentificationNumber(identificationNumber);
        metaCardUserInfo.setIdentificationExpiryDate(identificationExpiryDate);
        String idType = getMUcardIdType(identificationType);
        if (StringUtils.isEmpty(idType)) {
            String message = "IdentificationType Error";
            json.put("error_message", message);
            log.info("======调用错误===== " + message);
            return json;
        }
        identification.setIdentificationType(idType);
        identification.setIdentificationNumber(identificationNumber);
        identification.setIdentificationExpiryDate(identificationExpiryDate);


        String frontImgId = jsonObject.getString("frontImgFileId");
        identification.setFrontImgFileId(frontImgId);
        metaCardUserInfo.setFrontImgFileId(frontImgId);

        if ("ID_CARD".equals(identificationType)) {
            String backImgId = jsonObject.getString("backImgFileId");
            if (StringUtils.isEmpty(backImgId)) {
                String message = "Back Image Error";
                json.put("error_message", message);
                log.info("======调用错误===== " + message);
                return json;
            }
            identification.setBackImgFileId(backImgId);
            metaCardUserInfo.setBackImgFileId(backImgId);

        }

        String handheldImgId = jsonObject.getString("handheldImgFileId");
        identification.setHandheldImgFileId(handheldImgId);
        metaCardUserInfo.setHandheldImgFileId(handheldImgId);

        ucardApplyRequest.setIdentification(identification);
        ucardApplyRequest.setAttemptBankcardId(bankCardId);
        ApiResponse<String> apiResponse = moonbankUtil.setUcardKycApply(uid, ucardApplyRequest);
        if (apiResponse.isSuccess()) {
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
            log.info("ucardKycApply encode result===>" + descStr);
            JSONObject r = JSONObject.parseObject(descStr);
            String status = r.getString("status");


            json.put("status", getKycStatus(status));
            metaCardUserInfo.setKycStatue(getKycStatus(status));

        } else {
            String message = apiResponse.getMessage();
            json.put("error_message", message);
            log.info("======调用错误===== " + message);

        }
        metaCardUserInfoService.save(metaCardUserInfo);

        //设置kyc审核时间
        String kyc_check_time = sysConfigService.selectConfigByKey("kyc_check_time");
        redisTemplate.opsForValue().set(Constants.KYC_CHECK_TIME + "B-" + uid, "1",
                Long.valueOf(kyc_check_time.replaceAll(" ", "")), TimeUnit.MINUTES);

        String waitKey = Constants.KYC_WAIT_COUNT + uid;
        redisTemplate.opsForValue().increment(waitKey);
        redisTemplate.expire(waitKey, 6, TimeUnit.HOURS);

        return json;
    }

    /**
     * 查询kyc审核结果
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject getUcardKycStatue(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String email = jsonObject.getString("email");
        MetaPartnerUser metaPartnerUser = metaPartnerUserService.findEmailAndSysId(email, sysId);
        if (metaPartnerUser == null) {
            String message = "Please create kyc information first";
            bodyJson.put("error_message", message);
            log.info("======kyc1error===== " + message);
            log.info("======调用错误===== " + message);
            return bodyJson;
        } else {
            String key = Constants.KYC_CHECK_TIME + "B-" + metaPartnerUser.getUid();
            if (redisTemplate.hasKey(key)) {
                MetaCardUserInfo metaCardUserInfo = metaCardUserInfoService.findUid(metaPartnerUser.getUid(), PhysicalCardType.SingaporeUCard_10.getType());
                if (metaCardUserInfo != null) {
                    bodyJson.put("reason", "");
                    bodyJson.put("kycStatus", metaCardUserInfo.getKycStatue());
                } else {
                    bodyJson.put("error_message", "Inspection error");
                }
                return bodyJson;
            }
            ApiResponse<String> apiResponse = moonbankUtil.uCardKycstatus(metaPartnerUser.getUid());

            if (apiResponse.isSuccess()) {
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

                System.out.println("uCardKycstatus encode result===>" + descStr);
                JSONObject j = JSON.parseObject(descStr);
                String kycStatus = j.getString("status");
                String reason = j.getString("failedReason");

                String status = getKycStatus(kycStatus);

                if (StringUtils.isNotEmpty(kycStatus)) {
                    metaCardUserInfoService.updataStaue(metaPartnerUser.getUid(), status, PhysicalCardType.SingaporeUCard_10.getType());
                }
                bodyJson.put("reason", reason);
                bodyJson.put("kycStatus", status);
                return bodyJson;
            } else {
                String message = apiResponse.getMessage();

                bodyJson.put("error_message", message);
                log.info("======调用错误===== " + message);
                return bodyJson;
            }

        }
    }

    @Override
    public JSONObject getUcardKyc(String cardType, String uid) {

        JSONObject json = new JSONObject();
        if (PhysicalCardType.SingaporeUCard_10.getType().equals(cardType)) {
            ApiResponse<String> apiResponse = moonbankUtil.uCardKycstatus(uid);

            if (apiResponse.isSuccess()) {
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

                System.out.println("uCardKycstatus encode result===>" + descStr);
                JSONObject j = JSON.parseObject(descStr);
                String kycStatus = j.getString("status");

                String reason = j.getString("failedReason");

                String status = getKycStatus(kycStatus);

                if (StringUtils.isNotEmpty(kycStatus)) {
                    metaCardUserInfoService.updataStaue(uid, status, PhysicalCardType.SingaporeUCard_10.getType());
                }

                json.put("reason", reason);
                json.put("kycStatus", status);
                return json;
            }
        }
        return json;
    }

    /**
     * 通过base64上传文件
     *
     * @param uid
     * @param frontImgBase64
     * @param picName
     * @return
     */
    public String uploadImg(String uid, String frontImgBase64, String picName) {
        String documentFileId = "";
        ResponseEntity<String> responseEntity = moonbankUtil.uCardUpload(uid, frontImgBase64, picName);
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            JSONObject jf = JSON.parseObject(responseEntity.getBody());
            log.info("uplod uCardFile result===>" + responseEntity.getBody());
            if (jf.getBoolean("success")) {
                if (StringUtils.isNotEmpty(jf.getString("result"))) {
                    String desc = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, jf.getString("result"));

                    if (desc.contains("documentFileId")) {
                        JSONObject jf2 = JSON.parseObject(desc);
                        documentFileId = jf2.getString("documentFileId");
                    }

                }

            }
        }
        return documentFileId;
    }

    @Override
    public String uCardUploadFile(String fileId, String uid) {
        FileSource fileSource = fileSourceService.findById(Long.valueOf(fileId));
        ResponseEntity<String> responseEntity = moonbankUtil.uCardUploadFile(uid, fileSource.getAbsolutePath(), fileId);
        String documentFileId = "";
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            JSONObject jf = JSON.parseObject(responseEntity.getBody());
            log.info("uplod uCardFile result===>" + responseEntity.getBody());
            if (jf.getBoolean("success")) {
                if (StringUtils.isNotEmpty(jf.getString("result"))) {
                    String desc = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, jf.getString("result"));
                    log.info("uCardFile result===>" + responseEntity.getBody());
                    if (desc.contains("documentFileId")) {
                        JSONObject jf2 = JSON.parseObject(desc);
                        documentFileId = jf2.getString("documentFileId");
                    }

                }

            }
        }
        return documentFileId;
    }


}
