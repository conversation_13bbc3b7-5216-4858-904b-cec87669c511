package com.meta.system.service;

import com.meta.common.core.domain.AjaxResult;
import com.meta.system.domain.app.SysUserWhite;
import org.springframework.data.domain.Page;

/**
 * <AUTHOR>
 * @date 2025/05/11/11:36
 */
public interface SysUserWhiteService {

    SysUserWhite findByUserId(long userId);

    Page<SysUserWhite> selectUserWhiteList(SysUserWhite sysUserWhite);

    AjaxResult addUserWhiteList(SysUserWhite sysUserWhite);

    AjaxResult delUserWhiteList(SysUserWhite sysUserWhite);
}
