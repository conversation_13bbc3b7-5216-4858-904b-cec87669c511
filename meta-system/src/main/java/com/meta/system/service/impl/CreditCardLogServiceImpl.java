package com.meta.system.service.impl;

import com.meta.common.constant.Constants;
import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableSupport;
import com.meta.common.enums.VccTxnType;
import com.meta.common.enums.app.TxnStatus;
import com.meta.common.enums.app.TxnType;
import com.meta.common.utils.MyAesUtil;
import com.meta.common.utils.ServletUtils;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.system.dao.CreditCardLogDao;
import com.meta.system.domain.CreditCardLog;
import com.meta.system.dto.CardLogDto;
import com.meta.system.service.CreditCardLogService;
import com.meta.system.vo.CreditCardLogVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
public class CreditCardLogServiceImpl implements CreditCardLogService {
    @Autowired
    private CreditCardLogDao creditCardLogDao;

    @Override
    public Page<CreditCardLog> list(CreditCardLog log) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        Pageable pageable = PageRequest.of(pageReq.getPageNo(), pageReq.getPageSize());
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("cardId", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("transactionId", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("transactionType", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("transactionStatus", ExampleMatcher.GenericPropertyMatchers.exact())
                .withIgnoreNullValues();

        Example<CreditCardLog> example = Example.of(log, matcher);
        return creditCardLogDao.findAll(example, pageable);
    }

    @Override
    public CreditCardLog findByTransactionId(String txnId) {
        return creditCardLogDao.findByTransactionId(txnId);
    }

    @Override
    public CreditCardLog findById(String id) {
        return creditCardLogDao.findDataById(id);
    }

    @Override
    public List<String> findByCardId(String cardId) {
        return creditCardLogDao.findByCardId(cardId);
    }

    @Override
    public void save(CreditCardLog log) {
        creditCardLogDao.save(log);
    }

    @Override
    public void saveAll(List<CreditCardLog> list) {
        creditCardLogDao.saveAll(list);
    }

    @Override
    public Page<CreditCardLogVo> selectData(String cardID, String transactionId, String transactionStartTime, String transactionEndTime, PageRequest of){

        return creditCardLogDao.selectData(cardID, transactionId,  transactionStartTime, transactionEndTime,of);

    }

    @Override
    public Page<CreditCardLogVo> listForCardLog(CardLogDto vo) {
        Page<CreditCardLogVo> page = creditCardLogDao.page(vo);
        List<CreditCardLogVo> list=page.getContent();
        for(CreditCardLogVo cardLogVo:list){

            if (StringUtils.isNotEmpty(cardLogVo.getCardNo())) {
                String cardNo = "";
                if (!cardLogVo.getCardNo().contains("*")) {
                    cardNo = AESUtils.aesDecrypt(Constants.card_key, cardLogVo.getCardNo());
                    String xx = "";

                    if (cardNo.length() > 8) {
                        for (int i = 0; i < cardNo.length() - 8; i++) {
                            xx = xx + "*";
                        }
                        cardNo = cardNo.substring(0, 4) + xx + cardNo.substring(cardNo.length() - 4, cardNo.length());
                    }
                } else {
                    cardNo = cardLogVo.getCardNo();
                }

                cardNo = MyAesUtil.encode((MyAesUtil.SALT + cardNo).getBytes());
                cardLogVo.setCardNo(cardNo);
            }
        }

        return page;
    }

    @Override
    public void updateStatue(Long id, String status) {
        creditCardLogDao.updateStatue( id,  status);
    }
    @Override
    public void updateTxnStatue(String id, String status) {
        creditCardLogDao.updateTxnStatue( id,  status);
    }

    @Override
    public List<String> findPending(String cardId,  String transactionStartTime,String status) {
        return creditCardLogDao.findPending( cardId,    transactionStartTime,status);
    }

    @Override
    public List<CreditCardLog> findConfirm(String cardId, String transactionStartTime, String status) {
        return creditCardLogDao.findConfirm( cardId, transactionStartTime,status);
    }

    @Override
    public List<CreditCardLog> findData(String orderId) {
        return creditCardLogDao.findData(orderId);
    }
}
