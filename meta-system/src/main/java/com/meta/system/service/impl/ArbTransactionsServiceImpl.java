package com.meta.system.service.impl;


import com.meta.common.utils.StringUtils;
import com.meta.system.dao.ArbTransactionsDao;
import com.meta.system.domain.app.ArbTransactions;
import com.meta.system.dto.RechargeDto;
import com.meta.system.service.ArbTransactionsService;
import com.meta.system.vo.CollectRoportVo;
import com.meta.system.vo.RechargeDataVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
public class ArbTransactionsServiceImpl implements ArbTransactionsService {
    @Autowired
    ArbTransactionsDao arbTransactionsDao;

    @Override
    public ArbTransactions findTime(String walletAddress) {
        return arbTransactionsDao.findTime(walletAddress);
    }

    @Override
    public List<String> getList(String walletAddress, int timestamp) {
        return arbTransactionsDao.getList( walletAddress,timestamp) ;
    }

    @Override
    public List<String> getList(String walletAddress) {
        return arbTransactionsDao.getList(walletAddress) ;
    }

    @Override
    public Page<CollectRoportVo> getReportPage(String startDate, String endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        long startTimes=0;
        long endTimes=0;
        try {
            // 创建一个Calendar对象
            Calendar calendar = Calendar.getInstance();
            if(StringUtils.isNotEmpty(startDate)){
                // 解析字符串日期为Date对象
                Date date = sdf.parse(startDate);
                // 设置日期为解析得到的Date对象
                calendar.setTime(date);
                // 获取时间戳
                startTimes = calendar.getTimeInMillis()/1000;
            }

            if(StringUtils.isNotEmpty(endDate)){
                // 解析字符串日期为Date对象
                Date date = sdf.parse(endDate);
                // 设置日期为解析得到的Date对象
                calendar.setTime(date);
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                // 获取时间戳
                endTimes = calendar.getTimeInMillis()/1000;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }


        return arbTransactionsDao.getReportPage(startTimes, endTimes);
    }
    @Override
    public List<ArbTransactions> findByTxid(String txid) {
        return arbTransactionsDao.findByTxid(txid);
    }

    @Override
    public void save(ArbTransactions ArbTransactions) {
        arbTransactionsDao.save(ArbTransactions);
    }

    @Override
    public void updateTransactions(ArbTransactions Transactions) {
        arbTransactionsDao.updateArbTransactions(Transactions.getIsSync(), Transactions.getTxid());
    }

    @Override
    public Page<RechargeDataVo> page(RechargeDto dto) {
        return arbTransactionsDao.page( dto);
    }

    @Override
    public ArbTransactions findId(Long id) {
        Optional<ArbTransactions> op = arbTransactionsDao.findById(id);
        if(op.isPresent())
            return op.get();
        return null;
    }
}
