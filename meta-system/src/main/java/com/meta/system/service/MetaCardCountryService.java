package com.meta.system.service;

import com.meta.system.domain.app.MetaCardCountry;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/08/16:44
 */
public interface MetaCardCountryService {
    List<MetaCardCountry> getList();

    List<MetaCardCountry> getValidData();

    MetaCardCountry selectByCode(String nationality);

    String getName(String code);

    BigDecimal getLogisticeFee(String wlCountry, String wlState);
}
