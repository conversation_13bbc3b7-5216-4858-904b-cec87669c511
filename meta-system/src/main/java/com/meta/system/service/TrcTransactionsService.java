package com.meta.system.service;

import com.meta.system.domain.TrcTransactions;
import com.meta.system.vo.CollectRoportVo;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/23/17:22
 */
public interface TrcTransactionsService {

    TrcTransactions findTime(String walletAddress);

    List<String> getList(String walletAddress, int timestamp);

    List<String> getList(String walletAddress);

    TrcTransactions findId(Long id);

    Page<CollectRoportVo> getReportPage(String startDate, String endDate);




    void updateTransactions(TrcTransactions trcTransactions);

    TrcTransactions fingByTxid(String txid);
}
