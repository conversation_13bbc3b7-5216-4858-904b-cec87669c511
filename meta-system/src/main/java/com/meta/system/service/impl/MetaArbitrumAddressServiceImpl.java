package com.meta.system.service.impl;

import com.meta.common.utils.MyAesUtil;
import com.meta.system.dao.app.MetaArbitrumAddressDao;
import com.meta.system.domain.app.MetaArbitrumAddress;
import com.meta.system.service.MetaArbitrumAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/05/10/15:29
 */
@Service
public class MetaArbitrumAddressServiceImpl implements MetaArbitrumAddressService {

    @Autowired
    private MetaArbitrumAddressDao metaArbitrumAddressDao;

    @Override
    public MetaArbitrumAddress getFiatNFT() {
        List<MetaArbitrumAddress> list = metaArbitrumAddressDao.getFiatNFT();
        if (list.size() > 0) {
            MetaArbitrumAddress metaArbitrumAddress = list.get(0);
            metaArbitrumAddress.setKey(MyAesUtil.encode((MyAesUtil.SALT + metaArbitrumAddress.getKey()).getBytes()));
            return metaArbitrumAddress;
        }
        return null;
    }

    @Override
    public void updateNFT(String address) {
        metaArbitrumAddressDao.updateNft(address);
    }
}
