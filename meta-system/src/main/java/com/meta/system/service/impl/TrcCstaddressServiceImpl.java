package com.meta.system.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.meta.common.config.RedisLock;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.utils.sign.Md5Utils;
import com.meta.system.common.cache.LocalCacheUtil;
import com.meta.system.dao.TrcCstaddressDao;
import com.meta.system.domain.TrcCstaddress;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.TrcCstaddressService;
import com.meta.system.uitls.RedisConstants;
import com.meta.system.uitls.SimplePrivateKeyEncryptionUtil;
import com.meta.system.vcc.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@Slf4j
@Service
public class TrcCstaddressServiceImpl implements TrcCstaddressService {
    @Autowired
    private TrcCstaddressDao trcCstaddressDao;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private RedisLock redisLock;

    @Override
    public List<TrcCstaddress> findByCstId(Long cstId, String sysId) {
        return trcCstaddressDao.findByCstId(cstId, sysId);
    }

    @Override
    public TrcCstaddress findByAddress(String address) {
        return trcCstaddressDao.findByAddress(address);
    }

    /**
     * 创建钱包
     *
     * @param userId 用户id
     * @param key
     */
    @Override
    public String createWallet(Long userId, String sysId, String key) {
        //创建钱包地址
        Map headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        JSONObject req = new JSONObject();
        req.put("cstId", userId);
        req.put("sysId", sysId);
        req.put("md5sum", Md5Utils.hash(userId + key));
        try {
            String url = configService.selectConfigByKey("meta_waller_url");
            log.info("trc的地址：" + url);
            HttpResponse resultRep = HttpUtils.doPost(url + "/user/address", "", "POST", headers, null, req.toJSONString());
            if (resultRep.getStatusLine().getStatusCode() == 200) {
                /**读取服务器返回过来的json字符串数据**/
                String str = EntityUtils.toString(resultRep.getEntity());
                JSONObject resJson = JSONObject.parseObject(str);
                if (200 == resJson.getInteger("code")) {
                    log.info("创建钱包返回：" + resJson);
                    //保存到表里...........
                    //.......................................................
                    JSONObject dataJson = resJson.getJSONObject("data");
                    log.info("创建钱包成功：" + dataJson.toJSONString());
                    return dataJson.getString("cstAddress");
                } else {
                    log.info("创建钱包失败：" + resJson.getString("msg"));
                }


            } else {
                log.info("服务异常：" + EntityUtils.toString(resultRep.getEntity()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 查找全部钱包到hashset（注意缓存）
     */
    public Set<String> findAllUserAddressToHashSet() {
        // 首先尝试读取缓存
        Set<String> cachedAddresses = LocalCacheUtil.localCache.getIfPresent(RedisConstants.TRC_ADDRESS_SET_CACHE_NAME);
        if (cachedAddresses != null) {
            return cachedAddresses;
        }

        boolean locked = false;
        try {
            // 尝试获取写锁
            locked = redisLock.lock(RedisConstants.FIND_ALL_USER_ADDRESS_TO_HASH_SET_LOCK, 1);
            if (!locked) {
                cachedAddresses = LocalCacheUtil.localCache.getIfPresent(RedisConstants.TRC_ADDRESS_SET_CACHE_NAME);
                if (cachedAddresses != null) {
                    return cachedAddresses;
                }
            }

            // 双重检查，防止其他线程已经更新了缓存
            cachedAddresses = LocalCacheUtil.localCache.getIfPresent(RedisConstants.TRC_ADDRESS_SET_CACHE_NAME);
            if (cachedAddresses != null) {
                return cachedAddresses;
            }

            // 从数据库查询并更新缓存
            HashSet<String> all = new HashSet<>(trcCstaddressDao.findAllUserAddress());
            LocalCacheUtil.localCache.put(RedisConstants.TRC_ADDRESS_SET_CACHE_NAME, all);
            return all;
        } finally {
            if (locked) {
                redisLock.unlock(RedisConstants.FIND_ALL_USER_ADDRESS_TO_HASH_SET_LOCK);
            }
        }
    }

    @Override
    public TrcCstaddress findByCst(Long cstId, String sysId) {
        List<TrcCstaddress> byCstId = findByCstId(cstId, sysId);
        if (byCstId.size() > 0) {
            return byCstId.get(0);
        } else {
            return null;
        }
    }

    @Override
    public void save(TrcCstaddress trcCstaddress) {
        String encryptPrivateKey = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(trcCstaddress.getCstTrcPrivate());
        trcCstaddress.setCstTrcPrivate(encryptPrivateKey);
        trcCstaddressDao.save(trcCstaddress);
        LocalCacheUtil.localCache.invalidate(RedisConstants.TRC_ADDRESS_SET_CACHE_NAME);
    }

    @Override
    public AjaxResult trcCollect(String walletAddress, String trcCollectUrl, String coinType) {
        //进行归集

        String url = trcCollectUrl;
        String jsonBody = "{\"walletAddress\":\"" + walletAddress + "\"," +
                "\"coinType\":\"" + coinType + "\"}";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<>(jsonBody, headers);
        log.info("发送TRC归集请求,钱包地址:" + walletAddress);
        RestTemplate restTemplate = new RestTemplate();

        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                log.info("TRC归集成功");
                return AjaxResult.success("归集成功");
            } else {
                log.info("TRC归集失败，状态码：" + responseEntity.getStatusCode());
                return AjaxResult.error("归集失败");
            }
        } catch (RestClientException e) {
            log.error("TRC归集请求异常", e);
            return AjaxResult.error("请求异常");
        }
    }
}
