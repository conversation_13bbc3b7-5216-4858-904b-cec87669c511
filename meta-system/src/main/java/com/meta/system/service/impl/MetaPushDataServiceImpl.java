package com.meta.system.service.impl;

import com.meta.common.utils.SecurityUtils;
import com.meta.system.dao.MetaPushDataDao;
import com.meta.system.domain.groupkey.MetaPartnerAssetPoolKey;
import com.meta.system.domain.partner.MetaPartnerAssetPool;
import com.meta.system.domain.partner.MetaPushData;
import com.meta.system.dto.NotifyDto;
import com.meta.system.service.MetaPushDataService;
import com.meta.system.vo.MetaPushDataVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Random;

/**
 * <AUTHOR>
 * @date 2024/08/22/14:39
 */
@Service
public class MetaPushDataServiceImpl implements MetaPushDataService {

    @Autowired
    private MetaPushDataDao metaPushDataDao;

    @Autowired
    private MetaPartnerAssetPoolServiceImpl metaPartnerAssetPoolService;

    @Override
    public void save(MetaPushData metaPushData) {

        metaPushDataDao.save(metaPushData);
    }

    @Override
    public MetaPushData creatPashData(String sysId, String cardId, String userEmail, String apiUrl, String request, String type) {
        Random random = new Random();
        long timestamp = System.currentTimeMillis();
        int randomNum = random.nextInt(100000); // 生成一个5位数的随机数
        String requestNo = timestamp + String.format("%05d", randomNum);
        System.out.println("requestNo: " + requestNo);

        MetaPushData metaPushData = new MetaPushData();
        metaPushData.setRequestNo(requestNo);
        metaPushData.setSysId(sysId);
        metaPushData.setApiUrl(apiUrl);
        metaPushData.setRequest(request);
        metaPushData.setApiCode(type);
        metaPushData.setCardId(cardId);
        metaPushData.setUserEmail(userEmail);
        metaPushData.setCreateTime(new Date());
        metaPushData.setStatue("2");
        metaPushData.setNum(0);
        return metaPushData;
    }

    @Override
    public MetaPushData findById(Long id) {
        Optional<MetaPushData> op = metaPushDataDao.findById(id);
        if (op.isPresent()) {
            return op.get();
        }
        return null;
    }

    @Override
    public Page<MetaPushDataVo> getNotifyList(NotifyDto notifyDto) {

        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartner(userId);
        if(metaPartnerAssetPool!=null){
            notifyDto.setSysId(metaPartnerAssetPool.getSysId());
        }


        return metaPushDataDao.getNotifyList(notifyDto);
    }

    @Override
    public List<MetaPushData> findNoPush() {
        return metaPushDataDao.findNoPush();
    }

    @Override
    public void updateStatue(Long  id, String statue) {
        metaPushDataDao.updateStatue(id,statue);
    }

    @Transactional
    @Override
    public void updateFailStatue(long id, String s) {
        metaPushDataDao.updateFailStatue(id,s);
    }
}
