package com.meta.system.service;

import com.meta.system.domain.app.AllTransaction;
import com.meta.system.dto.AllTxnDto;
import com.meta.system.vo.RecentAddressVo;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/13/14:47
 */
public interface AllTransactionService {
    Page<AllTransaction> page(AllTxnDto txnDto);

    List<RecentAddressVo> recentAddress(Long userId, String type);
}
