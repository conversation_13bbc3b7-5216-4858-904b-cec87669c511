package com.meta.system.service.impl;

import com.meta.common.core.domain.entity.SysUser;
import com.meta.system.dao.app.AllTransactionDao;
import com.meta.system.domain.app.AllTransaction;
import com.meta.system.dto.AllTxnDto;
import com.meta.system.service.AllTransactionService;
import com.meta.system.service.ISysUserService;
import com.meta.system.vo.RecentAddressVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/13/14:49
 */
@Service
public class AllTransactionServiceImpl implements AllTransactionService {

    @Autowired
    private AllTransactionDao allTransactionDao;

    @Autowired
    private ISysUserService sysUserService;

    @Override
    public Page<AllTransaction> page(AllTxnDto txnDto) {
        return allTransactionDao.page(txnDto);
    }

    /**
     * 最近10个交易地址
     * @param userId
     * @return
     */
    @Override
    public List<RecentAddressVo> recentAddress(Long userId, String type) {
        List<String> list = allTransactionDao.recentAddress(userId,type);
        List<RecentAddressVo> result=new ArrayList<>();

        for(int i=0;i<list.size();i++){
            RecentAddressVo vo=new RecentAddressVo();
            String s=list.get(i);
            vo.setAddress(s);
            if(canConvertToLong(s)){
                SysUser  sysUser=sysUserService.selectUserById(Long.valueOf(s));
                if(sysUser!=null){

                    vo.setAddress(sysUser.getUserName());
                    vo.setType("email");
                }else {
                    vo.setType("chain_address");
                }
            }else {
                vo.setType("chain_address");
            }
            result.add(vo);
        }
        return  result;
    }


    public static boolean canConvertToLong(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        return str.matches("^-?\\d+$");
    }
}
