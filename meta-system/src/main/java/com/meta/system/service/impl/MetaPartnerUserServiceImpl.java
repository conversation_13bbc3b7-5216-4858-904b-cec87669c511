package com.meta.system.service.impl;

import com.meta.system.dao.MetaPartnerUserDao;
import com.meta.system.domain.partner.MetaPartnerUser;
import com.meta.system.service.MetaPartnerUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/05/30/14:46
 */
@Service
public class MetaPartnerUserServiceImpl implements MetaPartnerUserService {
    @Autowired
    private MetaPartnerUserDao metaPartnerUserDao;

    @Override
    public MetaPartnerUser findEmailOrPhone(String mobileNumber, String email, String mobilePrefix,String sysId) {
        return metaPartnerUserDao.findEmailOrPhone(mobileNumber, email,mobilePrefix,sysId);
    }

    @Override
    public void save(MetaPartnerUser metaPartnerUser) {
        metaPartnerUserDao.save(metaPartnerUser);
    }

    @Override
    public MetaPartnerUser findEmailAndSysId(String cardHolder, String sysId) {
        return metaPartnerUserDao.findEmailAndSysId(cardHolder,  sysId);
    }

    @Override
    public MetaPartnerUser findUid(String uid) {
        return metaPartnerUserDao.findUid(uid);
    }

    @Override
    @Transactional
    public void updatePartnerUser(Long id, String uid) {
        metaPartnerUserDao.updatePartnerUser( id,  uid);
    }
    @Override
    public MetaPartnerUser findData() {
        return metaPartnerUserDao.findData();
    }
}
