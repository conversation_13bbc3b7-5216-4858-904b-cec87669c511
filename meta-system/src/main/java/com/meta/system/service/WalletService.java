package com.meta.system.service;

import com.meta.common.core.domain.AjaxResult;
import com.meta.system.domain.Wallet;
import com.meta.system.domain.app.ExchangeRate;
import com.meta.system.domain.app.VccReq;
import com.meta.system.dto.UsdExchangeOutDto;
import com.meta.system.dto.VccReqDto;
import com.meta.system.dto.node.CodeExchangeDto;
import com.meta.system.dto.node.CodeTransferDto;
import com.meta.system.dto.node.NodeTransferDto;
import com.meta.system.vo.WalletVo;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;
import java.util.List;

public interface WalletService {
    /**
     * 保存
     * @param w
     */
    void save(Wallet w);

    /**
     *  通过userId查询
     * @param userId
     * @return
     */
    Wallet findByUserId(Long userId);

    /**
     * 操作个人账户
     * @param req
     * @return
     */
    AjaxResult update(VccReqDto req);

    /**
     * 操作公司账户
     * @param req
     * @return
     */
     AjaxResult updateComAcc(VccReqDto req);


    /**针对开卡USDT结算
     * 操作个人账户
     * @param req
     * @return
     */
    AjaxResult update2(VccReqDto req);

    /** 针对开卡USDT结算
     * 操作公司账户
     * @param req
     * @return
     */
    AjaxResult updateComAcc2(VccReqDto req);

    /**
     * 注册用户 初始化账户信息
     * @param userId 用户id
     * @param referrerId 推荐人ID
     */
    Wallet register(Long userId, Long referrerId);

    /**
     * 节点划转,转账
     * @param source 付款方
     * @param target 收款方
     */
    AjaxResult transfer(VccReqDto source,VccReqDto target);

    /**
     * 检测激活码余额是否足够
     * @param transfer
     * @return 是否足够
     */
    boolean checkCardBalance(NodeTransferDto transfer);

    /**
     * 检测激活码余额是否足够
     * @param transfer
     * @return 是否足够
     */
    boolean checkCardBalance(CodeTransferDto transfer);

    /**
     * 卡兑换
     * @param exchange 兑换数据
     * @return 兑换结果
     */
    AjaxResult exchangeCards(CodeExchangeDto exchange);

    /**
     * 节点升级
     */
    void upgradeNodeLevel(Long userId, String level);

    void upgradeNodeLevel(Long userId, String level, String oldLevel);

    /**
     * 重置交易密码
     * @param userId 用户id
     * @param newPwd 新密码
     */
    void resetTradePwd(Long userId,String newPwd);

    /**
     * 判断交易密码是否正确
     * @param password 交易密码
     * @return 交易密码是否正确
     */
    boolean checkTradePwd(String password);

    /**
     * 处理冻结的金额
     * @param userId
     * @param amount
     * @param freeze
     */
    void updateAmount(Long userId, BigDecimal amount, BigDecimal freeze);

    /**
     * 修改交易认证方式
     * @param tradeAuth 认证方式
     */
    void setTradeAuth(String tradeAuth);

    /**
     * 根据推荐人id 获取所有的LV4的username
     * @param userId
     * @return
     */
    List<String> findByReferrerIdAndLv4(Long userId);

    /**
     * 账号管理列表
     *
     * @param username    用户名
     * @param status      状态
     * @param startDate   注册开始时间
     * @param endDate     注册结束日期
     * @param nodeLevel
     * @param channelName
     * @param limitFlag
     * @return
     */
    Page<WalletVo> selectUserList(String nickName,String username, String status, String startDate, String endDate, String nodeLevel, String channelName, String limitFlag);


    void upgrade(Long userId, String nodeLevel);

    void upUid(Long userId, String uid);

    void wallerRecharge( VccReqDto dto, BigDecimal usdtAmount);

    Wallet findByGateUid(String uid);

    void updateCode(Long userId, BigDecimal usdBalance, BigDecimal freezeUsdBalacne, Integer silverActiveCodeNum, Integer silverActiveCodeUsed, Integer goldenActiveCodeNum, Integer goldenActiveCodeUsed, Integer goldenblackActiveCodeNum, Integer goldenblackActiveCodeUsed);

    AjaxResult updateLogisticsFee(VccReqDto dto);

    AjaxResult updateComLogisticsFee(VccReqDto dtoCom);

    void sendSliver(Long userId,String cardId);

    AjaxResult transferCode(VccReqDto source, VccReqDto target);

    /**
     * 操作个人账户
     * @param req
     * @return
     */
    AjaxResult updateCou(VccReqDto req);

    /**
     * 操作公司账户
     * @param req
     * @return
     */
    AjaxResult updateComAccCou(VccReqDto req);

    void dealAfterCode(Long userId, String cardId, Character value);

    AjaxResult usdExchangeOut(UsdExchangeOutDto usdExchangeOutDto);
}
