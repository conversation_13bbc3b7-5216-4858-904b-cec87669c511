package com.meta.system.service;

import com.meta.system.domain.SysIPBlacklist;

import java.util.List;

public interface SysIPBlacklistService {

    /**
     * 新增黑名单
     * @param sysIPBlacklist 黑名单
     */
    void insert(SysIPBlacklist sysIPBlacklist);

    /**
     * 根据id删除黑名单
     * @param id 主键
     */
    void deleteById(Long id);

    /**
     * 根据ip删除黑名单
     * @param ip ip地址
     */
    void deleteByIP(String ip);

    /**
     * 判断ip是否在黑名单中
     * @param ipAddr ip
     * @return 是否在黑名单中
     */
    boolean checkIP(String ipAddr);

    void deleteByIps(List<String> ips);
}
