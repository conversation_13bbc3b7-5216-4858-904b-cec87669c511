package com.meta.system.service.impl;

import com.meta.common.core.page.PageDomain;
import com.meta.common.core.page.TableSupport;
import com.meta.common.utils.SecurityUtils;
import com.meta.system.dao.UserCommissionDao;
import com.meta.system.domain.app.UserCommission;
import com.meta.system.service.ISysDictDataService;
import com.meta.system.service.UserCommissionService;
import com.meta.system.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class UserCommissionServiceImpl implements UserCommissionService {


    @Resource
    private UserCommissionDao userCommissionDao;


    @Autowired
    private ISysDictDataService iSysDictDataService;

    /**
     * 查询佣金分页数据
     *
     * @param commissio 查询条件
     * @return 分页数据
     */
    @Override
    public Page<UserCommission> getCommissionList(UserCommission commissio) {
        PageDomain pageReq = TableSupport.buildPageRequest();
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(pageReq.getPageNo(), pageReq.getPageSize(), sort);
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("userId", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("username", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("id", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("commisionType", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("txnId", ExampleMatcher.GenericPropertyMatchers.exact())
                .withIgnoreNullValues();
        Example<UserCommission> example = Example.of(commissio, matcher);
        return userCommissionDao.findAll(example, pageable);
    }

    /**
     * @param userId 用户id
     * @param id     佣金id
     * @return 根据id获取佣金详情
     */
    @Override
    public UserCommission getCommissionDetail(Long userId, Long id) {
        UserCommission commission = userCommissionDao.findById(id).orElse(null);
        if (userId != null) {
            if (commission != null && commission.getUserId().equals(userId)) {
                return commission;
            }
            return new UserCommission();
        } else {
            return commission;
        }
    }

    /**
     * 返佣统计
     *
     * @return 返佣统计
     */
    @Override
    public CommissionStat commissionStat() {
        CommissionStat commissionStat = userCommissionDao.commissionStat(SecurityUtils.getLoginUser().getUserId());
        if (commissionStat == null) {
            commissionStat = new CommissionStat();
        }
        commissionStat.paddingWithZeros();
        return commissionStat;
    }

    @Override
    public Page<UserCommissionVo> getCommissionList3(UserCommissionVo userCommission) {
        Page<UserCommissionVo> list3 = userCommissionDao.getCommissionList3(userCommission);
        for (UserCommissionVo vo : list3.getContent()) {
            vo.setCommissionDesc(iSysDictDataService.getLabel("coin_txn_type", vo.getCommissionType().toString()));
        }
        return userCommissionDao.getCommissionList3(userCommission);
    }

    @Override
    public Integer getCommissionCount(Long userId) {
        return userCommissionDao.getCommissionCount(userId);
    }

    @Override
    public UserTeamCommissionVo getCommissionTotal(Long userId) {
        return userCommissionDao.getCommissionTotal(userId);
    }

    @Override
    public Page<UserTeamCommissionVo> teamCommissionDetail(Long userId, TeamCommissionVo teamCommissionVo) {
        Page<UserTeamCommissionVo> page = userCommissionDao.teamCommissionDetail(userId, teamCommissionVo);
        return page;
    }
    /**
     * 佣金分类统计
     * @param userId
     * @return
     */
    @Override
    public List<CommissionVo> totalCommissin(Long userId) {
        return userCommissionDao.totalCommissin(userId);
    }
    /**
     * 团队总人数
     * @param userId
     * @return
     */
    @Override
    public CommissionVo totalNum(Long userId) {
        return userCommissionDao.totalNum(userId);
    }

    /**
     * 节点链接佣金列表
     *
     * @param vo
     * @return
     */
    @Override
    public Page<AllTeamCommissionVo> allTeamCommission(AllTeamCommissionVo vo) {
        return userCommissionDao.allTeamCommission(vo);
    }

    @Override
    public Page<UserCommissionVo> commissionDataList(UserCommissionVo userCommissionVo) {

        Page<UserCommissionVo> page = userCommissionDao.commissionDataList(userCommissionVo);
        return page;
    }
}
