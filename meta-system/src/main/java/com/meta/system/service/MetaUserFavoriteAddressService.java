package com.meta.system.service;

import com.meta.system.domain.app.MetaUserFavoriteAddress;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/09/17:31
 */
public interface MetaUserFavoriteAddressService {
    void save(MetaUserFavoriteAddress metaUserFavoriteAddress);

    MetaUserFavoriteAddress findAddress(String address,long userId);

    void delAddress(long id);

    Page<MetaUserFavoriteAddress> page(MetaUserFavoriteAddress metaUserFavoriteAddress);

    List<MetaUserFavoriteAddress> findTypeList(long userId,String type);


}
