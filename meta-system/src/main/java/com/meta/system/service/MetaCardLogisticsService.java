package com.meta.system.service;

import com.meta.common.core.domain.AjaxResult;
import com.meta.system.domain.app.MetaCardLogistics;
import com.meta.system.vo.CardLogisticsVo;
import org.springframework.data.domain.Page;

/**
 * <AUTHOR>
 * @date 2024/03/08/16:43
 */
public interface MetaCardLogisticsService {
    Page<CardLogisticsVo> page(String cstName, String cardHolder);

    void updateTrackingNumber(String trackingNumber,String type,  Integer id);


    void save(MetaCardLogistics metaCardLogistics);

    MetaCardLogistics selectByNumber(String cardId);

    AjaxResult getLogisticsInfo(String trackingNumber);

}
