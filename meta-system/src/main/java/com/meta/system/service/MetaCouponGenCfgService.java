package com.meta.system.service;

import com.meta.system.domain.app.MetaCouponGenCfg;
import com.meta.system.dto.CouponDto;
import com.meta.system.vo.MetaCouponGenCfgVo;
import org.springframework.data.domain.Page;

/**
 * <AUTHOR>
 * @date 2024/10/09/13:48
 */
public interface MetaCouponGenCfgService {
    Page<MetaCouponGenCfgVo> getCouponConfigList(CouponDto couponDto);

    void insertCouponConfig(MetaCouponGenCfg metaCouponGenCfg);

    void deleteCouponConfig(Integer id);

    void updateCouponConfig(MetaCouponGenCfg metaCouponGenCfg);
}
