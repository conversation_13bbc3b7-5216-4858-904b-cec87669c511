package com.meta.system.service;

import com.meta.system.domain.CreditCardLog;
import com.meta.system.dto.CardLogDto;
import com.meta.system.vo.CreditCardLogVo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * 卡日志 服务层
 *
 * <AUTHOR>
 */
public interface CreditCardLogService {
    /**
     *
     * @param log
     * @return
     */
    Page<CreditCardLog> list(CreditCardLog log);

    /**
     *
     * @param txnId
     * @return
     */
    CreditCardLog findByTransactionId(String txnId);

    CreditCardLog findById(String id);
    /**
     *
     * @param cardId
     * @return
     */
    List<String> findByCardId(String cardId);


    void save(CreditCardLog log);


    void saveAll(List<CreditCardLog> list);

    Page<CreditCardLogVo> selectData(String cardID, String transactionId, String transactionStartTime, String transactionEndTime, PageRequest of);

    Page<CreditCardLogVo> listForCardLog(CardLogDto vo);

    void updateStatue(Long id, String status);

    void updateTxnStatue(String id, String status);


    List<String> findPending(String cardId,String transactionStartTime,String status);

    List<CreditCardLog> findConfirm(String cardId,String transactionStartTime,String status);

    List<CreditCardLog> findData(String orderId);
}
