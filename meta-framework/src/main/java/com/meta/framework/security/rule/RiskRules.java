package com.meta.framework.security.rule;

import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.redis.RedisCache;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.ThreadPoolUtil;
import com.meta.common.utils.vcc.StringUtil;
import com.meta.system.constant.Constants;
import com.meta.system.domain.SysIPBlacklist;
import com.meta.system.domain.app.SysUserWhite;
import com.meta.system.email.SendEmail;
import com.meta.system.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/05/10/14:02
 */
@Slf4j
@Component
public class RiskRules {

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Resource
    private TradeRules tradeRules;

    @Autowired
    private RedisCache redisCache;

    @Resource
    private SysIPBlacklistService ipBlacklistService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    @Lazy
    private CreditCardService creditCardService;


    @Autowired
    private ISysLogininforService sysLogininforService;

    @Autowired
    private SysUserWhiteService sysUserWhiteService;

    @Autowired
    private SendEmail sendEmail;

    /**
     * 充值监控
     *
     * @param userId
     * @param amount
     */
    public boolean rechargeMonitoring(long userId, BigDecimal amount, String cardId) {
        boolean result = false;
        amount = amount.abs();
        log.info("amount:" + amount);

        //短时间内最大充值金额
        String recharge_max = sysConfigService.selectConfigByKey("recharge_max");
        log.info("recharge_max:" + recharge_max);
        //累计最大操作金额
        String total_operation_amount = sysConfigService.selectConfigByKey("total_operation_amount");
        log.info("total_operation_amount:" + total_operation_amount);
        //用户id
        String key1 = Constants.recharge_monitoring_key + userId;
        log.info("key1:" + key1);
        result = AmountRiskControl(amount, recharge_max, key1, userId);
        if (result) {
            return result;
        }

        String key2 = Constants.all_monitoring_key + userId;
        log.info("key2:" + key2);
        result = AmountRiskControl(amount, total_operation_amount, key2, userId);
        if (result) {
            return result;
        }
        return result;
    }

    /**
     * 卡片充值风控处理
     *
     * @param amount 金额
     * @param userId 用户id
     * @param cardId 卡 id
     * @return
     */
    public boolean cardRechargeRisk(BigDecimal amount, long userId, String cardId) {

        boolean result = false;
        String lockKey = "lock:" + Constants.user_rechage_key + userId;
        boolean locked = false;
        try {
            // 尝试获取锁，设置合理的超时时间
            locked = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 5, TimeUnit.SECONDS);
            if (!locked) {
                // 获取锁失败，可以重试或直接返回
                throw new RuntimeException("Operation failed");
            }
            SysUserWhite sy = sysUserWhiteService.findByUserId(userId);
            if (sy != null) {
                return result;
            }


            String cardKey = Constants.user_rechage_card_key + userId;
            log.info("cardKey:" + cardId);
            redisCache.addCardToSetWithExpire(cardKey, cardId, 10);
            int minute_count_1 = Integer.valueOf(dealStr(sysConfigService.selectConfigByKey("minute_count_1")));
            BigDecimal minute_amount_1 = new BigDecimal(dealStr(sysConfigService.selectConfigByKey("minute_amount_1")));

            //一分钟
            result = deal(result, userId, amount, minute_amount_1, minute_count_1, "1");
            if (result) {
                String countkey = Constants.user_rechage_key + userId + "_1" + "_count";
                String count = redisTemplate.opsForValue().get(countkey);
                String amountkey = Constants.user_rechage_key + userId + "_1" + "_amount";
                String amountAll = redisTemplate.opsForValue().get(amountkey);

                sendNotice(userId, "一分钟内充值次数：" + count + "，总充值金额：" + amountAll + "，累计充值大于" + minute_amount_1 + ",触发风控");
                return result;
            }

            int minute_count_5 = Integer.valueOf(dealStr(sysConfigService.selectConfigByKey("minute_count_5")));
            BigDecimal minute_amount_5 = new BigDecimal(dealStr(sysConfigService.selectConfigByKey("minute_amount_5")));
            //五分钟
            result = deal(result, userId, amount, minute_amount_5, minute_count_5, "5");
            if (result) {
                String countkey = Constants.user_rechage_key + userId + "_5" + "_count";
                String count = redisTemplate.opsForValue().get(countkey);
                String amountkey = Constants.user_rechage_key + userId + "_5" + "_amount";
                String amountAll = redisTemplate.opsForValue().get(amountkey);
                sendNotice(userId, "五分钟内充值次数：" + count + "，总充值金额：" + amountAll + "，累计充值大于" + minute_amount_5 + ",触发风控");
                return result;
            }

            int minute_count_10 = Integer.valueOf(dealStr(sysConfigService.selectConfigByKey("minute_count_10")));
            BigDecimal minute_amount_10 = new BigDecimal(dealStr(sysConfigService.selectConfigByKey("minute_amount_10")));
            //十分钟
            result = deal(result, userId, amount, minute_amount_10, minute_count_10, "10");
            if (result) {
                String countkey = Constants.user_rechage_key + userId + "_10" + "_count";
                String count = redisTemplate.opsForValue().get(countkey);
                String amountkey = Constants.user_rechage_key + userId + "_10" + "_amount";
                String amountAll = redisTemplate.opsForValue().get(amountkey);
                sendNotice(userId, "十分钟内充值次数：" + count + "，总充值金额：" + amountAll + "，累计充值大于" + minute_amount_10 + ",触发风控");
                return result;
            }

        } finally {
            if (locked) {
                // 释放锁
                redisTemplate.delete(lockKey);
            }
        }

        return result;
    }

    /**
     * @param result        结果
     * @param userId        用户id
     * @param amount        金额
     * @param minute_amount 分钟金额
     * @param minute_count  分钟次数
     * @param minute        分钟
     * @return
     */
    private boolean deal(boolean result, long userId, BigDecimal amount, BigDecimal minute_amount, int minute_count, String minute) {
        //判断1分钟
        if (minute_amount.compareTo(BigDecimal.ZERO) > 0) {

            String countkey = Constants.user_rechage_key + userId + "_" + minute + "_count";
            String amountkey = Constants.user_rechage_key + userId + "_" + minute + "_amount";


            //次数
            log.info("次数:" + countkey);
            redisCache.incrementWithExpiration(countkey, Long.valueOf(minute) * 60);
            String count = redisTemplate.opsForValue().get(countkey);
            log.info("amountkey:" + countkey + ",累计次数:" + count + ",设置的次数：" + minute_count);
            //金额
            amount = incrementAmount(amountkey, amount, Long.valueOf(minute));
            log.info("amountkey:" + amountkey + ",累计金额:" + amount + ",设置的金额：" + minute_amount);

            if (Integer.valueOf(count) >= minute_count) {

                if (amount.compareTo(minute_amount) >= 0) {
                    result = true;
                    //触发风控
                    restrictionProcessing(userId);
                }
            }
        }
        return result;
    }

    /**
     * 累加金额并智能管理TTL
     *
     * @param amountkey Redis键
     * @param amount    要累加的金额
     * @param minute    初始TTL(分钟)
     * @return 累加后的总金额
     */
    public BigDecimal incrementAmount(String amountkey, BigDecimal amount, long minute) {
        // 参数校验
        if (StringUtils.isEmpty(amountkey)) {
            throw new IllegalArgumentException("Redis键不能为空");
        }
        if (minute <= 0) {
            throw new IllegalArgumentException("过期时间必须为正数");
        }
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("金额不能为负数");
        }

        // Lua脚本保证原子性
        String script =
                "local current = tonumber(redis.call('GET', KEYS[1]) or 0)\n" +
                        "local newAmount = current + tonumber(ARGV[1])\n" +
                        "if current == 0 then\n" +
                        "   redis.call('SETEX', KEYS[1], ARGV[2], newAmount)\n" +
                        "else\n" +
                        "   local ttl = redis.call('TTL', KEYS[1])\n" +
                        "   if ttl > 0 then\n" +
                        "       redis.call('SETEX', KEYS[1], ttl, newAmount)\n" +
                        "   else\n" +
                        "       redis.call('SET', KEYS[1], newAmount)\n" +
                        "   end\n" +
                        "end\n" +
                        "return tostring(newAmount)";

        String result = redisTemplate.execute(
                new DefaultRedisScript<>(script, String.class),
                Collections.singletonList(amountkey),
                amount.toString(),
                String.valueOf(TimeUnit.MINUTES.toSeconds(minute))
        );

        log.info("金额更新: key={}, 累计金额={}", amountkey, result);
        return new BigDecimal(result);
    }

    /**
     * 提币监控
     *
     * @param userId
     * @param amount
     */

    public boolean widthdrawMonitoring(long userId, BigDecimal amount) {
        boolean result = false;
        amount = amount.abs();
        log.info("amount:" + amount);
        //短时间内最大提币金额
        String widthdraw_max = sysConfigService.selectConfigByKey("widthdraw_max");
        log.info("widthdraw_max:" + widthdraw_max);
        //累计最大操作金额
        String total_operation_amount = sysConfigService.selectConfigByKey("total_operation_amount");
        log.info("total_operation_amount:" + total_operation_amount);
        //用户id
        String key1 = Constants.widthdraw_monitoring_key + userId;
        log.info("key1:" + key1);
        result = AmountRiskControl(amount, widthdraw_max, key1, userId);
        if (result) {
            return result;
        }
        String key2 = Constants.all_monitoring_key + userId;
        log.info("key2:" + key2);
        result = AmountRiskControl(amount, total_operation_amount, key2, userId);
        if (result) {
            return result;
        }
        return result;

    }

    /**
     * 内转监控
     *
     * @param userId
     * @param amount
     */

    public boolean thransferMonitoring(long userId, BigDecimal amount) {
        boolean result = false;
        amount = amount.abs();
        log.info("amount:" + amount);
        //短时间内最大提币金额
        String thransfer_max = sysConfigService.selectConfigByKey("thransfer_max");
        log.info("thransfer_max:" + thransfer_max);
        //累计最大操作金额
        String total_operation_amount = sysConfigService.selectConfigByKey("total_operation_amount");
        log.info("total_operation_amount:" + total_operation_amount);
        //用户id
        String key1 = Constants.widthdraw_monitoring_key + userId;
        log.info("key1:" + key1);
        result = AmountRiskControl(amount, thransfer_max, key1, userId);
        if (result) {
            return result;
        }
        String key2 = Constants.all_monitoring_key + userId;
        log.info("key2:" + key2);
        result = AmountRiskControl(amount, total_operation_amount, key2, userId);
        if (result) {
            return result;
        }
        return result;
    }

    /**
     * @param amount 金额
     * @param max    最大金额
     * @param key    key
     * @param userId 用户id
     * @return
     */
    private boolean AmountRiskControl(BigDecimal amount, String max, String key, long userId) {

        boolean result = false;

        if (StringUtils.isNotEmpty(max)) {

            max = dealStr(max);
            BigDecimal decimal = new BigDecimal(max);
            if (decimal.compareTo(BigDecimal.ZERO) == 0) {
                return false;
            }

            // 使用Redis分布式锁
            String lockKey = "lock:" + key;
            boolean locked = false;
            try {
                // 尝试获取锁，设置合理的超时时间
                locked = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 5, TimeUnit.SECONDS);
                if (!locked) {
                    // 获取锁失败，可以重试或直接返回
                    throw new RuntimeException("Operation failed");
                }


                BigDecimal amt = BigDecimal.ZERO;
                if (!redisTemplate.hasKey(key)) {
                    String operation_minute = sysConfigService.selectConfigByKey("operation_minute");//操作时间
                    long minute = 0;
                    if (StringUtils.isNotEmpty(operation_minute)) {
                        String s = dealStr(operation_minute);

                        minute = Long.valueOf(s);
                    }
                    if (minute == 0) {
                        return false;
                    }

                    redisTemplate.opsForValue().set(key, String.valueOf(amount), Long.valueOf(minute), TimeUnit.MINUTES);
                } else {
                    String amtKey = redisTemplate.opsForValue().get(key);
                    if (StringUtils.isNotEmpty(amtKey)) {
                        amt = new BigDecimal(amtKey);
                    }
                }

                amount = amt.add(amount);
                log.info("key:" + key + ",累计金额:" + amount + ",设置的金额：" + max);
                // 更新Redis中的金额
                redisTemplate.opsForValue().set(key, String.valueOf(amount));
                if (amount.compareTo(new BigDecimal(max)) >= 0) {
                    //累计金额触发风控
                    restrictionProcessing(userId);
                    result = true;
                }

            } finally {
                if (locked) {
                    // 释放锁
                    redisTemplate.delete(lockKey);
                }
            }
        }
        return result;
    }

    /**
     * 风控处理
     *
     * @param userId 用户id
     */
    private void restrictionProcessing(long userId) {
        //冻结账户
//        tradeRules.setFreezeAccount(userId);
        //停止用户
        SysUser sysUser = sysUserService.selectUserByUserId(userId);
        sysUser.setStatus("1");
        sysUserService.updateUserStatus(sysUser);
        //强退用户
        String userIdKey = com.meta.common.constant.Constants.LOGIN_USERID_KEY + userId;
        String userKey = redisCache.getCacheObject(userIdKey);
        if (StringUtils.isNotEmpty(userKey)) {
            redisCache.deleteObject(userIdKey);
            redisCache.deleteObject(userKey);
        }
        //冻结卡片
        lockCard(userId);
        //将ip加入黑名单
        addIp(sysUser.getUserName());


    }

    /**
     * 发送邮件通知
     *
     * @param userId
     */
    public void sendNotice(long userId, String content) {
        SysUser sysUser = sysUserService.selectUserByUserId(userId);
        String userName = sysUser.getUserName();
        ThreadPoolUtil.execute(new Runnable() {
            @Override
            public void run() {
                sendEmail.rishMail(content + ":" + userName, "卡充值风控");
            }
        });

    }

    /**
     * 将ip加入黑名单
     */
    public void addIp(String userName) {

        ThreadPoolUtil.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    List<String> list = sysLogininforService.selectIplist(userName);
                    for (String ip : list) {
                        if (StringUtils.isNotEmpty(ip)) {
                            boolean b = ipBlacklistService.checkIP(ip);
                            if (!b) {
                                SysIPBlacklist sysIPBlacklist = new SysIPBlacklist();
                                sysIPBlacklist.setCreatedTime(LocalDateTime.now());
                                sysIPBlacklist.setUpdatedTime(LocalDateTime.now());
                                sysIPBlacklist.setIpAddress(ip);
                                sysIPBlacklist.setReason("充值触发风控将ip加入黑名单");
                                ipBlacklistService.insert(sysIPBlacklist);
                            }
                        }
                    }

                } catch (Exception e) {
                    log.error("异步添加IP黑名单失败: {}", e);
                    // 可在此处添加补偿逻辑（如重试或记录到文件）
                }
            }
        });

    }

//    public void addIp(String ipaddr) {
//        new Thread(() -> {
//            TransactionSynchronizationManager.clear();
//
//            try {
//                if (StringUtils.isNotEmpty(ipaddr) && !ipBlacklistService.checkIP(ipaddr)) {
//                    // ...创建entity...
//                    SysIPBlacklist entity = new SysIPBlacklist();
//                    entity.setIpAddress(ipaddr);
//                    entity.setReason("风控自动添加");
//                    entity.setCreatedTime(LocalDateTime.now());
//                    transactionTemplate.execute(status -> {
//                        ipBlacklistService.insert(entity);
//                        return null;
//                    });
//                }
//            } catch (Exception e) {
//                log.error("风控IP记录失败: {}", ipaddr, e);
//            }
//        }).start();
//    }


    /**
     * 获取Set集合并遍历（如果存在且非空）
     *
     * @param
     */
    private void lockCard(long userId) {
        String key = Constants.user_rechage_card_key + userId;
        List<String> failedCards = new ArrayList<>();

        try {
            Set<String> members = redisTemplate.opsForSet().members(key);
            if (members == null || members.isEmpty()) {
                return;
            }

            members.forEach(cardId -> {
                try {
                    creditCardService.lockCardAsync(cardId)
                            .exceptionally(e -> {
                                failedCards.add(cardId);
                                return null;
                            })
                            .join(); // 等待异步操作完成
                } catch (Exception e) {
                    failedCards.add(cardId);
                }
            });

            if (!failedCards.isEmpty()) {
                log.error("部分卡片冻结失败: {}", failedCards);
                // 可以添加重试逻辑或通知机制
            }
        } catch (Exception e) {
            log.error("处理卡片冻结时出错", e);
        }
    }


    /**
     * 去掉空字符
     *
     * @param str
     * @return
     */
    private String dealStr(String str) {
        str = str.replaceAll(" ", "");
        if (StringUtil.isEmpty(str)) {
            str = "0";
        }
        return str;
    }
}
