package com.meta.framework.security.rule;

import com.meta.common.constant.Constants;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.redis.RedisCache;
import com.meta.common.enums.LoginType;
import com.meta.common.exception.BaseException;
import com.meta.common.utils.DateUtils;
import com.meta.common.utils.ServletUtils;
import com.meta.common.utils.ip.AddressUtils;
import com.meta.common.utils.ip.IpUtils;
import com.meta.system.domain.SysIPBlacklist;
import com.meta.system.service.ISysUserService;
import com.meta.system.service.SysIPBlacklistService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 登陆风控规则类
 */
@Component
public class LoginRules extends BaseRule{
    private static final Logger log = LoggerFactory.getLogger(LoginRules.class);

    @Resource
    private RedisCache redisCache;

    @Resource
    private ISysUserService userService;

    @Resource
    private SysIPBlacklistService ipBlacklistService;

    // 登录规则开关
    @Value("${meta.loginRuleEnabled}")
    private boolean loginRuleEnabled;

    /**
     * 3.单个账号1天内密码错误超过5次，谷歌&邮箱验证码错误超过3次，锁定账号2小时; (自动解锁、找回密码解锁或后台解锁)
     */
    public boolean passwdAndEmailError(String key,LoginType type) {
        if (LoginType.PASSWD.equals(type)){
            // 获取密码错误次数
            Integer pass = redisCache.getCacheObject(Constants.LOGIN_FAIL_PASS_KEY + key);
            if (pass != null){
                return pass > 5;
            }
        }else if (LoginType.EMAIL.equals(type)){
            //获取验证码错误次数
            Integer email = redisCache.getCacheObject(Constants.LOGIN_FAIL_EMAIL_KEY + key);
            if (email != null){
                return email > 3;
            }
        } else if (LoginType.GOOGLE.equals(type)) {
            //谷歌身份验证错误次数
            Integer google = redisCache.getCacheObject(Constants.LOGIN_FAIL_GOOGLE_KEY + key);
            if (google != null){
                return google > 3;
            }
        }
        return false;
    }

    /**
     * 4.同一IP、多个账号1天内密码错误累计超过50次，永久锁定IP;
     */
    public void sameIpPasswdError(String ipAddress) {
        Integer count = redisCache.getCacheObject(Constants.LOGIN_FAIL_SAME_IP_KEY + ipAddress);
        if (count == null) {
            count = 0;
        }else if (count >= 50) {
            // 同一IP登录错误次数超过50次,锁定IP
            ipBlacklistService.insert(new SysIPBlacklist(ipAddress,"同一IP、多个账号1天内密码错误累计超过50次，永久锁定IP"));
            redisCache.deleteObject(Constants.LOGIN_FAIL_SAME_IP_KEY+ipAddress);
            return;
        }
        redisCache.setCacheObject(Constants.LOGIN_FAIL_SAME_IP_KEY+ipAddress,count+1 ,DateUtils.getMinutes(), TimeUnit.MINUTES);
    }


    /**
     * 登录次数统计
     */
    public void loginCount(String account, LoginType loginType) {
        if (!loginRuleEnabled) {
            return;
        }
        Integer count;
        switch (loginType){
            case PASSWD:
                count = redisCache.getCacheObject(Constants.LOGIN_FAIL_PASS_KEY + account);
                if (count == null) {
                    count = 0;
                }
                if (count <= 5) {
                    // 一天时间内同一账号密码错误
                    redisCache.setCacheObject(Constants.LOGIN_FAIL_PASS_KEY+account,count+1 ,  DateUtils.getMinutes(), TimeUnit.MINUTES);
                }else {
                    //当一天时间内累计错误5次以上 锁定账号两个小时
                    redisCache.setCacheObject(Constants.LOGIN_FAIL_PASS_KEY+account,count+1 , Constants.ACCOUNT_LOCK_TIME, TimeUnit.MINUTES);
                    userService.updateLoginLimitExpired(account,DateUtils.addHours(new Date(), 2));
                    log.info("登录用户：{} 已锁定,请用其他方式登录", account);
                    throw new BaseException("对不起，您的账号：" + account + " 已锁定,请用其他方式登录");
                }
                break;
            case EMAIL:
                count = redisCache.getCacheObject(Constants.LOGIN_FAIL_EMAIL_KEY + account);
                if (count == null) {
                    count = 0;
                }
                if (count <= 3) {
                    // 一天时间内同一邮箱验证码错误
                    redisCache.setCacheObject(Constants.LOGIN_FAIL_EMAIL_KEY+account,count+1 ,  DateUtils.getMinutes(), TimeUnit.MINUTES);
                }else {
                    //当一天时间内累计错误3次以上 锁定账号两个小时
                    redisCache.setCacheObject(Constants.LOGIN_FAIL_EMAIL_KEY+account,count+1 , Constants.ACCOUNT_LOCK_TIME, TimeUnit.MINUTES);
                    userService.updateLoginLimitExpired(account,DateUtils.addHours(new Date(), 2));
                    log.info("登录用户：{} 已锁定,请用其他方式登录", account);
                    throw new BaseException("对不起，您的账号：" + account + " 已锁定,请用其他方式登录");
                }
                break;
            case GOOGLE:
                count = redisCache.getCacheObject(Constants.LOGIN_FAIL_GOOGLE_KEY + account);
                if (count == null) {
                    count = 0;
                }
                if (count <= 3) {
                    redisCache.setCacheObject(Constants.LOGIN_FAIL_GOOGLE_KEY+account,count+1 ,  DateUtils.getMinutes(), TimeUnit.MINUTES);
                }else {
                    redisCache.setCacheObject(Constants.LOGIN_FAIL_GOOGLE_KEY+account,count+1 , Constants.ACCOUNT_LOCK_TIME, TimeUnit.MINUTES);
                    userService.updateLoginLimitExpired(account,DateUtils.addHours(new Date(), 2));
                    log.info("登录用户：{} 已锁定,请用其他方式登录", account);
                    throw new BaseException("对不起，您的账号：" + account + " 已锁定,请用其他方式登录");
                }
        }
    }

    /**
     * 检测登录城市是否发生变化
     */
    public boolean checkCityChange(SysUser user) {
        String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        return user.getLocation() != null && !user.getLocation().equals(AddressUtils.getRealAddressByIP(ip));
    }

    /**
     * 检测登录设备是否发生变化
     */
    public boolean checkDriveChange(SysUser user) {
        String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        return user.getLocation() != null && !user.getLocation().equals(AddressUtils.getRealAddressByIP(ip));
    }


    public void check(SysUser user, LoginType loginType,String account) {
        if (loginRuleEnabled){
            if (passwdAndEmailError(account,loginType)) {
                log.info("登录用户：{} 已锁定,请用其他方式登录", account);
                throw new BaseException("对不起，您的账号：" + account + " 已锁定,请用其他方式登录");
            }

            if(checkCityChange(user) && loginType.equals(LoginType.PASSWD)) {
                log.info("登录城市发生变更,请使用邮箱或者谷歌验证码登录!");
                throw new BaseException("登录城市发生变更,请使用邮箱或者谷歌验证码登录!");
            }

            if(checkDriveChange(user) && loginType.equals(LoginType.PASSWD)) {
                log.info("登录设备发生变更,请使用邮箱或者谷歌验证码登录!");
                throw new BaseException("登录设备发生变更,请使用邮箱或者谷歌验证码登录!");
            }
        }
    }
}
