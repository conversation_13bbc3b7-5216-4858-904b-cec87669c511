package com.meta.framework.security.rule;

import com.meta.common.constant.Constants;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.redis.RedisCache;
import com.meta.common.utils.DateUtils;
import com.meta.common.utils.SecurityUtils;
import com.meta.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 交易规则
 */
@Component
public class TradeRules extends BaseRule{
    private static final Logger log = LoggerFactory.getLogger(TradeRules.class);

    @Resource
    private RedisCache redisCache;

    @Resource
    private ISysUserService userService;

    // 登录规则开关
    @Value("${meta.tradeRuleEnabled}")
    private boolean tradeRuleEnabled;



    /**
     * 交易密码输⼊错误超过3次，冻结账号资⾦交易24⼩时，冻结期间可联系客服⼈
     * 员进⾏解冻
     * @return
     */
    public boolean tradeErrorCount() {
        if (tradeRuleEnabled) {
            SysUser sysUser = userService.selectUserById(SecurityUtils.getLoginUser().getUserId());
            if (sysUser.getTradeLimitExpired() != null && sysUser.getTradeLimitExpired().after(new Date())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断用户是否被冻结
     * @return
     */
    public boolean tradeFrezeCount() {

        SysUser sysUser = userService.selectUserById(SecurityUtils.getLoginUser().getUserId());
        if (sysUser.getTradeLimitExpired() != null ) {
            Date date = sysUser.getTradeLimitExpired();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

            // 将目标日期转换为字符串
            String targetDateString = "2999-01-01";

            // 如果date不为null，将其格式化为字符串并进行比较
            if (date != null && sdf.format(date).equals(targetDateString)) {
                return true;
            } else {
                return false;
            }

        }
        return false;

    }

    public boolean tradeErrorAdd() {
        if (tradeRuleEnabled){
            List<ExpiredTime> list = redisCache.getCacheObject(Constants.TRADE_ERROR_KEY + SecurityUtils.getLoginUser().getUserId());
            if (list == null) {
                list = new ArrayList<>();
            }else {
                list = deleteExpired(list);
            }
            if (list.size() == 5){
                list.add(new ExpiredTime(LocalDateTime.now().plusMinutes(1440)));
                userService.updateTradeLimitExpired(SecurityUtils.getLoginUser().getUserId(), DateUtils.addDays(new Date(), 1));
            }else {
                list.add(new ExpiredTime(LocalDateTime.now().plusMinutes(30)));
            }
            redisCache.deleteObject(Constants.TRADE_ERROR_KEY + SecurityUtils.getLoginUser().getUserId());
            redisCache.setCacheObject(Constants.TRADE_ERROR_KEY + SecurityUtils.getLoginUser().getUserId(),list ,1440, TimeUnit.MINUTES);
            return list.size() > 5;
        }
        return false;
    }
    // 30分钟内连续提现不超过三笔(包括失败交易)

    public boolean continuousPayoutAdd() {
        if (tradeRuleEnabled){
            List<ExpiredTime> list = redisCache.getCacheObject(Constants.WALLET_CONTINUOUS_PAYOUTS_30 + SecurityUtils.getUsername());
            if (list == null) {
                list = new ArrayList<>();
            }else {
                deleteExpired(list);
            }

            list.add(new ExpiredTime(LocalDateTime.now().plusMinutes(30)));
            redisCache.deleteObject(Constants.WALLET_CONTINUOUS_PAYOUTS_30 + SecurityUtils.getUsername());
            redisCache.setCacheObject(Constants.WALLET_CONTINUOUS_PAYOUTS_30 + SecurityUtils.getUsername(),list ,30, TimeUnit.MINUTES);
            return list.size() > 5;
        }
        return false;
    }

    public boolean continuousPayoutCount() {
        if (tradeRuleEnabled) {
            List<ExpiredTime> list = redisCache.getCacheObject(Constants.WALLET_CONTINUOUS_PAYOUTS_30 + SecurityUtils.getUsername());
            if (list == null) {
                return false;
            }else{
                deleteExpired(list);
            }
            return list.size() > 5;
        }
        return false;
    }

    // 5分钟内两笔提现金额一样,第二笔交易提示并禁止
    public boolean withdrawalAmount(BigDecimal amount) {
        BigDecimal lastAmount = redisCache.getCacheObject(Constants.WALLET_LAST_WITHDRAWAL_AMOUNT + SecurityUtils.getUsername());
        if (lastAmount == null){
            return false;
        }
        return lastAmount.compareTo(amount) == 0;
    }

    public void setLastWithdrawalAmount(BigDecimal amount) {
        redisCache.setCacheObject(Constants.WALLET_LAST_WITHDRAWAL_AMOUNT + SecurityUtils.getUsername(),amount,5, TimeUnit.MINUTES);
    }

    // 非正常营业时间(22时-8时),提现次数补充超过三次(包括失败交易)

    // 非正常营业时间(22时-8时),提现金额不超过单日限额50%

    // 单笔提现交易金额不超过单日限额的50%

    //账户冻结
    public void setFreezeAccount(Long userId){
        List<ExpiredTime> list = redisCache.getCacheObject(Constants.TRADE_ERROR_KEY + userId);
        if (list == null) {
            list = new ArrayList<>();
        }else {
            list = deleteExpired(list);
        }
        if (list.size() == 5){
            list.add(new ExpiredTime( LocalDateTime.of(2999, 1, 1, 0, 0)));
            ZoneId zoneId = ZoneId.systemDefault();
            Date date = Date.from(LocalDateTime.of(2999, 1, 1, 0, 0).atZone(zoneId).toInstant());
            userService.updateTradeLimitExpired(userId,  date);
        }else {
            list.add(new ExpiredTime( LocalDateTime.of(2999, 1, 1, 0, 0)));
            ZoneId zoneId = ZoneId.systemDefault();
            Date date = Date.from(LocalDateTime.of(2999, 1, 1, 0, 0).atZone(zoneId).toInstant());
            userService.updateTradeLimitExpired(userId,  date);
        }
        // 目标日期：2999年1月1日
        LocalDateTime futureDate = LocalDateTime.of(2999, 1, 1, 0, 0);

        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 计算时间差（毫秒）
        long durationMillis = futureDate.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() -
                now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

        // 将毫秒转换为秒
        long durationSeconds = durationMillis / 1000;
        redisCache.deleteObject(Constants.TRADE_ERROR_KEY + userId);
        redisCache.setCacheObject2(Constants.TRADE_ERROR_KEY + userId,list ,durationSeconds, TimeUnit.SECONDS);

    }

}

