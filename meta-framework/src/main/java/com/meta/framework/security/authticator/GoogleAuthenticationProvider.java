package com.meta.framework.security.authticator;

import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

/*** 自定义短信登录身份认证*/
public class GoogleAuthenticationProvider implements AuthenticationProvider {
    private UserDetailsService userDetailsService;

    public GoogleAuthenticationProvider(UserDetailsService userDetailsService) {
        setUserDetailsService(userDetailsService);
    }

    /*** 重写 authenticate方法，实现身份验证逻辑。*/
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        GoogleAuthenticationToken authenticationToken = (GoogleAuthenticationToken) authentication;
        String username = (String) authenticationToken.getPrincipal();

        // 委托 UserDetailsService 查找系统用户
        UserDetails userDetails = userDetailsService.loadUserByUsername(username);

        // 鉴权成功，返回一个拥有鉴权的 AbstractAuthenticationToken
        GoogleAuthenticationToken authenticationResult = new GoogleAuthenticationToken(userDetails, userDetails.getAuthorities());
        authenticationResult.setDetails(authenticationToken.getDetails());
        return authenticationResult;
    }

    /*** 重写supports方法，指定此 AuthenticationProvider 仅支持短信验证码身份验证。*/
    @Override
    public boolean supports(Class<?> authentication) {
        return GoogleAuthenticationToken.class.isAssignableFrom(authentication);
    }

    public UserDetailsService getUserDetailsService() {
        return userDetailsService;
    }

    public void setUserDetailsService(UserDetailsService userDetailsService) {
        this.userDetailsService = userDetailsService;
    }
}