package com.meta.framework.web.service.app;

import com.meta.common.core.domain.model.LoginUser;
import com.meta.common.enums.SecurityAuthType;
import com.meta.common.utils.DateUtils;
import com.meta.common.utils.SecurityUtils;
import com.meta.framework.web.service.TokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service("apps")
public class PerSecurityAuthService {

    /**
     * 验证接口是否需要 输入交易密码,才可访问
     * @param once 是否是一次性的
     * @param minute 有效时间
     */

    @Autowired
    private TokenService tokenService;
    public boolean authenticated(SecurityAuthType authType){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(loginUser.isAuthStatus(authType)){
            // 访问一次就删除
            setLoginUser(loginUser,authType,-999);
            tokenService.setLoginUser(loginUser);
            return true;
        }
        return false;
    }
    public void setAuth(SecurityAuthType authType){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        setLoginUser(loginUser,authType,10);
        tokenService.setLoginUser(loginUser);
    }

    /**
     *
     * @param loginUser 用户登录信息
     * @param authType 认证类型
     * @param expireTime 过期时间(秒数)
     */
    public void setLoginUser(LoginUser loginUser,SecurityAuthType authType,Integer expireTime){
        LocalDateTime time = DateUtils.getNowDate().plusSeconds(expireTime);
        if (SecurityAuthType.TRADE.equals(authType)){
            loginUser.setTradeAuthExpireTime(time);
        }
        if (SecurityAuthType.FACE.equals(authType)){
            loginUser.setFaceAuthExpireTime(time);
        }
        if (SecurityAuthType.ALL.equals(authType)){
            loginUser.setTradeAuthExpireTime(time);
            loginUser.setFaceAuthExpireTime(time);
        }
    }

    public static void main(String[] args) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        System.out.println(passwordEncoder.encode("123456"));
    }

}
