package com.meta.framework.web.service;

import com.meta.common.constant.Constants;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.domain.model.LoginUser;
import com.meta.common.core.redis.RedisCache;
import com.meta.common.utils.ServletUtils;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.ip.AddressUtils;
import com.meta.common.utils.ip.IpUtils;
import com.meta.common.utils.uuid.IdUtils;
import com.meta.framework.web.domain.server.LoginInfo;
import com.meta.system.domain.CreditCard;
import com.meta.system.domain.log.TxnDtlCode;
import com.meta.system.domain.log.TxnDtlUSD;
import com.meta.system.dto.CardUpdateLogDto;
import eu.bitwalker.useragentutils.UserAgent;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 *  存储过程处理服务类
 *
 * <AUTHOR>
 */
public interface DealAfterService {
    /**
     * 开卡成功后的业务逻辑
     * @param cardId
     * @param dtlUSD
     * @param dtlUSDCom
     * @param code
     */
     void dealAfterCreatCard(String cardId, TxnDtlUSD dtlUSD, TxnDtlUSD dtlUSDCom, TxnDtlCode code );

    /**
     * 充值成功后的业务逻辑
     * @param cardId
     * @param ar
     * @param transactionId
     */
    void dealAfterRecharge(String cardId, AjaxResult ar, String transactionId);

    /**
     * 提现成功后的业务逻辑
     * @param cardId
     * @param ar
     * @param transactionId
     */
    void dealAfterWithdraw(String cardId,AjaxResult ar,String transactionId);

    void dealAfterWithdrawMoonbank(String cardId,AjaxResult ar,String transactionId);

    void queryByTransactionPage(CardUpdateLogDto dto);

    void creditCardMoobankDeal(String cardId, Long userId);

    void creditCardMoobankDealCoin(String cardId, Long userId);

    void dealAfterWithdrawMoonbankSuccess(String cardId, String txnId);

    void dealAfterWithdrawMoonbankFail(String cardId, String txnId);


    void dealAfterWithdrawSuccessMerchants(String cardId, String txnId,String sysId, String moonbankUid);

    void dealAfterWithdrawFailMerchantsFail(String cardId, String txnId,String sysId, String moonbankUid);
}
