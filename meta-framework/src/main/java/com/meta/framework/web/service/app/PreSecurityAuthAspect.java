package com.meta.framework.web.service.app;

import com.meta.common.enums.SecurityAuthType;
import lombok.Data;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Order(1)
public class PreSecurityAuthAspect {


    @Autowired
    private PerSecurityAuthService perSecurityAuthService;

    @Before("@annotation(preSecurityAuth)")
    public void preSecurityAuth(PreSecurityAuth preSecurityAuth) {
        SecurityAuthType type = preSecurityAuth.type();
        boolean result = perSecurityAuthService.authenticated(type);
        if (!result){
            throw new PreSecurityAuthException(type);
        }
    }

    // 自定义异常类
    @Data
    public class PreSecurityAuthException extends RuntimeException {
        private SecurityAuthType securityAuthType;
        public PreSecurityAuthException(SecurityAuthType securityAuthType) {
            this.securityAuthType = securityAuthType;
        }
    }
}
