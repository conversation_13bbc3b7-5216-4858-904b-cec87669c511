package com.meta.framework.web.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meta.common.constant.Constants;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.enums.app.*;
import com.meta.common.enums.vcc.ApiCode;
import com.meta.common.enums.vcc.CardStatus;
import com.meta.common.utils.DateUtils;
import com.meta.common.utils.JsonUtils;
import com.meta.common.utils.ProcedureUtils;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.uuid.UniqueIDGenerator;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.system.config.MoonbankConfig;
import com.meta.system.dao.CreditCardDao;
import com.meta.system.domain.CreditCard;
import com.meta.system.domain.CreditCardLog;
import com.meta.system.domain.Wallet;
import com.meta.system.domain.app.VccReq;
import com.meta.system.domain.log.TxnDtlCode;
import com.meta.system.domain.log.TxnDtlUSD;
import com.meta.system.domain.partner.MetaPartnerAssetPool;
import com.meta.system.domain.partner.MetaPartnerTxnDtl;
import com.meta.system.domain.vcc.VccCreateCardResp;
import com.meta.system.dto.CardUpdateLogDto;
import com.meta.system.dto.VccReqDto;
import com.meta.system.email.SendEmail;
import com.meta.system.kun.service.KunService;
import com.meta.system.moonbank.models.ApiResponse;
import com.meta.system.moonbank.util.MoonbankEncryptUtil;
import com.meta.system.moonbank.util.MoonbankUtil;
import com.meta.system.service.*;
import com.meta.system.vcc.YJCardUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * 存储过程处理服务类
 *
 * <AUTHOR>
 */
@Service
public class DealAfterServiceImpl implements DealAfterService {
    Logger logger = LoggerFactory.getLogger(DealAfterServiceImpl.class);

    @Autowired
    private YJCardUtils yjCardUtils;

    @Autowired
    private CreditCardDao creditCardDao;

    @Autowired
    private WalletService walletService;

    @Autowired
    private TxnService txnService;

    @Autowired
    private ProcedureUtils procedureUtils;
    @Autowired
    private SendEmail sendEmail;

    @Autowired
    private CreditCardLogService creditCardLogService;

    @Autowired
    private CreditCardService creditCardService;

    @Autowired
    private MoonbankUtil moonbankUtil;

    @Autowired
    private CoinTxnDtlService coinTxnDtlService;

    @Autowired
    private KunService kunService;

    @Autowired
    private MetaPartnerTxnDtlService metaPartnerTxnDtlService;

    @Autowired
    private MetaPartnerAssetPoolService metaPartnerAssetPoolService;

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public void dealAfterCreatCard(String cardId, TxnDtlUSD dtlUSD, TxnDtlUSD dtlUSDCom, TxnDtlCode code) {
        //1.调用vcc接口
        boolean circle = false;//循环的查询的判断
        VccReq r = new VccReq();
        r.setCardId(cardId);
        int i = 0;
        try {
            while (i < 3 && !circle) {
                Thread.sleep(2000);
                r.setRequestNo(UniqueIDGenerator.generateID());
                VccCreateCardResp res = yjCardUtils.card(r, ApiCode.QueryCardDetail); //查询开卡结果
                //2.返回结果
                if (res != null && res.getCode().equals("SUCCESS")) {
                    JSONObject j = JSONObject.parseObject(res.getRes());
                    if (j.containsKey("cardStatus")) {
                        CreditCard cv = creditCardDao.findByCardId(cardId);
                        boolean cardOpen = false;//判断是否需要返佣
                        if (cv.getCardStatus().equals(CardStatus.PROCESSING.getValue())) {
                            cardOpen = true;
                        }
                        //1.处理卡信息
                        updateCard(res.getRes(), cv);
                        if (dtlUSD == null && code == null) {
                            //两个同时为空，只做余额更新
                        } else {
                            //2.处理日志信息
                            //开卡成功
                            if (j.getString("cardStatus").equals(CardStatus.USED.getValue())) {
                                //2.处理TxnDtlUSD
                                if (dtlUSD != null) {
                                    VccReqDto req = new VccReqDto(cv.getUserId(), AlgorithmType.SUB, FreezeType.FREE_FREEZE, dtlUSD);
                                    walletService.update(req);
                                    VccReqDto reqComm = new VccReqDto(null, AlgorithmType.ADD, FreezeType.NO, dtlUSDCom);
                                    reqComm.setUsdBalance(dtlUSDCom.getUsdBalance().add(dtlUSDCom.getTxnAmount().abs()));
                                    walletService.updateComAcc(reqComm);
                                } else {
                                    code.setTxnStatus(TxnStatus.SUCCESS.getValue());
                                    txnService.dealTxnForCode(code);
                                }
                            }
                            //开卡失败
                            if (j.getString("cardStatus").equals(CardStatus.OPEN_CARD_FAIL.getValue())) {
                                if (dtlUSD != null) {
                                    VccReqDto req = new VccReqDto(cv.getUserId(), AlgorithmType.ADD, FreezeType.FALLBACK, dtlUSD);
                                    req.setTxnProduct(cardId);
                                    walletService.update(req);
                                    VccReqDto reqComm = new VccReqDto(null, AlgorithmType.SUB, FreezeType.NORMAL_FALLBACK, dtlUSDCom);
                                    reqComm.setTxnProduct(cardId);
                                    walletService.updateComAcc(reqComm);
                                } else {
                                    code.setTxnStatus(TxnStatus.FAIL.getValue());
                                    txnService.dealTxnForCode(code);

                                }
                            }
                            // 开卡返佣
                            if (dtlUSD != null && cardOpen && code == null) {
                                String flag = procedureUtils.callStoredProcedure(dtlUSD.getId(), Constants.procedure.p_rel_card_open_benefit);
                                if ("-1".equals(flag)) {
                                    // 发邮件通知管理员
                                    sendEmail.errorMail("开卡返佣返佣失败,交易ID:" + dtlUSD.getId());
                                }
                            }
                        }
                        circle = true;
                    }
                }
                i++;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public void dealAfterWithdraw(String cardId, AjaxResult ar, String transactionId) {
        int i = 0;
        boolean circle = false;//循环的查询的判断
        VccReq r = new VccReq();
        r.setCardId(cardId);
        try {
            while (i < 3 && !circle) {
                Thread.sleep(2000);
                r.setRequestNo(UniqueIDGenerator.generateID());
                VccCreateCardResp res = yjCardUtils.card(r, ApiCode.QueryTransactionPage);  //1.调用vcc接口，查询开卡结果
                //2.返回结果
                if (res != null && res.getCode().equals("SUCCESS")) {
                    JSONObject j = JSON.parseObject(res.getRes());
                    List<CreditCardLog> list = JsonUtils.string2Obj(j.getString("records"), List.class, CreditCardLog.class);
                    if (list != null && !list.isEmpty()) {
                        CreditCardLog ccl = list.get(0);
                        if (!ccl.getTransactionStatus().equals(TxnStatus.PADDING.getName())) {
                            circle = true;
                            CreditCardLog log = creditCardLogService.findByTransactionId(transactionId);
                            //1.更新creditcardlog
                            ccl.setId(log.getId());
                            creditCardLogService.save(ccl);
                            //2.更新钱包余额
                            queryCardDetail(cardId, null);
                            //成功
                            if (ccl.getTransactionStatus().equals(TxnStatus.SUCCESS.getName())) {
                                //2.处理TxnDtlUSD
                                //A.个人t提现
                                TxnDtlUSD dtlUSD = (TxnDtlUSD) ar.get("dtlUSD");
                                VccReqDto req = new VccReqDto(dtlUSD.getUserId(), AlgorithmType.ADD, FreezeType.FREE_FREEZE, dtlUSD);
                                walletService.update(req);
                                //B.公司提现
                                TxnDtlUSD dtlUSDComm = (TxnDtlUSD) ar.get("dtlUSDComm");
                                VccReqDto reqComm = new VccReqDto(null, AlgorithmType.SUB, FreezeType.NO, dtlUSDComm);
                                reqComm.setUsdBalance(dtlUSDComm.getUsdBalance().add(dtlUSDComm.getTxnAmount()));
                                reqComm.setTxnId(dtlUSDComm.getRelaTransactionid());
                                walletService.updateComAcc(reqComm);
                            }
                            //失败
                            else if (ccl.getTransactionStatus().equals(TxnStatus.FAIL.getName())) {
                                //2.处理TxnDtlUSD
                                //A.个人提现
                                TxnDtlUSD dtlUSD = (TxnDtlUSD) ar.get("dtlUSD");
                                VccReqDto req = new VccReqDto(dtlUSD.getUserId(), AlgorithmType.SUB, FreezeType.FALLBACK, dtlUSD);
                                walletService.update(req);
                                //B.公司提现
                                TxnDtlUSD dtlUSDComm = (TxnDtlUSD) ar.get("dtlUSDComm");
                                VccReqDto reqComm = new VccReqDto(null, AlgorithmType.ADD, FreezeType.NORMAL_FALLBACK, dtlUSDComm);
                                walletService.updateComAcc(reqComm);
                            }
                        }
                    }

                }
                i++;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public void dealAfterWithdrawMoonbank(String cardId, AjaxResult ar, String transactionId) {
        TxnDtlUSD dtlUSD = (TxnDtlUSD) ar.get("dtlUSD");
        VccReqDto req = new VccReqDto(dtlUSD.getUserId(), AlgorithmType.ADD, FreezeType.FREE_FREEZE, dtlUSD);
        walletService.update(req);
        //B.公司提现
        TxnDtlUSD dtlUSDComm = (TxnDtlUSD) ar.get("dtlUSDComm");
        VccReqDto reqComm = new VccReqDto(null, AlgorithmType.SUB, FreezeType.NO, dtlUSDComm);
        reqComm.setUsdBalance(dtlUSDComm.getUsdBalance().add(dtlUSDComm.getTxnAmount()));
        reqComm.setTxnId(dtlUSDComm.getRelaTransactionid());
        walletService.updateComAcc(reqComm);
    }

    /**
     * 销卡成功处理
     *
     * @param cardId
     * @param txnId
     */
    @Override
    public void dealAfterWithdrawMoonbankSuccess(String cardId, String txnId) {
        List<TxnDtlUSD> dtlUsdlist = txnService.findAllByCardIdAndStatusAndTransactionId(cardId, String.valueOf(TxnStatus.PADDING.getValue()), txnId);
        TxnDtlUSD dtlUSD = null;
        TxnDtlUSD dtlUSDComm = null;
        if (dtlUsdlist != null && !dtlUsdlist.isEmpty()) {
            for (TxnDtlUSD d : dtlUsdlist) {
                if (d.getTxnType().equals(TxnType.CARD_WITHDRAW_D.getValue())) {
                    dtlUSD = d;
                }
                if (d.getTxnType().equals(TxnType.CARD_WITHDRAW_C.getValue())) {
                    dtlUSDComm = d;
                }

            }
        }
        if (dtlUSD != null) {
            VccReqDto req = new VccReqDto(dtlUSD.getUserId(), AlgorithmType.ADD, FreezeType.FREE_FREEZE, dtlUSD);
            walletService.update(req);
        }
        if (dtlUSDComm != null) {
            //B.公司提现
            VccReqDto reqComm = new VccReqDto(null, AlgorithmType.SUB, FreezeType.NO, dtlUSDComm);
            reqComm.setUsdBalance(dtlUSDComm.getUsdBalance().add(dtlUSDComm.getTxnAmount()));
            reqComm.setTxnId(dtlUSDComm.getRelaTransactionid());
            walletService.updateComAcc(reqComm);
        }

    }

    /**
     * 销卡失败处理
     *
     * @param cardId
     * @param txnId
     */
    @Override
    public void dealAfterWithdrawMoonbankFail(String cardId, String txnId) {
        List<TxnDtlUSD> dtlUsdlist = txnService.findAllByCardIdAndStatusAndTransactionId(cardId, String.valueOf(TxnStatus.PADDING.getValue()), txnId);
        TxnDtlUSD dtlUSD = null;
        TxnDtlUSD dtlUSDComm = null;
        if (dtlUsdlist != null && !dtlUsdlist.isEmpty()) {
            for (TxnDtlUSD d : dtlUsdlist) {
                if (d.getTxnType().equals(TxnType.CARD_WITHDRAW_D.getValue())) {
                    dtlUSD = d;
                }
                if (d.getTxnType().equals(TxnType.CARD_WITHDRAW_C.getValue())) {
                    dtlUSDComm = d;
                }

            }
        }
        if (dtlUSD != null) {
            VccReqDto req = new VccReqDto(dtlUSD.getUserId(), AlgorithmType.SUB, FreezeType.FALLBACK, dtlUSD);
            walletService.update(req);
        }
        if (dtlUSDComm != null) {
            //B.公司提现
            VccReqDto reqComm = new VccReqDto(null, AlgorithmType.ADD, FreezeType.NORMAL_FALLBACK, dtlUSDComm);
            walletService.updateComAcc(reqComm);
        }

    }

    /**
     * 销卡成功处理（B）
     *
     * @param cardId
     * @param txnId
     */
    @Override
    public void dealAfterWithdrawSuccessMerchants(String cardId, String txnId, String sysId, String moonbankUid) {

        CreditCard c = creditCardDao.getOne(cardId);
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectBySysIdForUpdate(sysId);
        List<MetaPartnerTxnDtl> dtlUsdlist = metaPartnerTxnDtlService.findCardRechare(cardId, String.valueOf(TxnStatus.PADDING.getValue()), txnId);
        if (dtlUsdlist != null && !dtlUsdlist.isEmpty()) {

            BigDecimal totAmount = BigDecimal.ZERO;//卡充值金额
            for (MetaPartnerTxnDtl d : dtlUsdlist) {
                if (d.getTxnType().equals(PartnerTxnType.CardWithdrawal.getValue())) {
                    totAmount = totAmount.add(d.getTxnAmount().abs()).add(d.getTxnFee().abs());
                }
            }

            if (totAmount.compareTo(BigDecimal.ZERO) > 0) {
                //更新冻结金额
                metaPartnerAssetPool.setUsdtBalacne(metaPartnerAssetPool.getUsdtBalacne().add(totAmount));
                metaPartnerAssetPool.setFreezeUstdBalacne(metaPartnerAssetPool.getFreezeUstdBalacne().subtract(totAmount));
                metaPartnerAssetPoolService.updateBalance(metaPartnerAssetPool.getId().getPartnerId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());

                //更新充值记录
                metaPartnerTxnDtlService.updateStatus(cardId, String.valueOf(TxnStatus.SUCCESS.getValue()), txnId);

            }
            //更新卡片最新信息
            creditCardService.getCardInfo( c);
        }

    }

    /**
     * 销卡失败处理（B）
     *
     * @param cardId
     * @param txnId
     */
    @Override
    public void dealAfterWithdrawFailMerchantsFail(String cardId, String txnId, String sysId, String moonbankUid) {
        CreditCard c = creditCardDao.getOne(cardId);
        //2.处理
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectByUserIdForUpdate(c.getUserId());
        List<MetaPartnerTxnDtl> dtlUsdlist = metaPartnerTxnDtlService.findCardRechare(cardId, String.valueOf(TxnStatus.PADDING.getValue()), txnId);


        if (dtlUsdlist != null && !dtlUsdlist.isEmpty()) {

            BigDecimal totAmount = BigDecimal.ZERO;//卡充值金额
            for (MetaPartnerTxnDtl d : dtlUsdlist) {
                if (d.getTxnType().equals(PartnerTxnType.CardWithdrawal.getValue())) {
                    totAmount = totAmount.add(d.getTxnAmount().abs()).add(d.getTxnFee().abs());
                }
            }

            if (totAmount.compareTo(BigDecimal.ZERO) > 0) {
                //更新冻结金额
                metaPartnerAssetPool.setFreezeUstdBalacne(metaPartnerAssetPool.getFreezeUstdBalacne().subtract(totAmount));
                metaPartnerAssetPoolService.updateBalance(metaPartnerAssetPool.getId().getPartnerId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());

                //更新充值记录
                metaPartnerTxnDtlService.updateStatus(cardId, String.valueOf(TxnStatus.FAIL.getValue()), txnId);

            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public void queryByTransactionPage(CardUpdateLogDto dto) {
        CreditCard c = creditCardService.findByCardId(dto.getCardId());
        if (c.getSource() != null && ("moonbank".equals(c.getSource()) || "moonbank_Triplink".equals(c.getSource()))) {
            Wallet wallet = walletService.findByUserId(c.getUserId());
            ApiResponse<String> apiResponse = moonbankUtil.queryBankcardTransactions(dto.getPageSize(), wallet.getMoonbankUid(), Integer.valueOf(c.getBankCardId()));
            System.out.println("queryBankcardTransactions response Object:  " + apiResponse);

            if (apiResponse.isSuccess()) {
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

                logger.info("queryBankcardTransactions encode result===>" + descStr);
                List<JSONObject> dataList = JSON.parseArray(descStr, JSONObject.class);
                //本地卡日志
                List<String> list = creditCardLogService.findByCardId(dto.getCardId());

                List<CreditCardLog> unsaveList = new ArrayList<>();
                List<TxnDtlUSD> txnList = new ArrayList<>();

                int i = 0;
                BigDecimal balance = new BigDecimal("0");

                for (JSONObject json : dataList) {
                    boolean exists = false;
                    String authType = json.getString("authType");
                    String id = json.getString("id");
                    String localCurrency = json.getString("localCurrency");
                    String localCurrencyAmt = json.getString("localCurrencyAmt");
                    String occurTime = json.getString("occurTime");
                    String recordNo = json.getString("recordNo");
                    String receiptNo = json.getString("receiptNo");//处理中状态和完成状态的关联

                    String transCurrency = json.getString("transCurrency");
                    String transCurrencyAmt = json.getString("transCurrencyAmt");
                    String transStatus = json.getString("transStatus");
                    if ("SETTLED".equals(transStatus)) {
                        transStatus = "APPROVED";
                    }
                    String transType = json.getString("transType");
                    String merchantName = json.getString("merchantName");
                    String respCode = json.getString("respCode");
                    String respCodeDesc = json.getString("respCodeDesc");

                    String feeAmount = json.getString("feeAmount");
                    String feeCurrency = json.getString("feeCurrency");

                    String failure_reason = json.getString("failure_reason");//失败原因
                    if (StringUtils.isNotEmpty(failure_reason)) {
                        respCodeDesc = failure_reason;
                    }

                    for (String txnId : list) {
                        if (recordNo.equals(txnId)) {
                            exists = true;
                            break;
                        }
                    }
                    if (!exists) {
                        //保存creditCardLog
                        CreditCardLog log = new CreditCardLog();
                        boolean saveFlag = false;//是否保存新数据
                        if (StringUtils.isNotEmpty(receiptNo)) {
                            CreditCardLog old = creditCardLogService.findByTransactionId(receiptNo);
                            if (old != null) {
                                if (StringUtils.isNotEmpty(feeAmount) && new BigDecimal(feeAmount).abs().compareTo(BigDecimal.ZERO) > 0) {
                                    if ("REFUND".equals(transType) || "refund".equals(transType)) {
                                        old.setTransactionStatus("CANCEL");
                                    }
                                    old.setNewTransactionId(recordNo);
                                    creditCardLogService.save(old);
                                    saveFlag = true;
                                } else {
                                    log = old;
                                    log.setTransactionStatus(transStatus);//交易状态
                                    log.setNewTransactionId(recordNo);
                                    saveFlag = false;
                                    creditCardLogService.save(log);
                                }

                            } else {
                                saveFlag = true;
                            }
                        } else {
                            saveFlag = true;
                        }
                        if (saveFlag) {
                            log.setCardId(c.getCardId());//卡号
                            log.setTransactionId(recordNo);//交易id
                            log.setTransactionCurrency(transCurrency);//交易币种:货币三位代码
                            log.setTransactionAmount(new BigDecimal(transCurrencyAmt));//交易金额
                            log.setCardCurrency(localCurrency);//卡币种:货币三位代码
                            log.setCardTransactionAmount(new BigDecimal(localCurrencyAmt));//卡交易金额
                            log.setTransactionType(transType);//交易类型：
                            log.setTransactionStatus(transStatus);//交易状态
//                            log.setRequestNo(uniqueNo);
                            long timestamp = Long.valueOf(occurTime);
                            // 将时间戳转换为LocalDateTime对象
                            LocalDateTime dateTime = LocalDateTime.ofEpochSecond(timestamp / 1000, 0,
                                    java.time.ZoneOffset.ofHours(0));
                            // 格式化日期时间
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            String formattedDateTime = dateTime.format(formatter);
                            log.setTransactionDate(formattedDateTime);//交易时间
                            log.setFee(null);//手续费金额
                            log.setFeeCurrency(null);//手续费卡币种
                            log.setMerchantName(merchantName);//商户名称
                            log.setMerchantCity(null);//商户所在城市
                            log.setTransactionDetail(authType);//交易详细信息
                            log.setDescription(respCodeDesc);//描述
                            log.setRespCode(respCode);
                            if (StringUtils.isNotEmpty(feeAmount)) {
                                log.setFee(new BigDecimal(feeAmount));
                            }

                            log.setFeeCurrency(feeCurrency);
                            //查询卡余额
                            if (i < 1) {
                                ApiResponse<String> apiResponse2 = moonbankUtil.queryBankcardBalance(wallet.getMoonbankUid(), Integer.valueOf(c.getBankCardId()));
                                if (apiResponse2.isSuccess()) {
                                    i++;
                                    String descStr2 = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse2.getResult());
                                    logger.info("queryBankcardInfo encode result===>" + descStr2);
                                    JSONObject j2 = JSONObject.parseObject(descStr2);
                                    balance = j2.getBigDecimal("balance");

                                }
                            }
                            log.setCardAvailableBalance(balance);//卡可用余额
                            log.setCardAvailableBalanceCurrency("USD");//卡可用余额币种
                            unsaveList.add(log);
                        }
                        //保存TxnDtlUSD
                        //金额不为0才记录
//                        if (log.getCardTransactionAmount() != null && log.getCardTransactionAmount().abs().compareTo(BigDecimal.ZERO) > 0) {
//
//                            TxnDtlUSD usd = new TxnDtlUSD();
//                            usd.setUsdBalance(log.getCardTransactionAmount());
//                            usd.setTxnTime(DateUtils.parse(log.getTransactionDate()).plusHours(8));
//                            usd.setUserId(c.getUserId());
//                            usd.setTxnAmount(log.getCardTransactionAmount());
//                            usd.setTxnProduct(log.getCardId());
//                            usd.setTxnFee(log.getFee());
//                            usd.setFromUserId(c.getUserId());
//                            usd.setTxnStatus(TxnStatus.getValueByName(log.getTransactionStatus()));
//                            if (log.getRespCode() != null && !log.getRespCode().equals("000000"))
//                                usd.setTxnDesc(McardTransactionRespCode.toType(log.getRespCode()).getValueEn());
//                            else
//                                usd.setTxnDesc(log.getTransactionDetail());
//                            usd.setTxnType(TxnType.getValueByCode(log.getTransactionType()));
//                            usd.setRelaTransactionid(log.getTransactionId());
//                            usd.setMerchantCity(log.getMerchantCity());
//                            usd.setMerchantName(log.getMerchantName());
//                            usd.setUsdBalance(log.getCardAvailableBalance());
//                            usd.setFromAmount(log.getTransactionAmount());
//                            usd.setFromCoin(log.getTransactionCurrency());
//                            if (log.getCardTransactionAmount().compareTo(BigDecimal.ZERO) == 0) {
//                                usd.setExchgRate(BigDecimal.ZERO);
//                            } else {
//                                usd.setExchgRate(log.getTransactionAmount().divide(log.getCardTransactionAmount(), 10, RoundingMode.HALF_UP));
//                            }
//                            txnList.add(usd);
//                        }


                    }
                }
                if (!unsaveList.isEmpty())
                    creditCardLogService.saveAll(unsaveList);
//                if (!txnList.isEmpty())
//                    txnService.saveAll(txnList);

            }


        } else if ("kun".equals(c.getSource())) {
            kunService.updateCardOrder(c);
        } else {
            for (int i = 1; i <= 2; i++) {
                //1.调用vcc接口
                VccReq req = new VccReq();
                req.setCardId(dto.getCardId());
                req.setRequestNo(UniqueIDGenerator.generateID());
                req.setPageNum(i);
                req.setPageSize(dto.getPageSize());
                VccCreateCardResp res = yjCardUtils.card(req, ApiCode.QueryTransactionPage);
                //2.返回结果
                if (res != null && res.getCode().equals("SUCCESS")) {
                    JSONObject j = JSON.parseObject(res.getRes());
                    List<CreditCardLog> vccList = JsonUtils.string2Obj(j.getString("records"), List.class, CreditCardLog.class);
                    List<String> list = creditCardLogService.findByCardId(dto.getCardId());
                    Iterator<CreditCardLog> it = vccList.iterator();
                    List<CreditCardLog> unsaveList = new ArrayList<>();
                    List<TxnDtlUSD> txnList = new ArrayList<>();
                    while (it.hasNext()) {
                        CreditCardLog log = it.next();
                        boolean exists = false;
                        for (String txnId : list) {
                            if (log.getTransactionId().equals(txnId)) {
                                exists = true;
                                break;
                            }
                        }
                        if (!exists) {
                            unsaveList.add(log);
                            if (!log.getTransactionType().equals("card_deposit") && !log.getTransactionType().equals("card_withdraw") && !log.getTransactionType().equals("open_card")) {
//                                TxnDtlUSD u = new TxnDtlUSD();
//                                u.setMerchantName(log.getMerchantName());
//                                u.setMerchantCity(log.getMerchantCity());
//                                u.setTxnStatus(TxnStatus.getValueByName(log.getTransactionStatus()));
//                                u.setTxnAmount(log.getCardTransactionAmount());
//                                u.setTxnProduct(log.getCardId());
//                                u.setRelaTransactionid(log.getTransactionId());
//                                u.setUserId(dto.getUserId());
//                                u.setTxnNum(1l);
//                                u.setTxnType(TxnType.getValueByCode(log.getTransactionType()));
//                                u.setTxnTime(DateUtils.parse(log.getTransactionDate()));
//                                u.setTxnDesc(log.getTransactionDetail());
//                                u.setFromUserId(dto.getUserId());
//                                u.setTxnFee(log.getFee());
//                                u.setUsdBalance(log.getCardAvailableBalance());
//                                u.setFromAmount(log.getTransactionAmount());
//                                u.setFromCoin(log.getTransactionCurrency());
//                                if (log.getCardTransactionAmount() != null && log.getTransactionAmount() != null) {
//                                    if (log.getCardTransactionAmount().compareTo(BigDecimal.ZERO) == 0) {
//                                        u.setExchgRate(BigDecimal.ZERO);
//                                    } else {
//                                        u.setExchgRate(log.getTransactionAmount().divide(log.getCardTransactionAmount(), 10, RoundingMode.HALF_UP));
//                                    }
//
//                                }
//                                txnList.add(u);
                            }

                        }
                    }
                    if (!unsaveList.isEmpty())
                        creditCardLogService.saveAll(unsaveList);
                    if (!txnList.isEmpty())
                        txnService.saveAll(txnList);
                }
            }
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public void creditCardMoobankDeal(String cardId, Long userId) {

        //b. 如果是开卡，需要更新dtl_usd的数据
        //如果是 usd开卡
        List<TxnDtlUSD> list = txnService.findAllByCardIdAndStatusAndTxnTypeForUsd(cardId, TxnStatus.PADDING.getValue().toString()
                , new String[]{TxnType.CARD_ACTIVATION_FEE_D.getValue(), TxnType.CARD_ACTIVATION_FEE_C.getValue(), TxnType.CARD_LOGISTICS_FEE_D.getValue(), TxnType.CARD_LOGISTICS_FEE_C.getValue()});
        if (list != null && !list.isEmpty()) {
            TxnDtlUSD dtlUSD = null;
            TxnDtlUSD dtlUSDCom = null;
            TxnDtlUSD dtlUSDLogistics = null;
            TxnDtlUSD dtlUSDComLogistics = null;
            for (TxnDtlUSD d : list) {
                if (d.getTxnType().equals(TxnType.CARD_ACTIVATION_FEE_C.getValue()))
                    dtlUSD = d;
                if (d.getTxnType().equals(TxnType.CARD_ACTIVATION_FEE_D.getValue()))
                    dtlUSDCom = d;
                if (d.getTxnType().equals(TxnType.CARD_LOGISTICS_FEE_C.getValue()))
                    dtlUSDLogistics = d;
                if (d.getTxnType().equals(TxnType.CARD_LOGISTICS_FEE_D.getValue()))
                    dtlUSDComLogistics = d;
            }
            //成功
            if (dtlUSD != null) {
                VccReqDto req = new VccReqDto(userId, AlgorithmType.SUB, FreezeType.FREE_FREEZE, dtlUSD);
                walletService.updateCou(req);
                VccReqDto reqComm = new VccReqDto(null, AlgorithmType.ADD, FreezeType.NO, dtlUSDCom);
                reqComm.setUsdBalance(dtlUSDCom.getUsdBalance().add(dtlUSDCom.getTxnAmount().abs()));
                //更新公司流水余额
                AjaxResult arCom = walletService.updateComAccCou(reqComm);
                TxnDtlUSD dtlCom = (TxnDtlUSD) arCom.get("dtlUSD");
                dtlCom.setUsdBalance(dtlCom.getUsdBalance().add(dtlUSDCom.getTxnAmount().abs()));
                txnService.save(dtlCom);
            }
            //更新物流费
            if (dtlUSDLogistics != null) {
                VccReqDto req2 = new VccReqDto(userId, AlgorithmType.SUB, FreezeType.FREE_FREEZE, dtlUSDLogistics);
                walletService.update(req2);
                VccReqDto reqComm2 = new VccReqDto(null, AlgorithmType.ADD, FreezeType.NO, dtlUSDComLogistics);
                reqComm2.setUsdBalance(dtlUSDComLogistics.getUsdBalance().add(dtlUSDComLogistics.getTxnAmount().abs()));
                //更新公司流水余额
                AjaxResult arCom2 = walletService.updateComAcc(reqComm2);
                TxnDtlUSD dtlCom2 = (TxnDtlUSD) arCom2.get("dtlUSD");
                dtlCom2.setUsdBalance(dtlCom2.getUsdBalance().add(dtlUSDComLogistics.getTxnAmount().abs()));
                txnService.save(dtlCom2);
            }


            // 开卡返佣
            if (dtlUSD != null && dtlUSD.getTxnAmount() != null && dtlUSD.getTxnAmount().abs().compareTo(BigDecimal.ZERO) > 0) {
                String flag = procedureUtils.callStoredProcedure(dtlUSD.getId(), Constants.procedure.p_rel_card_open_benefit);
                if ("-1".equals(flag)) {
                    // 发邮件通知管理员
                    sendEmail.errorMail("开卡返佣返佣失败,交易ID:" + dtlUSD.getId());
                }

            }


        }
        // 如果激活码开卡
        TxnDtlCode dtlCode = txnService.findByTxnProductAndStatusAndTxnTypeForCode(cardId, TxnStatus.PADDING.getValue().toString(), TxnType.CARD_ACTIVATION.getValue());
        if (dtlCode != null) {

            dtlCode.setTxnStatus(TxnStatus.SUCCESS.getValue());
            txnService.dealTxnForCode(dtlCode);

        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public void creditCardMoobankDealCoin(String cardId, Long userId) {
        CreditCard card = creditCardService.findByCardId(cardId);

        creditCardService.coinOpenCardDeal(card);


    }


    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public void dealAfterRecharge(String cardId, AjaxResult ar, String transactionId) {
        int i = 0;
        boolean circle = false;//循环的查询的判断
        VccReq r = new VccReq();
        r.setCardId(cardId);
        try {
            while (i < 3 && !circle) {
                Thread.sleep(2000);
                r.setRequestNo(UniqueIDGenerator.generateID());
                VccCreateCardResp res = yjCardUtils.card(r, ApiCode.QueryTransactionPage);  //1.调用vcc接口，查询开卡结果
                //2.返回结果
                if (res != null && res.getCode().equals("SUCCESS")) {
                    JSONObject j = JSON.parseObject(res.getRes());
                    List<CreditCardLog> list = JsonUtils.string2Obj(j.getString("records"), List.class, CreditCardLog.class);
                    if (list != null && !list.isEmpty()) {
                        CreditCardLog ccl = list.get(0);
                        if (!ccl.getTransactionStatus().equals(TxnStatus.PADDING.getName())) {
                            circle = true;
                            CreditCardLog log = creditCardLogService.findByTransactionId(transactionId);
                            //1.更新creditcardlog
                            ccl.setId(log.getId());
                            creditCardLogService.save(ccl);
                            //2.更新钱包余额
                            queryCardDetail(cardId, null);
                            //成功
                            if (ccl.getTransactionStatus().equals(TxnStatus.SUCCESS.getName())) {
                                //2.处理TxnDtlUSD
                                //A.个人充值
                                TxnDtlUSD dtlUSD = (TxnDtlUSD) ar.get("dtlUSD");
                                VccReqDto req = new VccReqDto(dtlUSD.getUserId(), AlgorithmType.SUB, dtlUSD.getTxnAmount(), FreezeType.FREE_FREEZE, dtlUSD.getId(), dtlUSD.getUsdBalance());
                                walletService.update(req);
                                //B.个人手续费
                                TxnDtlUSD dtlUSDFee = (TxnDtlUSD) ar.get("dtlUSDFee");
                                VccReqDto reqFee = new VccReqDto(dtlUSDFee.getUserId(), AlgorithmType.SUB, dtlUSDFee.getTxnAmount(), FreezeType.FREE_FREEZE, dtlUSDFee.getId(), dtlUSDFee.getUsdBalance());
                                walletService.update(reqFee);
                                //C.公司充值
                                TxnDtlUSD dtlUSDComm = (TxnDtlUSD) ar.get("dtlUSDComm");
                                VccReqDto reqComm = new VccReqDto(null, AlgorithmType.ADD, FreezeType.NO, dtlUSDComm);
                                reqComm.setUsdBalance(dtlUSDComm.getUsdBalance().add(dtlUSDComm.getTxnAmount()));
                                reqComm.setTxnId(dtlUSDComm.getRelaTransactionid());
                                walletService.updateComAcc(reqComm);
                                //D.公司手续费
                                TxnDtlUSD dtlUSDCommFee = (TxnDtlUSD) ar.get("dtlUSDCommFee");
                                VccReqDto reqCommFEE = new VccReqDto(null, AlgorithmType.ADD, FreezeType.NO, dtlUSDCommFee);
                                reqCommFEE.setUsdBalance(dtlUSDCommFee.getUsdBalance().add(dtlUSDCommFee.getTxnAmount()));
                                reqCommFEE.setTxnId(dtlUSDCommFee.getRelaTransactionid());
                                walletService.updateComAcc(reqCommFEE);
                                // 充值成功，才触及返佣
                                String flag = procedureUtils.callStoredProcedure(dtlUSDFee.getId(), Constants.procedure.p_rel_card_recharge_benefit);
                                if ("-1".equals(flag)) {
                                    // 发邮件通知管理员
                                    sendEmail.errorMail("充值返佣返佣失败,交易ID:" + dtlUSD.getId());
                                }

                            }
                            //失败
                            else if (ccl.getTransactionStatus().equals(TxnStatus.FAIL.getName())) {
                                //2.处理TxnDtlUSD
                                //A.个人充值
                                TxnDtlUSD dtlUSD = (TxnDtlUSD) ar.get("dtlUSD");
                                VccReqDto req = new VccReqDto(dtlUSD.getUserId(), AlgorithmType.ADD, FreezeType.FALLBACK, dtlUSD);
                                walletService.update(req);
                                //B.个人手续费
                                TxnDtlUSD dtlUSDFee = (TxnDtlUSD) ar.get("dtlUSDFee");
                                VccReqDto reqFee = new VccReqDto(dtlUSDFee.getUserId(), AlgorithmType.ADD, FreezeType.FALLBACK, dtlUSDFee);
                                walletService.update(reqFee);
                                //C.公司充值
                                TxnDtlUSD dtlUSDComm = (TxnDtlUSD) ar.get("dtlUSDComm");
                                VccReqDto reqComm = new VccReqDto(null, AlgorithmType.SUB, FreezeType.NORMAL_FALLBACK, dtlUSDComm);
                                walletService.updateComAcc(reqComm);
                                //C.公司充值
                                TxnDtlUSD dtlUSDCommFee = (TxnDtlUSD) ar.get("dtlUSDCommFee");
                                VccReqDto reqCommFEE = new VccReqDto(null, AlgorithmType.SUB, FreezeType.NORMAL_FALLBACK, dtlUSDCommFee);
                                walletService.updateComAcc(reqCommFEE);
                            }
                        }
                    }

                }
                i++;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void updateCard(String res, CreditCard c) {
        JSONObject j = JSONObject.parseObject(res);
        //卡片还在申请中
        if (j.getString("cardStatus").equals(CardStatus.PROCESSING.getValue())) {
            System.out.println("卡片还在申请中==================================");
        } else {
            c.setCvv2(AESUtils.aesEncrypt(Constants.card_key, j.getString("cvv2")));

            String status = c.getCardStatus();
            String newstatus = j.getString("cardStatus");
            if (StringUtils.isNotEmpty(newstatus) && !newstatus.equals(status)) {
                c.setStatusUpdateTime(new Date());
            }
            c.setCardStatus(newstatus);
            c.setCardLabel(j.getString("cardLabel"));
            c.setExpirationTime(j.getString("cardExpDate"));
            c.setBalance(j.getBigDecimal("balance"));
            c.setCardNo(AESUtils.aesEncrypt(Constants.card_key, j.getString("cardNo")));
            c.setSearchTime(DateUtils.getNowDate());
            creditCardDao.save(c);
        }
    }

    public AjaxResult queryCardDetail(String cardId, CreditCard c) {
        if (c == null)
            c = creditCardDao.findByCardId(cardId);
        //1.调用vcc接口
        VccReq req = new VccReq();
        req.setCardId(cardId);
        req.setRequestNo(UniqueIDGenerator.generateID());
        VccCreateCardResp res = yjCardUtils.card(req, ApiCode.QueryCardDetail);
        //2.返回结果
        if (res != null && res.getCode().equals("SUCCESS")) {
            updateCard(res.getRes(), c);
            CreditCard cv = c.clone();
            cv.setCardNo(formatCardNumber(AESUtils.aesDecrypt(Constants.card_key, c.getCardNo())));
            String cvv = AESUtils.aesDecrypt(Constants.card_key, c.getCvv2());
            cv.setCvv2(cvv.substring(0, 1) + "**");
            return AjaxResult.success(cv);
        }
        return AjaxResult.error(res.getCode(), res.getDetail());
    }

    public String formatCardNumber(String cardNumber) {
        if (StringUtils.isEmpty(cardNumber) || cardNumber.length() < 8) {
            return null;
        }
        String firstFour = cardNumber.substring(0, 4);
        String lastFour = cardNumber.substring(cardNumber.length() - 4);
        String middle = "";
        for (int i = 0; i < cardNumber.length() - 8; i++) {
            middle += "*";
        }
        return firstFour + middle + lastFour;
    }

    /**
     * 充值时的钱包解冻处理
     *
     * @param dtlUSD
     * @param success
     */
    public void freeWalletWhenRecharge(Long userId, TxnDtlUSD dtlUSD, boolean success) {
        if (success) {
            if (userId.equals(dtlUSD.getUserId())) {
                VccReqDto req = new VccReqDto(dtlUSD.getUserId(), AlgorithmType.SUB, dtlUSD.getTxnAmount(), FreezeType.FREE_FREEZE, dtlUSD.getId(), dtlUSD.getUsdBalance());
                walletService.update(req);
                // 充值成功，才触及返佣
//                String flag = procedureUtils.callStoredProcedure(dtlUSD.getId(), Constants.procedure.p_rel_card_recharge_benefit);
//                if ("-1".equals(flag)){
//                    // 发邮件通知管理员
//                    sendEmail.errorMail("充值返佣返佣失败,交易ID:"+dtlUSD.getId());
//                }
            } else {
                VccReqDto req = new VccReqDto(null, AlgorithmType.ADD, dtlUSD.getTxnAmount(), FreezeType.NO, dtlUSD.getId(), dtlUSD.getUsdBalance());
                walletService.updateComAcc(req);
            }

        } else {
            //失败
            //4.操作个人账户,余额增加， 冻结扣减
            if (userId.equals(dtlUSD.getUserId())) {
                VccReqDto req = new VccReqDto(dtlUSD.getUserId(), AlgorithmType.ADD, dtlUSD.getTxnAmount(), FreezeType.FALLBACK, dtlUSD.getId(), dtlUSD.getUsdBalance());
                walletService.update(req);
            } else {
                VccReqDto req = new VccReqDto(null, AlgorithmType.SUB, dtlUSD.getTxnAmount(), FreezeType.NORMAL_FALLBACK, dtlUSD.getId(), dtlUSD.getUsdBalance());
                walletService.updateComAcc(req);
            }
        }

    }

    /**
     * 提现时的解冻钱包处理
     *
     * @param dtlUSD
     * @param success
     */
    public void freeWalletWhenWithdraw(Long userId, TxnDtlUSD dtlUSD, boolean success) {
        if (success) {
            if (userId.equals(dtlUSD.getUserId())) {
                VccReqDto req = new VccReqDto(dtlUSD.getUserId(), AlgorithmType.ADD, dtlUSD.getTxnAmount(), FreezeType.FREE_FREEZE, dtlUSD.getId(), dtlUSD.getUsdBalance());
                walletService.update(req);
            } else {
                VccReqDto req = new VccReqDto(null, AlgorithmType.SUB, dtlUSD.getTxnAmount(), FreezeType.NO, dtlUSD.getId(), dtlUSD.getUsdBalance());
                walletService.updateComAcc(req);
            }

        } else {
            //失败
            //4.操作个人账户,余额增加， 冻结扣减
            if (userId.equals(dtlUSD.getUserId())) {
                VccReqDto req = new VccReqDto(dtlUSD.getUserId(), AlgorithmType.SUB, dtlUSD.getTxnAmount(), FreezeType.FALLBACK, dtlUSD.getId(), dtlUSD.getUsdBalance());
                walletService.update(req);
            } else {
                VccReqDto req = new VccReqDto(null, AlgorithmType.ADD, dtlUSD.getTxnAmount(), FreezeType.NORMAL_FALLBACK, dtlUSD.getId(), dtlUSD.getUsdBalance());
                walletService.updateComAcc(req);
            }
        }

    }


    public static void main(String[] args) throws Exception {
//          String cardNo = "****************";
//
//          String s = AESUtils.aesEncrypt(Constants.card_key,cardNo);
//          System.out.println("s:"+s);
//          String res = AESUtils.aesDecrypt(Constants.card_key,s);
//          System.out.println("res:"+res);
        //qD7kjxbB2yv9h7EYcAwX2a6jFG1Tl9yVxIuqjndjJHg=
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse("2023-07-08 13:36:30", formatter);
    }

}
