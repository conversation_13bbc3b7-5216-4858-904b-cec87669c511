package com.meta.framework.web.service;

import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.domain.model.LoginUser;
import com.meta.common.enums.LoginType;
import com.meta.common.enums.UserStatus;
import com.meta.common.exception.BaseException;
import com.meta.common.utils.ServletUtils;
import com.meta.common.utils.StringUtils;
import com.meta.framework.security.rule.LoginRules;
import com.meta.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * 用户验证处理
 *
 * <AUTHOR>
 */
@Service("userDetailsByGoogle")
public class UserDetailsByGoogleServiceImpl implements UserDetailsService {
    private static final Logger log = LoggerFactory.getLogger(UserDetailsByGoogleServiceImpl.class);

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private LoginRules loginRules;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        SysUser user = userService.selectUserByUserName(username);
        String acceptLanguage = null;
        try {
            acceptLanguage = ServletUtils.getRequest().getHeader("Accept-Language");
        }catch (Exception e){
//            e.printStackTrace();
            return null;
        }

        if (StringUtils.isNull(user)) {
            log.info("登录用户：{} 不存在.", username);
            throw new UsernameNotFoundException("登录用户：" + username + " 不存在");
        } else if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            log.info("登录用户：{} 已被删除.", username);
            throw new BaseException("对不起，您的账号：" + username + " 已被删除");
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", username);
            if (StringUtils.isNotEmpty(acceptLanguage)){
                if ("en".equals(acceptLanguage)||"en-US".equals(acceptLanguage)){
                    throw new BaseException("Sorry, your account: " + username + " has been deactivated");
                }else {
                    throw new BaseException("对不起，您的账号：" + username + " 已停用");
                }

            }else {
                throw new BaseException("对不起，您的账号：" + username + " 已停用");
            }

        }
        loginRules.check(user, LoginType.GOOGLE,username);
        return createLoginUser(user);
    }

    public UserDetails createLoginUser(SysUser user) {
        return new LoginUser(user, permissionService.getMenuPermission(user));
    }
}
