package com.meta.framework.web.service;

import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.domain.model.LoginUser;
import com.meta.common.enums.UserStatus;
import com.meta.common.utils.StringUtils;
import com.meta.framework.security.rule.LoginRules;
import com.meta.system.service.ISysUserService;
import org.hibernate.service.spi.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * 用户验证处理
 *
 * <AUTHOR>
 */
@Service("userDetailsByEmail")
public class UserDetailsByEmailServiceImpl implements UserDetailsService {
    private static final Logger log = LoggerFactory.getLogger(UserDetailsByEmailServiceImpl.class);
    @Autowired
    private ISysUserService userService;
    @Autowired
    private SysPermissionService permissionService;
    @Autowired
    private LoginRules loginRules;

    @Override
    public UserDetails loadUserByUsername(String email) throws UsernameNotFoundException {
        SysUser user = userService.selectUserByEmail(email);
        if (StringUtils.isNull(user)) {
            log.info("登录用户：{} 不存在.", email);
            throw new ServiceException("登录用户：" + email + " 不存在");
        }else if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            log.info("登录用户：{} 已被删除.", email);
            throw new ServiceException("对不起，您的账号：" + email + " 已被删除");
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", email);
            throw new ServiceException("对不起，您的账号：" + email + " 已停用");
        }
        return createLoginUser(user);
    }

    public UserDetails createLoginUser(SysUser user) {
        return new LoginUser(user, permissionService.getMenuPermission(user));
    }
}

