package com.meta.framework.web.service;

import com.meta.common.constant.Constants;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.domain.model.LoginBody;
import com.meta.common.core.domain.model.LoginUser;
import com.meta.common.core.redis.RedisCache;
import com.meta.common.enums.LoginType;
import com.meta.common.exception.CustomException;
import com.meta.common.exception.user.CaptchaException;
import com.meta.common.exception.user.CaptchaExpireException;
import com.meta.common.exception.user.UserPasswordNotMatchException;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.ServletUtils;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.ip.IpUtils;
import com.meta.common.utils.vcc.StringUtil;
import com.meta.framework.manager.AsyncManager;
import com.meta.framework.manager.factory.AsyncFactory;
import com.meta.framework.security.authticator.EmailAuthenticationToken;
import com.meta.framework.security.authticator.GoogleAuthenticationToken;
import com.meta.framework.security.authticator.GoogleAuthenticator;
import com.meta.framework.security.authticator.UuIdAuthenticationToken;
import com.meta.framework.security.rule.LoginRules;
import com.meta.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {
    @Autowired
    private TokenService tokenService;


    @Autowired
    private ISysUserService userService;

    @Autowired
    private LoginRules loginRules;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    /**
     * 登录验证
     * @param body 登录信息
     * @return 结果
     */
    public String login(LoginBody body) {
        // 用户验证
        Authentication authentication;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(body.getUsername(), body.getPassword()));
        } catch (Exception e) {
            // 登陆异常统计
            loginRules.loginCount(body.getUsername(), LoginType.PASSWD);
            if (e instanceof BadCredentialsException) {
                loginRules.sameIpPasswdError(IpUtils.getIpAddr(ServletUtils.getRequest()));
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(body.getUsername(), Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(body.getUsername(), Constants.LOGIN_FAIL, e.getMessage()));
                throw new CustomException(e.getMessage());
            }
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(body.getUsername(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        if(!body.getUserTypes().contains(loginUser.getUser().getUserType())){
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(body.getUsername(), Constants.LOGIN_FAIL, MessageUtils.message("user.not.exists")));
            throw new CustomException(MessageUtils.message("user.not.exists"));
        }

        // 生成token
        body.setUserId(loginUser.getUserId());
        body.setEmail(loginUser.getUser().getEmail());
        return tokenService.createToken(loginUser);
    }




    /**
     * 通过邮箱验证码登录
     * @param body 邮箱
     */
    public String loginByEmail(LoginBody body) {
        String verifyKey = Constants.CAPTCHA_EMAIL_LOGIN_KEY + body.getEmail();
        String captcha = redisCache.getCacheObject(verifyKey);
        if (captcha == null) {
            loginRules.loginCount(body.getEmail(), LoginType.EMAIL);
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(body.getEmail(), Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
            throw new CaptchaExpireException();
        }
        if (!captcha.equals(body.getCode())) {
            loginRules.loginCount(body.getEmail(), LoginType.EMAIL);
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(body.getEmail(), Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
            throw new CaptchaException();
        }

        // 用户验证
        Authentication authentication;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(new EmailAuthenticationToken(body.getEmail()));
        } catch (Exception e) {
            // 登陆异常统计
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(body.getEmail(), Constants.LOGIN_FAIL, e.getMessage()));
            throw new CustomException(e.getMessage());
        }
        redisCache.deleteObject(verifyKey);
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(body.getEmail(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        if(!body.getUserTypes().contains(loginUser.getUser().getUserType())){
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(body.getUsername(), Constants.LOGIN_FAIL, MessageUtils.message("user.not.exists")));
            throw new CustomException(MessageUtils.message("user.not.exists"));
        }

        body.setUserId(loginUser.getUserId());
        body.setUsername(loginUser.getUsername());
        return tokenService.createToken(loginUser);
    }
    /**
     * 通过邮箱验证码登录
     * @param email 邮箱
     */
    public String gateLoginByEmail(String email) {
        // 用户验证
        Authentication authentication;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(new EmailAuthenticationToken(email));
        } catch (Exception e) {
            // 登陆异常统计
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(email, Constants.LOGIN_FAIL, e.getMessage()));
            throw new CustomException(e.getMessage());
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(email, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        return tokenService.createToken(loginUser);
    }


    /**
     * 谷歌验证码登录
     * @param body 登录信息
     */
    public String loginByGoogle(LoginBody body) {
        SysUser user = userService.selectUserByUserName(body.getUsername());
        if (StringUtils.isNull(user)) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(body.getUsername(), Constants.LOGIN_FAIL, MessageUtils.message("user.not.exists")));
            throw new CustomException(MessageUtils.message("user.not.exists"));
        }
        if(!body.getUserTypes().contains(user.getUserType())){
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(body.getUsername(), Constants.LOGIN_FAIL, MessageUtils.message("user.not.exists")));
            throw new CustomException(MessageUtils.message("user.not.exists"));
        }

        if (StringUtils.isEmpty(user.getGoogleSecretKey())) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(body.getUsername(), Constants.LOGIN_FAIL, MessageUtils.message("google.authenticator.disabled")));
            throw new CaptchaExpireException();
        }
        long time = System.currentTimeMillis ();
        GoogleAuthenticator g = new GoogleAuthenticator ();
        boolean result = g.check_code
                (user.getGoogleSecretKey(),Long.parseLong(body.getCode()),time );
        if (!result) {
            loginRules.loginCount(body.getUsername(), LoginType.GOOGLE);
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(body.getUsername(), Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
            throw new CaptchaException();
        }
        // 用户验证
        Authentication authentication;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(new GoogleAuthenticationToken(body.getUsername()));
        } catch (Exception e) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(body.getUsername(), Constants.LOGIN_FAIL, e.getMessage()));
            throw new CustomException(e.getMessage());
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(body.getUsername(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        body.setUserId(loginUser.getUserId());
        body.setEmail(loginUser.getUser().getEmail());
        return tokenService.createToken(loginUser);
    }

    /**
     * UuId登录
     * @param uuid uuid
     */
    public String loginByUuId(String uuid) {
        Authentication    authentication = authenticationManager.authenticate(new UuIdAuthenticationToken(uuid));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 重置登录计数器
     */
    public void resetLoginCount(String username,String email) {
         redisCache.deleteObject(Constants.LOGIN_FAIL_PASS_KEY + username);
         redisCache.deleteObject(Constants.LOGIN_FAIL_EMAIL_KEY + email);
         redisCache.deleteObject(Constants.LOGIN_FAIL_GOOGLE_KEY + username);
    }

    /**
     * 重置登录计数器
     */
    public void resetLoginCount2(String username,String email) {
        redisCache.deleteObject(Constants.LOGIN_FAIL_PASS_KEY + username);
        if(StringUtils.isNotEmpty(email)){
            redisCache.deleteObject(Constants.LOGIN_FAIL_EMAIL_KEY + email);
        }
        redisCache.deleteObject(Constants.LOGIN_FAIL_GOOGLE_KEY + username);
    }


    /**
     * 更新登录数据
     * @param body 登录信息
     */
    public void updateLoginInfo(LoginBody body) {
        // 更新登录数据
        userService.updateLoginInfo(body);

        // 重置登录计数器
        resetLoginCount(body.getUsername(),body.getEmail());
    }

    /**
     * 更新登录信息
     * @param userName 用户名
     * @param email 邮箱地址
     */
    public void updateLoginInfo2(String userName,Long userId,String email,String ip) {

        // 更新登录数据
        userService.updateLoginInfo2(userId,ip);
        // 重置登录计数器
        resetLoginCount2(userName,email);

    }

    public void unLock(Long userId) {
        SysUser sysUser = userService.selectUserById(userId);
        if (sysUser != null) {
            // 重置登录计数器
            resetLoginCount(sysUser.getUserName(),sysUser.getEmail());
        }
    }
}
