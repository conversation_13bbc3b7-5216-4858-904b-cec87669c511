# 钱包私钥加密工具

## 概述

本工具为 `meta-wallet` 服务模块提供了一个高安全性的私钥加密解决方案，使用AES-GCM模式确保钱包私钥的机密性和完整性。

## 功能特性

- 🔐 **AES-GCM加密**: 使用最安全的AES-GCM认证加密模式
- 🛡️ **认证加密**: 同时提供数据机密性和完整性验证
- 🔄 **智能检测**: 自动判断私钥是否已加密
- 🎲 **随机IV**: 每次加密使用不同的初始化向量
- 📝 **Base64编码**: 便于存储和传输
- 🚀 **高性能**: 支持并行处理，性能优异
- 🧪 **完整测试**: 提供全面的功能演示和安全验证

## 配置说明

### 1. 简单配置

本工具使用简化的配置方式，主要通过代码控制：

```java
// 启用或禁用加密
SimplePrivateKeyEncryptionUtil.setEncryptionEnabled(true);  // 生产环境
SimplePrivateKeyEncryptionUtil.setEncryptionEnabled(false); // 开发环境
```

### 2. 默认配置

| 参数 | 值 | 说明 |
|------|------|------|
| 加密算法 | AES | 使用AES对称加密 |
| 密钥长度 | 128位 | 16字节密钥 |
| 编码方式 | Base64 | 加密结果使用Base64编码 |
| 默认密钥 | MetaWallet123456 | 16字节默认密钥 |

### 3. 生产环境配置建议

```java
// 在应用启动时配置
@PostConstruct
public void initEncryption() {
    // 生产环境启用加密
    SimplePrivateKeyEncryptionUtil.setEncryptionEnabled(true);

    // 可以根据需要生成新的密钥
    String newKey = SimplePrivateKeyEncryptionUtil.generateSecretKey();
    // 将新密钥保存到配置文件或环境变量中
}
```

## 使用方法

### 1. 基本用法

```java
// 加密私钥
String originalKey = "f90bd80f933cfef74730d69c133b85288c920ffc253300fe8cb326f668eba517";
String encryptedKey = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(originalKey);

// 解密私钥
String decryptedKey = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encryptedKey);

// 检查是否已加密
boolean isEncrypted = SimplePrivateKeyEncryptionUtil.isEncrypted(encryptedKey);

// 智能处理（推荐使用）
String keyToUse = SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(someKey);
```

### 2. 在 TronUtils 中的集成

工具已自动集成到 `TronUtils.getApiWrapper()` 方法中：

```java
public static ApiWrapper getApiWrapper(String hexPrivateKey) {
    // 智能检测并解密私钥
    String decryptedPrivateKey = SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(hexPrivateKey);

    // 使用解密后的私钥创建 ApiWrapper
    if (WalletConfig.turnMainNetwork) {
        return ApiWrapper.ofMainnet(decryptedPrivateKey, WalletConfig.apiKeyList[index]);
    } else {
        return ApiWrapper.ofShasta(decryptedPrivateKey);
    }
}
```

### 3. 批量处理

```java
// 批量加密私钥
String[] privateKeys = {"key1", "key2", "key3"};
String[] encryptedKeys = new String[privateKeys.length];

for (int i = 0; i < privateKeys.length; i++) {
    encryptedKeys[i] = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(privateKeys[i]);
}

// 批量解密验证
for (int i = 0; i < encryptedKeys.length; i++) {
    String decrypted = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encryptedKeys[i]);
    boolean isValid = privateKeys[i].equals(decrypted);
    System.out.println("Key " + (i+1) + ": " + (isValid ? "✓" : "✗"));
}
```

## 加密算法说明

### AES-GCM加密算法
- **算法类型**: 对称认证加密 (AEAD)
- **密钥长度**: 128位（16字节）
- **加密模式**: GCM模式 (Galois/Counter Mode)
- **IV长度**: 96位（12字节）
- **认证标签**: 128位（16字节）
- **编码方式**: Base64
- **性能**: 高效，支持并行处理
- **安全性**: 最高级别，提供完整性验证

### GCM模式优势
1. **认证加密**: 同时提供机密性和完整性保护
2. **抗篡改**: 数据被修改时解密自动失败
3. **随机IV**: 相同明文产生不同密文，防止模式攻击
4. **高性能**: 支持硬件加速和并行处理
5. **业界标准**: 广泛应用于TLS 1.3、IPSec等安全协议

### 智能检测机制
工具能够智能判断私钥是否已加密：
1. **十六进制检测**: 如果是纯十六进制字符串，认为是原始私钥
2. **Base64验证**: 尝试Base64解码，成功且长度合理则可能是加密数据
3. **长度校验**: GCM数据长度 >= IV(12) + 认证标签(16) + 数据(1) = 29字节

## 安全建议

### 1. 生产环境配置
```java
// 应用启动时配置
@PostConstruct
public void configureEncryption() {
    // 生产环境必须启用加密
    SimplePrivateKeyEncryptionUtil.setEncryptionEnabled(true);
}
```

### 2. 密钥管理
- 🔑 **使用强随机密钥**: 调用 `generateSecretKey()` 生成
- 🔄 **定期轮换密钥**: 建议每季度更换一次
- 🛡️ **密钥分离存储**: 密钥不要硬编码在代码中
- 📝 **记录使用日志**: 记录加密解密操作
- ✅ **验证密钥格式**: 使用 `isValidSecretKey()` 验证

### 3. 部署建议
- **开发环境**: 设置 `setEncryptionEnabled(false)` 便于调试
- **测试环境**: 启用加密，使用测试专用密钥
- **生产环境**: 必须启用加密，使用生产密钥

### 4. 密钥生成示例
```java
// 生成新的安全密钥
String newSecretKey = SimplePrivateKeyEncryptionUtil.generateSecretKey();
System.out.println("新密钥: " + newSecretKey);

// 验证密钥有效性
boolean isValid = SimplePrivateKeyEncryptionUtil.isValidSecretKey("MetaWallet123456");
System.out.println("密钥有效: " + isValid);
```

## 测试

### 基础功能测试
```bash
cd meta-wallet
javac src/main/java/com/meta/util/SimplePrivateKeyEncryptionUtil.java src/main/java/com/meta/util/SimpleEncryptionDemo.java
java -cp src/main/java com.meta.system.uitls.SimpleEncryptionDemo
```

### GCM模式专项测试
```bash
cd meta-wallet
javac src/main/java/com/meta/util/SimplePrivateKeyEncryptionUtil.java src/main/java/com/meta/util/GCMEncryptionDemo.java
java -cp src/main/java com.meta.system.uitls.GCMEncryptionDemo
```

测试覆盖：
- ✅ AES-GCM 认证加密解密
- ✅ 随机IV安全特性验证
- ✅ 数据完整性验证
- ✅ 加密状态智能检测
- ✅ 批量处理性能测试
- ✅ 实际应用场景模拟
- ✅ TronUtils集成测试
- ✅ 向后兼容性验证

## 故障排除

### 1. 常见错误

**错误**: `Given final block not properly padded`
**解决**: 通常是因为尝试解密未加密的数据，使用 `isEncrypted()` 先检查

**错误**: `私钥不能为空`
**解决**: 确保传入的私钥不为 null 或空字符串

**错误**: `私钥加密失败`
**解决**: 检查密钥是否正确，确保密钥长度为16字节

### 2. 调试技巧

```java
// 启用详细日志
System.out.println("原始私钥: " + originalKey);
System.out.println("是否已加密: " + SimplePrivateKeyEncryptionUtil.isEncrypted(originalKey));
System.out.println("加密状态: " + SimplePrivateKeyEncryptionUtil.encryptionEnabled);

// 测试加密解密
try {
    String encrypted = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(originalKey);
    String decrypted = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encrypted);
    System.out.println("测试结果: " + originalKey.equals(decrypted));
} catch (Exception e) {
    System.err.println("测试失败: " + e.getMessage());
}
```

## 更新日志

### v1.0.0 (2025-01-27)
- ✨ 初始版本发布
- 🔐 实现 AES 加密算法
- 🎯 智能私钥检测机制
- 🔄 TronUtils 自动集成
- 🧪 完整的功能演示
- 📚 详细的使用文档

## API 参考

### SimplePrivateKeyEncryptionUtil 类方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `encryptPrivateKey(String)` | 原始私钥 | 加密后私钥 | 加密私钥 |
| `decryptPrivateKey(String)` | 加密私钥 | 原始私钥 | 解密私钥 |
| `isEncrypted(String)` | 私钥 | boolean | 判断是否已加密 |
| `getDecryptedPrivateKey(String)` | 私钥 | 解密后私钥 | 智能处理私钥 |
| `setEncryptionEnabled(boolean)` | 启用状态 | void | 设置加密状态 |
| `generateSecretKey()` | 无 | Base64密钥 | 生成随机密钥 |
| `isValidSecretKey(String)` | 密钥 | boolean | 验证密钥格式 |

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个工具！

## 许可证

本项目遵循公司内部许可证。
