## Error message
not.null=* This field is required.
user.jcaptcha.error=Invalid verification code.
user.jcaptcha.expire=Verification code has expired.
user.not.exists=User does not exist or password is incorrect.
user.type.not.match=User type does not match
user.invitation.invalid=Invalid invitation code.
user.password.not.match=User does not exist or password is incorrect.
user.password.retry.limit.count=Incorrect password entered {0} times.
user.password.retry.limit.exceed=Incorrect password entered {0} times. Account locked for 10 minutes.
user.password.delete=Sorry, your account has been deleted.
user.blocked=User is blocked. Please contact the administrator.
role.blocked=Role is blocked. Please contact the administrator.
user.logout.success=Logout successful.
auth.error=Timed out, please log in again.
length.not.valid=Length must be between {min} and {max} characters.
user.username.not.valid=* Must consist of 2 to 20 Chinese characters, letters, numbers, or underscores, and must not start with a number.
user.password.not.valid=* Must be 5-50 characters.
user.email.not.valid=Invalid email format.
user.email.not.valid2=The email length should not exceed 50 characters.
user.mobile.phone.number.not.valid=Invalid phone number format.
user.login.success=Login successful.
user.notfound=Please login again.
user.login.in.other.device=Your account has been logged in from another location!
user.forcelogout=Administrator has forced logout. Please login again.
user.unknown.error=Unknown error. Please login again.
user.set.password.failed=Failed to set password
user.password.error=Login Password Error
user.password.not.same=New and old passwords cannot be the same.

# Google Authenticator is not enabled for authentication
google.authenticator.disabled=Google Authenticator is not enabled for authentication

## File upload message
upload.exceed.maxSize=Uploaded file size exceeds the limit! <br/> Maximum allowed file size is: {0}MB!
upload.filename.exceed.length=Uploaded file name exceeds {0} characters.

## Permissions
no.permission=You do not have permission to access the data. Please contact the administrator to add the permission [{0}].
no.create.permission=You do not have permission to create data. Please contact the administrator to add the permission [{0}].
no.update.permission=You do not have permission to modify data. Please contact the administrator to add the permission [{0}].
no.delete.permission=You do not have permission to delete data. Please contact the administrator to add the permission [{0}].
no.export.permission=You do not have permission to export data. Please contact the administrator to add the permission [{0}].
no.view.permission=You do not have permission to view data. Please contact the administrator to add the permission [{0}].
ip.address.blocked=IP address is blocked

## node
node.application.success=Node Application Successful
node.application.duplicate=Please do not submit duplicate applications
node.application.insufficient.balance=Insufficient balance
node.application.level.invalid=Invalid node level
node.application.level.mismatch=The application level cannot be less than {0}
node.transfer.success=Transfer Successful
node.transfer.failed=Transfer Failed
node.auto.upgrade.success=Node upgrade successful
node.auto.upgrade.failed=Node upgrade failed
node.user.not.exist=User does not exist
node.transfer.insufficient.num=Transfer amount exceeds the transfer limit of the receiving user!
node.transfer.level=Transfers are only allowed to users with a lower level than oneself!
node.user.level.not.match= User node level is not Lv04
node.user.info.exists=User node configuration already exists.
node.user.info.not.exists=User node configuration not exists.
node.user.info.card.open.rebate=The range of commission for card opening fee needs to be within {0}~{1}.
node.user.info.card.recharge=The range of commission for recharge needs to be within {0}~{1}.

## wallet
wallet.account.insufficient.balance=USD Insufficient balance
wallet.account.insufficient.balance2=USDT Insufficient balance
wallet.activitiCode.insufficient.num=Insufficient activation code
wallet.activitiCode.silver.insufficient.num=Insufficient Blue Card activation code
wallet.activitiCode.golden.insufficient.num=Insufficient Gold Card activation code
wallet.activitiCode.goldenBlack.insufficient.num=Insufficient Black Gold Card activation code
wallet.deduct.funds.failed=Failed to deduct funds, insufficient balance in USD.
wallet.trade.pwd.error=Trade password error
wallet.trade.pwd.error.tip=If the transaction password is entered incorrectly more than 3 times, the account's fund transactions will be frozen for 24 hours. During the freeze period, you can contact customer service personnel for thawing.
wallet.trade.pwd.noset=Transaction password not set
wallet.trade.pwd.failed=Failed to reset trading password
wallet.coin.insufficient.balance=Insufficient balance in {0} wallet!
wallet.coin.not.exist=You do not have a {0} asset account yet, please recharge now!
wallet.coin.exchange.type.not.exist=Exchange type does not exist
wallet.error.trade.not.auth=Please enter transaction password
wallet.error.face.not.auth=Please use facial recognition
wallet.error.not.auth=Please enter transaction password or use facial recognition
wallet.email.can.not.yourself=The recipient of the transfer cannot be yourself.
wallet.transfer.success=Transfer successful.
wallet.transfer.failed=Transfer failed.
wallet.trade.pwd.not.same=New and old passwords cannot be the same.
wallet.exchange.number=The exchange value cannot be less than 10.
wallet.withdrawal.number=The withdrawal value cannot be less than 10.

## 用户信息
registration.success=Registration Successful
kyc.already=You have already completed the KYC verification
kyc.idcard.invalid=Invalid ID card number
kyc.idcard.issueDate.not.empty=Issue date of the ID card cannot be empty
kyc.idcard.expireDate.not.empty=Expiration date of the ID card cannot be empty
faceFeature.already=Facial information has been collected, no need to repeat input.
faceFeature.not.exists=Face feature not collected

## 执行结果
operation.success=Operation successful
operation.failed=Operation failed

## 文件
read.success=Read successful
read.failed=Read failed
read.isEmpty=Empty file

## download
download.success=Download successful
download.failed=Download failed

## 卡
card.nofound=The card number does not exist.
card.status.execption=Card status is abnormal and cannot be recharged
card.userId.wrong=Not the cardholder's card
card.balance.too.low=The balance in the card must be kept at least 
card.deposit.amount.too.low=The recharge amount cannot be less than 
card.withdraw.amount.too.low=The withdraw amount cannot be less than 
card.balance.too.shao=Please recharge at least {0}USD to activate the card .
card.notUnfreeze.cards=Please contact the administrator to unfreeze the card
card.not.apply=The system is being fully upgraded, and card issuance is suspended. The specific recovery time will be announced through official announcements.
card.not.recharge=The system is being fully upgraded and recharges have been suspended. The specific recovery time will be announced through official announcements.

####链上交易结果######
trade.transactionid.error=The transaction number information is incorrect. Please verify and try again!
trade.transaction.error=This transaction was unsuccessful.
sys.busy=The  system is busy, please try again later...
sys.recharge.busy=The recharge system is busy, please try again later...

### mail
mail.send.content.template=【Metabank】Verification code: {0}. Please complete the operation within {1} minutes. If it wasn't you, please ignore this message. 
mail.send.subject.resetPasswd=Reset login Password
mail.send.subject.resetTradePasswd=Reset Trading Password 
mail.send.subject.login=Login Verification Code 
mail.send.subject.register=Account Registration 
mail.not.exists=Email does not exist 
mail.already.exists=Email already exists 
mail.send.success=Sent successfully 
mail.send.failed=Sending failed
mail.bind.error=This email is not a registered binding email for this user!

### VCC
vcc.sys.error=Current network request is poor, please try again later!

### 数据字典
sys_user_sex_0= Man
sys_user_sex_1=Woman
sys_user_sex_2=Unknown
sys_show_hide_0=Show
sys_show_hide_1=Hide
sys_normal_disable_0=Normal
sys_normal_disable_1=Disabled
sys_job_status_0=Normal
sys_job_status_1=Paused
sys_job_group_DEFAULT=Default
sys_job_group_SYSTEM=System
sys_yes_no_Y=Yes
sys_yes_no_N=No
sys_yes_no_2_1=Yes
sys_yes_no_2_0=No
sys_notice_type_1=Notification
sys_notice_type_2=Announcement
sys_notice_status_0=Normal
sys_notice_status_1=Closed
sys_oper_type_1=Add
sys_oper_type_2=Modify
sys_oper_type_3=Delete
sys_oper_type_4=Authorize
sys_oper_type_5=Export
sys_oper_type_6=Import
sys_oper_type_7=Force Logout
sys_oper_type_8=Generate Code
sys_oper_type_9=Clear Data
sys_common_status_0=Success
sys_common_status_1=Failure
credit_card_type_01=Virtual MasterCard
credit_card_type_02=Virtual VISA Card
credit_card_level_01=Silver Card
credit_card_level_02=Gold Card
credit_card_level_03=Black Gold Card
vcc_card_bin_493193=Visa
vcc_card_bin_222929=MasterCard
vcc_card_status_processing=In Progress
vcc_card_status_open_card_fail=Rejected
vcc_card_status_used=In Use
vcc_card_status_remove_processing=Under Cancellation
vcc_card_status_cancel=Cancelled
vcc_card_status_suspending=Locked
vcc_card_status_un_suspending=Unlocking
vcc_card_status_suspend=Locked
vcc_trade_type_card_deposit=Card recharge
vcc_trade_type_card_withdraw=Card withdrawal
vcc_trade_type_pay=Expenditure
vcc_trade_type_pay_refund=Refund of expenditure
vcc_trade_type_remove_refund=Card deletion refund
vcc_trade_type_pay_revoke=Transaction cancellation fee
vcc_trade_type_close_pay=Close transfer
vcc_trade_type_pay_auth=Transaction authorization fee
vcc_trade_type_balance_pay=Balance transfer
vcc_trade_type_pay_reversal=Expenditure reversal
vcc_trade_type_pay_refund_reversal=Refund reversal of expenditure
vcc_trade_type_query_auth=Authorization inquiry
usd_txn_type_d1010=Exchange out
usd_txn_type_c1020=Exchange out
usd_txn_type_d1030=Exchange in
usd_txn_type_c1040=Exchange in
usd_txn_type_d1011=Exchange fee
usd_txn_type_c1021=Exchange fee
usd_txn_type_d1012=Exchange fee
usd_txn_type_c1022=Exchange fee
usd_txn_type_d2010=Card withdrawal
usd_txn_type_c2020=Card withdrawal
usd_txn_type_d2030=Card recharge
usd_txn_type_c2040=Card recharge
usd_txn_type_d2011=Card recharge fee
usd_txn_type_c2021=Card recharge fee
usd_txn_type_d2012=Card withdrawal fee
usd_txn_type_d2022=Card withdrawal fee
usd_txn_type_d3010=USD dollar transfer
usd_txn_type_c3020=USD transfer out
usd_txn_type_d4010=Card activation fee
usd_txn_type_c4020=Card activation fee
usd_txn_type_d4030=Node pre-deposit
usd_txn_type_c4040=Node pre-deposit
usd_txn_type_d9010=Card activation commission
usd_txn_type_c9020=Card activation commission
usd_txn_type_d9030=Card recharge commission
usd_txn_type_c9040=Card recharge commission
usd_txn_type_d9050=Node referral commission
usd_txn_type_c9060=Node referral commission
code_txn_type_10=Apply for deposit
code_txn_type_20=Card activation and usage
code_txn_type_30=Transfer in
code_txn_type_31=Transfer out
code_txn_type_40=Exchange in
code_txn_type_41=Exchange out
node_type_00=V0
node_type_01=V1
node_type_02=V2
node_type_03=V3
node_type_04=V4
node_type_10=Global Partners
kyc_auth_type_0=Personal account
kyc_auth_type_1=Business account
kyc_cert_type_0=ID card
kyc_cert_type_1=Passport
userType_00=System user
userType_01=APP user
approval_status_0=In review
approval_status_1=Approved
approval_status_2=Not approved
file_type_01=ID card front
file_type_02=ID card back
file_type_03=Passport
exchange_type_1=USD to coins
exchange_type_0=Coins to USD
file_type_04=Business license
kyc_cert_type_3=Not verified
vcc_trade_status_processing=Processing
vcc_trade_status_success=Success
vcc_trade_status_fail=Failure
txn_status_0=Failure
txn_status_1=Success
txn_status_2=Processing
coin_txn_status_2=Processing
coin_txn_status_1=Success
coin_txn_status_0=Failure
usd_txn_type_c2160=Expenditure
usd_txn_type_d2150=Expenditure refund
usd_txn_type_d2170=Card deletion refund
usd_txn_type_c2161=Transaction cancellation fee
usd_txn_type_c2181=Close transfer out
usd_txn_type_c2162=Transaction authorization fee
usd_txn_type_c2180=Balance transfer out
usd_txn_type_d2160=Expenditure reversal
usd_txn_type_c2150=Expenditure refund reversal
usd_txn_type_z2100=Authorization inquiry
commission_type_1=Card activation commission
commission_type_2=Recharge commission
commission_type_3=Node upgrade
valid_type_Y=Valid
valid_type_N=Invalid
trade_auth_type_0=Trade Password
trade_auth_type_1=Facial Recognition
usd_txn_type_c2022=Card withdrawal fee
