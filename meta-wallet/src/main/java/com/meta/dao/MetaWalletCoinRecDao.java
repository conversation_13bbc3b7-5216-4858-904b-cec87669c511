package com.meta.dao;

import com.meta.entity.MetaWalletCoinRec;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 多链用户钱包代币余额记录数据访问层
 */
@Repository
public interface MetaWalletCoinRecDao extends JpaRepository<MetaWalletCoinRec, Long> {

    /**
     * 查询指定时间范围内的余额记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 余额记录列表
     */
    List<MetaWalletCoinRec> findByCreateTimeBetween(Date startTime, Date endTime);

    /**
     * 根据代币符号查询余额记录
     *
     * @param tokenSymbol 代币符号
     * @return 余额记录列表
     */
    List<MetaWalletCoinRec> findByTokenSymbol(String tokenSymbol);

    /**
     * 根据链类型查询余额记录
     *
     * @param chainType 链类型
     * @return 余额记录列表
     */
    List<MetaWalletCoinRec> findByChainType(String chainType);

    /**
     * 根据链类型和代币符号查询余额记录
     *
     * @param chainType 链类型
     * @param tokenSymbol 代币符号
     * @return 余额记录列表
     */
    List<MetaWalletCoinRec> findByChainTypeAndTokenSymbol(String chainType, String tokenSymbol);

    /**
     * 查询指定时间范围内的钱包地址集合
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 钱包地址集合
     */
    @Query("SELECT DISTINCT m.walletAddress FROM MetaWalletCoinRec m WHERE m.createTime BETWEEN :startTime AND :endTime")
    Set<String> findDistinctWalletAddressByCreateTimeBetween(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询特定时间之后有记录的钱包地址
     *
     * @param startTime 开始时间
     * @return 钱包地址列表
     */
    @Query("SELECT DISTINCT m.walletAddress FROM MetaWalletCoinRec m WHERE m.createTime >= :startTime ")
    List<String> findRecentWalletAddresses(@Param("startTime") Date startTime);

    /**
     * 查询指定链类型、时间范围内的钱包地址集合
     *
     * @param chainType 链类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 钱包地址集合
     */
    @Query("SELECT DISTINCT m.walletAddress FROM MetaWalletCoinRec m WHERE m.chainType = :chainType AND m.createTime BETWEEN :startTime AND :endTime")
    Set<String> findDistinctWalletAddressByChainTypeAndCreateTimeBetween(@Param("chainType") String chainType, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
