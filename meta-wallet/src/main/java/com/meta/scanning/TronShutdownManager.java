package com.meta.scanning;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/3/31 11:11
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class TronShutdownManager {


    /**
     * 关闭TronThreadPoolUtil线程池和tron扫描
     * 1. 关闭线程池
     * 2. 关闭tron扫描
     * 3. 等待任务完成
     */
    @PreDestroy
    public void shutdown() {
        // 关闭tron扫描
        MagicianBlockchainScan.shutdownAll();
        // 关闭线程池
        TronThreadPoolUtil.shutdown(60, TimeUnit.SECONDS);
    }
}
