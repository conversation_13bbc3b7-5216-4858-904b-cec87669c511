package com.meta.scanning.biz.thread;

import cn.hutool.extra.spring.SpringUtil;
import com.meta.scanning.biz.thread.model.EventModel;
import com.meta.scanning.chain.ChainScanner;
import com.meta.scanning.chain.model.TransactionModel;
import com.meta.system.uitls.RedisConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.math.BigInteger;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 事件消费者
 */
public class EventConsumer implements Runnable {

    private Logger logger = LoggerFactory.getLogger(EventConsumer.class);

    /**
     * 主要用于扫描的类，目前有 3 个实现类，未来可以扩展
     */
    private ChainScanner chainScanner;

    /**
     * 队列，每次扫描到一个区块，就会将任务放入其中，扫描的数据将被异步处理，并根据需要调用 MonitorEvent
     */
    private EventQueue eventQueue;

    /**
     * 是否停止
     */
    private boolean shutdown;

    public EventConsumer(ChainScanner chainScanner, EventQueue eventQueue) {
        this.chainScanner = chainScanner;
        this.eventQueue = eventQueue;
        this.shutdown = false;
    }

    public void setShutdown(boolean shutdown) {
        this.shutdown = shutdown;
    }

    private final static BigInteger five = new BigInteger("5");

    @Override
    public void run() {

        while (true) {
            EventModel eventModel = null;

            try {
                eventModel = eventQueue.getLinkedBlockingQueue().poll(2000, TimeUnit.MILLISECONDS);

                if (eventModel == null) {
                    if (shutdown) {
                        return;
                    }
                    continue;
                }

                logger.info("[{}], 正在处理区块高度 [{}] 的交易记录", chainScanner.getChainType().toString(), eventModel.getCurrentBlockHeight());

                //每处理10个块更新redis记录里的块高度（重复扫描不要紧，但是不能漏掉）
                if (eventModel.getCurrentBlockHeight().mod(five).equals(BigInteger.ZERO)) {
                    StringRedisTemplate redisTemplate = SpringUtil.getBean(StringRedisTemplate.class);
                    String s = redisTemplate.opsForValue().get(RedisConstants.BIZ_BLOCK_NUMBER_TRON);
                    if (s == null || new BigInteger(s).compareTo(eventModel.getCurrentBlockHeight()) < 0) {
                        redisTemplate.opsForValue().set(RedisConstants.BIZ_BLOCK_NUMBER_TRON, eventModel.getCurrentBlockHeight().toString());
                    }
                }

                List<TransactionModel> transactionResultList = eventModel.getTransactionModels();

                for (TransactionModel transactionModel : transactionResultList) {
                    chainScanner.call(transactionModel);
                }
            } catch (Exception e) {
                if (eventModel == null) {
                    logger.error("[{}], 处理交易记录时发生异常", chainScanner.getChainType().toString(), e);
                } else {
                    logger.error("[{}], 处理交易记录时发生异常, 区块高度: [{}]", chainScanner.getChainType().toString(), eventModel.getCurrentBlockHeight(), e);
                }
            }
        }
    }
}
