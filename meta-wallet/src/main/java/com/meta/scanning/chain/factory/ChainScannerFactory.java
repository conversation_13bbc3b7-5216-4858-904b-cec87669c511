package com.meta.scanning.chain.factory;


import cn.hutool.extra.spring.SpringUtil;
import com.meta.scanning.chain.ChainScanner;
import com.meta.scanning.chain.impl.ETHChainScanner;
import com.meta.scanning.chain.impl.SolChainScanner;
import com.meta.scanning.chain.impl.TronChainScanner;
import com.meta.scanning.commons.enums.ChainType;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Factory class, get scanner
 */
public class ChainScannerFactory {


    /**
     * get scanner
     * @param chainType
     * @return
     */
    public static ChainScanner getChainScanner(ChainType chainType) {
        switch (chainType) {
            case ETH:
                return new ETHChainScanner();
            case SOL:
                return new SolChainScanner();
            case TRON:
                return new TronChainScanner();
//                return SpringUtil.getBean(TronChainScanner.class);
        }

        return null;
    }
}
