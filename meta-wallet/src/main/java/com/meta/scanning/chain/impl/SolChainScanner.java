package com.meta.scanning.chain.impl;



import com.meta.scanning.chain.ChainScanner;
import com.meta.scanning.chain.model.TransactionModel;

import java.math.BigInteger;

/**
 * scan the sol chain
 *
 * TODO In development.......
 */
public class SolChainScanner extends ChainScanner {

    @Override
    public void scan(BigInteger beginBlockNumber) {

    }

    @Override
    public void call(TransactionModel transactionModel) {

    }
}
