package com.meta.scanning;



import com.meta.scanning.biz.scan.ScanService;
import com.meta.scanning.biz.thread.EventThreadPool;
import com.meta.scanning.chain.RetryStrategy;
import com.meta.scanning.commons.config.BlockChainConfig;
import com.meta.scanning.commons.config.rpcinit.RpcInit;
import com.meta.scanning.commons.config.rpcinit.impl.EthRpcInit;
import com.meta.scanning.commons.config.rpcinit.impl.SolRpcInit;
import com.meta.scanning.commons.config.rpcinit.impl.TronRpcInit;
import com.meta.scanning.commons.enums.ChainType;
import com.meta.scanning.monitor.EthMonitorEvent;
import com.meta.scanning.monitor.TronMonitorEvent;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * 主类, 用于创建区块扫描任务
 */
public class MagicianBlockchainScan {

    /**
     * 业务类, 用于执行区块扫描逻辑
     */
    private final ScanService scanService;

    /**
     * 配置此区块扫描任务所需的参数
     */
    private final BlockChainConfig blockChainConfig;

    /**
     * 是否存在 RPC 地址
     */
    private boolean rpcUrlExist = false;

    /**
     * 所有扫描任务
     */
    private static List<MagicianBlockchainScan> magicianBlockchainScans = new ArrayList<>();

    private MagicianBlockchainScan(){
        scanService = new ScanService();
        blockChainConfig = new BlockChainConfig();
    }

    public static MagicianBlockchainScan create(){
        return new MagicianBlockchainScan();
    }

    /**
     * 设置区块链的 RPC 地址
     */
    public MagicianBlockchainScan setRpcUrl(RpcInit rpcInit){
        if(rpcInit instanceof EthRpcInit){
            blockChainConfig.setChainType(ChainType.ETH);
            blockChainConfig.setHttpService(rpcInit.getBlockChainConfig().getHttpService());
        } else if(rpcInit instanceof SolRpcInit){
            blockChainConfig.setChainType(ChainType.SOL);
        } else if(rpcInit instanceof TronRpcInit){
            blockChainConfig.setChainType(ChainType.TRON);
            blockChainConfig.setTronRpcUrls(rpcInit.getBlockChainConfig().getTronRpcUrls());
        }
        rpcUrlExist = true;
        return this;
    }

    /**
     * 设置重试策略
     */
    public MagicianBlockchainScan setRetryStrategy(RetryStrategy retryStrategy){
        blockChainConfig.setRetryStrategy(retryStrategy);
        return this;
    }

    /**
     * 设置扫描轮询间隔, 毫秒
     */
    public MagicianBlockchainScan setScanPeriod(long scanPeriod) {
        blockChainConfig.setScanPeriod(scanPeriod);
        return this;
    }

    /**
     * 设置扫描的起始区块高度
     */
    public MagicianBlockchainScan setBeginBlockNumber(BigInteger beginBlockNumber) {
        blockChainConfig.setBeginBlockNumber(beginBlockNumber);
        return this;
    }

    /**
     * 设置扫描的结束区块高度
     */
    public MagicianBlockchainScan setEndBlockNumber(BigInteger endBlockNumber) {
        blockChainConfig.setEndBlockNumber(endBlockNumber);
        return this;
    }

    /**
     * 添加 ETH 监控事件
     */
    public MagicianBlockchainScan addEthMonitorEvent(EthMonitorEvent ethMonitorEvent) {
        blockChainConfig.getEventConfig().addEthMonitorEvent(ethMonitorEvent);
        return this;
    }

    /**
     * 添加 TRON 监控事件
     */
    public MagicianBlockchainScan addTronMonitorEvent(TronMonitorEvent tronMonitorEvent) {
        blockChainConfig.getEventConfig().addTronMonitorEvents(tronMonitorEvent);
        return this;
    }

    /**
     * 启动任务
     */
    public void start() throws Exception {
        if (!rpcUrlExist) {
            throw new Exception("rpcUrl 不能为空");
        }

        if (blockChainConfig.getChainType() == null) {
            throw new Exception("ChainType 不能为空");
        }

        if (blockChainConfig.getScanPeriod() < 1) {
            throw new Exception("scanPeriod 必须大于 1");
        }

        if(blockChainConfig.getDelayed() < 0){
            throw new Exception("delayed 必须大于 0");
        }

        if (blockChainConfig.getChainType().equals(ChainType.ETH)
                && (blockChainConfig.getEventConfig() == null
                || blockChainConfig.getEventConfig().getEthMonitorEvent() == null
                || blockChainConfig.getEventConfig().getEthMonitorEvent().isEmpty())
        ) {
            throw new Exception("您需要至少设置一个监控事件");
        }

        if (blockChainConfig.getChainType().equals(ChainType.TRON)
                && (blockChainConfig.getEventConfig() == null
                || blockChainConfig.getEventConfig().getTronMonitorEvents() == null
                || blockChainConfig.getEventConfig().getTronMonitorEvents().isEmpty())
        ) {
            throw new Exception("您需要至少设置一个监控事件");
        }

        // 初始化 scanService
        scanService.init(blockChainConfig);

        // 执行扫描
        scanService.start();

        magicianBlockchainScans.add(this);
    }

    /**
     * 停止当前扫描任务
     */
    public void shutdown() {
        scanService.shutdown();
    }

    /**
     * 停止所有扫描任务
     */
    public static void shutdownAll(){
        for(MagicianBlockchainScan magicianBlockchainScan : magicianBlockchainScans){
            magicianBlockchainScan.shutdown();
        }
        magicianBlockchainScans.clear();
        magicianBlockchainScans = null;

        EventThreadPool.shutdown();
    }

    /**
     * 获取当前区块高度
     */
    public BigInteger getCurrentBlockHeight() {
        return scanService.getCurrentBlockHeight();
    }
}
