package com.meta.scanning;//package org.springblade.modules.tweak.monitor;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;

/**
 * 线程池
 */
@Slf4j
public class TronThreadPoolUtil {
    /**
     *  归集线程池
     */
    public static final ThreadPoolExecutor aggregationExecutor = new ThreadPoolExecutor(
            5,                      // 核心线程数
            10,                     // 最大线程数
            60L,                    // 空闲线程存活时间
            TimeUnit.SECONDS,       // 时间单位
            new ArrayBlockingQueue<>(100), // 队列容量
            r -> {
                Thread t = new Thread(r, "tron-aggregation-thread-" + r.hashCode());
                t.setUncaughtExceptionHandler((thread, e) -> {
                    System.err.println("Thread " + thread.getName() + " encountered exception: " + e.getMessage());
                    log.error("归集线程池异常",e);
                });
                return t;
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
    );

    /**
     * 回收能量线程池(延迟20s)
     */
    public static final ScheduledThreadPoolExecutor recycleEnergyExecutor = new ScheduledThreadPoolExecutor(
            2, // 核心线程数
            r -> {
                Thread t = new Thread(r, "tron-recycle-energy-thread-" + System.nanoTime());
                t.setUncaughtExceptionHandler((thread, e) -> {
                    // 记录异常日志
                    System.err.println("Thread " + thread.getName() + " encountered exception: " + e.getMessage());
                    e.printStackTrace();
                });
                return t;
            },
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    public static void scheduleTask(Runnable task) {
        recycleEnergyExecutor.schedule(task, 40, TimeUnit.SECONDS);
    }

    /**
     * 关闭线程池并等待任务完成
     * @param timeout 超时时间
     * @param unit 时间单位
     */
    public static void shutdown(long timeout, TimeUnit unit) {
        // 关闭归集线程池
        log.info("关闭归集线程池");
        aggregationExecutor.shutdown();
        try {
            // 等待任务完成
            if (!aggregationExecutor.awaitTermination(timeout, unit)) {
                // 超时强制关闭
                aggregationExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            // 发生中断时强制关闭
            aggregationExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        // 关闭回收能量线程池
        log.info("关闭回收能量线程池");
        recycleEnergyExecutor.shutdown();
        try {
            // 等待任务完成
            if (!recycleEnergyExecutor.awaitTermination(timeout, unit)) {
                // 超时强制关闭
                recycleEnergyExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            // 发生中断时强制关闭
            recycleEnergyExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

}
