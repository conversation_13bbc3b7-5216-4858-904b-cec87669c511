package com.meta.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.Transient;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/11/17:42
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Embeddable
public class CoinBalancePK implements Serializable {

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "coin_code")
    private String coinCode;


    @Transient
    private String type;

    @Transient
    private String md5sum;

    @Transient
    private String sysId;
}
