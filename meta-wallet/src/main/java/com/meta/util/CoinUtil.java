package com.meta.util;


import cn.hutool.core.codec.Base58;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meta.common.enums.BizError;
import com.meta.common.exception.CustomException;
import com.meta.common.utils.ByteArray;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.Sha256Sm3Hash;
import com.meta.common.utils.StringUtils;
import com.meta.config.WalletConfig;
import com.meta.contants.Constants;
import com.meta.entity.MisttrackLog;
import com.meta.redis.BizReidsLock;
import com.meta.scanning.tron.wallet.AddressUtils;
import com.meta.service.CoinBalanceService;
import com.meta.service.MisttrackLogService;
import com.meta.system.domain.MetaMainAddress;
import com.meta.system.domain.TrcCstaddress;
import com.meta.system.domain.TrcTransactions;
import com.meta.system.domain.app.CoinBalance;
import com.meta.system.domain.app.CoinBalancePK;
import com.meta.system.domain.app.CoinTxnDtl;
import com.meta.system.domain.app.ExchangeRate;
import com.meta.system.domain.partner.MetaPartnerAssetPool;
import com.meta.system.domain.partner.MetaPushData;
import com.meta.system.email.SendEmail;
import com.meta.system.enums.CoinType;
import com.meta.system.service.*;
import com.meta.system.service.impl.SysDictDataServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URI;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
public class CoinUtil {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private BizReidsLock bizRedisLock;

    @Autowired
    private CoinTxnDtlService coinTxnDtlService;

    @Autowired
    private ISysConfigService configService;
    @Autowired
    private CoinBalanceService coinBalanceService;

    @Autowired
    private SysDictDataServiceImpl dataService;
    @Autowired
    private MisttrackLogService misttrackLogService;

    @Autowired
    private SendEmail sendEmail;

    @Autowired
    private StringRedisTemplate redisTemplate;


    @Autowired
    private ExchangeRateService exchangeRateService;


    @Autowired
    private MetaPartnerAssetPoolService metaPartnerAssetPoolService;

    @Autowired
    private TrcCstaddressService trcCstaddressService;

    @Autowired
    private TrcTransactionsService trcTransactionsService;

    @Autowired
    private MetaPushDataService metaPushDataService;


    @Autowired
    private MQutils mQutils;

    //trc20网络上进行USDT的固定地址
    @Transactional(rollbackFor = Exception.class)
    public boolean getUsdtRechargeByApiByMerLin(String txid) {

        //限制多次请求同一个Txid
        if (!bizRedisLock.locks(txid, Constants.redisLock.TXID)) {
            throw new CustomException(MessageUtils.message(BizError.SYS_RECHARGE_BUSY.value()));
        }
        //MistTrack的API开关
        String apiSwitch = configService.selectConfigByKey("MistTrack_API_switch");
        boolean apiFlag = apiSwitch.equals("1");

        CoinTxnDtl dtl = new CoinTxnDtl();
        dtl.setReceiptNo(txid);
        CoinTxnDtl tt = coinTxnDtlService.findByReceiptNo(txid);
        if (tt != null) {
            //如果已经有人充值了， 那么就不需要
            logger.debug("已经有人充值了， 那么就不需要处理");
            return true;
        }
        //****************获取区块链:充值信息回调*******************
        Map<String, String> params = new HashMap<>(); //交易id 号
        //txid = "f173382ee866dc60f832bf43d571de36da59a210a2cf8b9f54065af3d6cd8fc8";
        //********************获取txid:查询区块链信息必备***********************
        //txid = this.getTxID();
        params.put("value", txid);
        HttpHeaders standardHeaders = new HttpHeaders();
        standardHeaders.setContentType(MediaType.APPLICATION_JSON);
        standardHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(params, standardHeaders);
        String route = "/wallet/gettransactionbyid";
        JSONObject jsonObject = new JSONObject();
        boolean needAppendToTable = false;
        CoinBalance cb = null;
        TrcTransactions trcTransactions = null;

        String contractAddress = configService.selectConfigByKey("trc20_contractAddress");
        String contractAddressForUsdc = WalletConfig.trc20AddressForUsdc;
        String contractAddressForGasFree = WalletConfig.trc20AddressForGasFree;
        CoinType coinType = null;

        String trc20_url = configService.selectConfigByKey("trc20_url");
        JSONObject dataJson = new JSONObject();//推送数据
        //入账钱包的最低值
        BigDecimal receiveAmount = new BigDecimal("10");
        try {
            ResponseEntity<String> responseEntity = new RestTemplate().exchange(
                    trc20_url + route, HttpMethod.POST, httpEntity, String.class);
            String gtbid = responseEntity.getBody();
            logger.debug(gtbid);
            JSONObject parseObject = JSONObject.parseObject(gtbid);
            JSONObject rawData = parseObject.getJSONObject("raw_data");
            if (rawData == null || rawData.isEmpty()) {
                throw new CustomException(MessageUtils.message(BizError.TRADE_TRANSACTIONID_ERROR.value()));
            }
            boolean raw_data = rawData.equals("{}\n");
            boolean contract = rawData.getJSONArray("contract").getJSONObject(0).equals("{}\n");
            boolean parameter = rawData.getJSONArray("contract").getJSONObject(0).getJSONObject("parameter").equals("{}\n");
            boolean value = rawData.getJSONArray("contract").getJSONObject(0).getJSONObject("parameter").getJSONObject("value").equals("{}\n");
            boolean owner_address = rawData.getJSONArray("contract").getJSONObject(0).getJSONObject("parameter").getJSONObject("value").getString("owner_address").equals("{}\n");
            if (gtbid.equals("{}\n") && !raw_data && !contract && !parameter && !value && !owner_address) {
                /*********************校验失败.***********************/
                logger.debug("交易号有误");
                throw new CustomException(MessageUtils.message(BizError.TRADE_TRANSACTIONID_ERROR.value()));
            }


            byte[] fromaddress = ByteArray.fromHexString(rawData.getJSONArray("contract").getJSONObject(0).getJSONObject("parameter").getJSONObject("value").getString("owner_address"));
            jsonObject.put("contractRet", parseObject.getJSONArray("ret").getJSONObject(0).getString("contractRet"));
            if (!"SUCCESS".equals(jsonObject.get("contractRet"))) {
                logger.debug("此交易未成功");
                throw new CustomException(MessageUtils.message(BizError.TRADE_TRANSACTION_ERROR.value()));
            }
            jsonObject.put("from", encode58Check(fromaddress));
            jsonObject.put("txID", JSONObject.parseObject(gtbid).getString("txID"));
            jsonObject.put("time", JSONObject.parseObject(gtbid).getJSONObject("raw_data").getString("timestamp"));
            fromaddress = ByteArray.fromHexString(rawData.getJSONArray("contract").getJSONObject(0).getJSONObject("parameter").getJSONObject("value").getString("contract_address"));
            //trc20网络上进行USDT的固定地址
            jsonObject.put("contranctaddress", encode58Check(fromaddress));
            //判断是否是转账
            String rzData = rawData.getJSONArray("contract").getJSONObject(0).getJSONObject("parameter").getJSONObject("value").getString("data");
            //判断是否是入账
            if (StringUtils.isEmpty(rzData)) {
                logger.error("交易为空");
                return false;
            }
            String transactionContractAddress = encode58Check(fromaddress);
            //这个才是trc20 即是usdt的转账
            String toAddress;
            if (transactionContractAddress.equals(contractAddress)
                    || transactionContractAddress.equals(contractAddressForGasFree)
                    || transactionContractAddress.equals(contractAddressForUsdc)) {
                //GasFree模式的USDT转账
                if (transactionContractAddress.equals(contractAddressForGasFree)) {
                    coinType = CoinType.USDT;
                    String data = rawData.getJSONArray("contract").getJSONObject(0).getJSONObject("parameter").getJSONObject("value").getString("data");
                    Map<String, String> map = AddressUtils.decryptHex(data, txid);
                    if (map == null || map.isEmpty()) {
                        throw new CustomException("地址解密失败");
                    }
                    jsonObject.put("to", map.get("address"));
                    toAddress = map.get("address");
                    jsonObject.put("value", new BigDecimal(map.get("quantity")).divide(BigDecimal.valueOf(1000000), 6, RoundingMode.DOWN));
                    jsonObject.put("fee", new BigDecimal(map.get("fee")).divide(BigDecimal.valueOf(1000000), 6, RoundingMode.DOWN));
                }
                //标准USDT转账或USDC转账
                else {
                    if (transactionContractAddress.equals(contractAddress)) {
                        coinType = CoinType.USDT;
                    } else {
                        coinType = CoinType.USDC;
                    }

                    toAddress = encode58Check(ByteArray.fromHexString("41" + rawData.getJSONArray("contract").getJSONObject(0).getJSONObject("parameter").getJSONObject("value").getString("data").substring(32, 72)));
                    jsonObject.put("to", toAddress);
                    String amount = rawData.getJSONArray("contract").getJSONObject(0).getJSONObject("parameter").getJSONObject("value").getString("data").substring(72).replaceAll("^(0+)", "");
                    BigInteger bigInteger = new BigInteger(amount, 16);
                    BigDecimal amountbig = new BigDecimal(bigInteger);
                    jsonObject.put("value", amountbig.divide(BigDecimal.valueOf(1000000)));

                    //获取消耗的trx gas 费用
                    route = "/wallet/gettransactioninfobyid";
                    ResponseEntity<String> responseEntity2 = new RestTemplate().exchange(
                            trc20_url + route, HttpMethod.POST, httpEntity, String.class);
                    String gtbid2 = responseEntity2.getBody();
                    logger.debug(String.valueOf(gtbid2));
                    JSONObject jsonObject2 = new JSONObject().parseObject(gtbid2);
                    if (jsonObject2.containsKey("fee")) {
                        BigInteger bigInteger2 = new BigInteger(jsonObject2.getString("fee"));
                        BigDecimal feevalue = (new BigDecimal(bigInteger2)).divide(BigDecimal.valueOf(1000000));
                        jsonObject.put("fee", feevalue);
                        logger.debug(String.valueOf(feevalue));
                    } else {
                        jsonObject.put("fee", BigDecimal.ZERO);
                    }
                }

                logger.info("充值信息获取成功：" + jsonObject.toString());
                needAppendToTable = true;


                //判断是否是公司归集钱包地址
                //from：r_from_address 交易钱包付款人
                String fromAdres = jsonObject.getString("from");
                //to: r_to_address   交易收款人
                String toAdres = jsonObject.getString("to");

                //判断是不是主钱包地址
                List<MetaMainAddress> mainAddressList = WalletConfig.mainAddressList;

                String sysId = "";
                String mainAddress = "";//主钱包地址
                boolean isMainAddress = false;//是否是主钱包地址
                Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
                String kazePaySysId = String.valueOf(accountId);


                //kazepay和H5的数据
                for (MetaMainAddress metaMainAddress : mainAddressList) {
                    String mAddress = metaMainAddress.getMainaddress();
                    logger.info("toAdres：" + toAdres);
                    if (toAdres.equals(mAddress)) {
                        sysId = metaMainAddress.getSysId();
                        mainAddress = mAddress;
                        isMainAddress = true;
                        break;
                    }

                }

                trcTransactions = trcTransactionsService.fingByTxid(txid);
                if (trcTransactions == null) {
                    logger.debug("TrcTransactions找不到交易数据");
                    throw new CustomException("TrcTransactions找不到交易数据");
                }
                dataJson.put("trcTransactions", (JSONObject) JSONObject.toJSON(trcTransactions));
                dataJson.put("isRecorded", false);//是否入账
                dataJson.put("txid", txid);
                dataJson.put("from", fromAdres);
                dataJson.put("to", toAdres);
                dataJson.put("netWork", "TRC20");
                dataJson.put("mainAddress", mainAddress);
                dataJson.put("amount", new BigDecimal(jsonObject.getString("value")));
                dataJson.put("use", jsonObject.getBigDecimal("fee"));
                dataJson.put("coinType", coinType.getCode());

                logger.info("kazePaySysId:" + kazePaySysId);
                logger.info("sysId:" + sysId);
                logger.info("mainAddress:" + mainAddress);

                //主钱包归集
                if (isMainAddress) {
                    if (kazePaySysId.equals(sysId)) {
                        TrcCstaddress trcCstaddress = trcCstaddressService.findByAddress(fromAdres);
                        CoinBalance coinBalance = coinBalanceService.findByUserIdAndCoinCode(trcCstaddress.getCstId(), coinType.getCode());
                        if (coinBalance != null) {
                            //归集扣减
                            String vamout = jsonObject.getString("value");
                            BigDecimal bamount = new BigDecimal(vamout);
                            logger.info("公司钱包归集" + bamount);
                            BigDecimal subtract = coinBalance.getUncltCoinBalance().subtract(bamount);
                            if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                                coinBalance.setUncltCoinBalance(BigDecimal.ZERO);
                            } else {
                                coinBalance.setUncltCoinBalance(subtract);
                            }

                            coinBalanceService.updateCoinBalanceData(coinBalance);
                            return true;
                        }
                        return false;
                    } else {
                        logger.info("----11------");
                        dataJson.put("isRecorded", true);
                        pushData("H5_LISTENING_DATA", sysId, dataJson);
                        return true;
                    }
                }
                //根据钱包地址查询用户
                logger.info("fromaddress:" + toAddress);
                TrcCstaddress trcAddress = trcCstaddressService.findByAddress(toAddress);
                sysId = trcAddress.getSysId();
                //是否是B端数据
                if (!kazePaySysId.equals(sysId)) {
                    String txID = jsonObject.getString("txID");
                    //from： 交易钱包付款人
                    String from = jsonObject.getString("from");
                    //to:   交易收款人
                    String to = jsonObject.getString("to");
                    //value: 交易金额
                    BigDecimal fee = jsonObject.getBigDecimal("fee");
                    BigDecimal amt = new BigDecimal(jsonObject.getString("value"));
                    //判断金额是否小于10
                    if (amt.compareTo(receiveAmount) < 0) {
                        logger.info("充值小于10的情况下不对数据做操作");
                        dataJson.put("isRecorded", false);//是否入账
                        logger.info("----22------");
                        pushData("H5_LISTENING_DATA", sysId, dataJson);
                        return false;
                    } else {
                        if (StringUtils.isNotEmpty(jsonObject.getString("time"))) {
                            long timevalue = jsonObject.getBigInteger("time").longValue();
                            dataJson.put("time", new Date(timevalue));

                        } else {
                            dataJson.put("time", new Date());

                        }
                        dataJson.put("isRecorded", true);//是否入账
                        logger.info("推送的数据:" + dataJson);
                        logger.info("----33------");
                        pushData("H5_LISTENING_DATA", sysId, dataJson);
                        if (trcTransactions.getIsSync()==0){
                            trcTransactions.setIsSync(1);
                            trcTransactionsService.updateTransactions(trcTransactions);
                        }
                        return true;
                    }


                }


                cb = coinBalanceService.findByUserIdAndCoinCode(trcAddress.getCstId(), coinType.getCode());
                if (cb == null) {
                    /*********************用户钱包地址不同，校验失败.***********************/
                    logger.debug("eeeeeeeeeeeeeee5");
                    throw new CustomException(MessageUtils.message(BizError.TRADE_TRANSACTIONID_ERROR.value()));
                }
            } else {
                logger.warn("不是指定token合约转账");
                return false;
            }
        } catch (Exception e) {
            throw e;
        }

        try {
            boolean flag = false;
            flag = jsonObject.get("contractRet").equals("SUCCESS");
            //获取充值信息
            if (flag && needAppendToTable) {
                //钱包地址：contranctaddress
                String contranctaddress = jsonObject.getString("contranctaddress");
                //txID
                String txID = jsonObject.getString("txID");
                //from：r_from_address 交易钱包付款人
                String from = jsonObject.getString("from");
                //to: r_to_address   交易收款人
                String to = jsonObject.getString("to");
                //value: r_receipt_amount 交易金额
                String value = jsonObject.getString("value");
                BigDecimal amount = new BigDecimal(value);
                //判断金额是否小于10
                if (amount.compareTo(receiveAmount) < 0) {
                    logger.info("充值小于10的情况下不对数据做操作");
                    return false;
                }

                BigDecimal fee = jsonObject.getBigDecimal("fee");

                /**********************归集钱包******************************/
//                String configValue = configService.selectConfigByKey("mainaddress"); //设置全局的归集钱包
//                SysConfig config = new SysConfig();
//                if (StringUtils.isEmpty(configValue)) {
//                    config.setConfigKey("mainaddress");
//                    config.setConfigName("归集钱包地址");
//                    //提交到master
//                    config.setConfigValue("TBa1ucdkbb79ijWXmTxTwjxCpjksDhEtT5");
//                    configService.insertConfig(config);
//                    configValue = "TBa1ucdkbb79ijWXmTxTwjxCpjksDhEtT5";
//                }
//                if (!to.equals(configValue)) {
//                    /*********************用户钱包地址不同，校验失败.***********************/
//                    throw new CustomException(MessageUtils.message(BizError.TRADE_TRANSACTIONID_ERROR.value()));
//                }
                /**********************归集钱包******************************/


                CoinBalancePK pk = new CoinBalancePK();
                pk.setCoinCode(coinType.getCode());
                pk.setUserId(cb.getUserId().longValue());
                System.out.println("userid：" + pk.getUserId());
                //钱包余额信息
                CoinBalance ccb = coinBalanceService.findById(pk);

                CoinTxnDtl r = new CoinTxnDtl();
                r.setUserId(cb.getUserId().longValue());
                r.setFromAddress(from);
                r.setToAddress(to);
                r.setReceiptNo(txid);
                r.setRecordType(Constants.RECORD_TYPE_RECEIPTS);
                r.setTxnCode(Constants.txnCode.d1010);
                r.setTxnCoin(coinType.getCode());
                r.setTxnDesc("充值");

                r.setCoinNet("TRC20");
                r.setTxnAmount(amount);
                r.setUserBalance(ccb.getCoinBalance().add(amount));
                r.setTxnFee(fee);
                if (StringUtils.isNotEmpty(jsonObject.getString("time"))) {
                    long timevalue = jsonObject.getBigInteger("time").longValue();
                    r.setTxnTime(new Date(timevalue));
                } else {

                    r.setTxnTime(new Date());
                }
                r.setCreateTime(new Date()); // 获取时间错

                int txnStatus = 1;//交易状态0待审核1已完成2已拒绝--> 0失败 1完成 2处理中
                boolean apiResult = false;//api是否获取到结果

                if (apiFlag) {
                    int mistTrackMillion = Integer.valueOf(configService.selectConfigByKey("MistTrack_million"));
                    String key = Constants.redisLock.REDIS_LOCK + "misttrack_api:transfer_time";

                    synchronized (this) {

                        boolean bkflag = redisTemplate.opsForValue().setIfAbsent(key, "1", mistTrackMillion * 1000, TimeUnit.MILLISECONDS);

                        if (!bkflag) {
                            long millis = redisTemplate.opsForValue().getOperations().getExpire(key, TimeUnit.MILLISECONDS);

                            if (millis > 0) {

                                Thread.sleep(millis);
                            }
                            redisTemplate.opsForValue().setIfAbsent(key, "1", mistTrackMillion * 1000, TimeUnit.MILLISECONDS);
                        }

                    }


                    //获取地址标签url
                    String url_1 = configService.selectConfigByKey("MistTrack_Url") + "/address_labels";
                    logger.info("获取地址标签url：" + url_1);
                    //获取风险评分url
                    String url_2 = configService.selectConfigByKey("MistTrack_Url") + "/risk_score";
                    logger.info("获取风险评分url：" + url_2);
                    String api_key = configService.selectConfigByKey("MistTrack_api_key");
                    String coin = "USDT-TRC20";
                    String address = from;
                    logger.info("api_key：" + api_key);
                    logger.info("coin：" + coin);
                    logger.info("address：" + address);


                    MultiValueMap<String, String> paramMap = new LinkedMultiValueMap<>();
                    paramMap.add("api_key", api_key);
                    paramMap.add("coin", coin);
                    paramMap.add("address", address);
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
                    HttpEntity<MultiValueMap<String, Object>> hEntity = new HttpEntity<MultiValueMap<String, Object>>(null, headers);


                    //获取地址标签
                    URI uri = UriComponentsBuilder.fromHttpUrl(url_1).queryParams(paramMap).build().encode().toUri();
                    ResponseEntity<String> response = new RestTemplate().exchange(uri, HttpMethod.GET, hEntity, String.class);
                    String addressMes = response.getBody();
                    JSONObject addressObject = JSONObject.parseObject(addressMes);
                    MisttrackLog misttrackLog = new MisttrackLog();
                    misttrackLog.setFromAddress(from);
                    misttrackLog.setToAddress(to);
                    misttrackLog.setCreateTime(new Date());
                    misttrackLog.setAmount(r.getTxnAmount());

                    misttrackLog.setCoin(coin);
                    misttrackLog.setTxid(txid);
                    misttrackLog.setUserId(cb.getUserId().longValue());
                    if (addressObject.get("success").equals(true)) {
                        JSONObject dataObject = JSONObject.parseObject(addressObject.get("data").toString());
                        JSONArray dataArr = dataObject.getJSONArray("label_list");
                        //判断是否存在
                        if (dataArr.size() > 0 && StringUtils.isNotEmpty(dataService.selectDictLabel("misttrack_address_label", dataArr.get(0).toString()))) {
                            //未归集金额增加
                            logger.info("获取地址标签存在：" + dataArr.get(0).toString());
                            logger.info("1未归集金额增加:" + r.getTxnAmount());
                            ccb.setUncltCoinBalance(ccb.getUncltCoinBalance().add(r.getTxnAmount()));
                            misttrackLog.setAddressLabel(dataArr.get(0).toString());
                            apiResult = true;
                        }
                    }
                    if (!apiResult) {
                        //获取风险评分
                        URI uri2 = UriComponentsBuilder.fromHttpUrl(url_2).queryParams(paramMap).build().encode().toUri();
                        ResponseEntity<String> response2 = new RestTemplate().exchange(uri2, HttpMethod.GET, hEntity, String.class);
                        String storeMes = response2.getBody();
                        JSONObject scoreObject = JSONObject.parseObject(storeMes);
                        if (scoreObject.get("success").equals(true)) {

                            String data = scoreObject.get("data").toString();
                            if (!(data.equals("{}") || data.equals("{}\n") || StringUtils.isEmpty(data))) {
                                String dataScore = JSONObject.parseObject(data).get("score").toString();
                                String riskLevel = JSONObject.parseObject(data).get("risk_level").toString();

                                apiResult = true;
                                int score = Integer.valueOf(dataScore);
                                logger.info("获取风险评分:" + score);
                                logger.info("获取风险等级:" + riskLevel);
                                misttrackLog.setScore(String.valueOf(score));
                                misttrackLog.setLevel(riskLevel);
                                int unclt = Integer.valueOf(dataService.selectDictLabel("misttrack_score", "unclt"));
                                logger.info("未归集金额最大值设置" + unclt);
                                int freeze = Integer.valueOf(dataService.selectDictLabel("misttrack_score", "freeze"));
                                logger.info("冻结金额最大值设置" + freeze);


                                if (score >= 0 && score <= unclt) {//低风险
                                    //未归集金额增加
                                    logger.info("2未归集金额增加:" + r.getTxnAmount());
                                    ccb.setUncltCoinBalance(ccb.getUncltCoinBalance().add(r.getTxnAmount()));


                                } else if (score > unclt && score <= freeze) {//中风险
                                    //冻结金额增加
                                    logger.info("冻结金额增加:" + r.getTxnAmount());
                                    txnStatus = 2;
                                    ccb.setFreezeBalance(ccb.getFreezeBalance().add(r.getTxnAmount()));
                                    ccb.setUncltCoinBalance(ccb.getUncltCoinBalance().add(r.getTxnAmount()));
                                    sendEmail.remindMail("调用MistTrack的API，评分中风险：" + score + "，txid:" + txid + "，冻结金额：" + r.getTxnAmount() + "。", "调用MistTrack-API的信息提醒");

                                } else if (score > freeze) {//高风险
                                    //交易状态
                                    r.setTxnStatus(0);
                                    //后台未加服务费
                                    coinTxnDtlService.save(r);
                                    misttrackLogService.save(misttrackLog);
                                    sendEmail.remindMail("调用MistTrack的API，评分高风险：" + score + "，txid:" + txid + "。", "调用MistTrack-API的信息提醒");
                                    return false;
                                }

                            }


                        }

                    }

                    //两个接口调用api没成功
                    if (!apiResult) {
                        logger.info("调用MistTrack的API，标签地址和风险评分没获取数据，冻结金额增加:" + r.getTxnAmount());
                        txnStatus = 2;
                        ccb.setFreezeBalance(ccb.getFreezeBalance().add(r.getTxnAmount()));
                        ccb.setUncltCoinBalance(ccb.getUncltCoinBalance().add(r.getTxnAmount()));
                        sendEmail.remindMail("调用MistTrack的API，标签地址和风险评分没获取数据，txid:" + txid + "，冻结金额：" + r.getTxnAmount() + "。", "调用MistTrack-API的信息提醒");
                    }

                    misttrackLogService.save(misttrackLog);
                } else {
                    //MistTrack的api关闭的时候--未归集金额增加
                    logger.info("MistTrack的api关闭的时候--未归集金额增加:" + r.getTxnAmount());
                    ccb.setUncltCoinBalance(ccb.getUncltCoinBalance().add(r.getTxnAmount()));
                }

                //交易状态
                r.setTxnStatus(txnStatus);
                //后台未加服务费
                coinTxnDtlService.save(r);
                if (!apiFlag || txnStatus == 1) {
                    if (ccb.getCoinBalance() == null) {
                        ccb.setCoinBalance(new BigDecimal(0));
                    }
                    //判断是否是B端用户
                    MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartner(cb.getUserId());
                    if (metaPartnerAssetPool != null) {
                        //判断是否自动入池子
                        //并且金额大于等于入池子的金额
                        if (metaPartnerAssetPool.getAuto() == '0' && metaPartnerAssetPool.getAutoAmt() != null && r.getTxnAmount().compareTo(metaPartnerAssetPool.getAutoAmt()) >= 0) {

                            // 获取兑换利率
                            ExchangeRate rate = exchangeRateService.findByFromCurrencyCodeAndToCurrencyCode(coinType.getCode(), "USD");
                            BigDecimal rateAmount = r.getTxnAmount().multiply(rate.getRateVal());
                            rateAmount = rateAmount.setScale(2, RoundingMode.DOWN); // 保留两位小数,且向下取整
                            //个人兑换充值流水
                            metaPartnerAssetPool.setUsdtBalacne(metaPartnerAssetPool.getUsdtBalacne().add(rateAmount));

                            metaPartnerAssetPoolService.toPupdealDtl(metaPartnerAssetPool, coinType.getCode(), rateAmount, txid);
                            metaPartnerAssetPoolService.updateBalance(cb.getUserId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());

                        } else {
                            ccb.setCoinBalance(ccb.getCoinBalance().add(r.getTxnAmount()));
                        }
                    } else {
                        ccb.setCoinBalance(ccb.getCoinBalance().add(r.getTxnAmount()));
                    }


                }
                coinBalanceService.updateCoinBalanceData(ccb);
                if (trcTransactions.getIsSync()==0){
                    trcTransactions.setIsSync(1);
                    trcTransactionsService.updateTransactions(trcTransactions);
                }

                return true;

            }

        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            bizRedisLock.unlocks(txid, Constants.redisLock.TXID);
        }
        return false;
    }

    /**
     * 推送数据
     *
     * @param type
     * @param sysId
     * @param json
     */
    @Transactional
    public void pushData(String type, String sysId, JSONObject json) {
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartnerBySysId(sysId);
        String apiUrl = metaPartnerAssetPool.getApiUrl();
        MetaPushData metaPushData = metaPushDataService.creatPashData(sysId, "", "", apiUrl, JSON.toJSONString(json), type);
        metaPushDataService.save(metaPushData);
        if (StringUtils.isNotEmpty(apiUrl)) {
            // 发送消息（确保在事务提交后发送）
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    String routingKey = "";
                    if ("H5_LISTENING_DATA".equals(type)) {
                        routingKey = com.meta.common.constant.Constants.routing_key_h5_listening;
                    }
                    mQutils.send(metaPushData, routingKey, 3);
                }
            });
        }
    }

    public static String encode58Check(byte[] input) {
        byte[] hash0 = Sha256Sm3Hash.hash(input);
        byte[] hash1 = Sha256Sm3Hash.hash(hash0);
        byte[] inputCheck = new byte[input.length + 4];
        System.arraycopy(input, 0, inputCheck, 0, input.length);
        System.arraycopy(hash1, 0, inputCheck, input.length, 4);
        return Base58.encode(inputCheck);
    }
}
