package com.meta.util;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 安全的AES加密工具类
 * 支持多种AES加密模式
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
public class SecureAESUtil {

    // AES算法
    private static final String ALGORITHM = "AES";
    
    // 不同的加密模式
    public enum AESMode {
        ECB("AES/ECB/PKCS5Padding"),           // 简单模式，不推荐
        CBC("AES/CBC/PKCS5Padding"),           // 推荐模式
        GCM("AES/GCM/NoPadding");              // 最安全模式

        private final String transformation;

        AESMode(String transformation) {
            this.transformation = transformation;
        }

        public String getTransformation() {
            return transformation;
        }
    }

    /**
     * ECB模式加密（不推荐用于生产环境）
     * 优点：简单，无需IV
     * 缺点：相同明文产生相同密文，安全性低
     */
    public static String encryptECB(String data, String secretKey) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(AESMode.ECB.getTransformation());
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            
            byte[] encrypted = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encrypted);
            
        } catch (Exception e) {
            throw new RuntimeException("ECB加密失败: " + e.getMessage(), e);
        }
    }

    /**
     * ECB模式解密
     */
    public static String decryptECB(String encryptedData, String secretKey) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(AESMode.ECB.getTransformation());
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            
            byte[] encrypted = Base64.getDecoder().decode(encryptedData);
            byte[] decrypted = cipher.doFinal(encrypted);
            return new String(decrypted, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            throw new RuntimeException("ECB解密失败: " + e.getMessage(), e);
        }
    }

    /**
     * CBC模式加密（推荐）
     * 优点：安全性高，相同明文产生不同密文
     * 缺点：需要IV，串行处理
     */
    public static String encryptCBC(String data, String secretKey) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(AESMode.CBC.getTransformation());
            
            // 生成随机IV
            byte[] iv = new byte[16]; // AES块大小为16字节
            new SecureRandom().nextBytes(iv);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            byte[] encrypted = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            
            // 将IV和密文组合
            byte[] result = new byte[iv.length + encrypted.length];
            System.arraycopy(iv, 0, result, 0, iv.length);
            System.arraycopy(encrypted, 0, result, iv.length, encrypted.length);
            
            return Base64.getEncoder().encodeToString(result);
            
        } catch (Exception e) {
            throw new RuntimeException("CBC加密失败: " + e.getMessage(), e);
        }
    }

    /**
     * CBC模式解密
     */
    public static String decryptCBC(String encryptedData, String secretKey) {
        try {
            byte[] data = Base64.getDecoder().decode(encryptedData);
            
            // 提取IV和密文
            byte[] iv = new byte[16];
            byte[] encrypted = new byte[data.length - 16];
            System.arraycopy(data, 0, iv, 0, 16);
            System.arraycopy(data, 16, encrypted, 0, encrypted.length);
            
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(AESMode.CBC.getTransformation());
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] decrypted = cipher.doFinal(encrypted);
            return new String(decrypted, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            throw new RuntimeException("CBC解密失败: " + e.getMessage(), e);
        }
    }

    /**
     * GCM模式加密（最安全）
     * 优点：认证加密，提供完整性验证，支持并行
     * 缺点：实现复杂
     */
    public static String encryptGCM(String data, String secretKey) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(AESMode.GCM.getTransformation());
            
            // 生成随机IV（GCM推荐12字节）
            byte[] iv = new byte[12];
            new SecureRandom().nextBytes(iv);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(128, iv); // 128位认证标签
            
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, gcmSpec);
            byte[] encrypted = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            
            // 将IV和密文组合
            byte[] result = new byte[iv.length + encrypted.length];
            System.arraycopy(iv, 0, result, 0, iv.length);
            System.arraycopy(encrypted, 0, result, iv.length, encrypted.length);
            
            return Base64.getEncoder().encodeToString(result);
            
        } catch (Exception e) {
            throw new RuntimeException("GCM加密失败: " + e.getMessage(), e);
        }
    }

    /**
     * GCM模式解密
     */
    public static String decryptGCM(String encryptedData, String secretKey) {
        try {
            byte[] data = Base64.getDecoder().decode(encryptedData);
            
            // 提取IV和密文
            byte[] iv = new byte[12];
            byte[] encrypted = new byte[data.length - 12];
            System.arraycopy(data, 0, iv, 0, 12);
            System.arraycopy(data, 12, encrypted, 0, encrypted.length);
            
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(AESMode.GCM.getTransformation());
            GCMParameterSpec gcmSpec = new GCMParameterSpec(128, iv);
            
            cipher.init(Cipher.DECRYPT_MODE, keySpec, gcmSpec);
            byte[] decrypted = cipher.doFinal(encrypted);
            return new String(decrypted, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            throw new RuntimeException("GCM解密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成安全的AES密钥
     */
    public static String generateSecretKey(int keySize) {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
            keyGenerator.init(keySize); // 128, 192, 或 256
            SecretKey secretKey = keyGenerator.generateKey();
            return Base64.getEncoder().encodeToString(secretKey.getEncoded());
        } catch (Exception e) {
            throw new RuntimeException("生成密钥失败: " + e.getMessage(), e);
        }
    }

    /**
     * 演示不同加密模式
     */
    public static void demonstrateAESModes() {
        String data = "f90bd80f933cfef74730d69c133b85288c920ffc253300fe8cb326f668eba517";
        String secretKey = "MetaWallet123456"; // 16字节密钥

        System.out.println("=== AES加密模式演示 ===");
        System.out.println("原始数据: " + data);
        System.out.println();

        // ECB模式
        System.out.println("1. ECB模式 (不推荐):");
        String ecbEncrypted = encryptECB(data, secretKey);
        String ecbDecrypted = decryptECB(ecbEncrypted, secretKey);
        System.out.println("  加密结果: " + ecbEncrypted.substring(0, 32) + "...");
        System.out.println("  解密成功: " + data.equals(ecbDecrypted));
        
        // 测试ECB的弱点：相同明文产生相同密文
        String ecbEncrypted2 = encryptECB(data, secretKey);
        System.out.println("  相同明文加密结果相同: " + ecbEncrypted.equals(ecbEncrypted2));
        System.out.println();

        // CBC模式
        System.out.println("2. CBC模式 (推荐):");
        String cbcEncrypted = encryptCBC(data, secretKey);
        String cbcDecrypted = decryptCBC(cbcEncrypted, secretKey);
        System.out.println("  加密结果: " + cbcEncrypted.substring(0, 32) + "...");
        System.out.println("  解密成功: " + data.equals(cbcDecrypted));
        
        // 测试CBC的优点：相同明文产生不同密文
        String cbcEncrypted2 = encryptCBC(data, secretKey);
        System.out.println("  相同明文加密结果不同: " + !cbcEncrypted.equals(cbcEncrypted2));
        System.out.println();

        // GCM模式
        System.out.println("3. GCM模式 (最安全):");
        String gcmEncrypted = encryptGCM(data, secretKey);
        String gcmDecrypted = decryptGCM(gcmEncrypted, secretKey);
        System.out.println("  加密结果: " + gcmEncrypted.substring(0, 32) + "...");
        System.out.println("  解密成功: " + data.equals(gcmDecrypted));
        
        // 测试GCM的优点：认证加密
        String gcmEncrypted2 = encryptGCM(data, secretKey);
        System.out.println("  相同明文加密结果不同: " + !gcmEncrypted.equals(gcmEncrypted2));
        System.out.println("  提供完整性验证: ✓");
        System.out.println();

        System.out.println("推荐使用顺序: GCM > CBC > ECB");
    }

    public static void main(String[] args) {
        demonstrateAESModes();
    }
}
