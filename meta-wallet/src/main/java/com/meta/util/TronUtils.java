package com.meta.util;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.meta.common.exception.CustomException;
import com.meta.config.WalletConfig;
import com.meta.system.domain.TrcCstaddress;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.tron.trident.abi.FunctionEncoder;
import org.tron.trident.abi.TypeReference;
import org.tron.trident.abi.datatypes.Address;
import org.tron.trident.abi.datatypes.Bool;
import org.tron.trident.abi.datatypes.Function;
import org.tron.trident.abi.datatypes.generated.Uint256;
import org.tron.trident.core.ApiWrapper;
import org.tron.trident.core.contract.Trc20Contract;
import org.tron.trident.core.exceptions.IllegalException;
import org.tron.trident.proto.Chain;
import org.tron.trident.proto.Common;
import org.tron.trident.proto.Response;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2025/2/20 17:09
 **/
@Slf4j
public class TronUtils {

    public final static BigInteger million = new BigInteger("1000000");

    private static final AtomicInteger apiKeyIndex = new AtomicInteger(0);

    public static ApiWrapper getApiWrapper(String hexPrivateKey) {
        // 密钥解密 判断传入的密钥是否需要解密
        String decryptedPrivateKey = SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(hexPrivateKey);

        if (WalletConfig.turnMainNetwork) {
            int length = WalletConfig.apiKeyList.length;
            int index = apiKeyIndex.getAndUpdate(i -> i == Integer.MAX_VALUE ? 0 : i + 1) % length;
            if (index < 0) index += length;
            return ApiWrapper.ofMainnet(decryptedPrivateKey, WalletConfig.apiKeyList[index]);
        } else {
            return ApiWrapper.ofShasta(decryptedPrivateKey);
        }
    }

    /**
     * trc20转账
     */
    public static void trc20Transfer(TrcCstaddress fromWallet, BigInteger amount, String toAddress, String contractAddress) {
        ApiWrapper apiWrapper = TronUtils.getApiWrapper(fromWallet.getCstTrcPrivate());

        Trc20Contract trc20Contract = new Trc20Contract(apiWrapper.getContract(contractAddress), fromWallet.getCstAddress(), apiWrapper);
        String hash;
        BigInteger energyPrice = TronUtils.getEnergyPrice();
        BigInteger energyBurnTrx = WalletConfig.burnEnergyLimit.multiply(energyPrice);
        try {
            hash = trc20Contract.transfer(toAddress, amount.longValue(), 0, "", energyBurnTrx.longValue());
        } catch (RuntimeException e) {
            throw new CustomException("转账失败" + e);
        } finally {
            apiWrapper.close();
        }
        //确认hash结果
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            log.warn("线程休眠异常", e);
        }
        String urlQuery = WalletConfig.tronApiUrl + "/wallet";
        JSONObject queryJson = new JSONObject();
        queryJson.append("value", hash);
        queryJson.append("visible", true);
        String jsonObjectResult = HttpUtil.post(urlQuery + "/gettransactionbyid", queryJson.toString());

        JSONObject entries = JSONUtil.parseObj(jsonObjectResult);

        if (!ObjectUtils.isEmpty(entries.get("ret"))) {
            if (entries.get("ret").toString().indexOf("SUCCESS") > 0) {
                log.info("查询trc20转账成功，txid:" + hash);
            } else if (entries.get("ret").toString().indexOf("OUT_OF_ENERGY") > 0) {
                throw new CustomException("查询trc20转账失败，交易api报错，超过trx或能量限制");
            } else {
                throw new CustomException(String.format("查询trc20转账失败，txid:%s", entries.getJSONArray("txid").get(0).toString()));
            }
        } else {
            throw new CustomException("查询trc20转账失败，交易api报错，txid:" + hash);
        }
    }

    /**
     * trx转账
     */
    public static void trxTransfer(TrcCstaddress fromWallet, BigInteger amount, String toAddress) {
        ApiWrapper apiWrapper = TronUtils.getApiWrapper(fromWallet.getCstTrcPrivate());
        Response.TransactionExtention transfer;
        try {
            transfer = apiWrapper.transfer(fromWallet.getCstAddress(), toAddress, amount.longValue());
        } catch (IllegalException e) {
            apiWrapper.close();
            throw new CustomException(String.format("参数错误，转账失败: fromWallet:%s;toAddress:%s;amount:%s", fromWallet.getCstAddress(), toAddress, amount.longValue()), e);
        }
        //签名
        Chain.Transaction signedTxn = apiWrapper.signTransaction(transfer);
        //广播
        apiWrapper.broadcastTransaction(signedTxn);

        Response.TransactionReturn result = transfer.getResult();
        if (!result.getResult()) {
            apiWrapper.close();
            log.error("转账失败: code:{}，message:{}", result.getCode().getNumber(), result.getMessage());
            throw new CustomException(String.format("转账失败(%s)", result.getMessage()));
        }
        apiWrapper.close();
    }

    /**
     * 代理能量
     */
    public static void delegateResourceV2(TrcCstaddress fromWallet, String toAddress, BigInteger balance, Boolean lock, Integer lockTime, Integer permissionId) {
        ApiWrapper apiWrapper = TronUtils.getApiWrapper(fromWallet.getCstTrcPrivate());
        try {
            // resourceCode 1能量  0带宽
            Response.TransactionExtention transactionExtention = apiWrapper.delegateResourceV2(fromWallet.getCstAddress(), balance.longValue(), 1, toAddress, lock, lockTime); //创建交易
//            // 多签
//            Chain.Transaction.raw.Builder raw = transactionExtention.getTransaction().getRawData().toBuilder();
//            Chain.Transaction.Contract.Builder contract = raw.getContract(0).toBuilder().setPermissionId(permissionId);
//            raw.clearContract();
//            raw.addContract(contract);
//            Chain.Transaction transaction = transactionExtention.getTransaction().toBuilder().setRawData(raw).build();
            Chain.Transaction signedTxn = apiWrapper.signTransaction(transactionExtention); //签名交易
            String hash = apiWrapper.broadcastTransaction(signedTxn);

            //确认hash结果
            TronUtils.confirmResult(fromWallet.getCstTrcPrivate(), hash);
        } catch (Exception e) {
            throw new CustomException(e.getMessage());
        } finally {
            apiWrapper.close();
        }
    }

    /**
     * 能量回收
     */
    public static void unDelegateResource(TrcCstaddress fromWallet, String toAddress, BigInteger balance, Integer permissionId) {
        ApiWrapper wrapper = getApiWrapper(fromWallet.getCstTrcPrivate());
        try {
            // resourceCode 1能量  0带宽
            //创建交易
            Response.TransactionExtention transactionExtention = wrapper.undelegateResource(fromWallet.getCstAddress(), balance.longValue(), 1, toAddress);
//            // 多签
//            Chain.Transaction.raw.Builder raw = transactionExtention.getTransaction().getRawData().toBuilder();
//            Chain.Transaction.Contract.Builder contract = raw.getContract(0).toBuilder().setPermissionId(permissionId);
//            raw.clearContract();
//            raw.addContract(contract);
//            Chain.Transaction transaction = transactionExtention.getTransaction().toBuilder().setRawData(raw).build();
            //签名交易
            Chain.Transaction signedTxn = wrapper.signTransaction(transactionExtention);
            //广播交易
            String hash = wrapper.broadcastTransaction(signedTxn);
            //确认hash结果
            TronUtils.confirmResult(fromWallet.getCstTrcPrivate(), hash);
        } catch (Exception e) {
            throw new CustomException(e.getMessage());
        } finally {
            wrapper.close();
        }
    }


    /**
     * 获取权限id
     */
    public static List<Common.Permission> getPermissionList(String privateKey, String address) {
        ApiWrapper wrapper = TronUtils.getApiWrapper(privateKey);
        List<Common.Permission> activePermissionList = wrapper.getAccount(address).getActivePermissionList();
        wrapper.close();
        return activePermissionList;
    }

    /**
     * 判断能量是否足够
     */
    public static Boolean enough(String privateKey, String address, int energyNumber) {
        ApiWrapper wrapper = TronUtils.getApiWrapper(privateKey);
        Response.AccountResourceMessage resourceMessage = wrapper.getAccountResource(address);
        boolean b = resourceMessage.getEnergyLimit() - resourceMessage.getEnergyUsed() > energyNumber;
        wrapper.close();
        return b;
    }

    /**
     * 查询 质押trx获取能量的数量（比例）
     */
    public static BigDecimal getTrxEnergyRatioByHttp(String address) {
        String url = WalletConfig.tronScanUrl + "/api/accountv2?address=" + address;
        String result = HttpUtil.get(url);
        JSONObject jsonObject = JSONUtil.parseObj(result);
        if (!jsonObject.isEmpty()) {
            BigDecimal energyCost = new BigDecimal(jsonObject.get("energyCost").toString());
            //保留4位
            return energyCost.setScale(6, RoundingMode.UP);
        }
        log.error("获取质押 1 TRX 可获得多少能量失败");
        return null;
    }

    /**
     * 获取每笔订单需要消耗多少能量
     */
    public static long getNeedEnergy(TrcCstaddress fromWallet, String toAddress, String contractAddress) {
        ApiWrapper client = TronUtils.getApiWrapper(fromWallet.getCstTrcPrivate());
        Function trc20Transfer = new Function("transfer",
                Arrays.asList(new Address(toAddress),
                        new Uint256(BigInteger.valueOf(1).multiply(BigInteger.valueOf(10).pow(6)))),
                Collections.singletonList(new TypeReference<Bool>() {
                }));
        String encodedHex = FunctionEncoder.encode(trc20Transfer);

        Response.TransactionExtention transactionExtention = client.triggerConstantContract(fromWallet.getCstAddress(), contractAddress, encodedHex,
                0L, 0L, null);
//        Response.TransactionReturn result = transactionExtention.getResult();
//        log.info("字节数（带宽）:{}", transactionExtention.toByteArray().length);
        long energyUsed = transactionExtention.getEnergyUsed();
        client.close();
        return energyUsed;
    }

    /**
     * 获取钱包资源信息
     */
    public static Response.AccountResourceMessage getAccountResource(TrcCstaddress targetWallet) {
        ApiWrapper wrapper = TronUtils.getApiWrapper(targetWallet.getCstTrcPrivate());
        Response.AccountResourceMessage resourceMessage = wrapper.getAccountResource(targetWallet.getCstAddress());
        wrapper.close();
        return resourceMessage;
    }

    /**
     * 获取钱包剩余可用能量
     */
    public static long getAvailableEnergy(TrcCstaddress targetWallet) {
        ApiWrapper wrapper = TronUtils.getApiWrapper(targetWallet.getCstTrcPrivate());
        Response.AccountResourceMessage resourceMessage = wrapper.getAccountResource(targetWallet.getCstAddress());
        long l = resourceMessage.getEnergyLimit() - resourceMessage.getEnergyUsed();
        wrapper.close();
        return l;
    }

    /**
     * 获取钱包剩余可用能量
     */
    public static long getAvailableEnergy(Response.AccountResourceMessage resourceMessage) {
        return resourceMessage.getEnergyLimit() - resourceMessage.getEnergyUsed();
    }

    /**
     * 获取钱包剩余可用带宽
     */
    public static long getAvailableWidth(TrcCstaddress targetWallet) {
        ApiWrapper wrapper = TronUtils.getApiWrapper(targetWallet.getCstTrcPrivate());
        Response.AccountResourceMessage resourceMessage = wrapper.getAccountResource(targetWallet.getCstAddress());
        long l = resourceMessage.getFreeNetLimit() - resourceMessage.getFreeNetUsed();
        wrapper.close();
        return l;
    }

    /**
     * 获取钱包剩余可用带宽
     */
    public static long getAvailableWidth(Response.AccountResourceMessage resourceMessage) {
        return resourceMessage.getFreeNetLimit() - resourceMessage.getFreeNetUsed();
    }

    /**
     * 获取钱包数据
     */
    public static Response.Account getAccount(TrcCstaddress toWallet) {
        ApiWrapper wrapper = TronUtils.getApiWrapper(toWallet.getCstTrcPrivate());
        Response.Account account = wrapper.getAccount(toWallet.getCstAddress());
        wrapper.close();
        return account;
    }

    /**
     * 获取能量的trx单价
     */
    public static BigInteger getEnergyPrice() {
        String url = WalletConfig.tronApiUrl + "/wallet/getenergyprices";

        String result = HttpUtil.get(url);
        JSONObject jsonObject = JSONUtil.parseObj(result);
        if (!jsonObject.isEmpty()) {
            String prices = jsonObject.getStr("prices");
            String[] priceEntries = prices.split(",");
            // 获取最后一项数据（即最后一个价格和时间戳）
            String lastEntry = priceEntries[priceEntries.length - 1];
            String[] parts = lastEntry.split(":");
//            long timestamp = Long.parseLong(parts[0]);
            int price = Integer.parseInt(parts[1]);
            return BigInteger.valueOf(price);
        }

        throw new CustomException("获取能量的trx单价失败");
    }

    /**
     * 获取带宽的trx单价
     */
    public static BigInteger getBandwidthPrice() {
        String url = WalletConfig.tronApiUrl + "/wallet/getbandwidthprices";
        String result = HttpUtil.get(url);
        JSONObject jsonObject = JSONUtil.parseObj(result);
        if (!jsonObject.isEmpty()) {
            String prices = jsonObject.getStr("prices");
            String[] priceEntries = prices.split(",");
            // 获取最后一项数据（即最后一个价格和时间戳）
            String lastEntry = priceEntries[priceEntries.length - 1];
            String[] parts = lastEntry.split(":");
//            long timestamp = Long.parseLong(parts[0]);
            int price = Integer.parseInt(parts[1]);
            return BigInteger.valueOf(price);
        }
        throw new CustomException("获取带宽的trx单价失败");
    }

    public static void confirmResult(String privateKey, String signResult) {
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            log.warn("线程休眠异常", e);
        }
        ApiWrapper apiWrapper = getApiWrapper(privateKey);
        try {
            Chain.Transaction transaction = apiWrapper.getTransactionById(signResult);
            if (transaction != null) {
                Chain.Transaction.Result ret = transaction.getRet(0);
                if (ret.getContractRet().name().equals("SUCCESS")) {
                    return;
                }
                throw new CustomException(String.format("交易失败: txid:%s,result:%s", signResult, ret.getContractRet()));
            }
            //二次重试
            else {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    log.warn("线程休眠异常", e);
                }
                Chain.Transaction transaction2 = apiWrapper.getTransactionById(signResult);
                if (transaction2 != null) {
                    Chain.Transaction.Result ret = transaction2.getRet(0);
                    if (ret.getContractRet().name().equals("SUCCESS")) {
                        return;
                    }
                    throw new CustomException(String.format("交易失败: txid:%s,result:%s", signResult, ret.getContractRet()));
                }
            }
            throw new CustomException(String.format("获取交易结果失败: txid:%s", signResult));
        } catch (IllegalException e) {
            throw new CustomException(String.format("获取交易结果失败: txid:%s,result:%s", signResult, e.getMessage()));
        } finally {
            apiWrapper.close();
        }

    }

    /**
     * 查询指定trc20 token代币余额
     *
     * @param address    主地址
     * @param privateKey 主私钥
     */
    public static BigInteger findTokenBalance(String address, String privateKey, String contractAddress) {
        ApiWrapper apiWrapper = TronUtils.getApiWrapper(privateKey);
        Trc20Contract trc20Contract = new Trc20Contract(apiWrapper.getContract(contractAddress), address, apiWrapper);
        BigInteger bigInteger = trc20Contract.balanceOf(address);
        apiWrapper.close();
        return bigInteger;
    }

    /**
     * 能量换算trx
     */
    public static BigInteger energyToTrx(BigInteger energy, String address) {

        BigDecimal trxEnergyRatio = TronUtils.getTrxEnergyRatioByHttp(address);
        if (trxEnergyRatio == null) {
            throw new CustomException("获取能量换算trx失败");
        }
        trxEnergyRatio = trxEnergyRatio.multiply(BigDecimal.TEN);
        return new BigDecimal(energy).multiply(trxEnergyRatio).multiply(new BigDecimal("1000000")).toBigInteger();
    }


}
