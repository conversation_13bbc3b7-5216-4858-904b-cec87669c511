package com.meta.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.crypto.symmetric.SM4;
import com.meta.config.EncryptionConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 私钥加密工具类
 * 支持AES、SM4、RSA、SM2等多种加密算法
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Component
public class PrivateKeyEncryptionUtil {

    private static final Logger log = LoggerFactory.getLogger(PrivateKeyEncryptionUtil.class);

    /**
     * 加密私钥
     * 
     * @param privateKey 原始私钥
     * @return 加密后的私钥
     */
    public static String encryptPrivateKey(String privateKey) {
        if (StrUtil.isBlank(privateKey)) {
            throw new IllegalArgumentException("私钥不能为空");
        }

        // 如果未启用加密，直接返回原始私钥
        if (!EncryptionConfig.enabled) {
            log.debug("加密功能未启用，返回原始私钥");
            return privateKey;
        }

        try {
            String encryptedData;
            String algorithm = EncryptionConfig.algorithm.toUpperCase();

            switch (algorithm) {
                case "AES":
                    encryptedData = encryptByAes(privateKey);
                    break;
                case "SM4":
                    encryptedData = encryptBySm4(privateKey);
                    break;
                case "RSA":
                    encryptedData = encryptByRsa(privateKey);
                    break;
                case "SM2":
                    encryptedData = encryptBySm2(privateKey);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的加密算法: " + algorithm);
            }

            log.debug("私钥加密成功，算法: {}", algorithm);
            return encryptedData;

        } catch (Exception e) {
            log.error("私钥加密失败", e);
            throw new RuntimeException("私钥加密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解密私钥
     * 
     * @param encryptedPrivateKey 加密后的私钥
     * @return 原始私钥
     */
    public static String decryptPrivateKey(String encryptedPrivateKey) {
        if (StrUtil.isBlank(encryptedPrivateKey)) {
            throw new IllegalArgumentException("加密私钥不能为空");
        }

        // 如果未启用加密，直接返回原始数据
        if (!EncryptionConfig.enabled) {
            log.debug("加密功能未启用，返回原始数据");
            return encryptedPrivateKey;
        }

        try {
            String decryptedData;
            String algorithm = EncryptionConfig.algorithm.toUpperCase();

            switch (algorithm) {
                case "AES":
                    decryptedData = decryptByAes(encryptedPrivateKey);
                    break;
                case "SM4":
                    decryptedData = decryptBySm4(encryptedPrivateKey);
                    break;
                case "RSA":
                    decryptedData = decryptByRsa(encryptedPrivateKey);
                    break;
                case "SM2":
                    decryptedData = decryptBySm2(encryptedPrivateKey);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的解密算法: " + algorithm);
            }

            log.debug("私钥解密成功，算法: {}", algorithm);
            return decryptedData;

        } catch (Exception e) {
            log.error("私钥解密失败", e);
            throw new RuntimeException("私钥解密失败: " + e.getMessage(), e);
        }
    }

    /**
     * AES加密
     */
    private static String encryptByAes(String data) {
        AES aes = SecureUtil.aes(EncryptionConfig.secretKey.getBytes(StandardCharsets.UTF_8));
        byte[] encrypted = aes.encrypt(data.getBytes(StandardCharsets.UTF_8));
        return encodeResult(encrypted);
    }

    /**
     * AES解密
     */
    private static String decryptByAes(String encryptedData) {
        AES aes = SecureUtil.aes(EncryptionConfig.secretKey.getBytes(StandardCharsets.UTF_8));
        byte[] decoded = decodeData(encryptedData);
        byte[] decrypted = aes.decrypt(decoded);
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    /**
     * SM4加密
     */
    private static String encryptBySm4(String data) {
        // SM4要求密钥长度为16位
        String key = EncryptionConfig.secretKey;
        if (key.length() != 16) {
            throw new IllegalArgumentException("SM4密钥长度必须为16位");
        }
        SM4 sm4 = new SM4(key.getBytes(StandardCharsets.UTF_8));
        byte[] encrypted = sm4.encrypt(data.getBytes(StandardCharsets.UTF_8));
        return encodeResult(encrypted);
    }

    /**
     * SM4解密
     */
    private static String decryptBySm4(String encryptedData) {
        String key = EncryptionConfig.secretKey;
        if (key.length() != 16) {
            throw new IllegalArgumentException("SM4密钥长度必须为16位");
        }
        SM4 sm4 = new SM4(key.getBytes(StandardCharsets.UTF_8));
        byte[] decoded = decodeData(encryptedData);
        byte[] decrypted = sm4.decrypt(decoded);
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    /**
     * RSA加密
     */
    private static String encryptByRsa(String data) {
        if (StrUtil.isBlank(EncryptionConfig.publicKey)) {
            throw new IllegalArgumentException("RSA加密需要配置公钥");
        }
        RSA rsa = SecureUtil.rsa(null, EncryptionConfig.publicKey);
        byte[] encrypted = rsa.encrypt(data.getBytes(StandardCharsets.UTF_8), KeyType.PublicKey);
        return encodeResult(encrypted);
    }

    /**
     * RSA解密
     */
    private static String decryptByRsa(String encryptedData) {
        if (StrUtil.isBlank(EncryptionConfig.privateKey)) {
            throw new IllegalArgumentException("RSA解密需要配置私钥");
        }
        RSA rsa = SecureUtil.rsa(EncryptionConfig.privateKey, null);
        byte[] decoded = decodeData(encryptedData);
        byte[] decrypted = rsa.decrypt(decoded, KeyType.PrivateKey);
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    /**
     * SM2加密
     */
    private static String encryptBySm2(String data) {
        if (StrUtil.isBlank(EncryptionConfig.publicKey)) {
            throw new IllegalArgumentException("SM2加密需要配置公钥");
        }
        SM2 sm2 = new SM2(null, EncryptionConfig.publicKey);
        byte[] encrypted = sm2.encrypt(data.getBytes(StandardCharsets.UTF_8), KeyType.PublicKey);
        return encodeResult(encrypted);
    }

    /**
     * SM2解密
     */
    private static String decryptBySm2(String encryptedData) {
        if (StrUtil.isBlank(EncryptionConfig.privateKey)) {
            throw new IllegalArgumentException("SM2解密需要配置私钥");
        }
        SM2 sm2 = new SM2(EncryptionConfig.privateKey, null);
        byte[] decoded = decodeData(encryptedData);
        byte[] decrypted = sm2.decrypt(decoded, KeyType.PrivateKey);
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    /**
     * 编码加密结果
     */
    private static String encodeResult(byte[] data) {
        String encoding = EncryptionConfig.encoding.toUpperCase();
        switch (encoding) {
            case "BASE64":
                return Base64.encode(data);
            case "HEX":
                return HexUtil.encodeHexStr(data);
            default:
                throw new IllegalArgumentException("不支持的编码方式: " + encoding);
        }
    }

    /**
     * 解码加密数据
     */
    private static byte[] decodeData(String data) {
        String encoding = EncryptionConfig.encoding.toUpperCase();
        switch (encoding) {
            case "BASE64":
                return Base64.decode(data);
            case "HEX":
                return HexUtil.decodeHex(data);
            default:
                throw new IllegalArgumentException("不支持的编码方式: " + encoding);
        }
    }

    /**
     * 判断私钥是否已加密
     * 简单的判断逻辑：如果私钥长度和格式符合加密后的特征，则认为已加密
     * 
     * @param privateKey 私钥
     * @return true-已加密，false-未加密
     */
    public static boolean isEncrypted(String privateKey) {
        if (StrUtil.isBlank(privateKey)) {
            return false;
        }

        // 如果未启用加密，认为都是未加密的
        if (!EncryptionConfig.enabled) {
            return false;
        }

        try {
            // 尝试解码，如果成功且长度合理，可能是加密的
            String encoding = EncryptionConfig.encoding.toUpperCase();
            if ("BASE64".equals(encoding)) {
                // Base64编码的特征：长度是4的倍数，只包含A-Z、a-z、0-9、+、/、=
                return privateKey.matches("^[A-Za-z0-9+/]*={0,2}$") && privateKey.length() % 4 == 0;
            } else if ("HEX".equals(encoding)) {
                // 十六进制编码的特征：只包含0-9、A-F、a-f
                return privateKey.matches("^[0-9A-Fa-f]+$") && privateKey.length() % 2 == 0;
            }
        } catch (Exception e) {
            // 解码失败，可能不是加密的
            return false;
        }

        return false;
    }
}
