package com.meta.util;

import com.meta.contants.Constants;
import com.meta.system.domain.app.BepCstaddress;
import com.meta.system.domain.app.CoinBalance;
import com.meta.entity.CoinBalancePK;
import com.meta.system.service.BepCstaddressService;
import io.github.novacrypto.bip39.MnemonicGenerator;
import io.github.novacrypto.bip39.SeedCalculator;
import io.github.novacrypto.bip39.Words;
import io.github.novacrypto.bip39.wordlists.English;
import io.github.novacrypto.hashing.Sha256;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.web3j.crypto.Credentials;
import org.web3j.crypto.ECKeyPair;
import org.web3j.crypto.Keys;
import org.web3j.crypto.WalletUtils;

import java.security.SecureRandom;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/12/10:25
 */
@Component
public class BepCreateWalletUtil {
    @Autowired
    StringRedisTemplate redisTemplate;

    @Autowired
    private BepCstaddressService bepCstaddressService;

    /**
     * 创建钱包
     */
    public CoinBalance createWallet(CoinBalancePK pk) {

        //密码
        String password = pk.getUserId() + "Meta#5566";


        StringBuilder sb = new StringBuilder();
        byte[] entropy = new byte[Words.TWELVE.byteLength()];
        new SecureRandom().nextBytes(entropy);
        new MnemonicGenerator(English.INSTANCE).createMnemonic(entropy, sb::append);
        String mnemonic = sb.toString();

        System.out.println("mnemonic=" + mnemonic);
        Credentials credentials = WalletUtils.loadBip39Credentials(password, mnemonic);
        ECKeyPair ecKeyPair1 = credentials.getEcKeyPair();

        List mnemonicList = Arrays.asList(mnemonic.split(" "));
        byte[] seed = new SeedCalculator().withWordsFromWordList(English.INSTANCE).calculateSeed(mnemonicList, password);
        ECKeyPair ecKeyPair = ECKeyPair.create(Sha256.sha256(seed));

        System.out.println("====================" + ecKeyPair1.equals(ecKeyPair));
        String privateKey = ecKeyPair.getPrivateKey().toString(16);
        String publicKey = ecKeyPair.getPublicKey().toString(16);
        String address = "0x" + Keys.getAddress(publicKey);
        //保存到表里
        BepCstaddress bepCstaddress = new BepCstaddress();
        bepCstaddress.setCstAdress(address);
        bepCstaddress.setCstId(pk.getUserId());
        bepCstaddress.setCstTrcPrivate(privateKey);
        bepCstaddress.setSysId(pk.getSysId());
        bepCstaddressService.save(bepCstaddress);
        //添加redis数据
        String key = Constants.BEP_ADDRESS + pk.getSysId();
        redisTemplate.opsForList().rightPush(key, address);

        CoinBalance cb = new CoinBalance();

        // todo
        cb.setWalletAddress(address);
        return cb;


    }
}
