package com.meta.util;

import com.meta.config.EncryptionConfig;

/**
 * 私钥加密工具演示类
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
public class EncryptionDemo {

    public static void main(String[] args) {
        // 初始化配置
        EncryptionConfig.algorithm = "AES";
        EncryptionConfig.secretKey = "MetaWallet123456";
        EncryptionConfig.publicKey = "";
        EncryptionConfig.privateKey = "";
        EncryptionConfig.enabled = true;
        EncryptionConfig.encoding = "BASE64";

        // 测试私钥
        String originalPrivateKey = "f90bd80f933cfef74730d69c133b85288c920ffc253300fe8cb326f668eba517";

        System.out.println("=== 私钥加密工具演示 ===");
        System.out.println("原始私钥: " + originalPrivateKey);
        System.out.println("加密算法: " + EncryptionConfig.algorithm);
        System.out.println("编码方式: " + EncryptionConfig.encoding);
        System.out.println();

        try {
            // 1. 加密私钥
            System.out.println("1. 加密私钥...");
            String encryptedKey = PrivateKeyEncryptionUtil.encryptPrivateKey(originalPrivateKey);
            System.out.println("加密后的私钥: " + encryptedKey);
            System.out.println();

            // 2. 检查是否已加密
            System.out.println("2. 检查加密状态...");
            boolean isOriginalEncrypted = PrivateKeyEncryptionUtil.isEncrypted(originalPrivateKey);
            boolean isEncryptedKeyEncrypted = PrivateKeyEncryptionUtil.isEncrypted(encryptedKey);
            System.out.println("原始私钥是否已加密: " + isOriginalEncrypted);
            System.out.println("加密后私钥是否已加密: " + isEncryptedKeyEncrypted);
            System.out.println();

            // 3. 解密私钥
            System.out.println("3. 解密私钥...");
            String decryptedKey = PrivateKeyEncryptionUtil.decryptPrivateKey(encryptedKey);
            System.out.println("解密后的私钥: " + decryptedKey);
            System.out.println();

            // 4. 验证结果
            System.out.println("4. 验证结果...");
            boolean isMatched = originalPrivateKey.equals(decryptedKey);
            System.out.println("加密解密是否成功: " + isMatched);
            System.out.println();

            // 5. 测试不同编码方式
            System.out.println("5. 测试HEX编码...");
            EncryptionConfig.encoding = "HEX";
            String hexEncryptedKey = PrivateKeyEncryptionUtil.encryptPrivateKey(originalPrivateKey);
            System.out.println("HEX编码加密结果: " + hexEncryptedKey);
            String hexDecryptedKey = PrivateKeyEncryptionUtil.decryptPrivateKey(hexEncryptedKey);
            boolean hexMatched = originalPrivateKey.equals(hexDecryptedKey);
            System.out.println("HEX编码解密成功: " + hexMatched);
            System.out.println();

            // 6. 测试禁用加密
            System.out.println("6. 测试禁用加密...");
            EncryptionConfig.enabled = false;
            String disabledResult = PrivateKeyEncryptionUtil.encryptPrivateKey(originalPrivateKey);
            System.out.println("禁用加密时的结果: " + disabledResult);
            boolean disabledMatched = originalPrivateKey.equals(disabledResult);
            System.out.println("禁用加密时是否返回原值: " + disabledMatched);
            System.out.println();

            // 7. 测试SM4算法
            System.out.println("7. 测试SM4算法...");
            EncryptionConfig.enabled = true;
            EncryptionConfig.algorithm = "SM4";
            EncryptionConfig.secretKey = "MetaWallet123456"; // SM4需要16位密钥
            EncryptionConfig.encoding = "BASE64";
            
            String sm4EncryptedKey = PrivateKeyEncryptionUtil.encryptPrivateKey(originalPrivateKey);
            System.out.println("SM4加密结果: " + sm4EncryptedKey);
            String sm4DecryptedKey = PrivateKeyEncryptionUtil.decryptPrivateKey(sm4EncryptedKey);
            boolean sm4Matched = originalPrivateKey.equals(sm4DecryptedKey);
            System.out.println("SM4解密成功: " + sm4Matched);

            System.out.println();
            System.out.println("=== 演示完成 ===");

        } catch (Exception e) {
            System.err.println("演示过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 演示在TronUtils中的使用场景
     */
    public static void demonstrateTronUtilsUsage() {
        System.out.println("\n=== TronUtils 使用场景演示 ===");
        
        // 初始化配置
        EncryptionConfig.algorithm = "AES";
        EncryptionConfig.secretKey = "MetaWallet123456";
        EncryptionConfig.enabled = true;
        EncryptionConfig.encoding = "BASE64";

        // 模拟存储在数据库中的加密私钥
        String originalPrivateKey = "f90bd80f933cfef74730d69c133b85288c920ffc253300fe8cb326f668eba517";
        String encryptedPrivateKey = PrivateKeyEncryptionUtil.encryptPrivateKey(originalPrivateKey);
        
        System.out.println("数据库中存储的加密私钥: " + encryptedPrivateKey);
        
        // 模拟TronUtils.getApiWrapper方法中的解密逻辑
        String privateKeyToUse = PrivateKeyEncryptionUtil.isEncrypted(encryptedPrivateKey) 
            ? PrivateKeyEncryptionUtil.decryptPrivateKey(encryptedPrivateKey) 
            : encryptedPrivateKey;
            
        System.out.println("解密后用于创建ApiWrapper的私钥: " + privateKeyToUse);
        System.out.println("解密是否成功: " + originalPrivateKey.equals(privateKeyToUse));
        
        // 测试未加密的私钥
        String unencryptedKey = "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef";
        String keyToUse2 = PrivateKeyEncryptionUtil.isEncrypted(unencryptedKey) 
            ? PrivateKeyEncryptionUtil.decryptPrivateKey(unencryptedKey) 
            : unencryptedKey;
            
        System.out.println("未加密私钥处理结果: " + keyToUse2);
        System.out.println("未加密私钥是否保持不变: " + unencryptedKey.equals(keyToUse2));
    }
}
