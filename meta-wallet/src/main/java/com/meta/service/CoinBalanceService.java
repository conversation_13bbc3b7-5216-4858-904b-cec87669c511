package com.meta.service;

import com.meta.common.core.domain.AjaxResult;
import com.meta.dto.WithdrawalDto;
import com.meta.system.domain.app.CoinBalance;
import com.meta.system.domain.app.CoinBalancePK;
import com.meta.system.domain.app.ExchangeRate;
import com.meta.system.dto.node.WalletExchangeDto;

import java.util.List;

public interface CoinBalanceService {
    int energy(String address);

    /**
     * 保存
     * @param c
     */
    void save(CoinBalance c);

    /**
     * 查询
     * @return
     */
    CoinBalance findById(CoinBalancePK pk);


    /**
     * 更新 用户数字货币资产余额
     * @param cb
     */
    void updateCoinBalance(CoinBalance cb);
    /**
     * 更新 用户数字货币资产余额
     *
     * @param cb
     */
    void updateCoinBalanceData(CoinBalance cb);

    /**
     * 根据数据查询兑换所需的余额是否充足
     * @param exchange 兑换详情
     * @return 余额是否足够
     */
    AjaxResult checkExchange(WalletExchangeDto exchange);

    /**
     * 兑换
     * @param exchange 兑换信息
     */
    AjaxResult exchange(WalletExchangeDto exchange) throws Exception;

    /**
     * 获取兑换汇率
     * @param fromCurrencyCode 本币币种
     * @param toCurrencyCode 兑换币种
     * @return 兑换汇率
     */
    ExchangeRate getExchangeRate(String fromCurrencyCode, String toCurrencyCode);


    /**
     * 根据 用户id获取所有的数字货币钱包
     * @param userId 用户id
     * @return 数字货币钱包
     */
    List<CoinBalance> findAllByUserId(Long userId);

    /**
     *
     * @param withdrawal 提现
     * @return 余额是否充足
     */
    boolean checkWithdrawal(WithdrawalDto withdrawal);

    /**
     *
     * @param withdrawal 提款
     */
    void withdrawal(WithdrawalDto withdrawal);

    CoinBalance findByUserIdAndCoinCode(Long userId,String coinCode);
}
