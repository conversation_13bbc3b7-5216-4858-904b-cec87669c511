package com.meta.service.impl;

import com.meta.dao.MetaWalletCoinRecDao;
import com.meta.entity.MetaWalletCoinRec;
import com.meta.service.MetaWalletCoinRecService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 多链用户钱包代币余额记录服务实现类
 */
@Service
@Transactional(readOnly = true)
public class MetaWalletCoinRecServiceImpl implements MetaWalletCoinRecService {

    @Autowired
    private MetaWalletCoinRecDao metaWalletCoinRecDao;

    @Override
    @Transactional
    public MetaWalletCoinRec save(MetaWalletCoinRec metaWalletCoinRec) {
        return metaWalletCoinRecDao.save(metaWalletCoinRec);
    }

    @Override
    @Transactional
    public List<MetaWalletCoinRec> saveAll(List<MetaWalletCoinRec> metaWalletCoinRecList) {
        return metaWalletCoinRecDao.saveAll(metaWalletCoinRecList);
    }

    @Override
    public MetaWalletCoinRec findById(Long id) {
        Optional<MetaWalletCoinRec> optional = metaWalletCoinRecDao.findById(id);
        return optional.orElse(null);
    }

    @Override
    public List<MetaWalletCoinRec> findByTimeRange(Date startTime, Date endTime) {
        return metaWalletCoinRecDao.findByCreateTimeBetween(startTime, endTime);
    }

    @Override
    public List<MetaWalletCoinRec> findByTokenSymbol(String tokenSymbol) {
        return metaWalletCoinRecDao.findByTokenSymbol(tokenSymbol);
    }

    @Override
    public List<MetaWalletCoinRec> findByChainType(String chainType) {
        return metaWalletCoinRecDao.findByChainType(chainType);
    }

    @Override
    public List<MetaWalletCoinRec> findByChainTypeAndTokenSymbol(String chainType, String tokenSymbol) {
        return metaWalletCoinRecDao.findByChainTypeAndTokenSymbol(chainType, tokenSymbol);
    }

    @Override
    @Transactional
    public void deleteById(Long id) {
        metaWalletCoinRecDao.deleteById(id);
    }

    @Override
    public Set<String> getRecentWalletAddresses() {
        // 获取24小时前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, -24);
        Date startTime = calendar.getTime();
        Date endTime = new Date(); // 当前时间

        // 查询24小时内有记录的钱包地址集合
        return metaWalletCoinRecDao.findDistinctWalletAddressByCreateTimeBetween(startTime, endTime);
    }

    @Override
    public Set<String> getRecentWalletAddressesByChainType(String chainType) {
        // 获取24小时前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, -24);
        Date startTime = calendar.getTime();
        Date endTime = new Date(); // 当前时间

        // 查询指定链类型24小时内有记录的钱包地址集合
        return metaWalletCoinRecDao.findDistinctWalletAddressByChainTypeAndCreateTimeBetween(chainType, startTime, endTime);
    }
}
