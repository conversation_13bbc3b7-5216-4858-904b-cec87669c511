package com.meta.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.meta.common.config.RedisLock;
import com.meta.common.exception.CustomException;
import com.meta.config.WalletConfig;
import com.meta.scanning.TronThreadPoolUtil;
import com.meta.service.CoinBalanceService;
import com.meta.service.ITronService;
import com.meta.system.domain.TrcCstaddress;
import com.meta.system.email.SendEmail;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.TrcCstaddressService;
import com.meta.system.uitls.RedisConstants;
import com.meta.util.TronUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.tron.trident.core.ApiWrapper;
import org.tron.trident.core.key.KeyPair;
import org.tron.trident.proto.Response;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/2/19 14:54
 **/
@Slf4j
@Component
public class TronServiceImpl implements ITronService {

    @Autowired
    private TrcCstaddressService trcCstaddressService;
    @Autowired
    private CoinBalanceService coinBalanceService;
    private static final BigDecimal MILLION_BIG_DECIMAL = new BigDecimal("1000000");
    @Autowired
    private SendEmail sendEmail;
    @Autowired
    private RedisLock redisLock;
    @Autowired
    private ISysConfigService sysConfigServiceImpl;


    /**
     * 归集
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void aggregation(String walletAddress, String contractAddress) {
        redisLock.lock(RedisConstants.TRC_AGGREGATION_LOCK + walletAddress, 15000);
        /*
         * 查询基础信息
         */
        ITronService service = SpringUtil.getBean(ITronService.class);
        //通过cstId和sysId找到用户唯一的钱包地址
        TrcCstaddress fromWallet = trcCstaddressService.findByAddress(walletAddress);
        if (fromWallet == null) {
            throw new CustomException("用户钱包不存在");
        }


//        //测试代码
//        BigDecimal trxEnergyRatio = TronUtils.getTrxEnergyRatioByHttp(fromWallet.getCstAddress());
//        //使用 能量钱包 代理能量给 归集钱包
//        try {
//            TronUtils.delegateResourceV2(WalletConfig.getEnergyWallet(), fromWallet.getCstAddress(), new BigInteger("1000000"), false, 2 * 1200, null);
//        } catch (Exception e) {
//            log.warn("使用能量钱包代理能量失败，尝试使用第三方api代理能量。错误原因:{}", e.getMessage());
//        } finally {
//            // 延迟回收能量
//            ThreadUtil.execAsync(() -> {
//                ThreadUtil.sleep(20000);
//                try {
//                    TronUtils.unDelegateResource(WalletConfig.getEnergyWallet(), fromWallet.getCstAddress(), new BigInteger("1000000"), null);
//                } catch (Exception e) {
//                    sendEmail.remindMail(String.format("回收能量失败。用户钱包地址：%s，能量钱包地址：%s，失败信息: %s", fromWallet.getCstAddress(), WalletConfig.getEnergyWallet(), e.getMessage()), "自动激活用户钱包失败的信息提醒");
//                }
//
//            });
//
//        }

        //通过sysId查找归集钱包地址（主钱包地址）
        String toAddress = service.getAggregationAddress(fromWallet.getSysId());
        //检查归集钱包的token代币是否大于设定阈值（暂定为10）
        BigInteger tokenAmount = TronUtils.findTokenBalance(fromWallet.getCstAddress(), fromWallet.getCstTrcPrivate(),contractAddress);
        //判断是否有token代币
        if (tokenAmount.compareTo(BigInteger.ONE) < 1) {
            throw new CustomException("usdt余额小于0.000001，不需要归集");
        }
        long needEnergy = TronUtils.getNeedEnergy(fromWallet, toAddress, contractAddress);
        //如果需要的能量大于限制，不进行归集
        if (BigInteger.valueOf(needEnergy).compareTo(WalletConfig.burnEnergyLimit) > 0) {
            throw new CustomException(String.format("需要的能量大于限制，不进行归集。付款钱包地址：%s", fromWallet.getCstAddress()));
        }

        Response.Account account = TronUtils.getAccount(fromWallet);
        if (account.getBalance() == 0L) {
            activateWallet(fromWallet);
            ThreadUtil.sleep(1000);
        }

        /*
         * 尝试代理能量或消耗trx进行转账
         */
        BigInteger allBurnTrx = BigInteger.ZERO;
        Response.AccountResourceMessage accountResource = TronUtils.getAccountResource(fromWallet);
        //计算归集钱包差多少能量
        long availableEnergy = TronUtils.getAvailableEnergy(fromWallet);
        long energyDifference = needEnergy - availableEnergy;
        if (!service.getEnergyBySingle(fromWallet, energyDifference, WalletConfig.getEnergyWallet())) {
            if (WalletConfig.burnTrxLimit.compareTo(BigInteger.ZERO) <= 0) {
                throw new CustomException("能量代理失败，且不允许消耗trx");
            }
            //计算能量耗费的trx：需要消耗的能量 * 获取比例
            BigInteger energyPrice = TronUtils.getEnergyPrice();
            BigInteger energyBurnTrx = BigInteger.valueOf(energyDifference).multiply(energyPrice);

            allBurnTrx = allBurnTrx.add(energyBurnTrx);
            log.info("归集功能：消耗trx归集，能量所需消耗trx数量：{}", allBurnTrx);
        }
        //计算带宽耗费的trx（暂定为500）
        String collectWidthForTron = sysConfigServiceImpl.selectConfigByKey("collect_width_for_tron");
        long needWidth = 500;
        if (StringUtils.isNotBlank(collectWidthForTron)) {
            needWidth = Long.parseLong(collectWidthForTron);
        }
        long availableWidth = TronUtils.getAvailableWidth(accountResource);
        if (availableWidth < needWidth) {
            BigInteger bandwidthPrice = TronUtils.getBandwidthPrice();
            BigInteger widthBurnTrx = BigInteger.valueOf(needWidth).multiply(bandwidthPrice);
            //总消耗trx
            allBurnTrx = allBurnTrx.add(widthBurnTrx);
        }

        if (allBurnTrx.compareTo(WalletConfig.burnTrxLimit) > 0) {
            throw new CustomException("归集功能：消耗trx归集失败，消耗trx超过限制");
        }
        if (allBurnTrx.compareTo(BigInteger.valueOf(account.getBalance())) > 0) {
            log.info("归集功能：账户余额不足，尝试从备用钱包（能量钱包）获取trx。" +
                    "归集需要：{}，账户剩余：{}", allBurnTrx, account.getBalance());
            BigInteger needTrx = allBurnTrx.subtract(BigInteger.valueOf(account.getBalance()));
            Response.Account accountBackup = TronUtils.getAccount(WalletConfig.getEnergyWallet());
            if (needTrx.compareTo(BigInteger.valueOf(accountBackup.getBalance())) > 0) {
                throw new CustomException(String.format("归集功能：备用钱包（能量钱包）余额不足，钱包剩余trx:%s，需要trx:%s", accountBackup.getBalance(), needTrx));
            }

            try {
                TronUtils.trxTransfer(WalletConfig.getEnergyWallet(), needTrx, fromWallet.getCstAddress());
                ThreadUtil.sleep(1, TimeUnit.SECONDS);
            } catch (Exception e) {
                throw new CustomException(String.format("归集功能：账户余额不足，尝试从备用钱包（能量钱包）获取trx失败：%s", e.getMessage()));
            }
        }

        // 执行转账操作
        TronUtils.trc20Transfer(fromWallet, tokenAmount.subtract(WalletConfig.reserveUsdt), toAddress, contractAddress);
        redisLock.unlock(RedisConstants.TRC_AGGREGATION_LOCK + walletAddress);
    }


    /**
     * 获取单笔能量
     */
    @Override
    public boolean getEnergyBySingle(TrcCstaddress toAddress, long needEnergy, TrcCstaddress backupAddress) {
        //归集钱包能量充足，不需要代理
        if (needEnergy <= 0) {
            log.info("能量充足，不需要代理");
            return true;
        }
        //归集钱包能量不够，获取代理能量
        else {
            log.info("归集钱包能量不够，获取代理能量");
            ITronService service = SpringUtil.getBean(ITronService.class);
            return service.getEnergy(toAddress, backupAddress, needEnergy);
        }
    }

    @Override
    public boolean getEnergy(TrcCstaddress trcCstaddress, TrcCstaddress backupAddress, long needEnergy) {
        //查看能量钱包（备用钱包）是否能够代理能量
        long availableEnergyForBackupAddress = TronUtils.getAvailableEnergy(backupAddress);
        //使用能量钱包代理能量
        if (availableEnergyForBackupAddress >= needEnergy) {
            //能量 换算 trx
            BigDecimal trxEnergyRatio = TronUtils.getTrxEnergyRatioByHttp(trcCstaddress.getCstAddress());
            assert trxEnergyRatio != null;
            //使用 能量钱包 代理能量给 归集钱包
            BigInteger balance = new BigDecimal(needEnergy).divide(trxEnergyRatio, RoundingMode.UP).multiply(MILLION_BIG_DECIMAL).toBigInteger();
            try {
                TronUtils.delegateResourceV2(backupAddress, trcCstaddress.getCstAddress(), balance, false, 2 * 1200, null);
                return true;
            } catch (Exception e) {
                log.warn("使用能量钱包代理能量失败，尝试使用第三方api代理能量。错误原因:{}", e.getMessage());
            } finally {
                // 延迟回收能量
                TronThreadPoolUtil.scheduleTask(() -> {
                    try {
                        TronUtils.unDelegateResource(backupAddress, trcCstaddress.getCstAddress(), balance, null);
                    } catch (Exception e) {
                        sendEmail.remindMail(String.format("自动回收能量失败。用户钱包地址：%s，能量钱包地址：%s，失败信息: %s", trcCstaddress.getCstAddress(), backupAddress, e.getMessage())
                                , "自动回收能量失败信息提醒");
                    }

                });
            }
        }
        //使用第三方api代理能量
        log.info("进行第三方发送能量");
        if (coinBalanceService.energy(trcCstaddress.getCstAddress()) == 200) {
            // 第三方代理能量接口表明有延迟情况
            ThreadUtil.sleep(5000);
            return true;
        } else {
            log.error("第三方能量代理失败");
        }
        return false;
    }

    /**
     * 创建用户钱包
     *
     * @param trcCstaddress 用户钱包基础数据
     */
    @Override
    public void createUserWallet(TrcCstaddress trcCstaddress) {
        //有唯一索引
        KeyPair keyPair = ApiWrapper.generateAddress();
        String privateKey = keyPair.toPrivateKey();
        String address = keyPair.toBase58CheckAddress();
        String hexAddress = keyPair.toHexAddress();
        trcCstaddress.setCstTrcPrivate(privateKey);
        trcCstaddress.setCstAddress(address);
        trcCstaddress.setCstHexaddress(hexAddress);
        trcCstaddressService.save(trcCstaddress);
    }

    /**
     * 激活钱包
     */
    @Override
    public boolean activateWallet(TrcCstaddress trcCstaddress) {
        try {
            TronUtils.trxTransfer(WalletConfig.getEnergyWallet(), new BigInteger("1"), trcCstaddress.getCstAddress());
            return true;
        } catch (Exception e) {
            log.error("激活钱包失败，错误信息：{}", e.getMessage());
            sendEmail.remindMail(String.format("用户钱包地址: %s，失败信息: %s", trcCstaddress.getCstAddress(), e.getMessage()), "自动激活用户钱包失败的信息提醒");
        }
        return false;
    }


    /**
     * 获取归集钱包地址（主钱包地址）
     */
    @Override
    public String getAggregationAddress(String sysId) {
        if (StringUtils.isBlank(sysId)) {
            throw new CustomException("sysId不能为空");
        }
        //从里面找到对应的主钱包地址
        return WalletConfig.mainAddressList.stream()
                .filter(metaMainAddress -> sysId.equals(metaMainAddress.getSysId()))
                .findFirst()
                .orElseThrow(() -> new CustomException("配置文件中没有该sysId对应的主钱包地址"))
                .getMainaddress();
    }

}
