package com.meta.service.impl;

import com.meta.service.CreateWalletService;
import com.meta.entity.CoinBalancePK;
import com.meta.system.domain.app.CoinBalance;
import com.meta.util.BepCreateWalletUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/12/11/17:40
 */
@Service
public class CreateWalletServiceImpl implements CreateWalletService {
    @Autowired
    private BepCreateWalletUtil bepCreateWalletUtil;

    @Override
    public CoinBalance createWallet(CoinBalancePK pk) {

        return bepCreateWalletUtil.createWallet(pk);
    }
}
