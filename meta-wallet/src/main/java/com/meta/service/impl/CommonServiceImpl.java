package com.meta.service.impl;


import com.meta.common.core.domain.model.LoginUser;
import com.meta.common.core.redis.RedisCache;
import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.StringUtils;
import com.meta.contants.Constants;
import com.meta.service.ICommonService;
import com.meta.system.domain.SysConfig;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.ISysUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class CommonServiceImpl implements ICommonService {


    @Resource
    private ISysUserService userService;

    @Resource
    private ISysConfigService configService;
    @Resource
    private RedisCache redisCache;



    @Override
    public String fetchUserSysId() {
        LoginUser user = SecurityUtils.getLoginUser();
//    if (user == null) throw new CustomException("用户尚未登录");
        if (user == null) return null;
        Long id = user.getUser().getUserId();
        return id.toString();
//        return fetchSysIdByCstId(id.toString());
    }

//    @Override
//    public String fetchSysIdByCstId(String cstId) {
//        SysUser sysUser = userService.selectUserById(Long.parseLong(cstId));
//        if (sysUser == null) throw new CustomException("查无此用户");
//        return commonMapper.fetchUserSysId(sysUser.getUserId());
//    }

//    @Override
//    public Boolean isQCShareholder(Long loginID) {
//        return loginID != null && !userService.isAdmin(loginID);
//    }

    @Override
    public void i18nChangeLanguage(String language) {
        i18nChangeLanguage(language, SecurityUtils.getCstId());
    }

    @Override
    public void i18nChangeLanguage(String language, String cstID) {
        String languageKey = Constants.language.base  + ":" + cstID + "";
        if (Constants.language.zhCN.equals(language)) {
            // 如果是简体中文，就移除 key
            redisCache.deleteObject(languageKey);
        } else if (Constants.language.zhHK.equals(language)){
            // 如果是繁体中文，就移除 key
            redisCache.deleteObject(languageKey);
        }else if (Constants.language.enUS.equals(language)){
            redisCache.deleteObject(languageKey);
        }
        // 把客户对应的 信息 填充进 redis
        redisCache.setCacheObject(languageKey, language);
    }

    /*/*
     * @description: 通用获取Config的Value
     * @throws
     * <AUTHOR>
     * @date 2022/6/9 11:15
     */
    @Override
    public String getConfigValue(String key, String name, String value){
        String configValue = configService.selectConfigByKey(key);
        if (StringUtils.isEmpty(configValue)) {
            SysConfig config = new SysConfig();
            config.setConfigKey(key);
            config.setConfigName(name);
            config.setConfigValue(value);
            configService.insertConfig(config);
            configValue = config.getConfigValue();
        }
        return configValue;
    }
}
