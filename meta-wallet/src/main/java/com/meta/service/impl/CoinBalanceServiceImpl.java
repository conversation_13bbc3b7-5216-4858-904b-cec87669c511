package com.meta.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.enums.app.AlgorithmType;
import com.meta.common.enums.app.ExchangeType;
import com.meta.common.enums.app.FreezeType;
import com.meta.common.enums.app.TxnType;
import com.meta.common.utils.DateUtils;
import com.meta.common.utils.MessageUtils;
import com.meta.common.utils.SecurityUtils;
import com.meta.common.utils.StringUtils;
import com.meta.contants.Constants;
import com.meta.dto.WithdrawalDto;
import com.meta.service.CoinBalanceService;
import com.meta.system.dao.app.CoinBalanceDao;
import com.meta.system.domain.Wallet;
import com.meta.system.domain.app.CoinBalance;
import com.meta.system.domain.app.CoinBalancePK;
import com.meta.system.domain.app.CoinTxnDtl;
import com.meta.system.domain.app.ExchangeRate;
import com.meta.system.dto.VccReqDto;
import com.meta.system.dto.node.WalletExchangeDto;
import com.meta.system.email.Mail;
import com.meta.system.email.SendEmail;
import com.meta.system.service.CoinTxnDtlService;
import com.meta.system.service.ExchangeRateService;
import com.meta.system.service.ISysConfigService;
import com.meta.system.service.WalletService;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.List;
import java.util.Optional;

@Transactional(readOnly = true)
@Service
public class CoinBalanceServiceImpl implements CoinBalanceService {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private CoinBalanceDao coinBalanceDao;

    @Autowired
    private ExchangeRateService exchangeRateService;

    @Autowired
    private WalletService walletService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private CoinTxnDtlService coinTxnDtlService;

    @Autowired
    private SendEmail sendEmail;

    /**
     * 发送能量（迁移自bk模块）
     */
    @Override
    public int energy(String address) {
        int responseCode = 0;
        try {
            String baseUrl = configService.selectConfigByKey("bochang_url");
            String apiKey = configService.selectConfigByKey("bochang_key");
            String value = configService.selectConfigByKey("bochang_value");
            String hour = configService.selectConfigByKey("bochang_hour");


            // 构建带参数的URL
            String urlWithParams = baseUrl + "?" +
                    "apikey=" + URLEncoder.encode(apiKey, "UTF-8") +
                    "&add=" + URLEncoder.encode(address, "UTF-8") +
                    "&value=" + URLEncoder.encode(value, "UTF-8") +
                    "&hour=" + URLEncoder.encode(hour, "UTF-8");
            logger.info("urlWithParams:" + urlWithParams);

            // 创建URL对象
            URL obj = new URL(urlWithParams);

            // 打开连接
            HttpURLConnection connection = (HttpURLConnection) obj.openConnection();

            // 设置请求方式为GET
            connection.setRequestMethod("GET");

            // 获取响应码
            responseCode = connection.getResponseCode();

            logger.info("Response Code: " + responseCode);

            // 读取响应内容
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String inputLine;
            StringBuilder response = new StringBuilder();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }

            // 关闭连接
            in.close();

            // 打印响应内容
            System.out.println("Response Content: " + response.toString());

            String content = response.toString();
            if (StringUtils.isNotEmpty(content)) {
                JSONObject jsonObject = JSONObject.parseObject(content);
                responseCode = jsonObject.getIntValue("code");

            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        return responseCode;
    }

    @Override
    @Transactional
    public void save(CoinBalance c) {
        coinBalanceDao.save(c);
    }

    @Override
    public CoinBalance findById(CoinBalancePK p) {
        Optional<CoinBalance> op = coinBalanceDao.findById(p);
        if (op.isPresent()) {
            return op.get();
        }
        return null;
    }


    @Override
    public void updateCoinBalance(CoinBalance cb) {
        coinBalanceDao.updateCoinBalance(cb.getUserId(), cb.getCoinCode(), cb.getCoinBalance());
    }

    @Override
    public void updateCoinBalanceData(CoinBalance cb) {
        coinBalanceDao.updateCoinBalanceData(cb.getUserId(), cb.getCoinCode(), cb.getCoinBalance(), cb.getFreezeBalance(), cb.getUncltCoinBalance());
    }

    /**
     * 根据数据查询兑换所需的余额是否充足
     *
     * @param exchange 兑换详情
     * @return 余额是否足够
     */
    @Override
    public AjaxResult checkExchange(WalletExchangeDto exchange) {
        if (ExchangeType.U_TO_B.getOrdinal().equals(exchange.getExchangeType())) {
            // 查找用户钱包余额
            Wallet wallet = walletService.findByUserId(SecurityUtils.getLoginUser().getUserId());
            return wallet.getUsdBalance().compareTo(exchange.getQuantity()) >= 0 ? AjaxResult.success() : AjaxResult.error(MessageUtils.message("wallet.account.insufficient.balance"));
        } else if (ExchangeType.B_TO_U.getOrdinal().equals(exchange.getExchangeType())) {
            // 获取币余额
            CoinBalancePK coinBalancePK = new CoinBalancePK(SecurityUtils.getLoginUser().getUserId(), exchange.getCoinCode(), "");
            Optional<CoinBalance> coinBalance = coinBalanceDao.findById(coinBalancePK);
            if (coinBalance.isPresent()) {
                CoinBalance balance = coinBalance.get();
                return balance.getCoinBalance().compareTo(exchange.getQuantity()) >= 0 ? AjaxResult.success() : AjaxResult.error(MessageUtils.message("wallet.coin.insufficient.balance", exchange.getCoinCode()));
            }
            return AjaxResult.error(MessageUtils.message("wallet.coin.not.exist", exchange.getCoinCode()));
        }
        return AjaxResult.error(MessageUtils.message("wallet.coin.exchange.type.not.exist", exchange.getCoinCode()));
    }

    /**
     * 兑换
     *
     * @param exchange 兑换信息
     */
    @Override
    public AjaxResult exchange(WalletExchangeDto exchange) throws Exception {
        AjaxResult result = AjaxResult.success();

        // 公司账户id
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        if (ExchangeType.U_TO_B.getOrdinal().equals(exchange.getExchangeType())) {

            // 1.计算兑换数量
            // 获取兑换利率
            ExchangeRate rate = exchangeRateService.findByFromCurrencyCodeAndToCurrencyCode("USD", exchange.getCoinCode());
            // USD 余额
            // 扣款 客户usd余额
            VccReqDto vccReqC = new VccReqDto();
            vccReqC
                    .setActiveCodeNum(0) //需要处理的激活码数量
                    .setUserId(SecurityUtils.getLoginUser().getUserId()) // 用户id
                    .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                    .setWalletAmount(exchange.getQuantity()) //钱包需要处理的金额
                    .setFreezeType(FreezeType.NO) // 冻结类型
                    .setTxnType(TxnType.WALLET_RECHARGE_C) // 交易类型
                    .setFromUserId(SecurityUtils.getLoginUser().getUserId()) // 支出账户id
                    .setToUserId(accountId) // 收入账户id
                    .setFromCoin("USD")
                    .setFromAmount(exchange.getQuantity())
                    .setExchgRate(rate.getRateVal())
                    .setFee(BigDecimal.ZERO); // 手续费

            AjaxResult res = walletService.update(vccReqC);
            if (!res.isSuccess()) {
                throw new Exception(MessageUtils.message("wallet.deduct.funds.failed"));
            }

            // 入账 加到公司总账
            VccReqDto vccReqP = new VccReqDto();
            vccReqP
                    .setActiveCodeNum(0) //需要处理的激活码数量
                    .setActiveCodeAlgorithmType(AlgorithmType.ADD)
                    .setUserId(accountId) // 公司总账id
                    .setWalletAlgorithmType(AlgorithmType.ADD) //钱包需要处理的类型：加法或者减法
                    .setWalletAmount(exchange.getQuantity()) //钱包需要处理的金额
                    .setFreezeType(FreezeType.NO) // 冻结类型
                    .setTxnType(TxnType.WALLET_RECHARGE_D) // 交易类型
                    .setFromUserId(SecurityUtils.getLoginUser().getUserId()) // 支出账户id
                    .setToUserId(accountId) // 收入账户id
                    .setFromCoin("USD")
                    .setFromAmount(exchange.getQuantity().negate())
                    .setExchgRate(rate.getRateVal())
                    .setFee(BigDecimal.ZERO); // 手续费

            res = walletService.updateComAcc(vccReqP);
            if (!res.isSuccess()) {
                throw new Exception(MessageUtils.message("wallet.deduct.funds.failed"));
            }

            // 钱包
            // 1.计算兑换数量
            // 获取兑换利率
            //ExchangeRate rate = exchangeRateDao.findByFromCurrencyCodeAndToCurrencyCode("USD", exchange.getCoinCode());

            // 最终入账
            BigDecimal amount = exchange.getQuantity().multiply(rate.getRateVal());

            // 2.更新

            // 充币 客户钱包
            CoinBalance balanceC = coinBalanceDao.findByUserIdAndCoinCode(SecurityUtils.getLoginUser().getUserId(), exchange.getCoinCode());
            checkCoinExist(balanceC, exchange.getCoinCode());

            coinBalanceDao.updateCoinBalance(SecurityUtils.getLoginUser().getUserId(), exchange.getCoinCode(), balanceC.getCoinBalance().add(amount));
        } else if (ExchangeType.B_TO_U.getOrdinal().equals(exchange.getExchangeType())) {
            // 扣币(个人账户)
            CoinBalance balanceC = coinBalanceDao.findByUserIdAndCoinCode(SecurityUtils.getLoginUser().getUserId(), exchange.getCoinCode());
            checkCoinExist(balanceC, exchange.getCoinCode());
            coinBalanceDao.updateCoinBalance(SecurityUtils.getLoginUser().getUserId(), exchange.getCoinCode(), balanceC.getCoinBalance().subtract(exchange.getQuantity()));

            // 加钱
            // 获取兑换利率
            // 最终入账
            ExchangeRate rate = exchangeRateService.findByFromCurrencyCodeAndToCurrencyCode(exchange.getCoinCode(), "USD");
            BigDecimal amount = exchange.getQuantity().multiply(rate.getRateVal());
            amount = amount.setScale(2, RoundingMode.DOWN); // 保留两位小数,且向下取整

            result.put("amount", amount);
            VccReqDto vccReqP = new VccReqDto();
            vccReqP
                    .setActiveCodeNum(0) //需要处理的激活码数量
                    .setActiveCodeAlgorithmType(AlgorithmType.ADD)
                    .setUserId(accountId) // 公司总帐
                    .setWalletAlgorithmType(AlgorithmType.SUB) //钱包需要处理的类型：加法或者减法
                    .setWalletAmount(amount) //钱包需要处理的金额
                    .setFreezeType(FreezeType.NO) // 冻结类型
                    .setTxnType(TxnType.WALLET_WITHDRAW_C) // 交易类型
                    .setFromUserId(accountId) // 支出账户id
                    .setToUserId(SecurityUtils.getLoginUser().getUserId()) // 收入账户id
                    .setFromCoin(exchange.getCoinCode())
                    .setFromAmount(exchange.getQuantity())
                    .setExchgRate(rate.getRateVal())
                    .setFee(BigDecimal.ZERO); // 手续费
            AjaxResult res = walletService.updateComAcc(vccReqP);
            if (!res.isSuccess()) {
                throw new Exception(MessageUtils.message("wallet.deduct.funds.failed"));
            }

            VccReqDto vccReqC = new VccReqDto();
            vccReqC
                    .setActiveCodeNum(0) //需要处理的激活码数量
                    .setActiveCodeAlgorithmType(AlgorithmType.ADD)
                    .setUserId(SecurityUtils.getLoginUser().getUserId()) // 用户id
                    .setWalletAlgorithmType(AlgorithmType.ADD) //钱包需要处理的类型：加法或者减法
                    .setWalletAmount(amount) //钱包需要处理的金额
                    .setFreezeType(FreezeType.NO) // 冻结类型
                    .setTxnType(TxnType.WALLET_WITHDRAW_D) // 交易类型
                    .setFromUserId(accountId) // 支出账户id
                    .setToUserId(SecurityUtils.getLoginUser().getUserId()) // 收入账户id
                    .setFromCoin(exchange.getCoinCode())
                    .setFromAmount(exchange.getQuantity().negate())
                    .setExchgRate(rate.getRateVal())
                    .setFee(BigDecimal.ZERO); // 手续费
            res = walletService.update(vccReqC);
            if (!res.isSuccess()) {
                throw new Exception(MessageUtils.message("wallet.deduct.funds.failed"));
            }
        }

        return result;
    }

    /**
     * 获取兑换汇率
     *
     * @param fromCurrencyCode 本币币种
     * @param toCurrencyCode   兑换币种
     * @return 兑换汇率
     */
    @Override
    public ExchangeRate getExchangeRate(String fromCurrencyCode, String toCurrencyCode) {
        return exchangeRateService.findByFromCurrencyCodeAndToCurrencyCode(fromCurrencyCode, toCurrencyCode);
    }

    /**
     * 根据 用户id获取所有的数字货币钱包
     *
     * @param userId 用户id
     * @return 数字货币钱包
     */
    @Override
    public List<CoinBalance> findAllByUserId(Long userId) {
        return coinBalanceDao.findAllByUserId(userId);
    }

    /**
     * @param withdrawal 提现
     * @return 余额是否充足
     */
    @SneakyThrows
    @Override
    public boolean checkWithdrawal(WithdrawalDto withdrawal) {
        CoinBalance coinBalance = coinBalanceDao.findByUserIdAndCoinCode(SecurityUtils.getLoginUser().getUserId(), withdrawal.getCoinCode());
        if (coinBalance == null) {
            return false;
        }
        checkCoinExist(coinBalance, withdrawal.getCoinCode());
        return coinBalance.getCoinBalance().compareTo(withdrawal.getQuantity()) >= 0; // 对比时不需要算上手续费(手续费从本次的提现数量中扣)
    }

    /**
     * @param withdrawal 提款
     */
    @Override
    public void withdrawal(WithdrawalDto withdrawal) {
        CoinBalance balance = coinBalanceDao.findByUserIdAndCoinCode(SecurityUtils.getLoginUser().getUserId(), withdrawal.getCoinCode());
        balance.setCoinBalance(balance.getCoinBalance().subtract(withdrawal.getQuantity()));  // 原本的余额减去本次提现数量
        balance.setFreezeBalance(balance.getFreezeBalance().add(withdrawal.getQuantity())); // 原本冻结的余额 加上本次提现数量
        coinBalanceDao.save(balance);
        // 插入提币记录
        CoinTxnDtl coinTxnDtl = new CoinTxnDtl();
        coinTxnDtl.setUserId(SecurityUtils.getLoginUser().getUserId());
        coinTxnDtl.setRecordType(0);
        coinTxnDtl.setTxnCoin(balance.getCoinCode());
        coinTxnDtl.setTxnCode(Constants.txnCode.c1010);
        coinTxnDtl.setReceiptNo("");
//        coinTxnDtl.setFromAddress();
        coinTxnDtl.setToAddress(withdrawal.getAddress());
        coinTxnDtl.setTxnStatus(2);
        coinTxnDtl.setTxnDesc("提币");
        coinTxnDtl.setTxnAmount(withdrawal.getQuantity().subtract(withdrawal.getFee()));  // 到账金额等于本次提现数量减去本次手续费
        coinTxnDtl.setTxnFee(withdrawal.getFee());
        // coinTxnDtl.setTxnTime(DateUtils.getNowDateOld());
        coinTxnDtl.setCreateTime(DateUtils.getNowDateOld());
        coinTxnDtl.setUserBalance(balance.getCoinBalance());
        String type = withdrawal.getType();
        if (StringUtils.isEmpty(type)) {
            type = "TRC20";
        }
        if (withdrawal.getAddress().startsWith("0x")) {
            type = "BEP20";
        }
        coinTxnDtl.setCoinNet(type);//币种网络
        coinTxnDtlService.save(coinTxnDtl);
        //发送邮件
        String auditEmail = configService.selectConfigByKey("audit_email");
        if (StringUtils.isNotEmpty(auditEmail)) {
            if (auditEmail.contains(",")) {
                String[] result = auditEmail.split(",");
                for (String address : result) {
                    Mail mail = new Mail();
                    mail.setEmail(address);
                    mail.setContent("提币审核:" + SecurityUtils.getLoginUser().getUserId());
                    mail.setSubject("提币审核");
                    sendEmail.sendCheckEmail(mail);

                }
            } else {
                Mail mail = new Mail();
                mail.setEmail(auditEmail);
                mail.setContent("提币审核:" + SecurityUtils.getLoginUser().getUserId());
                mail.setSubject("提币审核");
                sendEmail.sendCheckEmail(mail);
            }

        }


    }

    @Override
    @Transactional
    public CoinBalance findByUserIdAndCoinCode(Long userId, String coinCode) {
        return coinBalanceDao.findByUserIdAndCoinCode(userId, coinCode);
    }

    /**
     * 判断钱包是否存在
     *
     * @param coin
     */
    public AjaxResult checkCoinExist(CoinBalance coin, String coinCode) {
        if (coin == null) {
            return AjaxResult.error(MessageUtils.message("wallet.coin.not.exist", coinCode));
        }
        return AjaxResult.success();
    }


}
