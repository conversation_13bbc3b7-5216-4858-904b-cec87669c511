package com.meta.service;

public interface ICommonService {
    String fetchUserSysId();

//    String fetchSysIdByCstId(String cstId);

//    Boolean isQCShareholder(Long loginID);

    /** ----- 变更语言 ----- */
    void i18nChangeLanguage(String language);
    void i18nChangeLanguage(String language, String cstID);

    /*/*
     * @description: 通用获取Config的Value
     * @throws
     * <AUTHOR>
     * @date 2022/6/9 11:15
     */
    String getConfigValue(String key, String name, String value);
}
