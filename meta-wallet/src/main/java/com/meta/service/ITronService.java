package com.meta.service;

import com.meta.system.domain.TrcCstaddress;
import org.springframework.scheduling.annotation.Async;

/**
 * <AUTHOR>
 * @date 2025/2/19 14:54
 **/
public interface ITronService {


    void aggregation(String fromAddress, String contractAddress);

    boolean getEnergyBySingle(TrcCstaddress trcCstaddress, long needEnergy, TrcCstaddress backupAddress);

    boolean getEnergy(TrcCstaddress trcCstaddress, TrcCstaddress backupAddress, long needEnergy);

    void createUserWallet(TrcCstaddress trcCstaddress);


    @Async
    boolean activateWallet(TrcCstaddress trcCstaddress);

    String getAggregationAddress(String sysId);
}
