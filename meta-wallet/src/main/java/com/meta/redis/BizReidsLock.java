package com.meta.redis;


import com.meta.common.config.RedisLock;
import com.meta.contants.Constants;
import com.meta.service.ICommonService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class BizReidsLock {


    @Resource
    private ICommonService commonService;

    @Resource
    private RedisLock redisLock;

    public Boolean lock(String cstId, String biz) {
        String key = createKey(cstId, biz);
        return redisLock.lock(key, 5000);
    }

    public void unlock(String cstId, String biz) {
        String key = createKey(cstId, biz);
        redisLock.unlock(key);
    }

    //新增锁
    public Boolean locks(String txId, String biz) {
        String key = createKeys(txId, biz);
        return redisLock.lock(key, 5000);
    }

    public void unlocks(String txId, String biz) {
        String key = createKeys(txId, biz);
        redisLock.unlock(key);
    }

    /**
     * description: 根据 业务 & 客户id 形成 key
     *
     * @Date 2021/5/31 下午1:39
     */
    private String createKeys(String txId, String biz) {
        //String sysId = commonService.fetchUserSysId();
        return Constants.redisLock.REDIS_LOCK + biz + "82" + ":" + txId;
    }

    /**
     * description: 根据 业务 & 客户id 形成 key
     * @Date 2021/5/31 下午1:39
     */
    private String createKey(String cstId, String biz) {
        String sysId = Constants.META_APP;
        return Constants.redisLock.REDIS_LOCK + biz + sysId + ":" + cstId;
    }
}
