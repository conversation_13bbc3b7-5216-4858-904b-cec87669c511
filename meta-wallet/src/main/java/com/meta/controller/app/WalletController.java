package com.meta.controller.app;

import com.meta.common.annotation.Log;
import com.meta.common.annotation.RepeatSubmit;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.enums.BusinessType;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.sign.Md5Utils;
import com.meta.common.utils.vcc.StringUtil;
import com.meta.config.WalletConfig;
import com.meta.dto.ReqTxId;
import com.meta.entity.CoinBalancePK;
import com.meta.service.CreateWalletService;
import com.meta.util.CoinUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/12/11/17:33
 */
@RestController
@RequestMapping("/walletServe")
public class WalletController {

    @Autowired
    private CreateWalletService createWalletService;

    @Autowired
    private CoinUtil coinUtil;


    @PostMapping("/create")
    public AjaxResult walletAddress(@RequestBody CoinBalancePK pk) {
        String md = Md5Utils.hash(pk.getUserId() + WalletConfig.key);
        if (!md.equals(pk.getMd5sum())) {
            return AjaxResult.error("md5sun验证失败");
        }
        if (StringUtils.isEmpty(pk.getSysId())) {
            return AjaxResult.error("sysId不能为空");
        }
        return AjaxResult.success(createWalletService.createWallet(pk));
    }
//    /**
//     * 监控公链上充值是否完成
//     *
//     * @param reqTxId
//     * @return
//     */
//    @Transactional(rollbackFor = Exception.class)
//    @Log(title = "监控公链上充值是否完成", businessType = BusinessType.OTHER)
//    @RepeatSubmit
//    @RequestMapping(value = "/getTxID", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
//    public AjaxResult getTxID(@RequestBody ReqTxId reqTxId) {
//        if (!StringUtil.isEmpty(reqTxId.getTxId()) && coinUtil.getUsdtRechargeByApiByMerLin(reqTxId.getTxId())) {
//            return AjaxResult.success();
//        }
//        return AjaxResult.error(1, "订单错误");
//    }
}
