package com.meta.controller.system;

import com.meta.common.core.domain.AjaxResult;
import com.meta.system.uitls.SimplePrivateKeyEncryptionUtil;

/**
 * 解密控制器测试类
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
public class DecryptControllerTest {

    public static void main(String[] args) {
        System.out.println("=== 解密接口测试 ===");
        
        // 创建控制器实例
        DecryptController controller = new DecryptController();
        
        // 测试数据
        String originalKey = "f90bd80f933cfef74730d69c133b85288c920ffc253300fe8cb326f668eba517";
        
        try {
            // 1. 先加密一个私钥
            System.out.println("1. 准备测试数据");
            String encryptedKey = SimplePrivateKeyEncryptionUtil.encryptPrivateKey(originalKey);
            System.out.println("原始私钥: " + originalKey);
            System.out.println("加密后的私钥: " + encryptedKey.substring(0, 40) + "...");
            System.out.println();
            
            // 2. 测试解密接口
            System.out.println("2. 测试解密接口");
            AjaxResult decryptResult = controller.decryptPrivateKey(encryptedKey);
            System.out.println("解密接口返回: " + (decryptResult.isSuccess() ? "成功" : "失败"));
            if (decryptResult.isSuccess()) {
                System.out.println("解密结果验证: " + originalKey.equals(((java.util.Map)decryptResult.get("data")).get("decryptedData")));
            }
            System.out.println();
            
            // 3. 测试智能解密接口（加密数据）
            System.out.println("3. 测试智能解密接口（加密数据）");
            AjaxResult smartResult1 = controller.smartDecrypt(encryptedKey);
            System.out.println("智能解密返回: " + (smartResult1.isSuccess() ? "成功" : "失败"));
            if (smartResult1.isSuccess()) {
                java.util.Map data = (java.util.Map)smartResult1.get("data");
                System.out.println("是否识别为加密: " + data.get("isEncrypted"));
                System.out.println("处理方式: " + data.get("processed"));
                System.out.println("结果验证: " + originalKey.equals(data.get("resultData")));
            }
            System.out.println();
            
            // 4. 测试智能解密接口（明文数据）
            System.out.println("4. 测试智能解密接口（明文数据）");
            AjaxResult smartResult2 = controller.smartDecrypt(originalKey);
            System.out.println("智能解密返回: " + (smartResult2.isSuccess() ? "成功" : "失败"));
            if (smartResult2.isSuccess()) {
                java.util.Map data = (java.util.Map)smartResult2.get("data");
                System.out.println("是否识别为加密: " + data.get("isEncrypted"));
                System.out.println("处理方式: " + data.get("processed"));
                System.out.println("结果验证: " + originalKey.equals(data.get("resultData")));
            }
            System.out.println();
            
            // 5. 测试批量解密接口
            System.out.println("5. 测试批量解密接口");
            String[] testData = {
                encryptedKey,
                originalKey,
                SimplePrivateKeyEncryptionUtil.encryptPrivateKey("test_key_123"),
                "plain_text_key"
            };
            
            AjaxResult batchResult = controller.batchDecrypt(testData);
            System.out.println("批量解密返回: " + (batchResult.isSuccess() ? "成功" : "失败"));
            if (batchResult.isSuccess()) {
                java.util.Map data = (java.util.Map)batchResult.get("data");
                System.out.println("总数: " + data.get("total"));
                System.out.println("成功数: " + data.get("successCount"));
                System.out.println("失败数: " + data.get("errorCount"));
            }
            System.out.println();
            
            // 6. 测试检查接口
            System.out.println("6. 测试检查接口");
            AjaxResult checkResult1 = controller.checkEncryption(encryptedKey);
            AjaxResult checkResult2 = controller.checkEncryption(originalKey);
            
            System.out.println("加密数据检查: " + (checkResult1.isSuccess() ? "成功" : "失败"));
            if (checkResult1.isSuccess()) {
                java.util.Map data = (java.util.Map)checkResult1.get("data");
                System.out.println("  识别为加密: " + data.get("isEncrypted"));
            }
            
            System.out.println("明文数据检查: " + (checkResult2.isSuccess() ? "成功" : "失败"));
            if (checkResult2.isSuccess()) {
                java.util.Map data = (java.util.Map)checkResult2.get("data");
                System.out.println("  识别为加密: " + data.get("isEncrypted"));
            }
            System.out.println();
            
            // 7. 测试状态接口
            System.out.println("7. 测试状态接口");
            AjaxResult statusResult = controller.getStatus();
            System.out.println("状态接口返回: " + (statusResult.isSuccess() ? "成功" : "失败"));
            if (statusResult.isSuccess()) {
                java.util.Map data = (java.util.Map)statusResult.get("data");
                System.out.println("服务名称: " + data.get("serviceName"));
                System.out.println("算法: " + data.get("algorithm"));
                System.out.println("状态: " + data.get("status"));
            }
            System.out.println();
            
            // 8. 测试错误处理
            System.out.println("8. 测试错误处理");
            AjaxResult errorResult1 = controller.decryptPrivateKey("");
            AjaxResult errorResult2 = controller.decryptPrivateKey("invalid_data");
            
            System.out.println("空数据处理: " + (errorResult1.isSuccess() ? "意外成功" : "正确失败"));
            System.out.println("无效数据处理: " + (errorResult2.isSuccess() ? "意外成功" : "正确失败"));
            
            System.out.println();
            System.out.println("=== 所有测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
