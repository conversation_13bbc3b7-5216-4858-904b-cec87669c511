package com.meta.controller.system;

import com.meta.common.core.domain.AjaxResult;
import com.meta.entity.MetaWalletCoinRec;
import com.meta.service.MetaWalletCoinRecService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户钱包代币余额记录控制器
 */
@Api(tags = "钱包代币余额记录")
@Slf4j
@RestController
@RequestMapping("/system/walle/balance")
public class WalletCoinRecController {

    @Autowired
    private MetaWalletCoinRecService walletCoinRecService;

//    @Autowired
//    private TronCoinRecSchedule tronCoinRecSchedule;

    /**
     * 外部允许新增接口（主要用于solana、bsc的接入）
     */
    @PostMapping("/save")
    public AjaxResult insert(@RequestBody MetaWalletCoinRec rec) {
        walletCoinRecService.save(rec);
        return AjaxResult.success();
    }

//
//    /**
//     * 查询指定时间范围内的余额记录
//     */
//    @ApiOperation("查询时间范围内的余额记录")
//    @GetMapping("/time-range")
//    public AjaxResult getByTimeRange(
//            @ApiParam(value = "开始时间(yyyy-MM-dd HH:mm:ss)", required = true) @RequestParam String startTime,
//            @ApiParam(value = "结束时间(yyyy-MM-dd HH:mm:ss)", required = true) @RequestParam String endTime) {
//
//        Date startDate = DateUtils.parseDate(startTime);
//        Date endDate = DateUtils.parseDate(endTime);
//
//        if (startDate == null || endDate == null) {
//            return AjaxResult.error("日期格式错误");
//        }
//
//        List<MetaTronCoinRec> records = metaTronCoinRecService.findByTimeRange(startDate, endDate);
//        List<MetaTronCoinRecDTO> dtoList = convertToDtoList(records);
//        return AjaxResult.success(dtoList);
//    }
//
//    /**
//     * 根据代币符号查询余额记录
//     */
//    @ApiOperation("根据代币符号查询余额记录")
//    @GetMapping("/token/{tokenSymbol}")
//    public AjaxResult getByTokenSymbol(
//            @ApiParam(value = "代币符号", required = true) @PathVariable String tokenSymbol) {
//
//        List<MetaTronCoinRec> records = metaTronCoinRecService.findByTokenSymbol(tokenSymbol);
//        List<MetaTronCoinRecDTO> dtoList = convertToDtoList(records);
//        return AjaxResult.success(dtoList);
//    }
//
//    /**
//     * 手动添加/更新余额记录
//     */
//    @ApiOperation("手动添加/更新余额记录")
//    @PostMapping("/save")
//    public AjaxResult save(@RequestBody MetaTronCoinRecDTO dto) {
//        MetaTronCoinRec record = new MetaTronCoinRec();
//        BeanUtils.copyProperties(dto, record);
//
//        // 设置创建/更新时间
//        if (record.getCreateTime() == null) {
//            record.setCreateTime(new Date());
//        }
//        record.setUpdateTime(new Date());
//
//        record = metaTronCoinRecService.save(record);
//
//        return AjaxResult.success(convertToDto(record));
//    }
//
//    /**
//     * 手动触发余额记录任务
//     */
//    @ApiOperation("手动触发余额记录任务")
//    @PostMapping("/trigger-record")
//    public AjaxResult triggerBalanceRecord() {
//        log.info("接收到手动触发余额记录任务的请求");
//        try {
//            tronCoinRecSchedule.manualTriggerBalanceRecord();
//            return AjaxResult.success("已触发余额记录任务");
//        } catch (Exception e) {
//            log.error("手动触发余额记录任务时出错: {}", e.getMessage(), e);
//            return AjaxResult.error("触发余额记录任务失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 将实体对象转换为DTO
//     */
//    private MetaTronCoinRecDTO convertToDto(MetaTronCoinRec record) {
//        MetaTronCoinRecDTO dto = new MetaTronCoinRecDTO();
//        BeanUtils.copyProperties(record, dto);
//
//        // 计算可读余额
//        if (record.getRawBalance() != null && record.getDecimals() != null) {
//            BigInteger rawBalance = record.getRawBalance(); // 直接使用BigInteger类型
//            BigDecimal divisor = BigDecimal.TEN.pow(record.getDecimals());
//            dto.setReadableBalance(new BigDecimal(rawBalance).divide(divisor));
//        }
//
//        return dto;
//    }
//
//    /**
//     * 将实体对象列表转换为DTO列表
//     */
//    private List<MetaTronCoinRecDTO> convertToDtoList(List<MetaTronCoinRec> records) {
//        return records.stream()
//                .map(this::convertToDto)
//                .collect(Collectors.toList());
//    }
}
