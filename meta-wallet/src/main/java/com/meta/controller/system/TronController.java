package com.meta.controller.system;

import com.meta.common.config.RedisLock;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.exception.CustomException;
import com.meta.common.utils.sign.Md5Utils;
import com.meta.config.WalletConfig;
import com.meta.manager.MagicianBlockchainScanComponent;
import com.meta.service.ITronService;
import com.meta.system.domain.TrcCstaddress;
import com.meta.system.service.TrcCstaddressService;
import com.meta.system.uitls.RedisConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2025/2/19 15:16
 **/
@Slf4j
@RestController
@RequestMapping("/user")
public class TronController {

    @Autowired
    private ITronService tronService;

    @Autowired
    private MagicianBlockchainScanComponent scanComponent;

    @Autowired
    private RedisLock redisLock;

    @Autowired
    private TrcCstaddressService trcCstaddressService;
    @Autowired
    private WalletConfig walletConfig;

    @PostMapping("/address")
    public AjaxResult createUserWallet(@RequestBody TrcCstaddress trcCstaddress) {
        if (WalletConfig.openCreateAddress.equals(false)) {
            return AjaxResult.error("钱包创建功能已关闭");
        }
        /*
         * 参数校验
         */
        if (StringUtils.isBlank(trcCstaddress.getSysId())
                || StringUtils.isBlank(trcCstaddress.getMd5sum())
                || trcCstaddress.getCstId() == null) {
            throw new CustomException("缺少必要参数");
        }
        /*
         * md5校验
         */
        String md = Md5Utils.hash(trcCstaddress.getCstId() + WalletConfig.key);
        if (!md.equals(trcCstaddress.getMd5sum())) {
            throw new CustomException("md5验证错误");
        }

        redisLock.lock(RedisConstants.LOCK_CREATE_USER_WALLET + trcCstaddress.getSysId() + trcCstaddress.getCstId(), 1500);
        TrcCstaddress exist = trcCstaddressService.findByCst(trcCstaddress.getCstId(), trcCstaddress.getSysId());
        //存在直接返回
        if (exist != null) {
            return AjaxResult.success(new TrcCstaddress().setCstAddress(exist.getCstAddress()));
        }
        tronService.createUserWallet(trcCstaddress);
        redisLock.unlock(RedisConstants.LOCK_CREATE_USER_WALLET + trcCstaddress.getSysId() + trcCstaddress.getCstId());

        return AjaxResult.success(new TrcCstaddress().setCstAddress(trcCstaddress.getCstAddress()));
    }


    /**
     * 手动钱包归集
     */
    @PostMapping("/guijiyuer")
    public AjaxResult aggregation(@RequestBody TrcCstaddress trcCstaddress) {
        if (trcCstaddress.getCoinType().equalsIgnoreCase("USDC")) {
            trcCstaddress.setContractAddress(WalletConfig.trc20AddressForUsdc);
        } else {
            trcCstaddress.setContractAddress(WalletConfig.trc20Address);
        }
        if (StringUtils.isBlank(trcCstaddress.getContractAddress())) {
            trcCstaddress.setContractAddress(WalletConfig.trc20Address);
        }
        tronService.aggregation(trcCstaddress.getWalletAddress(), trcCstaddress.getContractAddress());
        return AjaxResult.success();
    }

    /**
     * 手动区间扫描
     */
    @PostMapping("/scan/{startBlock}/{endBlock}")
    public AjaxResult scan(@PathVariable BigInteger startBlock, @PathVariable BigInteger endBlock) {
        scanComponent.startTronScan(startBlock, endBlock);
        return AjaxResult.success();
    }


}
