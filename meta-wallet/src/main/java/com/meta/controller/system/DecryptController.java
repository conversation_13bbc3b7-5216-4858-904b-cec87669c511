package com.meta.controller.system;

import com.meta.common.core.domain.AjaxResult;
import com.meta.system.uitls.SimplePrivateKeyEncryptionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 私钥解密接口
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Api(tags = "私钥解密接口")
@RestController
@RequestMapping("/system/decrypt")
public class DecryptController {

    private static final Logger log = LoggerFactory.getLogger(DecryptController.class);

    /**
     * 解密私钥
     * 
     * @param encryptedData 加密后的密文
     * @return 解密后的明文数据
     */
    @ApiOperation("解密私钥")
    @PostMapping("/privateKey")
    public AjaxResult decryptPrivateKey(@ApiParam("加密后的密文") @RequestParam String encryptedData) {
        try {
            // 参数验证
            if (encryptedData == null || encryptedData.trim().isEmpty()) {
                return AjaxResult.error("加密数据不能为空");
            }

            // 执行解密
            String decryptedData = SimplePrivateKeyEncryptionUtil.decryptPrivateKey(encryptedData.trim());
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("encryptedData", encryptedData);
            result.put("decryptedData", decryptedData);
            result.put("algorithm", "AES-GCM");
            result.put("success", true);
            
            log.info("私钥解密成功，密文长度: {}", encryptedData.length());
            return AjaxResult.success("解密成功", result);
            
        } catch (Exception e) {
            log.error("私钥解密失败，密文: {}", encryptedData, e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("encryptedData", encryptedData);
            errorResult.put("error", e.getMessage());
            errorResult.put("success", false);
            
            return AjaxResult.error("解密失败: " + e.getMessage(), errorResult);
        }
    }

    /**
     * 智能解密私钥（推荐使用）
     * 自动判断是否需要解密，如果是明文则直接返回
     * 
     * @param data 可能是加密或未加密的数据
     * @return 解密后的明文数据
     */
    @ApiOperation("智能解密私钥")
    @PostMapping("/smart")
    public AjaxResult smartDecrypt(@ApiParam("可能是加密或未加密的数据") @RequestParam String data) {
        try {
            // 参数验证
            if (data == null || data.trim().isEmpty()) {
                return AjaxResult.error("数据不能为空");
            }

            String trimmedData = data.trim();
            
            // 检查是否已加密
            boolean isEncrypted = SimplePrivateKeyEncryptionUtil.isEncrypted(trimmedData);
            
            // 智能处理
            String resultData = SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(trimmedData);
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("originalData", data);
            result.put("isEncrypted", isEncrypted);
            result.put("resultData", resultData);
            result.put("algorithm", "AES-GCM");
            result.put("processed", isEncrypted ? "解密" : "直接返回");
            result.put("success", true);
            
            log.info("智能解密处理完成，是否加密: {}, 数据长度: {}", isEncrypted, data.length());
            return AjaxResult.success("处理成功", result);
            
        } catch (Exception e) {
            log.error("智能解密失败，数据: {}", data, e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("originalData", data);
            errorResult.put("error", e.getMessage());
            errorResult.put("success", false);
            
            return AjaxResult.error("处理失败: " + e.getMessage(), errorResult);
        }
    }

    /**
     * 批量解密私钥
     * 
     * @param encryptedDataList 加密数据列表
     * @return 解密结果列表
     */
    @ApiOperation("批量解密私钥")
    @PostMapping("/batch")
    public AjaxResult batchDecrypt(@ApiParam("加密数据列表") @RequestBody String[] encryptedDataList) {
        try {
            if (encryptedDataList == null || encryptedDataList.length == 0) {
                return AjaxResult.error("加密数据列表不能为空");
            }

            Map<String, Object> results = new HashMap<>();
            Map<String, String> successResults = new HashMap<>();
            Map<String, String> errorResults = new HashMap<>();
            
            for (String encryptedData : encryptedDataList) {
                if (encryptedData != null && !encryptedData.trim().isEmpty()) {
                    try {
                        String decrypted = SimplePrivateKeyEncryptionUtil.getDecryptedPrivateKey(encryptedData.trim());
                        successResults.put(encryptedData, decrypted);
                    } catch (Exception e) {
                        errorResults.put(encryptedData, e.getMessage());
                        log.warn("批量解密中单个数据失败: {}", encryptedData, e);
                    }
                }
            }
            
            results.put("success", successResults);
            results.put("errors", errorResults);
            results.put("total", encryptedDataList.length);
            results.put("successCount", successResults.size());
            results.put("errorCount", errorResults.size());
            results.put("algorithm", "AES-GCM");
            
            log.info("批量解密完成，总数: {}, 成功: {}, 失败: {}", 
                    encryptedDataList.length, successResults.size(), errorResults.size());
            
            return AjaxResult.success("批量解密完成", results);
            
        } catch (Exception e) {
            log.error("批量解密失败", e);
            return AjaxResult.error("批量解密失败: " + e.getMessage());
        }
    }

    /**
     * 检查数据是否已加密
     * 
     * @param data 待检查的数据
     * @return 检查结果
     */
    @ApiOperation("检查数据是否已加密")
    @GetMapping("/check")
    public AjaxResult checkEncryption(@ApiParam("待检查的数据") @RequestParam String data) {
        try {
            if (data == null || data.trim().isEmpty()) {
                return AjaxResult.error("数据不能为空");
            }

            boolean isEncrypted = SimplePrivateKeyEncryptionUtil.isEncrypted(data.trim());
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", data);
            result.put("isEncrypted", isEncrypted);
            result.put("dataLength", data.length());
            result.put("algorithm", "AES-GCM");
            result.put("encryptionEnabled", true); // 假设总是启用的
            
            return AjaxResult.success("检查完成", result);
            
        } catch (Exception e) {
            log.error("检查加密状态失败，数据: {}", data, e);
            return AjaxResult.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取解密服务状态
     * 
     * @return 服务状态信息
     */
    @ApiOperation("获取解密服务状态")
    @GetMapping("/status")
    public AjaxResult getStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("serviceName", "私钥解密服务");
        status.put("algorithm", "AES-128-GCM");
        status.put("version", "1.0.0");
        status.put("status", "运行中");
        status.put("features", new String[]{
            "AES-GCM认证加密",
            "智能加密检测", 
            "批量处理",
            "错误处理",
            "日志记录"
        });
        
        return AjaxResult.success("服务状态正常", status);
    }
}
