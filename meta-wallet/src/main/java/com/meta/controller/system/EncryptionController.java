package com.meta.controller.system;

import com.meta.common.core.controller.BaseController;
import com.meta.common.core.domain.AjaxResult;
import com.meta.config.EncryptionConfig;
import com.meta.util.PrivateKeyEncryptionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 私钥加密管理控制器
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Api(tags = "私钥加密管理")
@RestController
@RequestMapping("/system/encryption")
public class EncryptionController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(EncryptionController.class);

    /**
     * 加密私钥
     */
    @ApiOperation("加密私钥")
    @PostMapping("/encrypt")
    public AjaxResult encryptPrivateKey(@ApiParam("原始私钥") @RequestParam String privateKey) {
        try {
            String encryptedKey = PrivateKeyEncryptionUtil.encryptPrivateKey(privateKey);
            
            Map<String, Object> result = new HashMap<>();
            result.put("originalKey", privateKey);
            result.put("encryptedKey", encryptedKey);
            result.put("algorithm", EncryptionConfig.algorithm);
            result.put("encoding", EncryptionConfig.encoding);
            result.put("enabled", EncryptionConfig.enabled);
            
            return AjaxResult.success("私钥加密成功", result);
        } catch (Exception e) {
            log.error("私钥加密失败", e);
            return AjaxResult.error("私钥加密失败: " + e.getMessage());
        }
    }

    /**
     * 解密私钥
     */
    @ApiOperation("解密私钥")
    @PostMapping("/decrypt")
    public AjaxResult decryptPrivateKey(@ApiParam("加密后的私钥") @RequestParam String encryptedPrivateKey) {
        try {
            String decryptedKey = PrivateKeyEncryptionUtil.decryptPrivateKey(encryptedPrivateKey);
            
            Map<String, Object> result = new HashMap<>();
            result.put("encryptedKey", encryptedPrivateKey);
            result.put("decryptedKey", decryptedKey);
            result.put("algorithm", EncryptionConfig.algorithm);
            result.put("encoding", EncryptionConfig.encoding);
            
            return AjaxResult.success("私钥解密成功", result);
        } catch (Exception e) {
            log.error("私钥解密失败", e);
            return AjaxResult.error("私钥解密失败: " + e.getMessage());
        }
    }

    /**
     * 检查私钥是否已加密
     */
    @ApiOperation("检查私钥是否已加密")
    @GetMapping("/check")
    public AjaxResult checkEncryption(@ApiParam("私钥") @RequestParam String privateKey) {
        try {
            boolean isEncrypted = PrivateKeyEncryptionUtil.isEncrypted(privateKey);
            
            Map<String, Object> result = new HashMap<>();
            result.put("privateKey", privateKey);
            result.put("isEncrypted", isEncrypted);
            result.put("algorithm", EncryptionConfig.algorithm);
            result.put("enabled", EncryptionConfig.enabled);
            
            return AjaxResult.success("检查完成", result);
        } catch (Exception e) {
            log.error("检查私钥加密状态失败", e);
            return AjaxResult.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取加密配置信息
     */
    @ApiOperation("获取加密配置信息")
    @GetMapping("/config")
    public AjaxResult getEncryptionConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("algorithm", EncryptionConfig.algorithm);
        config.put("encoding", EncryptionConfig.encoding);
        config.put("enabled", EncryptionConfig.enabled);
        config.put("secretKeyLength", EncryptionConfig.secretKey != null ? EncryptionConfig.secretKey.length() : 0);
        config.put("hasPublicKey", EncryptionConfig.publicKey != null && !EncryptionConfig.publicKey.isEmpty());
        config.put("hasPrivateKey", EncryptionConfig.privateKey != null && !EncryptionConfig.privateKey.isEmpty());
        
        return AjaxResult.success("获取配置成功", config);
    }

    /**
     * 批量加密私钥
     */
    @ApiOperation("批量加密私钥")
    @PostMapping("/batchEncrypt")
    public AjaxResult batchEncryptPrivateKeys(@ApiParam("私钥列表") @RequestBody String[] privateKeys) {
        try {
            Map<String, String> results = new HashMap<>();
            
            for (String privateKey : privateKeys) {
                if (privateKey != null && !privateKey.trim().isEmpty()) {
                    String encryptedKey = PrivateKeyEncryptionUtil.encryptPrivateKey(privateKey.trim());
                    results.put(privateKey, encryptedKey);
                }
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("results", results);
            response.put("total", privateKeys.length);
            response.put("success", results.size());
            response.put("algorithm", EncryptionConfig.algorithm);
            
            return AjaxResult.success("批量加密完成", response);
        } catch (Exception e) {
            log.error("批量加密私钥失败", e);
            return AjaxResult.error("批量加密失败: " + e.getMessage());
        }
    }

    /**
     * 批量解密私钥
     */
    @ApiOperation("批量解密私钥")
    @PostMapping("/batchDecrypt")
    public AjaxResult batchDecryptPrivateKeys(@ApiParam("加密私钥列表") @RequestBody String[] encryptedPrivateKeys) {
        try {
            Map<String, String> results = new HashMap<>();
            
            for (String encryptedKey : encryptedPrivateKeys) {
                if (encryptedKey != null && !encryptedKey.trim().isEmpty()) {
                    String decryptedKey = PrivateKeyEncryptionUtil.decryptPrivateKey(encryptedKey.trim());
                    results.put(encryptedKey, decryptedKey);
                }
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("results", results);
            response.put("total", encryptedPrivateKeys.length);
            response.put("success", results.size());
            response.put("algorithm", EncryptionConfig.algorithm);
            
            return AjaxResult.success("批量解密完成", response);
        } catch (Exception e) {
            log.error("批量解密私钥失败", e);
            return AjaxResult.error("批量解密失败: " + e.getMessage());
        }
    }
}
