package com.meta.contants;


import java.math.BigDecimal;

/**
 * 通用常量信息
 *
 */
public class Constants
{
    public static final Integer RECORD_TYPE_EXPENSES  = 0; //支出
    public static final  Integer RECORD_TYPE_RECEIPTS = 1;//收入


    public static class redis{
        public static final String cardTransactionPape = "cardTransactionPape_";
    }

    public static class txnCode {
        public static final String d1010 = "d1010"; //充值
        public static final String c1010 = "c1010";//提币
        public static final String t1010 = "t1010"; // 转账

    }
    public static final String defaultPwd = "123456";

    public static final String META_APP = "meta_app";

    /** -------------------- 单位相关 -------------------- */
    public static class unit {
        /** -------------------- 数据空间单位 -------------------- */
        public static final String GiB = "GiB";
        public static final String GB = "GB";
        public static final String TiB = "TiB";
        public static final String TB = "TB";
        public static final String PiB = "PiB";
        public static final String PB = "PB";
        public static final String EiB = "EiB";
        public static final String EB = "EB";
    }

    //角色相关
    public static class userregist{
        public static final Long DEP_ID = 104L;
        public static final Long ROLE_ID_SUG = 1L;
        public static final Long ROLE_ID_PU = 2L;
    }
    /** -------------------- 货币标识 -------------------- */
    public static class coin {
        /** -------------------- 数据空间单位 -------------------- */
        public static final String FIL = "FIL";
        public static final String XCH = "XCH";
        public static final String USD = "USD";
        public static final String USDT = "USDT";
        public static final String HKD = "HKD";
    }

    /** -------------------- 短信签名模板ID -------------------- */
    public static class smsTemplateId {
        /** -------------------- 数据空间单位 -------------------- */
        public static final String PHONE_CODE = "phone_code";
        public static final String AUTH_NOTICE = "auth_notice";
        public static final String RECHARGE_NOTICE="recharge_notice";
        public static final String WITHDRAW_NOTICE = "withdraw_notice";
        public static final String SERVE_EXPIRE = "serve_expire";
        public static final String APIKEY_EXPIRE = "apikey_expire";
    }

    /** -------------------- 分布式锁相关 -------------------- */
    public static class redisLock {
        public static final int before = 9999;
        public static final int lock = 10000;

        public static final String REDIS_LOCK = "redis_lock:";
        public static final String FIL = "biz_fil:";
        public static final String USD = "biz_usd:";
        public static final String XCH = "biz_xch:";
        public static final String UNI = "biz_uni:";
        public static final String TRANS = "biz_tra:";

        public static final String TXID = "txid:";

        public static final String PRODUCT = "biz_product:";

        // 开仓 : 0 - 可停止
        public static final String POSITION_0 = "biz_position0:";
        // 开仓 : 1 - 可发送
        public static final String POSITION_1 = "biz_position1:";
        public static final String USERJOIN = "biz_userjoin:";
        public static final String USERLEAVE = "biz_userleave:";
        public static final String USERFORCELEAVE = "biz_userforceleave:";
    }

    /** -------------------- 语言 & 地区 相关 -------------------- */
    public static class language {
        public static final String base = "language:";
        public static final String version = base + "time:";

        // language:82:137  = zh_HK
        public static final String zhCN = "zh_CN";
        public static final String zhHK = "zh_HK";
        public static final String enUS = "en_US";


        public static final String PRODUCT_LEVEL = "PRODUCT_LEVEL";  // 产品名字
        public static final String CUST_LEVEL = "CUST_LEVEL";  // 用户等级
    }
    public static class area {
        public static final String base = "area:";
        // area:82:137  = MAC
        public static final String ALL = "ALL";
        public static final String CHN = "CHN";
        public static final String MAC = "MAC";
        public static final String HKG = "HKG";
    }

    /** -------------------- 产品相关 -------------------- */
    public static class product {
        public static final String ID = "product:";
        public static final String TIME = ID + "Time:";
        public static final String PRODUCTTYPE = "productType:";
    }

    /** -------------------- 用户相关 -------------------- */
    public static class user {
        public static final String Base = "user:";
        public static final String Validate = Base + "validate:";
    }

    public static final String BEP_ADDRESS="biz_bep20:";
    public static final String OK = "OK";

    public static final String CODE = "0";

    public static final Integer COIN_TYPE_USDT = 3; //交易货币 1BTC 2ETH 3USDT 4FIL 5DOT 6UNI
    public static final Integer COIN_TYPE_FIL = 4;
    public static final Integer COIN_TYPE_XCH = 1;

    public static final String MAC_GUDO_RATIO = "0.03";  //澳门股东3点分红

    /**
     * 数据库删除标志
     */
    public static final String DELETE = "2";
    public static final String NOT_DELETE = "0";

    /**
     * 小数位
     */
    public static int FIL_SCALE = 4;
    public static final int USDT_SCALE = 6;
    public static final int XCH_SCALE = 11;
    public static final int XCH_APP_SCALE = 4;
    public static final int FIL_POW_SCALE = 4;

    /**
     * 平台标志
     */
    public static final String LY_SYS = "36";
    public static final String CLOUD_SYS = "82";
    public static final String LM_SYS = "2";
    public static final String QC_SYS = "3";
    public static final String RC_SYS = "4";

    public static final BigDecimal ZERO = new BigDecimal("0");
    public static final BigDecimal ONE = new BigDecimal("1");

    /**
     * 手机验证码类型
     */
    public static final String MOBILE_CODE_LOGIN = "MOBILE_CODE_LOGIN"; // 登录验证码
    public static final String MOBILE_CODE_REGISTER = "MOBILE_CODE_REGISTER"; //注册验证码
    public static final String MOBILE_CODE_BIND_PHONE = "MOBILE_CODE_BIND_PHONE"; //绑定新手机验证码
    public static final String MOBILE_CODE_UPDATE_PWD = "MOBILE_CODE_UPDATE_PWD"; //修改密码验证码
    public static final String MOBILE_CODE_AUTH_NAME = "MOBILE_CODE_AUTH_NAME"; //实名认证验证码
    public static final String MOBILE_CODE_TOP_UP = "MOBILE_CODE_TOP_UP"; //后台充值验证码
    public static final String MOBILE_CODE_LEVEL = "MOBILE_CODE_LEVEL"; //设置等级验证码
    public static final String MOBILE_CODE_FORGET_PASSWORD = "MOBILE_CODE_FORGET_PASSWORD"; //忘记密码验证码
    public static final String MOBILE_CODE_UPDATE_FUNDPWD = "MOBILE_CODE_UPDATE_FUNDPWD"; //修改资金验证码
    public static final String MOBILE_CODE_WITHDRAW_USDT = "MOBILE_CODE_WITHDRAW_USDT"; //提币USDT
    public static final String MOBILE_CODE_WITHDRAW_FIL = "MOBILE_CODE_WITHDRAW_FIL"; //提币FIL
    public static final String MOBILE_CODE_WITHDRAW_XCH = "MOBILE_CODE_WITHDRAW_XCH"; //提币XCH
    public static final String MOBILE_CODE_FIL_ADDRESS = "MOBILE_CODE_FIL_ADDRESS"; //更改FIL收款地址
    public static final String MOBILE_CODE_USDT_TRANSFERS = "MOBILE_CODE_USDT_TRANSFERS"; //USDT内部转账
    public static final String MOBILE_CODE_FIL_TRANSFERS = "MOBILE_CODE_FIL_TRANSFERS"; //FIL内部转账
    public static final String MOBILE_CODE_XCH_TRANSFERS = "MOBILE_CODE_XCH_TRANSFERS"; //XCH内部转账
    public static final String MOBILE_CODE_COUPONL_TRANSFERS = "MOBILE_CODE_COUPONL_TRANSFERS"; //优惠券内部转账
    public static final String MOBILE_CODE_STRATEGY_STANDARDS = "MOBILE_CODE_STRATEGY_STANDARDS"; //策略达标
    public static final String MOBILE_CODE_APIKEY_OVERDUE = "MOBILE_CODE_APIKEY_OVERDUE"; //APIKEY过期
    public static final String MOBILE_CODE_RESIDUE_DAYS = "MOBILE_CODE_RESIDUE_DAYS"; //剩余天数
    public static final String MOBILE_CODE_DELETE_WADDRESS = "MOBILE_CODE_DELETE_WADDRESS"; //剩余天数
    public static final String MOBILE_SYS_ALARM_CODE= "MOBILE_SYS_ALARM_CODE"; //系统告警短信

    public static final String MOBILE_CODE_MAC_RATIO = "MOBILE_CODE_MAC_RATIO"; //澳门股东设置3点分红比例

    public static final String LONG_DATE = "2999-12-31";

    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";


    public static final String POOL_ID_KEY = "pool_id";
    public static final String NODE_IDE_KEY = "node_id";

    public static final String GLOABLE_HDCG_PCTG = "gloable_hdcg_pctg";

    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";

    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = "sub";

    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";


    //联合封装id
    public static final String LHFZ = "600001";


    //量化相关参数
    //量化等级划分
    public static int QUANTIZE_JS_ACCOUNT = 10; //独立账号技术
    public static int QUANTIZE_SXY_ACCOUNT = 9; //独立账号商学院
    public static int QUANTIZE_YY_ACCOUNT = 8; //独立账号运营
    public static int QUANTIZE_YL_ACCOUNT = 7; //独立账号遗漏
    public static int QUANTIZE_LEVEL_A = 6; //市场总号
    public static int QUANTIZE_LEVEL_B = 5; //市场核心股东
    public static int QUANTIZE_LEVEL_C = 4; //市场合伙人
    public static int QUANTIZE_LEVEL_D = 3; //市场总监
    public static int QUANTIZE_LEVEL_SUP = 2; //SUP客户
    public static int QUANTIZE_LEVEL_VIP = 1; //VIP客户
    public static int QUANTIZE_LEVEL_GUKE = 0; //普通客户

    public static final String YY_CSTID = "87";
    public static final String STRING_INTF = "strtg_intf";
    public static final String U_STOCK = "uservice_all_stock";
    public static final String BI_STOCK = "cservice_all_stock";
    public static final String STOP_LOSS = "bvl_stop_loss";
    public static final String BUY_CSRID_LIST = "product_buy_cstid_list";
    public static final String APIKEY_LOCK_IND = "apikey_lock_ind";
    public static final String APIKEY_BAN = "Y";
    public static final String APIKEY_AGREE= "N";
    public static final String RESIDUE_STOCK= "total_product_stock";
    public static final String USERVICE_CCY_LIST= "uservice_ccy_list";
    public static final String STOP_WIN_ADJUST= "bvl_stop_win_adjust";
    public static final String OKEX_ADDRESS_ACCONF = "okex_address_acconf";

    //量化推荐限制
    public static int QUANTIZE_PU_TUI = 100;
    public static int QUANTIZE_B_TUI = 11;
    public static int QUANTIZE_C_TUI = 2;
    public static int QUANTIZE_D_TUI = 3;

    //新股东总号
    public static final String BVL_A = "102";
    public static final String BVL_B = "103";
    public static final String BVL_C = "101";
}
