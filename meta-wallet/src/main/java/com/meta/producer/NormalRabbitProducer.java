package com.meta.producer;

import com.meta.common.constant.Constants;
import com.meta.common.exception.CustomException;
import com.meta.config.WalletConfig;
import com.meta.system.domain.MetaMainAddress;
import com.meta.system.dto.MqMonitorDto;
import com.meta.system.email.SendEmail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/4/10 10:16
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class NormalRabbitProducer {

    private final SendEmail sendEmail;
    private final WalletConfig walletConfig;
    private final MQSender mqSender;

//    public void send(String message) {
//        rabbitTemplate.convertAndSend(Constants.exchange_name_notify_for_wallet, Constants.routing_key_wallet_monitoring, message);
//        log.info("【生产者】Message send: {}", message);
//    }

    public void sendMonitor(MqMonitorDto message) {
        //超过多少钱入账，需要通知管理员检查
        BigDecimal alarmThreshold = null;
        //查询主钱包配置的告警阈值
        for (MetaMainAddress item : WalletConfig.mainAddressList) {
            if (item.getSysId().equals(message.getSysId())) {
                alarmThreshold = item.getAlarmThreshold();
            }
        }
        if (alarmThreshold == null) {
            sendEmail.remindMail(
                    String.format("入账失败，没有配置'notificationThreshold'。message: %s", message.getMessage()), "入账失败的信息提醒");
            throw new CustomException("入账失败，没有配置'notificationThreshold'");
        } else {
            if (message.getAmount().compareTo(alarmThreshold) >= 0) {
                mqSender.send(message, Constants.exchange_name_notify, Constants.routing_key_amount_alert);
            }
        }
    }

}
