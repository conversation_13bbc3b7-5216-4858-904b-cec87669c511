package com.meta.utils;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * ID工具类
 */
@Component
public class IdUtils {
    
    private static Snowflake snowflake;
    
    @Value("${snowflake.worker-id:1}")
    private long workerId;
    
    @Value("${snowflake.datacenter-id:1}")
    private long datacenterId;
    
    @PostConstruct
    public void init() {
        snowflake = IdUtil.getSnowflake(workerId, datacenterId);
    }
    
    /**
     * 获取雪花算法生成的ID
     * @return 雪花ID
     */
    public static long nextId() {
        if (snowflake == null) {
            synchronized (IdUtils.class) {
                if (snowflake == null) {
                    // 使用默认值初始化
                    snowflake = IdUtil.getSnowflake(1, 1);
                }
            }
        }
        return snowflake.nextId();
    }
    
    /**
     * 获取雪花算法生成的ID字符串
     * @return 雪花ID字符串
     */
    public static String nextIdStr() {
        return String.valueOf(nextId());
    }
} 