package com.meta.manager;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.meta.config.WalletConfig;
import com.meta.producer.NormalRabbitProducer;
import com.meta.scanning.TronThreadPoolUtil;
import com.meta.scanning.chain.model.TransactionModel;
import com.meta.scanning.chain.model.tron.TronContractModel;
import com.meta.scanning.chain.model.tron.TronTransactionModel;
import com.meta.scanning.monitor.TronMonitorEvent;
import com.meta.scanning.tron.wallet.AddressUtils;
import com.meta.service.ITronService;
import com.meta.system.dao.TrcCstaddressDao;
import com.meta.system.dao.TrcTransactionsDao;
import com.meta.system.domain.MetaMainAddress;
import com.meta.system.domain.TrcCstaddress;
import com.meta.system.domain.TrcTransactions;
import com.meta.system.dto.MqMonitorDto;
import com.meta.system.email.SendEmail;
import com.meta.system.enums.CoinType;
import com.meta.system.service.MetaMainAddressService;
import com.meta.system.service.impl.TrcCstaddressServiceImpl;
import com.meta.util.CoinUtil;
import com.meta.util.TronUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.tron.trident.abi.datatypes.Address;
import org.tron.trident.proto.Response;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.Map;
import java.util.Set;

@Slf4j
@Component
@RequiredArgsConstructor
public class TronEventOne implements TronMonitorEvent {

    private final TrcCstaddressServiceImpl trcCstaddressService;
    private final CoinUtil coinUtil;
    private final TrcTransactionsDao trcTransactionsDao;
    private final ITronService tronService;
    private final TrcCstaddressDao trcCstaddressDao;
    private final SendEmail sendEmail;
    private final NormalRabbitProducer rabbitProducer;
    private final MetaMainAddressService metaMainAddressService;


    @Override
    public void call(TransactionModel transactionModel) {
        /*
         * TriggerSmartContract 智能合约转账，疑似只有USDT
         * TransferAssetContract TRC10通证转账
         * TransferContract trx转账
         */
        TronContractModel tronContractModel = transactionModel.getTronTransactionModel().getRawData().getContract().get(0);
        String txId = transactionModel.getTronTransactionModel().getTxID();
        String contractType = tronContractModel.getType();
        // 只处理USDT和TRX转账
        if (!("TriggerSmartContract".equals(contractType) || "TransferAssetContract".equals(contractType))) {
            return;
        }

        CoinType coinType = null;
        String currentContractAddress = null;
        boolean isTrustMethod = false;

        // 智能合约转账时验证合约地址
        if ("TriggerSmartContract".equals(contractType)) {
            String contractAddress = new Address(tronContractModel.getParameter().getValue().getContractAddress()).toString();

            if (contractAddress.equals(WalletConfig.trc20Address)) {
                coinType = CoinType.USDT;
                currentContractAddress = WalletConfig.trc20Address;
            } else if (contractAddress.equals(WalletConfig.trc20AddressForUsdc)) {
                coinType = CoinType.USDC;
                currentContractAddress = WalletConfig.trc20AddressForUsdc;
            } else if (contractAddress.equals(WalletConfig.trc20AddressForGasFree)) {
                coinType = CoinType.USDT;
                currentContractAddress = WalletConfig.trc20Address;
            } else {
//                log.warn("其他币种交易不处理:{}", txId);
                return;
            }

            if (contractAddress.equals(WalletConfig.trc20Address) || contractAddress.equals(WalletConfig.trc20AddressForUsdc)) {
                if (tronContractModel.getParameter().getValue().getData().startsWith("a9059cbb")
                        || tronContractModel.getParameter().getValue().getData().startsWith("23b872dd")) {
                    isTrustMethod = true;
                }
            }
            if (contractAddress.equals(WalletConfig.trc20AddressForGasFree) && tronContractModel.getParameter().getValue().getData().startsWith("6f21b898")) {
                isTrustMethod = true;
            }


        } else {
            coinType = CoinType.TRX;
        }

        if (!transactionModel.getTronTransactionModel().getRet().get(0).getContractRet().equals("SUCCESS")) {
            return;
        }

        String data = tronContractModel.getParameter().getValue().getData();
        Map<String, String> map = AddressUtils.decryptHex(data, txId);
        if (map == null || map.isEmpty()) {
            return;
        }
        String fromAddress = tronContractModel.getParameter().getValue().getOwnerAddress();
        fromAddress = new Address(fromAddress).toString();
        String toAddress = map.get("address");
        // 交易双方地址为空不受理
        if (StringUtils.isBlank(fromAddress) || StringUtils.isBlank(toAddress)) {
            return;
        }


        Set<String> userTrc20Address = trcCstaddressService.findAllUserAddressToHashSet();

        //主钱包入账(collect)
        if (WalletConfig.mainAddressSet.contains(toAddress)) {
            if (isExist(txId)) return;
            mainWalletReceiveHandler(transactionModel, fromAddress, toAddress, txId, tronContractModel, map, currentContractAddress, userTrc20Address, isTrustMethod);
        }
        //主钱包出账(send)
        else if (WalletConfig.mainAddressSet.contains(fromAddress)) {
            if (isExist(txId)) return;
            mainWalletSendHandler(transactionModel, fromAddress, toAddress, txId, tronContractModel, map, currentContractAddress);
        }
        //用户钱包入账(receive)
        else if (userTrc20Address.contains(toAddress)) {
            if (isExist(txId)) return;
            receive(transactionModel, toAddress, txId, fromAddress, tronContractModel, map, coinType, currentContractAddress, isTrustMethod);
        }
        //用户钱包出账（报错）
        else if (userTrc20Address.contains(fromAddress)) {
            log.error("warning");
        }

    }

    private void collection(String toAddress, CoinType coinType, String currentContractAddress) {
        //触发自动归集
        TronThreadPoolUtil.aggregationExecutor.execute(() -> {
            TrcCstaddress trcCstaddressByCstAddress = trcCstaddressDao.getTrcCstaddressByCstAddress(toAddress);
            try {
                BigInteger tokenBalance = TronUtils.findTokenBalance(trcCstaddressByCstAddress.getCstAddress(), trcCstaddressByCstAddress.getCstTrcPrivate(), currentContractAddress);
                if (tokenBalance.compareTo(WalletConfig.aggregationUsdtThreshold.multiply(TronUtils.million)) >= 0) {
                    log.info("触发自动归集,地址:{}", toAddress);
                    tronService.aggregation(trcCstaddressByCstAddress.getCstAddress(), currentContractAddress);
                } else {
                    log.info("余额不足，不进行自动归集,地址:{}", toAddress);
                }
            } catch (Exception e) {
                log.error("自动归集失败", e);
                sendEmail.remindMail(String.format(
                        "归集失败。用户钱包地址：%s，失败信息: %s",
                        trcCstaddressByCstAddress.getCstAddress(), e.getMessage()
                ), "归集失败的信息提醒");
            }
        });
    }

    private boolean isExist(String txId) {
        TrcTransactions byTxid = trcTransactionsDao.findByTxid(txId);
        if (byTxid != null) {
            log.info("交易:{},已存在不保存", txId);
            return true;
        }
        return false;
    }

    private void receive(TransactionModel transactionModel, String toAddress, String txId, String fromAddress, TronContractModel tronContractModel, Map<String, String> map, CoinType coinType, String currentContractAddress, boolean isTrustMethod) {
        TrcTransactions trcTransactions;
        trcTransactions = getTrcTransactions(transactionModel, toAddress, txId, fromAddress, tronContractModel, map);
        trcTransactions.setType("receive");
        TrcCstaddress userWallet = trcCstaddressDao.findByAddress(toAddress);
        log.info("属于{}转账，txid:{}", coinType.getDescription(), txId);
        if (tronContractModel.getType().equals("TriggerSmartContract")) {
            BigInteger tokenBalance;
            tokenBalance = TronUtils.findTokenBalance(toAddress, userWallet.getCstTrcPrivate(), currentContractAddress);
            Response.Account account = TronUtils.getAccount(userWallet);
            BigDecimal trxBalance = new BigDecimal(account.getBalance()).divide(new BigDecimal("1000000"), 6, RoundingMode.DOWN);
            trcTransactionsDao.save(trcTransactions);
            alert(trcTransactions, toAddress, fromAddress, trxBalance, tokenBalance, userWallet);
            if (isTrustMethod) {
                coinUtil.getUsdtRechargeByApiByMerLin(txId);
                collection(toAddress, coinType, currentContractAddress);
            }
        } else {
            trcTransactions.setIsSync(2);
            trcTransactionsDao.save(trcTransactions);
        }
    }

    private void mainWalletSendHandler(TransactionModel transactionModel, String fromAddress, String toAddress, String txId, TronContractModel tronContractModel, Map<String, String> map, String currentContractAddress) {
        log.info("监听到主钱包:{},出账,txid:{}", fromAddress, txId);
        TrcTransactions trcTransactions;
        trcTransactions = getTrcTransactions(transactionModel, toAddress, txId, fromAddress, tronContractModel, map);
        trcTransactions.setType("send");
        trcTransactions.setIsSync(2);
        trcTransactionsDao.save(trcTransactions);

        MetaMainAddress mainWallet = metaMainAddressService.findAddress(fromAddress);
        TrcCstaddress userWallet = new TrcCstaddress();
        userWallet.setCstAddress(fromAddress);
        userWallet.setCstTrcPrivate("f90bd80f933cfef74730d69c133b85288c920ffc253300fe8cb326f668eba517");
        userWallet.setSysId(mainWallet.getSysId());
        BigInteger tokenBalance = TronUtils.findTokenBalance(fromAddress, userWallet.getCstTrcPrivate(), currentContractAddress);
        Response.Account account = TronUtils.getAccount(userWallet);
        BigDecimal trxBalance = new BigDecimal(account.getBalance()).divide(new BigDecimal("1000000"), 6, RoundingMode.DOWN);
        alert(trcTransactions, fromAddress, toAddress, trxBalance, tokenBalance, userWallet);

    }

    private void mainWalletReceiveHandler(TransactionModel transactionModel, String fromAddress, String toAddress, String txId, TronContractModel tronContractModel, Map<String, String> map, String currentContractAddress, Set<String> userTrc20Address, boolean isTrustMethod) {
        log.info("监听到主钱包:{}入账,txid:{}", toAddress, txId);
        TrcTransactions trcTransactions;
        trcTransactions = getTrcTransactions(transactionModel, toAddress, txId, fromAddress, tronContractModel, map);
        trcTransactions.setType("collect");
        trcTransactions.setIsSync(2);
        trcTransactionsDao.save(trcTransactions);

        MetaMainAddress mainWallet = metaMainAddressService.findAddress(toAddress);
        TrcCstaddress userWallet = new TrcCstaddress();
        userWallet.setCstAddress(fromAddress);
        userWallet.setCstTrcPrivate("f90bd80f933cfef74730d69c133b85288c920ffc253300fe8cb326f668eba517");
        userWallet.setSysId(mainWallet.getSysId());
        BigInteger tokenBalance = TronUtils.findTokenBalance(fromAddress, userWallet.getCstTrcPrivate(), currentContractAddress);
        Response.Account account = TronUtils.getAccount(userWallet);
        BigDecimal trxBalance = new BigDecimal(account.getBalance()).divide(new BigDecimal("1000000"), 6, RoundingMode.DOWN);
        alert(trcTransactions, fromAddress, toAddress, trxBalance, tokenBalance, userWallet);

        //来自用户，属于归集，需要入账操作
        if (userTrc20Address.contains(fromAddress) && isTrustMethod) {
            coinUtil.getUsdtRechargeByApiByMerLin(txId);
        }

    }

    private void alert(TrcTransactions trcTransactions, String userAddress, String otherAddress, BigDecimal trxBalance, BigInteger usdtBalance, TrcCstaddress userWallet) {
        String type;
        switch (trcTransactions.getType()) {
            case "receive":
                type = "转入";
                break;
            case "send":
                type = "转出";
                break;
            case "collect":
                type = "归集转出";
                break;
            default:
                type = "异常类型";
                break;
        }

        //超过多少钱入账，需要通知管理员检查
        rabbitProducer.sendMonitor(
                new MqMonitorDto(
                        trcTransactions.getTimestamp(),
                        "TRON",
                        userAddress,
                        otherAddress,
                        type,
                        trcTransactions.getAmount(),
                        "USDT",
                        trxBalance,
                        "TRX",
                        new BigDecimal(usdtBalance).divide(new BigDecimal("1000000"), 6, RoundingMode.DOWN),
                        "USDT",
                        userWallet.getSysId())
        );
    }

    private static @NotNull TrcTransactions getTrcTransactions(TransactionModel transactionModel, String toAddress, String txId, String fromAddress, TronContractModel tronContractModel, Map<String, String> map) {
        log.info("监听到用户钱包收款:{}", toAddress);
        TronTransactionModel tronTransactionModel = transactionModel.getTronTransactionModel();
        TrcTransactions trcTransactions = new TrcTransactions();
        trcTransactions.setTxid(txId);
        trcTransactions.setBlockHeight(tronTransactionModel.getTronBlockHeaderModel().getTronRawDataModel().getNumber().longValue());
        trcTransactions.setAddress(toAddress);
        trcTransactions.setFromaddress(fromAddress);

        if (tronContractModel.getType().equals("TriggerSmartContract")) {
            trcTransactions.setContract(new Address(tronContractModel.getParameter().getValue().getContractAddress()).getValue());
            trcTransactions.setAmount(new BigDecimal(map.get("quantity")).divide(BigDecimal.valueOf(1000000), 6, RoundingMode.DOWN));
        } else {
            trcTransactions.setContract("trx");
            trcTransactions.setAmount(tronContractModel.getParameter().getValue().getAmount().divide(BigDecimal.valueOf(1000000), 6, RoundingMode.DOWN));
        }

        //gasFree的fee计算不同
        if (StringUtils.isNotBlank(map.get("fee"))) {
            trcTransactions.setFee(new BigDecimal(map.get("fee")).divide(new BigDecimal("1000000"), 6, RoundingMode.DOWN));
        } else {
            //获取手续费
            trcTransactions.setFee(getFee(txId));
        }

        if (tronTransactionModel.getRawData().getTimestamp() != null) {
            trcTransactions.setTimestamp((BigInteger.valueOf(tronTransactionModel.getRawData().getTimestamp()).divide(new BigInteger("1000"))).intValue());
        } else if (tronTransactionModel.getTronBlockHeaderModel().getTronRawDataModel().getTimestamp() != null) {
            trcTransactions.setTimestamp((BigInteger.valueOf(tronTransactionModel.getTronBlockHeaderModel().getTronRawDataModel().getTimestamp()).divide(new BigInteger("1000"))).intValue());
        } else {
            //填充当前时间戳
            trcTransactions.setTimestamp((int) (System.currentTimeMillis() / 1000));
        }

        trcTransactions.setIsSync(0);
        return trcTransactions;
    }

    private static BigDecimal getFee(String txId) {
        String urlQuery = WalletConfig.tronApiUrl + "/wallet/gettransactioninfobyid";
        JSONObject queryJson = new JSONObject();
        queryJson.append("value", txId);
        queryJson.append("visible", true);
        String jsonObjectResult = HttpUtil.post(urlQuery, queryJson.toString());
        if (jsonObjectResult == null) {
            //重试一次
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                log.warn("线程休眠异常: {}", e.getMessage());
            }
            jsonObjectResult = HttpUtil.post(urlQuery, queryJson.toString());
            if (jsonObjectResult == null) {
                log.error("获取交易详情失败");
                return BigDecimal.ZERO;
            }
        }
        JSONObject jsonObject = JSONUtil.parseObj(jsonObjectResult);
        if (jsonObject.containsKey("fee")) {
            BigInteger bigInteger = new BigInteger(jsonObject.get("fee").toString());
            return (new BigDecimal(bigInteger)).divide(BigDecimal.valueOf(1000000), 6, RoundingMode.DOWN);
        } else {
            return BigDecimal.ZERO;
        }

    }

}
