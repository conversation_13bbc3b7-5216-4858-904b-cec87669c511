package com.meta.config;

import com.meta.util.SimplePrivateKeyEncryptionUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 加密配置初始化器
 * 在应用启动时配置加密参数
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Component
public class EncryptionInitializer {

    @Value("${encryption.enabled:true}")
    private boolean encryptionEnabled;

    @PostConstruct
    public void initializeEncryption() {
        // 设置加密启用状态
        SimplePrivateKeyEncryptionUtil.setEncryptionEnabled(encryptionEnabled);
        
        System.out.println("=== 私钥加密配置初始化 ===");
        System.out.println("加密功能启用: " + encryptionEnabled);
        
        if (encryptionEnabled) {
            System.out.println("加密算法: AES-128");
            System.out.println("编码方式: Base64");
            System.out.println("⚠️  生产环境请确保使用强密钥");
        } else {
            System.out.println("⚠️  加密功能已禁用，仅适用于开发环境");
        }
        
        System.out.println("========================");
    }

    /**
     * 获取当前加密状态
     */
    public boolean isEncryptionEnabled() {
        return encryptionEnabled;
    }

    /**
     * 动态设置加密状态
     */
    public void setEncryptionEnabled(boolean enabled) {
        this.encryptionEnabled = enabled;
        SimplePrivateKeyEncryptionUtil.setEncryptionEnabled(enabled);
        System.out.println("加密状态已更新: " + enabled);
    }
}
