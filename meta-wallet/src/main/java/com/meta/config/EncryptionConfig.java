package com.meta.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 私钥加密配置类
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Component
@ConfigurationProperties(prefix = "encryption")
public class EncryptionConfig {

    /**
     * 加密算法类型
     */
    public static String algorithm;

    /**
     * AES/SM4 密钥
     */
    public static String secretKey;

    /**
     * RSA/SM2 公钥
     */
    public static String publicKey;

    /**
     * RSA/SM2 私钥
     */
    public static String privateKey;

    /**
     * 是否启用加密
     */
    public static Boolean enabled;

    /**
     * 编码方式
     */
    public static String encoding;

    @Value("${encryption.algorithm:AES}")
    public void setAlgorithm(String algorithm) {
        EncryptionConfig.algorithm = algorithm;
    }

    @Value("${encryption.secret-key:MetaWallet123456}")
    public void setSecretKey(String secretKey) {
        EncryptionConfig.secretKey = secretKey;
    }

    @Value("${encryption.public-key:}")
    public void setPublicKey(String publicKey) {
        EncryptionConfig.publicKey = publicKey;
    }

    @Value("${encryption.private-key:}")
    public void setPrivateKey(String privateKey) {
        EncryptionConfig.privateKey = privateKey;
    }

    @Value("${encryption.enabled:true}")
    public void setEnabled(Boolean enabled) {
        EncryptionConfig.enabled = enabled;
    }

    @Value("${encryption.encoding:BASE64}")
    public void setEncoding(String encoding) {
        EncryptionConfig.encoding = encoding;
    }
}
