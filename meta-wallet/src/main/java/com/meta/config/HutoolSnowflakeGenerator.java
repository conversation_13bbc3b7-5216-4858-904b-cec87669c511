package com.meta.config;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.IdentifierGenerator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * 基于Hutool的雪花算法ID生成器
 */
@Component
public class HutoolSnowflakeGenerator implements IdentifierGenerator {

    private static Snowflake snowflake;
    
    // 添加无参构造函数，Hibernate需要它来实例化生成器
    public HutoolSnowflakeGenerator() {
        // 如果snowflake为空，使用默认配置初始化
        if (snowflake == null) {
            synchronized (HutoolSnowflakeGenerator.class) {
                if (snowflake == null) {
                    // 默认workerId=1, datacenterId=1
                    snowflake = IdUtil.getSnowflake(1, 1);
                }
            }
        }
    }
    
    // 保留带参数的构造函数，用于Spring注入配置值
    public HutoolSnowflakeGenerator(
            @Value("${snowflake.worker-id:1}") long workerId,
            @Value("${snowflake.datacenter-id:1}") long datacenterId) {
        // 静态初始化雪花算法，确保全局唯一实例
        if (snowflake == null) {
            synchronized (HutoolSnowflakeGenerator.class) {
                if (snowflake == null) {
                    snowflake = IdUtil.getSnowflake(workerId, datacenterId);
                }
            }
        }
    }
    
    @Override
    public Serializable generate(SharedSessionContractImplementor session, Object object) throws HibernateException {
        return snowflake.nextId();
    }
} 