package com.meta.util;

import com.meta.config.EncryptionConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 私钥加密工具类测试
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public class PrivateKeyEncryptionUtilTest {

    private static final String TEST_PRIVATE_KEY = "f90bd80f933cfef74730d69c133b85288c920ffc253300fe8cb326f668eba517";

    @BeforeEach
    public void setUp() {
        // 初始化配置
        EncryptionConfig.algorithm = "AES";
        EncryptionConfig.secretKey = "MetaWallet123456";
        EncryptionConfig.publicKey = "";
        EncryptionConfig.privateKey = "";
        EncryptionConfig.enabled = true;
        EncryptionConfig.encoding = "BASE64";
    }

    @Test
    public void testAesEncryptionAndDecryption() {
        // 测试AES加密解密
        EncryptionConfig.algorithm = "AES";
        EncryptionConfig.enabled = true;

        String encrypted = PrivateKeyEncryptionUtil.encryptPrivateKey(TEST_PRIVATE_KEY);
        assertNotNull(encrypted);
        assertNotEquals(TEST_PRIVATE_KEY, encrypted);

        String decrypted = PrivateKeyEncryptionUtil.decryptPrivateKey(encrypted);
        assertEquals(TEST_PRIVATE_KEY, decrypted);

        // 测试加密状态判断
        assertTrue(PrivateKeyEncryptionUtil.isEncrypted(encrypted));
        assertFalse(PrivateKeyEncryptionUtil.isEncrypted(TEST_PRIVATE_KEY));
    }

    @Test
    public void testSm4EncryptionAndDecryption() {
        // 测试SM4加密解密
        EncryptionConfig.algorithm = "SM4";
        EncryptionConfig.secretKey = "MetaWallet123456"; // SM4需要16位密钥
        EncryptionConfig.enabled = true;

        String encrypted = PrivateKeyEncryptionUtil.encryptPrivateKey(TEST_PRIVATE_KEY);
        assertNotNull(encrypted);
        assertNotEquals(TEST_PRIVATE_KEY, encrypted);

        String decrypted = PrivateKeyEncryptionUtil.decryptPrivateKey(encrypted);
        assertEquals(TEST_PRIVATE_KEY, decrypted);
    }

    @Test
    public void testEncryptionDisabled() {
        // 测试禁用加密
        EncryptionConfig.enabled = false;

        String result = PrivateKeyEncryptionUtil.encryptPrivateKey(TEST_PRIVATE_KEY);
        assertEquals(TEST_PRIVATE_KEY, result);

        String decrypted = PrivateKeyEncryptionUtil.decryptPrivateKey(TEST_PRIVATE_KEY);
        assertEquals(TEST_PRIVATE_KEY, decrypted);

        assertFalse(PrivateKeyEncryptionUtil.isEncrypted(TEST_PRIVATE_KEY));
    }

    @Test
    public void testHexEncoding() {
        // 测试HEX编码
        EncryptionConfig.algorithm = "AES";
        EncryptionConfig.encoding = "HEX";
        EncryptionConfig.enabled = true;

        String encrypted = PrivateKeyEncryptionUtil.encryptPrivateKey(TEST_PRIVATE_KEY);
        assertNotNull(encrypted);
        assertNotEquals(TEST_PRIVATE_KEY, encrypted);
        // HEX编码应该只包含0-9和A-F
        assertTrue(encrypted.matches("^[0-9A-Fa-f]+$"));

        String decrypted = PrivateKeyEncryptionUtil.decryptPrivateKey(encrypted);
        assertEquals(TEST_PRIVATE_KEY, decrypted);
    }

    @Test
    public void testInvalidInput() {
        EncryptionConfig.enabled = true;

        // 测试空字符串
        assertThrows(IllegalArgumentException.class, () -> {
            PrivateKeyEncryptionUtil.encryptPrivateKey("");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            PrivateKeyEncryptionUtil.encryptPrivateKey(null);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            PrivateKeyEncryptionUtil.decryptPrivateKey("");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            PrivateKeyEncryptionUtil.decryptPrivateKey(null);
        });
    }

    @Test
    public void testUnsupportedAlgorithm() {
        EncryptionConfig.algorithm = "UNSUPPORTED";
        EncryptionConfig.enabled = true;

        assertThrows(IllegalArgumentException.class, () -> {
            PrivateKeyEncryptionUtil.encryptPrivateKey(TEST_PRIVATE_KEY);
        });
    }

    @Test
    public void testSm4InvalidKeyLength() {
        EncryptionConfig.algorithm = "SM4";
        EncryptionConfig.secretKey = "short"; // 无效的密钥长度
        EncryptionConfig.enabled = true;

        assertThrows(IllegalArgumentException.class, () -> {
            PrivateKeyEncryptionUtil.encryptPrivateKey(TEST_PRIVATE_KEY);
        });
    }

    @Test
    public void testIsEncryptedMethod() {
        EncryptionConfig.algorithm = "AES";
        EncryptionConfig.encoding = "BASE64";
        EncryptionConfig.enabled = true;

        // 测试明文私钥
        assertFalse(PrivateKeyEncryptionUtil.isEncrypted(TEST_PRIVATE_KEY));

        // 测试加密后的私钥
        String encrypted = PrivateKeyEncryptionUtil.encryptPrivateKey(TEST_PRIVATE_KEY);
        assertTrue(PrivateKeyEncryptionUtil.isEncrypted(encrypted));

        // 测试无效输入
        assertFalse(PrivateKeyEncryptionUtil.isEncrypted(""));
        assertFalse(PrivateKeyEncryptionUtil.isEncrypted(null));

        // 测试HEX编码
        EncryptionConfig.encoding = "HEX";
        String hexEncrypted = PrivateKeyEncryptionUtil.encryptPrivateKey(TEST_PRIVATE_KEY);
        assertTrue(PrivateKeyEncryptionUtil.isEncrypted(hexEncrypted));
    }
}
