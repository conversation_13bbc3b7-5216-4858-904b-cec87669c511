# 项目相关配置
meta:
  # 名称
  name: metabank-es
  # 版本
  version: 3.2.1
  # 版权年份
  copyrightYear: 2022
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/meta/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/home/<USER>/file
  #  profile: /home/<USER>/file
  # 获取ip地址开关
  addressEnabled: false
  # 登录风控规则
  loginRuleEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
  tradeRuleEnabled: false

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8890
  servlet:
    # 应用的访问路径
    context-path: /meta
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

# 日志配置
logging:
  level:
    com.meta: debug
    org.springframework: warn
  file:
    path: /Users/<USER>/meta/logs
#    path: G:/home/<USER>/logs

# Spring配置
spring:
  config:
    additional-location: classpath:/config/
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  jpa:
    database: mysql
    generate-ddl: false
    hibernate:
      ddlAuto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
        jdbc:
          #为spring data jpa saveAll方法提供批量插入操作 此处可以随时更改大小 建议500
          batch_size: 500
          batch_versioned_data: true
        order_inserts: true
        order_updates: true
    show-sql: true
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: *********************************************************************************************************************************************
        username: root
        password: root
      #        url: ***************************************************************************************************************************
      #        username: metabank
      #        password: 555666
      #        url: ************************************************************************************************************************************************
      #        username: metabank
      #        password: Z;J1!q2}AGU:5
      #        url: **************************************************************************************************************************************************
      #        username: metabank
      #        password: 555666
      #        url: *****************************************************************************************************************************************************************************************
      #        username: metabank
      #        password: TC]n}(E$43Tr2
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size:  10MB
      # 设置总上传的文件大小
      max-request-size:  20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址 **************
    host: localhost
    # 端口
    port: 6379
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
    database: 10
  boot:
    filters:
      # 这里假设你的Filter类的包名为com.meta.system.filter
      # Filter的名称可以根据你的需要进行修改
      filter-name:
        order: -1
        class: com.meta.system.filter.IPBlacklistFilter
  rabbitmq:
    host: 127.0.0.1 # rabbitMQ的ip地址
    port: 5672 # 端口
    username: guest
    password: guest
    virtual-host: /
#    host: b-3a6e11d3-116c-43cf-b645-37b01f200194.mq.ap-southeast-1.amazonaws.com # rabbitMQ的ip地址
#    port: 5671 # 端口
  #    username: mqpaytest
#    password: 3nmK7gbiKMqr
#    virtual-host: /
    publisher-confirm-type: correlated
    publisher-returns: true
    template:
      mandatory: true
    listener:
      simple:
        prefetch: 1
        acknowledge-mode: auto
        retry:
          enabled: true
          initial-interval: 10000
          multiplier: 1
          max-attempts: 5
          max-interval: 1200000ms
#/
wallet:
  userAddress:
    key: ja88@uP660088#Meta
#vcc 配置
vcc:
  url: https://test-open.whalet.com/api
  version: 1.0
  partnerId: 123456
  #本地的私钥
  privateStr: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC3sFy3kifJJgHSXJh4yTAm0KcX+rR4c1DZLtSKq49vhUDxIe+7nZFA7ftEVWI+mqZanuq2R7dBI3dWLNmVP+gEqJ9ZbinWYNMru5FVI1Y6cGQR1aKOkZdwPWbp28IAqiLI9Iz4Lr3q+IRN1Ot/HtFqctx9agpt79NCWIgzNSR1Ubaj+Bdugj9/Dzdwb28CGHtuMHc424B38OvUtigk2SGugFaPjTIMLj1LDzFWVkYE7x20Oueb7UQTK+O7WIghCvWZ7y5xmzm48VCF54aGWCN5XXazV4MiCneR0BA3+a72wYAN5iSIOBNEyoKcCXYYVQ51omxpyNbB+XEJLM6/A95JAgMBAAECggEAB9ehvbyhsjbLMR3+7Hk6cw3JdH6hodragss7C8iTUSGRSjo2r/3kK699YMd8cEvN+mR6hNm++yr2d1lm2LZnw3sngnvvkVWE7oMNi2L81XXi79f9HrUckBAvCqD1QW4CGi5GrnngZd3CWEhU3ZKFQlQxEbGrTFJG5Lg+6GDGc7O83Ybt3DpRdBu3xyKqstio3Bnyi726xgoFvyapVxMA80NqHygp3I0xQM7+xcqcpToA76BnzEwxrcJA46fVz6ARpeoZJZm2LEpLjwtUUPP4nPGWaFbpvJWBVrYtrIUsbdvU/k872MJrGU2HKM2Y3pKE9ftf3r9BsUmjhg4Qqz3EAQKBgQDivK++jRWQg76C5tjomhTtdHP3UyY5NNepfxWU/fXGI2pXGxI8JFvMXTmA2mcOw3smPsXJTzRVh5rsLFZcAI1cNkgr7St0Cq83sbes2qgWLUU/9hwD/44zcK2AbGCUA/l1XjshHdzBUjYN/Te90HSE6POhdrRus9Mk7H6L2wyWiQKBgQDPZWFKIrwhIImf9vlaiKT6DT3+61sErPnOB6MXee7iMqtfSdFrry6vU+tR/YvQP5BQm5RHaFqx+wMXlv5g4HSzAlN4Y5pt2KslVRyx77uesFqDyKPK8AzVZuXGQwL35zqyeR3qWMQAiyJpbYa5i9JOlOL1NWa19VMOG2/6rHsZwQKBgAUSZ3QBv+u9prNS6qM5XyJ4qmdr6rz426Ik/5yXmbnW7PgJ2PyTa6JEq5agTBHeeZC/crkwFmGbaPHDhCMGuCLJ7A9ffMtZudWrGgq50Wy9koD9xl6ohsvLx3XJ9tcYx6nCc9wnyNpiNmdVtLuAQDsA4wJHn32idCStTZ9fRQbRAoGBAImA4scfTnH8O6LmQR6oOBFdVDw+WMM52AmkgJohCqPICl0L+SAXLrGpxvw+SUNR0WHQNLg/VNpp3Pv37UHXrye8JBFOoGWx4I7I5lSG6HLm46w6C3aSP0ABj9gqN76a/Qy1RoNIRPNDTBZwOoGsVUwqZGtE84syUtWolU6yqo9BAoGAAedNwM57PufFJHSA11rFM1nIOyTeG83bQInAjacLsdQDe12kFZeC0jTZKC/8R6S+BpsDGO2NLUhOJ5QstID7qX+ba+bqkcUzVTzrJJM2/U/wmeSIRKBvb//XGcMKy+A+pnlwV6ODQHzEE5h0BSRW1hgwWICYPGBBGIKGEsScz7c=
  # vcc的公钥
  #publicStr: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvw2IzvWHwCAWbVYbh/lOv1fUGR5AtHvwLQLZuyrY0GiNSovSuGbhsWQ/ImTqqn5UtSPHt+2ANGg6wibHOIYG87uqp2q/HLLgs5IYh39tHcyX+saRBAADpFShgeM+VZEfOKa97HvNfJskqIj0JsCjZsNRardxbvJtQpn6hMuo0l7w0cY9qvzjKn0kcslKFYafz9h24wQTyjJrd+G/VjPt/EDn3zbY8OzGv6nI8j0uQSRN5SkIzV09Yk2Ymzivb/gHNOr6qU9r2pGsjn+ObmL1avVXA4niL6yoqe5YQ0QOMJ5hIey3WBF9PRgBljz64hoyPB8r1mvz6uTmnCQlcOntOwIDAQAB
  #测试服务器rsa
  #  publicStr: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvRO37qV3ZsGceXxkGYI/hXM4AhCPbui8hcWP0Ra5GFx6I2O6j4Mo2s56gqzd8XxqJVju4qvRG6aEXrUef6LyxvHlpTH1lZGHymXkOqX79XlZfvaeDtK5QaVRlTc2w7S07sF+18NEcRRScmVUdhEM6NHG/F8mRt2e18QzzJzs6hiD1XB6dJy/LxPCDxcq4Nc9QGKFlMvdAeysdOYf5zV+47Yza1Z6cRzaZivJMeQ0F6xI2WT1a7p18x0V4NrxpKW+g5ETx1An7jKRU0y+ToHvDvbIzO9bSXhj0pm/vpPCi/+o/e0uP2uvT1B92ZXSDL4LCrszGAmNDuz8MK3+Qeok6wIDAQAB
  #我的公钥
  #publicStr: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt7Bct5InySYB0lyYeMkwJtCnF/q0eHNQ2S7UiquPb4VA8SHvu52RQO37RFViPpqmWp7qtke3QSN3VizZlT/oBKifWW4p1mDTK7uRVSNWOnBkEdWijpGXcD1m6dvCAKoiyPSM+C696viETdTrfx7RanLcfWoKbe/TQliIMzUkdVG2o/gXboI/fw83cG9vAhh7bjB3ONuAd/Dr1LYoJNkhroBWj40yDC49Sw8xVlZGBO8dtDrnm+1EEyvju1iIIQr1me8ucZs5uPFQheeGhlgjeV12s1eDIgp3kdAQN/mu9sGADeYkiDgTRMqCnAl2GFUOdaJsacjWwflxCSzOvwPeSQIDAQAB
  publicStr: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuzMcz4HMfo1l9/4SbUnCbUxcXbBnhJq6XGSph8kBSdW7KBouoYSL+0ysQQ7kntjV0QCSjCJuwUqnssDB7lVoVmo24uZBSirYDOBAHO/BZcHo94USCI+V2A9ONLu9Q0Qgm/ywxPaNfDCWEzvDyzkiVVBnSsWVPOrB2Z25zck52qcbAC71WVtq05+tQrLGUTPtw2ccpLyXBx4LIskNna7aU7xQtLHlsORUAPk4/JQESoky4xtiOy//y8b2LUJbRreLF4Yx+lc3EkYTlmKM9I7PDOwEmuqsJOHqOCnPwwuQZczx3lEDZiv6XTId46Td8+byM9oV+fudgoiZlRHHS6pXgwIDAQAB

#meta 配置
es:

  version: 1.0
  #我的私钥
  privateStr: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDnFCS4VtrKk9fKMhivx044jBm/F9YCzcj0RTxJ0SN+RfxQhtfQVO0+Tg/9ZmuZZBpRCN827YFeSj355/AtgFdmeX/nX4+xjplK6e6/678n1wmlQGIKFRSgs+qDA++Atqtv7FEjpCphpj3+tWt3FsLcBhIrTL7L4pTu8OQyS+InqFj6burPzBdivRQ1DbmCXwGK9WmfiHr403Rd8bfOxVs/IHmLG20QjlP3xfE+IDWp7Wx90TqNBoTMf93wos+SmNRL+HUK+6QaMQuuOuDFfgePsiURT+FEyQE+AnMrB3Npv2UY9UysYeEgS7z1gI8ddxx9orXU3D8hsS/l6vpo8kgTAgMBAAECggEBAKhcB8j7FxvDjdR2nX/soXQXt2aAMmAGmORIUX2iz85fpU7yf+j9B4F+lK3WYoz23ymtYhx9/OS7CN68e0RbntI9sc/c0y/VqqBnQpk4ZSTcyt1IxGV+KHJRw4pDYsuPy+aW8iMap/pPihm7CppQzhZKohBEMTsRUdupTUnL+fFTl1mgu4+b+OxnAiVEJB9/yZTp2NMrJEYYHcnHWr48boemmoUOVvzZ998GxdwFLObO3U+lU9IWdHXwXY/+kCPsMA+l2w2u5Skxl77zz0VpvDGJTPV9Y/HERikGxCDPztF50nFY8VVB9bxFJhyIZPBZ0/Pd+0Dk1PxsSFrjN+M8hOkCgYEA9ZdORJEvG4I0C3N2AKZYUA7LYrquSumAoLvGBVzVbyHIfBkYq/6kiYIUBn7i+cUdWVP8tMlSZXADmcpSuLcfQbbwJTCLCQX+Le8wz0XpOiBAXyGz5gJlrdDyBE0u9GGWKD363DWaRizFHzZMHXeL4/RPzRcMLVfsYx7ZsonHyM8CgYEA8N9gdvoXkZi/NpguDuPSckKPL8eYcbrhQIi/DozbDqAUeTAEt1lKFSCnpSx0mIswI6Q6DxlcV940ZPWVK3gcYwA6ZkFr2z+nZrcv81mmSFv9oJAgdsSx5EtBe8CRTflNIeygNuy5D1cJ8N2rufwt3GykEaN+TGyQtcVkRUTf1X0CgYApe7tOva9VWDLuVDx0c6SrMTSNorbaFIMTo2QZd8rdkLlKrPqL4uFsELrNNhWk1vTUJ+mhp/fxBnC1Q4Whid0PvpTl00NI7Og4XhTCji6NEPGOoCTBD1qZd5fJvjfWM72nYoDDPZKnk9xgH03QFGdew9/O5ru51QYq4AUpsTg75QKBgD5eCDMmND4Np8zyAFL4qD/PfhWn/4/LDJHSFm4lCH8z8bkjNsVmiCkKSH4bEaGBwJgp6KKKNX/G2BggAly7/9WedICPhebCB40v92lyF/z6XKRbTRyTd2AeIDlztevTliYnh6BREOWo6rkEHHfOlO1S7/RlOcl9/sYh8wGAgNhdAoGACiWWO/NWwyKB6JgXq1E4TRwMbIzeTmzvfLVYikjHYyw/8gtFSiJRYdpeJ1cEwKvlMgAK6CpWGdnuVq6GuaDv0udESTLP5xycJJU7882seD2x7BebyttMsxzBtpEhicOWjEW8NTqGrlFS1xIBBLTMY2R4BMsCnMJMym6wMMpfMHo=
  #我的公钥
  publicStr: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5xQkuFbaypPXyjIYr8dOOIwZvxfWAs3I9EU8SdEjfkX8UIbX0FTtPk4P/WZrmWQaUQjfNu2BXko9+efwLYBXZnl/51+PsY6ZSunuv+u/J9cJpUBiChUUoLPqgwPvgLarb+xRI6QqYaY9/rVrdxbC3AYSK0y+y+KU7vDkMkviJ6hY+m7qz8wXYr0UNQ25gl8BivVpn4h6+NN0XfG3zsVbPyB5ixttEI5T98XxPiA1qe1sfdE6jQaEzH/d8KLPkpjUS/h1CvukGjELrjrgxX4Hj7IlEU/hRMkBPgJzKwdzab9lGPVMrGHhIEu89YCPHXccfaK11Nw/IbEv5er6aPJIEwIDAQAB
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 300000
  # 是否允许账户多终端登录
  soloLogin: false


# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*


mail:
  username: <EMAIL>
  password: TGGLTiJnN2KrrcZ
  authorizationCode: FFTPBUWAVPNIMZDC
  serverHost: smtp.163.com
  serverPort: 465
  reconnectSecond: 600




#moonbank 配置
moonbank:
  gateway: https://test.asinx.io/api-web
  appId: app_35940
  appSecret: b635dd5c87f7bf73387929203321b1e1
  notifyTimeout: 60000
  notifyConnectTimeout: 60000
  useProxy: false
  proxyAddress:
  proxyPort:

#kun 配置 + 配置文件
kun:
  environment: test
  ip: *************

wello:
  clientId: Kaze-Pay
  merchantCode: Kaze-Pay
  apiPrivateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDP79RdR+eUCgdLNPhpn9MHdgamcgL90S4hm+DrlyCIAX9tM86B2INYyNjl7dwVQZFJZMM0vAc5AhoSuJrywsX8llos5FVgBSx4oCLjNpLwPa7V/0o8PrxLCuHVvMZz62R3pj7mo8yzSeoFbg2xsGFeOn85GpsoUIXpBBsrRhYk9Q2C0N/uudbsHq5ok7qox8T9KexHX1hEMHxbZMR9n87iL3fkwbdx9L0n187AqqrdtH+6+NzmFNM4vsH7SRSqvJQhfYeCEll/d4WIaMhrrVFM9udeBgA6eulwQDkWPfqhLAGfUY2N4LYW9LfeYQzuJ9XLB7bt3WM6IQCH32T4JHolAgMBAAECggEAELaui2vo64+NzAf/MqA/2mDsgmr2Hhg2/MkWgGFGol/6cdjg77NrRVLpiju+/a9mQe+5OA8oBndKiLgxbjNlLhsYtvSIfz4TN8PpYfmEQI64s6dAPIzvXhnLEr7Idc9VJT/l3OyMGtolVxfkPmTWwrfxe3HNyGUGincDxwvsYj5yHO7Pw5FIIveSCw0aC8yomvKaUFAt26nPsQntWQVEH84kp7ef+kdgCiZLtlyMEdy+6WTwhXvT4E+m4sp1ePhwxzUdU/AjtCCJYLimHtfaudDLw29S+M+IQzAEOg4iVegN1nHGNVjQa+mfPjEs+6C9JPp2tzlWT60pf0/B5TXiuQKBgQDcPbNl5bIY2FFQoT9IjcaC19P6K53zLAhaAiiMIazY9YTSR3Hyv4fQxbZYstJ8+5fDfAUpM5VWkbe2KUEt05EsaJdFyhehO99B8qrIje+Rkq9FmPIMcOTuXg0IsN9xoe9xW6QTBn1oJnnbcS7lmNl2SkmgZ1+DAkwoTznYD5ctWQKBgQDxsrTWJU/TvWK36LvR+zAWOHkbBP/+EB4niBC+MxKs+dwoXGh1ZwutHsUxc2/w0yQYwbw+eK+z2kKa6B0mfqEC7HRd1G9SpUngNvBqPygEMYNRbUIAx99iRQHg+Mv+kJXQNOw/RTWDMPqd14TuX836JF2KF+36BdNnDQe1H93drQKBgC+do2CfZX7bndD52627ATUepnTK405EsdWL6+pICY+qnTm9OpSJ9job1M2ec8WTu38HHDKJNXtO69IVX0MiH5vjTPlItQ0l3aHrbiMIFMh7VyuL4qDv837ZlKXRX624ngT7FCEM4SYYU3YvJT18a1+YtgxY7Rxs7bw1TMM6s6uZAoGBAK+BqgjafTXvDtEm4cPfl5J7HY2+OrldMNhZ2HaUlZofsXtpZrzhv7vNXTnEbmO8njiP7cabUlLSSY08wXYje32o//LmgJPNzHBQ1JECHWe2IHovUT9J4+7y1yNq5o6KpQ7UbzyeX/ZMyZ92UWPbE7HxWC8fo0Hrmw/4iUE/y5bdAoGBANb8sPOzz2ZsooVyMCWS7i3ps3ouGGgWqt3BGMdPpXRo7qtFh4ocnTdv2O3r58mHv4zRHCXVDxMv1vvylIuucvtaVgxoeilnAEf8jtz9HHgvLj9DsRPysiw73ZIVt5ITLXYHzxRS7lKNGs2/F8/jLJGSBZGN5rrmg0I5lww56ny0
  webhookPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzqc85e3543ZFMGGE/zFzCYTaZDMSrFeOVnlly2iFNeF3fjTvKRYpKpOX2XQi/dfna0ru5OKe9AYfC+dfY5fGHxrVleerZTNwS1AX1HjaDV1bJ20ipS7KBzXGdHIPw0GSnvF8QXEKCysrtZnpXZLx5ndjt0X1sA4Fo1D0g/FeRbgRGkn1fR6IDc5REgnsasVV0tNbqxTNykmOaASvgA5n+F9AhWpHjF3UPV7g7mxfi2Je/QeLsIym6yq+jspcHgfEBpGUdSfy8EWqw72qobA+4CoRlfmBXirwm1VFPjYlViuD0F7nu+qhjjJzlqKpcoUTPan4N+Cid9x0w/7Y/RLeywIDAQAB
  walletAddress: "******************************************"
fornax:
  userNo: 2025534874059126850
  version: 1.0.0
  dataType: json
  # fornax的公钥
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQD6WIZEnVtdRxhxnjZ9m1kD3k+21fCc2UBxCy80lIY0tgTmZ5pSCq18cYkE2CBlb0QUXwdd8uBycMVW2JTStVpBp70UAlvvK2C5VVajzoz6hKbHgDH1GYGSbbevuPnMpP1qNhlC0mItMt1n3dz6NsoSt5t2RO0RPOZ6eN7hcna8zwIDAQAB
  # kazepay的私钥
  privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAN08oQ3nVFUsn3A83rsefGVVRbPoBOwaS02gawc5b1wuzopbK0V9BrLN8Zplye0QCFe7Fw062PMYyrkafIwqBwa+si2P4MolH26D6dlcqNUUoZSl0hdd42X1eU/xX4cICqkXcjZdG3xp7VCSxbwyZp4mDpOLPKlWAm0nmuS3lZpJAgMBAAECgYEAnS2y+q7MQosmZ1ZHo8df95pioBOKU7fGoksDXymWDXcPtEpbbs4J0UTxjpEcqfkHBFVeZj7V3bhSZroDnI1LWVPalkyQ6QXAu9tON2mg5fGqmuUJpe6iR8wMkuV6nMBGWnhV7Pr/XniEYr4Hl44HcpNwMyW2g/HuLOZZ6+/JowECQQD5wgR4Aeqxxf/BeWKXGlw69vKJIhcDmuiNUz+oVmyPh5Tqz9b5i3lXkfUh91MavfFKp8I/UG8ris11i+bVv93hAkEA4sQhYOP7oAG7zLFngRBvPIJu3wL5bc5UTP3AWkqfrk40UX3lY/1OsxdJ2vDltHMzxvBOkp1QXtpmNZeUvgy5aQJAJ4fq9yeugwbIzVGuJiZ49KhDiSsJI3vwVPYZPEa5lhoqmuPcrdDCmvayKvcfgzsPJvbYivbrMaQdkv5awfhFYQJAYeaXGoOS8OOOtrKoovn7bD5Lq5BXK2b20j6DOvgYtGIjCEO4M1D7FyqowhNauGs8IVlCMt4kAq97jQuiNeK8cQJAZzxL/GGpyfoDzCvqbDzfIVskxAOKCOLdGh2/ty8f2w3hDbjvPq5JKNz8nTegJa/SrhdzKhEBLdKwD1vWFNLGew==
  # kazepay的公钥: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDdPKEN51RVLJ9wPN67HnxlVUWz6ATsGktNoGsHOW9cLs6KWytFfQayzfGaZcntEAhXuxcNOtjzGMq5GnyMKgcGvrItj+DKJR9ug+nZXKjVFKGUpdIXXeNl9XlP8V+HCAqpF3I2XRt8ae1QksW8MmaeJg6TizypVgJtJ5rkt5WaSQIDAQAB
  url: https://api.wewallex.com
