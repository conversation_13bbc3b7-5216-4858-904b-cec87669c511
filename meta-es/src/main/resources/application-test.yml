# 项目相关配置
meta:
  # 名称
  name: metabank
  # 版本
  version: 3.2.1
  # 版权年份
  copyrightYear: 2023
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/meta/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /data/file/upload
  # 获取ip地址开关
  addressEnabled: true
  # 登录风控规则
  loginRuleEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8890
  servlet:
    # 应用的访问路径
    context-path: /meta
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

# 日志配置
logging:
  level:
    com.meta: debug
    org.springframework: warn
  file:
    path: /home/<USER>/logs

# Spring配置
spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  jpa:
    database: mysql
    generate-ddl: false
    hibernate:
      ddlAuto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
        jdbc:
          #为spring data jpa saveAll方法提供批量插入操作 此处可以随时更改大小 建议500
          batch_size: 500
          batch_versioned_data: true
        order_inserts: true
        order_updates: true
    show-sql: true
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: *****************************************************************************************************************************************************************************************
        username: metabank
        password: TC]n}(E$43Tr2
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size:  10MB
      # 设置总上传的文件大小
      max-request-size:  20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为16379
    port: 6379
    # 密码
    password: Meta@Bank
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
    database: 10
  boot:
    filters:
      # 这里假设你的Filter类的包名为com.meta.system.filter
      # Filter的名称可以根据你的需要进行修改
      filter-name:
        order: -1
        class: com.meta.system.filter.IPBlacklistFilter

#vcc 配置
vcc:
  url: https://test-open.whalet.com/api
  version: 1.0
  partnerId: 202305061716298892600001
  privateStr: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC3sFy3kifJJgHSXJh4yTAm0KcX+rR4c1DZLtSKq49vhUDxIe+7nZFA7ftEVWI+mqZanuq2R7dBI3dWLNmVP+gEqJ9ZbinWYNMru5FVI1Y6cGQR1aKOkZdwPWbp28IAqiLI9Iz4Lr3q+IRN1Ot/HtFqctx9agpt79NCWIgzNSR1Ubaj+Bdugj9/Dzdwb28CGHtuMHc424B38OvUtigk2SGugFaPjTIMLj1LDzFWVkYE7x20Oueb7UQTK+O7WIghCvWZ7y5xmzm48VCF54aGWCN5XXazV4MiCneR0BA3+a72wYAN5iSIOBNEyoKcCXYYVQ51omxpyNbB+XEJLM6/A95JAgMBAAECggEAB9ehvbyhsjbLMR3+7Hk6cw3JdH6hodragss7C8iTUSGRSjo2r/3kK699YMd8cEvN+mR6hNm++yr2d1lm2LZnw3sngnvvkVWE7oMNi2L81XXi79f9HrUckBAvCqD1QW4CGi5GrnngZd3CWEhU3ZKFQlQxEbGrTFJG5Lg+6GDGc7O83Ybt3DpRdBu3xyKqstio3Bnyi726xgoFvyapVxMA80NqHygp3I0xQM7+xcqcpToA76BnzEwxrcJA46fVz6ARpeoZJZm2LEpLjwtUUPP4nPGWaFbpvJWBVrYtrIUsbdvU/k872MJrGU2HKM2Y3pKE9ftf3r9BsUmjhg4Qqz3EAQKBgQDivK++jRWQg76C5tjomhTtdHP3UyY5NNepfxWU/fXGI2pXGxI8JFvMXTmA2mcOw3smPsXJTzRVh5rsLFZcAI1cNkgr7St0Cq83sbes2qgWLUU/9hwD/44zcK2AbGCUA/l1XjshHdzBUjYN/Te90HSE6POhdrRus9Mk7H6L2wyWiQKBgQDPZWFKIrwhIImf9vlaiKT6DT3+61sErPnOB6MXee7iMqtfSdFrry6vU+tR/YvQP5BQm5RHaFqx+wMXlv5g4HSzAlN4Y5pt2KslVRyx77uesFqDyKPK8AzVZuXGQwL35zqyeR3qWMQAiyJpbYa5i9JOlOL1NWa19VMOG2/6rHsZwQKBgAUSZ3QBv+u9prNS6qM5XyJ4qmdr6rz426Ik/5yXmbnW7PgJ2PyTa6JEq5agTBHeeZC/crkwFmGbaPHDhCMGuCLJ7A9ffMtZudWrGgq50Wy9koD9xl6ohsvLx3XJ9tcYx6nCc9wnyNpiNmdVtLuAQDsA4wJHn32idCStTZ9fRQbRAoGBAImA4scfTnH8O6LmQR6oOBFdVDw+WMM52AmkgJohCqPICl0L+SAXLrGpxvw+SUNR0WHQNLg/VNpp3Pv37UHXrye8JBFOoGWx4I7I5lSG6HLm46w6C3aSP0ABj9gqN76a/Qy1RoNIRPNDTBZwOoGsVUwqZGtE84syUtWolU6yqo9BAoGAAedNwM57PufFJHSA11rFM1nIOyTeG83bQInAjacLsdQDe12kFZeC0jTZKC/8R6S+BpsDGO2NLUhOJ5QstID7qX+ba+bqkcUzVTzrJJM2/U/wmeSIRKBvb//XGcMKy+A+pnlwV6ODQHzEE5h0BSRW1hgwWICYPGBBGIKGEsScz7c=
  publicStr: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvw2IzvWHwCAWbVYbh/lOv1fUGR5AtHvwLQLZuyrY0GiNSovSuGbhsWQ/ImTqqn5UtSPHt+2ANGg6wibHOIYG87uqp2q/HLLgs5IYh39tHcyX+saRBAADpFShgeM+VZEfOKa97HvNfJskqIj0JsCjZsNRardxbvJtQpn6hMuo0l7w0cY9qvzjKn0kcslKFYafz9h24wQTyjJrd+G/VjPt/EDn3zbY8OzGv6nI8j0uQSRN5SkIzV09Yk2Ymzivb/gHNOr6qU9r2pGsjn+ObmL1avVXA4niL6yoqe5YQ0QOMJ5hIey3WBF9PRgBljz64hoyPB8r1mvz6uTmnCQlcOntOwIDAQAB
wallet:
  userAddress:
    url: http://127.0.0.1:8290/user/address
    key: ja88@uP660088#Meta
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30000
  # 是否允许账户多终端登录
  soloLogin: false


# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

arcFace:
  # 应用ID
  appId: BuQo8C4eBakpMMhTEtDJp1xEKcDBrDGca2PtDW1oK9Tw
  # 应用密钥
  sdkKeyForWindows: A8CdeNmhc1LjfYm6T1X8aJTMA5N4i9T2rBNzGq5xpLoj
  sdkKeyForLinux: A8CdeNmhc1LjfYm6T1X8aJTM1xnwytPEH8A6fLhXP2J5
  isAbsolutePath: true
  ## SDK路径
  sdkPath: /data/sdk/libs/LINUX64

mail:
  username: <EMAIL>
  password: lwtqdzhegfmgjfwb
  authorizationCode:
  serverHost: smtp.gmail.com
  serverPort: 587
  reconnectSecond: 600