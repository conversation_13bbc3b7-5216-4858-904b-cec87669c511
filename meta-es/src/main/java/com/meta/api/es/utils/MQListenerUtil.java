package com.meta.api.es.utils;

import com.meta.common.constant.Constants;
import com.meta.system.service.MetaOrderService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/05/13/16:22
 */
@Slf4j
@Component
public class MQListenerUtil {

    @Autowired
    private MetaOrderService  metaOrderService;

    /**
     * B端的消息处理
     * @param id
     * @param channel
     * @param deliveryTag
     */
    @RabbitListener(queues = Constants.queue_kz_open_card_B)
    public void listenOpenCardQueue(Long id, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        log.info("消费者接收到" + Constants.queue_kz_open_card_B + "的消息：【" + id + "】，开卡操作");
        metaOrderService.deal(id);
    }

    /**
     * B端的消息处理
     * @param id
     * @param channel
     * @param deliveryTag
     */
    @RabbitListener(queues = Constants.queue_kz_recharge_card_B)
    public void listenRechargeCardQueue(Long id, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        log.info("消费者接收到" + Constants.queue_kz_recharge_card_B + "的消息：【" + id + "】,充值操作");
        metaOrderService.deal(id);
    }
}
