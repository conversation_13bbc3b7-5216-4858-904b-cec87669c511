package com.meta.api.es.service;

import com.alibaba.fastjson.JSONObject;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2024/05/29/17:46
 */
public interface CardService {
    JSONObject createCard(JSONObject jsonObject, String sysId, String requestNo);

    JSONObject queryCard(JSONObject jsonObject, String sysId);

    JSONObject cardBalance(JSONObject jsonObject, String sysId);

    JSONObject cardRecharge(JSONObject jsonObject, String sysId);

    JSONObject queryTransactions(JSONObject jsonObject, String sysId);

    JSONObject createKYC(JSONObject jsonObject, String sysId);

    JSONObject startKYC(JSONObject jsonObject, String sysId);

    JSONObject activateCard(JSONObject jsonObject, String sysId);

    JSONObject queryKYC(JSONObject jsonObject, String sysId);

    JSONObject closeCard(JSONObject jsonObject, String sysId);

    JSONObject updCardStatus(JSONObject jsonObject, String sysId);

    JSONObject getCardPin(JSONObject jsonObject, String sysId);

    JSONObject createCardQuery(JSONObject jsonObject, String sysId, String requestNo);

    JSONObject rechargeQuery(JSONObject jsonObject, String sysId, String requestNo);


    JSONObject approve3dsAuth(JSONObject jsonObject, String sysId);

    JSONObject reject3dsAuth(JSONObject jsonObject, String sysId);

    JSONObject query3dsAuth(JSONObject jsonObject, String sysId);

    JSONObject getEURAmount(JSONObject jsonObject, String sysId);

    JSONObject walletAddress(JSONObject jsonObject, String sysId);

    JSONObject setWebhookUrl(JSONObject jsonObject, String sysId);

    JSONObject deleteWebhookUrl(JSONObject jsonObject, String sysId);

    JSONObject getCoinExchangeRate(JSONObject jsonObject, String sysId);

    JSONObject getWebhookUrl(JSONObject jsonObject, String sysId);

    JSONObject setUcardKyc(JSONObject jsonObject, String sysId);

    JSONObject setUcardKycNew(JSONObject jsonObject, String sysId);

    JSONObject queryUcardKycStatus(JSONObject jsonObject, String sysId);

    JSONObject ucardAssign(JSONObject jsonObject, String sysId);

    JSONObject ucardActive(JSONObject jsonObject, String sysId);

    JSONObject cardSetPin(JSONObject jsonObject, String sysId);

    JSONObject cardUnlock(JSONObject jsonObject, String sysId);

    JSONObject createWalletAddress(JSONObject jsonObject, String sysId);

    JSONObject collectWalletAddress(JSONObject jsonObject, String sysId);

    JSONObject oldCardTransferNewCard(JSONObject jsonObject, String sysId);


    JSONObject uploadFile(String sysId, String email, String apicode, String mobilePrefix, String mobileNumber, MultipartFile file);

    JSONObject cardSetting(JSONObject jsonObject, String sysId);

    JSONObject setcardholder(JSONObject jsonObject, String sysId);

    JSONObject getCardholder(JSONObject jsonObject, String sysId);
}
