package com.meta.api.es.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 *  钱包 配置
 *
 */
@Component
@ConfigurationProperties(prefix = "wallet")
public class WalletConfig {

    /**
     * 协商key
     */
    public static String key;




    @Value("${wallet.userAddress.key}")
    public void setKey(String key) {
        WalletConfig.key = key;
    }


}
