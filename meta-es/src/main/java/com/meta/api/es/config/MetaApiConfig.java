package com.meta.api.es.config;

import com.meta.system.config.VccConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "es")
public class MetaApiConfig {

    /**
     * 公钥（易鲸）
     */
    public static String publicKey;
    /**
     * 版本
     */
    public static String version;
    /**
     * 私钥
     */
    public static String privateStr;


    @Value("${es.version}")
    public void setVersion(String version) {
        MetaApiConfig.version = version;
    }

    @Value("${es.privateStr}")
    public void setPrivateStr(String privateStr) {
        MetaApiConfig.privateStr = privateStr;
    }


    @Value("${es.publicStr}")
    public void setPublicKey(String publicKey) {
        MetaApiConfig.publicKey = publicKey;
    }
}
