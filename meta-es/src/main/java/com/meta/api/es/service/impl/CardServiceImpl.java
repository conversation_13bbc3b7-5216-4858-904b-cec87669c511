package com.meta.api.es.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meta.api.es.config.WalletConfig;
import com.meta.api.es.enums.RespCode;
import com.meta.api.es.service.CardConfigService;
import com.meta.api.es.service.CardService;
import com.meta.api.es.service.CreditCardApplicationService;
import com.meta.api.es.utils.MQDlxutils;
import com.meta.api.es.utils.MQOrderutils;
import com.meta.common.constant.Constants;
import com.meta.common.core.domain.AjaxResult;
import com.meta.common.core.domain.entity.SysUser;
import com.meta.common.core.page.PageDomain;
import com.meta.common.enums.PhysicalCardType;
import com.meta.common.enums.VccTxnType;
import com.meta.common.enums.app.*;
import com.meta.common.enums.vcc.CardStatus;
import com.meta.common.utils.StringUtils;
import com.meta.common.utils.ThreadPoolUtil;
import com.meta.common.utils.uuid.UniqueIDGenerator;
import com.meta.common.utils.vcc.AESUtils;
import com.meta.framework.web.controller.card.BaseCardCommon;
import com.meta.system.config.KunConfig;
import com.meta.system.config.MoonbankConfig;
import com.meta.system.constant.ChannelType;
import com.meta.system.constant.KzOrderStatus;
import com.meta.system.constant.KzOrderType;
import com.meta.system.domain.CreditCard;
import com.meta.system.domain.CreditCardApplication;
import com.meta.system.domain.MetaCreditCardLogSub;
import com.meta.system.domain.TrcCstaddress;
import com.meta.system.domain.app.*;
import com.meta.system.domain.groupkey.CardUserInfoKey;
import com.meta.system.domain.partner.MetaPartnerAssetPool;
import com.meta.system.domain.partner.MetaPartnerTxnDtl;
import com.meta.system.domain.partner.MetaPartnerUser;
import com.meta.system.email.Mail;
import com.meta.system.email.SendEmail;
import com.meta.system.fornax.service.FornaxService;
import com.meta.system.kun.constants.KunMethod;
import com.meta.system.kun.constants.KunOrderStatus;
import com.meta.system.kun.constants.KunPriceSide;
import com.meta.system.kun.service.KunService;
import com.meta.system.kun.utils.KunUtils;
import com.meta.system.moonbank.models.*;
import com.meta.system.moonbank.util.MoonbankEncryptUtil;
import com.meta.system.moonbank.util.MoonbankUtil;
import com.meta.system.service.*;
import com.meta.system.vo.AddressVo;
import com.meta.system.vo.CreditCardLogVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/05/29/17:46
 */
@Service
public class CardServiceImpl implements CardService {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private CreditCardApplicationService creditCardApplicationService;
    @Autowired
    private BaseCardCommon baseCardCommon;
    @Autowired
    private MetaPartnerTxnDtlService metaPartnerTxnDtlService;

    @Autowired
    private MoonbankUtil moonbankUtil;

    @Autowired
    private MetaPartnerUserService metaPartnerUserService;

    @Autowired
    private CardConfigService cardConfigService;

    @Autowired
    private CreditCardService creditCardService;

    @Autowired
    private MetaPartnerAssetPoolService metaPartnerAssetPoolService;


    @Autowired
    private ISysConfigService configService;

    @Autowired
    private CreditCardLogService creditCardLogService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private SendEmail sendEmail;

    @Autowired
    private MetaCardUserInfoService metaCardUserInfoService;

    @Autowired
    private KunUtils kunUtils;

    @Autowired
    private KunService kunService;

    @Autowired
    private ExchangeRateService exchangeRateService;

    @Autowired
    private BepCstaddressService bepCstaddressService;

    @Autowired
    private TrcCstaddressService trcCstaddressService;

    @Autowired
    private UcardService ucardService;


    @Autowired
    private MetaUcardCodeService metaUcardCodeService;

    @Autowired
    private MQDlxutils mqDlxutils;

    @Autowired
    private MetaPhysicalCardService metaPhysicalCardService;

    @Autowired
    private MetaCreditCardUoldService metaCreditCardUoldService;

    @Autowired
    private MetaCreditCardLogSubService metaCreditCardLogSubService;

    @Autowired
    private MetaOrderService metaOrderService;

    @Autowired
    private MQOrderutils mqOrderutils;

    @Autowired
    private MetaFxUserInfoService metaFxUserInfoService;

    @Autowired
    private FornaxService fornaxService;

    @Autowired
    private McardService mcardService;

    /**
     * 申请卡
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject createCard(JSONObject jsonObject, String sysId, String requestNo) {
        String email = jsonObject.getString("email");
        String mobileNumber = jsonObject.getString("mobileNumber");
        String mobilePrefix = jsonObject.getString("mobilePrefix");
        String cardBin = jsonObject.getString("cardBin");
        String cardLevel = jsonObject.getString("cardLevel");
        cardLevel = "01";

        JSONObject bodyJson = new JSONObject();
        //判断b端卡配置是否存在
        CardConfig cardConfig = cardConfigService.findCardPartnerConfig(sysId, cardBin, cardLevel);
        if (cardConfig == null) {
            String message = "kazepay internal error";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;

        }

        //判断是否是461199
        if ("461199".equals(cardBin)) {
            MetaFxUserInfo fxUserInfo = metaFxUserInfoService.findByEmail(email);
            if (fxUserInfo == null || StringUtils.isEmpty(fxUserInfo.getFid())) {
                String message = "Please set the cardholder information first";
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
        }


        if (cardConfig.getChannel().startsWith("moonbank")) {


            //判断用户是否已经存在
            MetaPartnerUser metaPartnerUser = metaPartnerUserService.findEmailAndSysId(email, sysId);
            if (metaPartnerUser != null && StringUtils.isNotEmpty(metaPartnerUser.getUid())) {
                //申请卡
                bodyJson = apply(cardConfig, sysId, metaPartnerUser, requestNo, mobilePrefix, mobileNumber);
                return bodyJson;
            } else {
                //不存在则注册
                ApiResponse<String> apiResponse = moonbankUtil.userRegister(mobilePrefix, mobileNumber, email);
                if (apiResponse.isSuccess()) {
                    //注册获得uid
                    String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
                    logger.info("userRegister encode result===>" + descStr);
                    JSONObject j = JSON.parseObject(descStr);
                    String uid = j.getString("uid");
                    if (metaPartnerUser == null) {
                        metaPartnerUser = savePartnerUser(sysId, email, mobilePrefix, mobileNumber, uid);
                    } else {
                        metaPartnerUser.setUid(uid);
                        //更新moonbank_uid
                        metaPartnerUserService.updatePartnerUser(metaPartnerUser.getId(), metaPartnerUser.getUid());
                    }

                    //申请卡
                    bodyJson = apply(cardConfig, sysId, metaPartnerUser, requestNo, mobilePrefix, mobileNumber);
                    return bodyJson;
                } else {
                    String message = apiResponse.getMessage();
                    bodyJson.put("error_message", message);
                    logger.info("======调用错误===== " + message);
                    return bodyJson;
                }
            }
        } else if ("kun".equals(cardConfig.getChannel()) || ChannelType.FORNAX.getValue().equals(cardConfig.getChannel())) {
            MetaPartnerUser metaPartnerUser = metaPartnerUserService.findEmailAndSysId(email, sysId);
            if (metaPartnerUser == null) {
                metaPartnerUser = savePartnerUser(sysId, email, mobilePrefix, mobileNumber, "");
            }
            bodyJson = apply(cardConfig, sysId, metaPartnerUser, requestNo, mobilePrefix, mobileNumber);
            return bodyJson;
        }

        String message = "kazepay internal error";
        bodyJson.put("error_message", message);
        logger.info("======调用错误===== " + message);
        return bodyJson;
    }


    /**
     * 查询卡信息
     *
     * @param jsonObject
     * @return
     */
    @Override
    public JSONObject queryCard(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String cardID = jsonObject.getString("cardID");
        //先判断卡id是否存在
        CreditCard c = creditCardService.findByCardIdAndSysId(cardID, sysId);
        if (c == null) {
            String message = "Card id " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(c.getCardHolder(), sysId);
        CreditCard cardInfo = creditCardService.getCardInfo(c);
        if (cardInfo == null) {
            String message = "kazepay internal error";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }

        bodyJson.put("cardID", cardID);
        String cardNo = cardInfo.getCardNo();
        if (StringUtils.isNotEmpty(cardNo)) {
            if (cardNo.contains("*")) {
                bodyJson.put("cardNo", cardNo);
            } else {
                bodyJson.put("cardNo", AESUtils.aesDecrypt(Constants.card_key, cardNo));
            }
        }
        String cvv2 = cardInfo.getCvv2();
        if (StringUtils.isNotEmpty(cvv2)) {
            bodyJson.put("cardCvv", AESUtils.aesDecrypt(Constants.card_key, cvv2));
        }
        bodyJson.put("expiryDate", cardInfo.getExpirationTime());
        bodyJson.put("currency", cardInfo.getCurrency());
        bodyJson.put("userBankCardStatus", cardInfo.getCardStatus());

//        bodyJson.put("cardHolderEmail", partnerUser.getEmail());
//        bodyJson.put("cardHolderMobileNumber", partnerUser.getMobileNumber());
//        bodyJson.put("cardHolderMobilePrefix", partnerUser.getMobilePrefix());

        bodyJson.put("countryCode", cardInfo.getBillingCountry());
        bodyJson.put("billingState", cardInfo.getProvince());
        bodyJson.put("billingCity", cardInfo.getCity());
        bodyJson.put("billingAddress", cardInfo.getAddressLine1());
        bodyJson.put("billingZipCode", cardInfo.getPostalCode());
        return bodyJson;
    }


    /**
     * 查询卡余额
     *
     * @param jsonObject
     * @return
     */
    @Override
    public JSONObject cardBalance(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String cardID = jsonObject.getString("cardID");
        String currency = jsonObject.getString("currency");
        //先判断卡id是否存在
        CreditCard c = creditCardService.findByCardIdAndSysId(cardID, sysId);
        if (c == null) {
            String message = "Card id " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        CreditCard cardInfo = creditCardService.getCardInfo(c);
        if (cardInfo == null) {
            String message = "kazepay internal error";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        bodyJson.put("cardID", cardID);
        bodyJson.put("currency", c.getCurrency());
        bodyJson.put("avaBalance", cardInfo.getBalance());
        bodyJson.put("blockBalance", BigDecimal.ZERO);
        return bodyJson;
    }


    /**
     * 卡充值
     *
     * @param jsonObject
     * @return
     */
    @Override
    public JSONObject cardRecharge(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String cardID = jsonObject.getString("cardID");
        String currency = jsonObject.getString("currency");
        String amount = jsonObject.getString("amount");
        boolean isH5 = false;
        String original = "";
        if (sysId.startsWith("H5")) {
            isH5 = true;
            original = jsonObject.getString("originalAmount");
            if (StringUtils.isEmpty(original)) {
                String message = "originalAmount cannot be empty";
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
        }

        //先判断卡id是否存在
        CreditCard c = creditCardService.findByCardIdAndSysId(cardID, sysId);
        if (c == null) {
            String message = "Card id " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        boolean checkRecharge = creditCardService.checkRecharge(c.getCardType());
        if (!checkRecharge) {
            String message = "This card has stopped recharging.";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        //充值金额
        BigDecimal rechargeAmount = new BigDecimal(amount);

        MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(c.getCardHolder(), sysId);
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartnerBySysId(sysId);
        if (!metaPartnerAssetPool.getId().getCoinCode().equals(currency)) {
            String message = currency + "  is not supported";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        CardConfig cardConfig = cardConfigService.findCardConfigByCardCfgId(c.getCardCfgId());

        //判断池子金额是否低于最低限额
        boolean flag = checkPoolLimit(metaPartnerAssetPool);
        if (flag) {
            String message = "The fund pool is below the minimum limit";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            //发送邮件提醒（后台配置多长时间发送一次）
            sendStopEmail(metaPartnerAssetPool);

            return bodyJson;
        }

        if (rechargeAmount.compareTo(cardConfig.getRechargerAmtLimit()) < 0) {
            //不能小于充值金额
            String message = "Card ID " + cardID + "Amount cannot be less than  " + cardConfig.getRechargerAmtLimit();
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        //充值手续费
        BigDecimal rechargeFee = BigDecimal.ZERO;
        if (cardConfig.getCardRechargeFee().compareTo(BigDecimal.ZERO) > 0) {
            if (isH5) {
                BigDecimal originalAmount = new BigDecimal(original);
                rechargeFee = originalAmount.multiply(cardConfig.getCardRechargeFee());
                rechargeFee = rechargeFee.setScale(2, RoundingMode.CEILING);
                logger.info("计算的手续费：" + rechargeFee);
            } else {
                rechargeFee = rechargeAmount.multiply(cardConfig.getCardRechargeFee());
                rechargeFee = rechargeFee.setScale(2, RoundingMode.CEILING);
                logger.info("计算的手续费：" + rechargeFee);
            }


        }

        if (rechargeAmount.add(rechargeFee).compareTo(metaPartnerAssetPool.getUsdtBalacne()) > 0) {
            // 余额不足
            String message = "Insufficient pool balance";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        if (c.getSource().startsWith("moonbank")) {


            //是否是首次激活金额
            if (c.getCardStatus().equals("TBA") && rechargeAmount.compareTo(cardConfig.getActiveAmtLimit()) < 0) {
                //  首次不能小于激活金额
                String message = "Card ID " + cardID + " The first recharge cannot be less than the activation amount of " + cardConfig.getActiveAmtLimit();
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }

        }
        String requestNo = "";
        if (c.getSource().startsWith("moonbank")) {
            //充值流水id
            requestNo = "mr-" + UniqueIDGenerator.generateID();

        } else if ("kun".equals(c.getSource())) {
            //充值流水id
            requestNo = "kr-" + UniqueIDGenerator.generateID();

        } else if (ChannelType.FORNAX.getValue().equals(c.getSource())) {
            //充值流水id
            requestNo = "fr-" + UniqueIDGenerator.generateID();

        }
        //调用充值
        MetaOrder metaOrder = metaOrderService.insert(
                KzOrderType.recharge.getValue(),
                requestNo,
                c.getCardId(),
                KzOrderStatus.create.getValue(),
                rechargeAmount,
                "USD",
                c.getUserId(),
                sysId,
                null, null,
                c.getUserId(), null, "usd", null, null, null
        );
        //保存充值记录
        baseCardCommon.saveMoonbankCreditCardLogNew(VccTxnType.card_deposit.getValue(), requestNo, rechargeAmount, c.getCardId(), "processing", "USD", cardConfig.getCurrency(), String.valueOf(rechargeAmount), rechargeFee, metaPartnerAssetPool.getId().getCoinCode());

        //用户充值流水
        dealRecharge(cardID, currency, rechargeAmount, sysId, rechargeFee, requestNo, TxnStatus.PADDING.getValue());

        bodyJson.put("sendAmount", rechargeAmount);
        bodyJson.put("cardID", cardID);
        bodyJson.put("currency", currency);
        bodyJson.put("transactionId", requestNo);
        bodyJson.put("transactionStatus", TxnStatus.PENDING.getName());
        //检查额度是否到预警
        checkPoolAlarm(metaPartnerAssetPool);

        // 发送消息（确保在事务提交后发送）
        MetaOrder finalMetaOrder = metaOrder;
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                mqOrderutils.send(finalMetaOrder, 3);
            }
        });


        return bodyJson;
    }

    private void dealRecharge(String cardID, String currency, BigDecimal rechargeAmount, String sysId, BigDecimal rechargeFee, String txnId, Character status) {
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectBySysIdForUpdate(sysId);
        if (TxnStatus.SUCCESS.getValue() == status) {
            //用户充值流水
            metaPartnerAssetPool.setUsdtBalacne(metaPartnerAssetPool.getUsdtBalacne().subtract(rechargeAmount).subtract(rechargeFee));
            saveUSD(metaPartnerAssetPool, PartnerTxnType.CardTopup, cardID, currency, 0, rechargeAmount.negate(), rechargeFee.negate(), TxnStatus.SUCCESS.getValue(), txnId);

        } else if (TxnStatus.PADDING.getValue() == status) {
            metaPartnerAssetPool.setUsdtBalacne(metaPartnerAssetPool.getUsdtBalacne().subtract(rechargeAmount).subtract(rechargeFee));
            metaPartnerAssetPool.setFreezeUstdBalacne(metaPartnerAssetPool.getFreezeUstdBalacne().add(rechargeAmount).add(rechargeFee));
            saveUSD(metaPartnerAssetPool, PartnerTxnType.CardTopup, cardID, currency, 0, rechargeAmount.negate(), rechargeFee.negate(), TxnStatus.PADDING.getValue(), txnId);

        }
        metaPartnerAssetPoolService.updateBalance(metaPartnerAssetPool.getId().getPartnerId(), metaPartnerAssetPool.getUsdtBalacne(), metaPartnerAssetPool.getFreezeUstdBalacne());
        //检查额度是否到预警
        checkPoolAlarm(metaPartnerAssetPool);
    }

    /**
     * 发送提醒邮件(停止池子消费)
     *
     * @param
     */
    private void sendStopEmail(MetaPartnerAssetPool metaPartnerAssetPool) {
        //判断是否发送过邮件
        String sendRemindMailKey = "sendForBPoolStop_" + metaPartnerAssetPool.getId().getPartnerId();
        //后台配置多少分钟发送一次
        if (!redisTemplate.hasKey(sendRemindMailKey)) {
            long minute = Long.valueOf(configService.selectConfigByKey("b_send_minute"));
            redisTemplate.opsForValue().set(sendRemindMailKey, "1", minute, TimeUnit.MINUTES);
            SysUser sysUser = iSysUserService.selectUserById(metaPartnerAssetPool.getId().getPartnerId().longValue());
            BigDecimal amout = metaPartnerAssetPool.getUsdtBalacne();
            ThreadPoolUtil.execute(new Runnable() {
                @Override
                public void run() {
                    String content = "";
                    content += "<img src=\"https://m.kazepay.io/download/kazepay-logo.png\" width=450 />  <br><br>";
                    content += "Dear user, the pool has reached the minimum limit, and currently there is " + amout + " USD left.";
                    content += "<div style=\"font-weight: bold; margin-top: 14px; margin-bottom: 10px;\">KazePay Team</div>";
                    content += "<div style=\"font-size: 14px;color: #666; \">System mail, please do not reply</div>";
                    content += "<br><br>";

                    Mail mail = new Mail();
                    mail.setEmail(sysUser.getEmail());
                    mail.setContent(content);
                    mail.setSubject("The pool reaches the minimum limit value");
                    //发送通知邮件
                    sendEmail.sendCheckEmail(mail);
                }
            });


        }
    }


    /**
     * 发送提醒邮件(预警)
     *
     * @param
     */
    private void sendAlarmEmail(MetaPartnerAssetPool metaPartnerAssetPool) {
        //判断是否发送过邮件
        String sendAlarmMailKey = "sendForBPoolAlarm_" + metaPartnerAssetPool.getId().getPartnerId();
        //后台配置多少分钟发送一次
        if (!redisTemplate.hasKey(sendAlarmMailKey)) {
            long minute = Long.valueOf(configService.selectConfigByKey("b_send_minute"));
            redisTemplate.opsForValue().set(sendAlarmMailKey, "1", minute, TimeUnit.MINUTES);
            SysUser sysUser = iSysUserService.selectUserById(metaPartnerAssetPool.getId().getPartnerId());
            BigDecimal amout = metaPartnerAssetPool.getUsdtBalacne();
            ThreadPoolUtil.execute(new Runnable() {
                @Override
                public void run() {
                    String content = "";
                    content += "<img src=\"https://m.kazepay.io/download/kazepay-logo.png\" width=450 />  <br><br>";
                    content += "Dear user, the pool has reached the warning value, currently " + amout + " USD.";
                    content += "<div style=\"font-weight: bold; margin-top: 14px; margin-bottom: 10px;\">KazePay Team</div>";
                    content += "<div style=\"font-size: 14px;color: #666; \">System mail, please do not reply</div>";
                    content += "<br><br>";

                    Mail mail = new Mail();
                    mail.setEmail(sysUser.getEmail());
                    mail.setContent(content);
                    mail.setSubject("The pool reaches the warning value");
                    //发送通知邮件
                    sendEmail.sendCheckEmail(mail);
                }
            });


        }
    }

    /**
     * 卡交易记录
     *
     * @param jsonObject
     * @return
     */
    @Override
    public JSONObject queryTransactions(JSONObject jsonObject, String sysId) {

        JSONObject bodyJson = new JSONObject();
        String cardID = jsonObject.getString("cardID");
        String transactionId = jsonObject.getString("transactionId");
        Integer pageSize = jsonObject.getInteger("pageSize");
        Integer pageNum = jsonObject.getInteger("pageNum");
        String transactionStartTime = jsonObject.getString("transactionStartTime");
        String transactionEndTime = jsonObject.getString("transactionEndTime");


        //先判断卡id是否存在

        CreditCard c = creditCardService.findByCardIdAndSysId(cardID, sysId);
        if (c == null) {
            String message = "Card id " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }

        PageDomain pageDomain = new PageDomain();
        pageDomain.setPageNum(StringUtils.isNull(pageNum) ? 1 : pageNum);
        pageDomain.setPageSize(StringUtils.isNull(pageSize) ? 10 : pageSize);
        Page<CreditCardLogVo> page = creditCardLogService.selectData(cardID, transactionId, transactionStartTime, transactionEndTime, PageRequest.of(pageDomain.getPageNum() - 1, pageDomain.getPageSize()));
        JSONArray jsonArray = new JSONArray();
        for (CreditCardLogVo vo : page.getContent()) {
            JSONObject object = new JSONObject();
            object.put("cardID", vo.getCardID());
            object.put("transactionId", vo.getTransactionId());
            object.put("transactionTime", vo.getTransactionTime());
            object.put("cardCurrency", vo.getCardCurrency());
            object.put("cardCurrencyAmt", vo.getCardCurrencyAmt());
            object.put("transCurrency", vo.getTransCurrency());
            object.put("transCurrencyAmt", vo.getTransCurrencyAmt());
            object.put("feeCurrency", vo.getFeeCurrency());
            object.put("fee", vo.getFee());
            object.put("transStatus", vo.getTransStatus());
            object.put("transType", vo.getTransType());
            object.put("merchantName", vo.getMerchantName());
            object.put("respCode", vo.getRespCode());
            object.put("respCodeDesc", vo.getRespCodeDesc());

            object.put("cardID", vo.getCardID());


            if ("89".equals(c.getCardType())) {


                List<MetaCreditCardLogSub> subList = metaCreditCardLogSubService.findList(vo.getTransactionId());
                List<JSONObject> jsList = new ArrayList<>();
                BigDecimal refundAmount = BigDecimal.ZERO;
                for (MetaCreditCardLogSub sub : subList) {

                    if ("CLEARING".equals(sub.getTransactionType()) && vo.getCardCurrencyAmt().compareTo(sub.getCardTransactionAmount()) != 0) {
                        BigDecimal amount = sub.getCardTransactionAmount();
                        if (amount != null) {
                            refundAmount = vo.getCardCurrencyAmt().subtract(amount);
                        }
                    } else if ("REVERSAL".equals(sub.getTransactionType())) {
                        refundAmount = sub.getCardTransactionAmount();
                    } else if ("REFUND".equals(sub.getTransactionType())) {
                        refundAmount = sub.getCardTransactionAmount();
                    }

                    JSONObject jData = new JSONObject();
                    jData.put("cardId", sub.getCardId());
                    jData.put("transactionId", sub.getTransactionId());
                    jData.put("transactionIdSub", sub.getTransactionIdSub());
                    jData.put("transactionDate", sub.getTransactionDate());
                    jData.put("transactionCurrency", sub.getTransactionCurrency());
                    jData.put("transactionAmount", sub.getTransactionAmount());
                    jData.put("cardCurrency", sub.getCardCurrency());
                    jData.put("cardTransactionAmount", sub.getCardTransactionAmount());
                    jData.put("fee", sub.getFee());
                    jData.put("feeCurrency", sub.getFeeCurrency());
                    jData.put("transactionType", sub.getTransactionType());
                    jData.put("transactionStatus", sub.getTransactionStatus());
                    jsList.add(jData);

                }


                object.put("refundAmount", refundAmount);
                object.put("refundCurrency", c.getCurrency());
                object.put("subList", jsList);
            }

            jsonArray.add(object);

        }


        bodyJson.put("data", jsonArray);
        return bodyJson;

    }

    /**
     * 保存用户信息
     *
     * @param sysId
     * @param email
     * @param mobilePrefix
     * @param mobileNumber
     * @param uid
     */
    public void saveUser(String sysId, String email, String mobilePrefix, String mobileNumber, String uid) {
        ThreadPoolUtil.execute(new Runnable() {
            @Override
            public void run() {
                MetaPartnerUser metaPartnerUser = new MetaPartnerUser();
                metaPartnerUser.setEmail(email);
                metaPartnerUser.setMobilePrefix(mobilePrefix);
                metaPartnerUser.setMobileNumber(mobileNumber);
                metaPartnerUser.setSysId(sysId);
                metaPartnerUser.setUid(uid);
                metaPartnerUserService.save(metaPartnerUser);
            }
        });
    }

    /**
     * 保存用户信息
     *
     * @param sysId
     * @param email
     * @param mobilePrefix
     * @param mobileNumber
     * @param uid
     * @return
     */
    @Transactional
    public MetaPartnerUser savePartnerUser(String sysId, String email, String mobilePrefix, String mobileNumber, String uid) {
        MetaPartnerUser metaPartnerUser = new MetaPartnerUser();
        metaPartnerUser.setEmail(email);
        metaPartnerUser.setMobilePrefix(mobilePrefix);
        metaPartnerUser.setMobileNumber(mobileNumber);
        metaPartnerUser.setSysId(sysId);
        metaPartnerUser.setUid(uid);
        metaPartnerUserService.save(metaPartnerUser);
        return metaPartnerUser;
    }

    /**
     * 申请卡
     *
     * @param cardConfig      卡配置
     * @param sysId           系统id
     * @param metaPartnerUser 申请人信息
     * @param requestNo       请求号
     * @return
     */
    public JSONObject apply(CardConfig cardConfig, String sysId, MetaPartnerUser metaPartnerUser, String requestNo, String mobilePrefix, String mobileNumber) {


        JSONObject json = new JSONObject();
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartnerBySysId(sysId);


        //判断池子金额是否低于最低限额
        boolean flag = checkPoolLimit(metaPartnerAssetPool);
        if (flag) {
            String message = "The fund pool is below the minimum limit";
            json.put("error_message", message);
            logger.info("======调用错误===== " + message);
            //发送邮件提醒（后台配置多长时间发送一次）
            sendStopEmail(metaPartnerAssetPool);
            return json;
        }

        if (metaPartnerAssetPool.getUsdtBalacne().compareTo(cardConfig.getCardFee()) < 0) {

            // 余额不足
            String message = "Insufficient pool balance";
            json.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return json;

        }
        String requestNo_card = UniqueIDGenerator.generateID();
        String cardId = creditCardService.generateCardId();
        String cardStatus = "AUDITING"; //卡片是审核状态
        String cardNo = cardConfig.getCardBin() + "**** ****";
        json.put("cardID", cardId);
        json.put("status", cardStatus);
        savaCard(cardConfig, sysId, metaPartnerUser, requestNo_card, metaPartnerAssetPool, cardNo, cardId, cardStatus, FreezeType.FREEZE, AlgorithmType.SUB, requestNo_card);

        //3.个人账户余额处理
        MetaOrder metaOrder = metaOrderService.insert(KzOrderType.open.getValue(), requestNo_card, cardId, KzOrderStatus.create.getValue(), null, null,
                metaPartnerAssetPool.getId().getPartnerId(), sysId, null, null, metaPartnerAssetPool.getId().getPartnerId(), null, null, metaPartnerUser.getEmail(), mobilePrefix, mobileNumber);
//        if (ChannelType.FORNAX.getValue().equals(cardConfig.getChannel())) {
//            String authLimitAmount = configService.selectConfigByKey("authLimitAmount");
//            if (StringUtils.isNotEmpty(authLimitAmount)) {
//                metaOrder.setOrderAmount(new BigDecimal(authLimitAmount));
//            }
//        }
        // 发送消息（确保在事务提交后发送）
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                mqOrderutils.send(metaOrder, 3);
            }
        });

        return json;
    }

    /**
     * 保存卡片信息
     *
     * @param cardConfig
     * @param sysId
     * @param metaPartnerUser
     * @param requestNo
     * @param metaPartnerAssetPool
     * @param cardNo
     * @param userBankcardId
     * @param status
     */
    private void savaCard(CardConfig cardConfig, String sysId, MetaPartnerUser metaPartnerUser, String requestNo, MetaPartnerAssetPool metaPartnerAssetPool, String cardNo, String userBankcardId, String status, FreezeType freezeType, AlgorithmType algorithmType, String txnId) {
        //公司账户
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        //用户流水

        metaPartnerTxnDtlService.dealPool(cardConfig.getCardFee(), sysId, userBankcardId, freezeType, algorithmType, txnId);


        //保存申请信息
        CreditCardApplication c = new CreditCardApplication();
        c.setRequestNo(requestNo);
        c.setFirstName(metaPartnerUser.getEmail());
        c.setLastName(metaPartnerUser.getEmail());
        c.setMobile(metaPartnerUser.getMobilePrefix() + " " + metaPartnerUser.getMobileNumber());
        c.setEmail(metaPartnerUser.getEmail());
        c.setCardLevel(cardConfig.getCardLevel());
        c.setCardType(cardConfig.getCardType());
        c.setCardFee(cardConfig.getCardFee());
        c.setCardBin(cardConfig.getCardBin());
        c.setStatus("03");
        c.setCardId(userBankcardId);
        c.setCardNo(cardNo);
        c.setCreatedAt(LocalDateTime.now());
        if (StringUtils.isNotEmpty(cardConfig.getBankCardId())) {
            c.setMoonbankUid(cardConfig.getBankCardId());
        }
        c.setCurrency(cardConfig.getCurrency());

        creditCardApplicationService.save(c);

        //保存卡信息
        CreditCard cc = new CreditCard();
        cc.setCardId(userBankcardId);
        cc.setCardLevel(cardConfig.getCardLevel());
        cc.setCardType(cardConfig.getCardType());
        cc.setCardFee(cardConfig.getCardFee());
        cc.setBalance(BigDecimal.ZERO);
        cc.setUserId(metaPartnerAssetPool.getId().getPartnerId());
        cc.setCreatedAt(LocalDateTime.now());
        cc.setCardHolder(metaPartnerUser.getEmail());
        cc.setCardStatus(status);
        cc.setStatusUpdateTime(new Date());
        cc.setCardNo(cardNo);
        cc.setIsActive("N");
        cc.setCountry("");
        cc.setSource(cardConfig.getChannel());
        cc.setCardLabel(cardConfig.getCardLabel());
        cc.setCardCfgId(cardConfig.getCardCfgId());
        cc.setSysId(sysId);
        cc.setCurrency(cardConfig.getCurrency());
        creditCardService.save(cc);

        //检查额度是否到预警
        checkPoolAlarm(metaPartnerAssetPool);
    }


    /**
     * 判断池子金额是否低于最低限制
     *
     * @param metaPartnerAssetPool
     * @return
     */
    private boolean checkPoolLimit(MetaPartnerAssetPool metaPartnerAssetPool) {
        if (metaPartnerAssetPool.getMinDepositAmt() != null && metaPartnerAssetPool.getUsdtBalacne().compareTo(metaPartnerAssetPool.getMinDepositAmt()) <= 0) {
            return true;
        }
        return false;
    }

    /**
     * 判断池子是否到预警值
     *
     * @param metaPartnerAssetPool
     * @return
     */
    private void checkPoolAlarm(MetaPartnerAssetPool metaPartnerAssetPool) {
        //余额不足告警设定金额
        if (metaPartnerAssetPool.getAlarmInsufficientAmt() != null && metaPartnerAssetPool.getUsdtBalacne().compareTo(metaPartnerAssetPool.getAlarmInsufficientAmt()) <= 0) {
            // todo 发送提醒邮件
            sendAlarmEmail(metaPartnerAssetPool);
        } else if (metaPartnerAssetPool.getAlarmCardinalityAmt() != null && metaPartnerAssetPool.getAlarmInsufficientRatio() != null
                && metaPartnerAssetPool.getUsdtBalacne().compareTo(metaPartnerAssetPool.getAlarmCardinalityAmt().multiply(metaPartnerAssetPool.getAlarmInsufficientRatio())) <= 0) {
            // 资金池子额度*百分比
            // todo 发送提醒邮件
            sendAlarmEmail(metaPartnerAssetPool);

        }

    }

    /**
     * @param metaPartnerAssetPool 商户
     * @param txnType              交易类型
     * @param cardId               卡id
     * @param currency             币种
     * @param num                  交易数量
     * @param txnAmount            金额
     * @param fee                  手续费
     * @param txnStatus            状态
     * @param txnId                交易流水
     */

    public void saveUSD(MetaPartnerAssetPool metaPartnerAssetPool, PartnerTxnType txnType, String cardId, String currency, Integer num, BigDecimal txnAmount, BigDecimal fee, Character txnStatus, String txnId) {

        MetaPartnerTxnDtl txnDtl = new MetaPartnerTxnDtl();
        txnDtl.setPartnerId(metaPartnerAssetPool.getId().getPartnerId());
        txnDtl.setCoinCode(metaPartnerAssetPool.getId().getCoinCode());
        txnDtl.setCardId(cardId);
        txnDtl.setCurrency(currency);
        txnDtl.setTxnType(txnType.getValue());
        txnDtl.setTxnAmount(txnAmount);
        txnDtl.setTxnNum(num);
        txnDtl.setTxnFee(fee);
        txnDtl.setTxnTime(new Date());
        txnDtl.setTxnStatus(txnStatus);
        txnDtl.setTxnDesc(txnType.getEname());
        txnDtl.setAssetBalance(metaPartnerAssetPool.getUsdtBalacne());
        txnDtl.setTxnId(txnId);
        metaPartnerTxnDtlService.save(txnDtl);

    }

    /**
     * 设置用户信息
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject createKYC(JSONObject jsonObject, String sysId) {
        String email = jsonObject.getString("email");
        String mobileNumber = jsonObject.getString("mobileNumber");
        String mobilePrefix = jsonObject.getString("mobilePrefix");


        JSONObject bodyJson = new JSONObject();

        //判断用户是否已经存在
        MetaPartnerUser metaPartnerUser = metaPartnerUserService.findEmailAndSysId(email, sysId);
        if (metaPartnerUser != null && StringUtils.isNotEmpty(metaPartnerUser.getUid())) {
            //设置用户信息
            bodyJson = setUserInfo(metaPartnerUser.getUid(), jsonObject);
            return bodyJson;
        } else {
            //不存在则注册
            ApiResponse<String> apiResponse = moonbankUtil.userRegister(mobilePrefix, mobileNumber, email);
            if (apiResponse.isSuccess()) {
                //注册获得uid
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
                logger.info("userRegister encode result===>" + descStr);
                JSONObject j = JSON.parseObject(descStr);
                String uid = j.getString("uid");
                if (metaPartnerUser == null) {
                    saveUser(sysId, email, mobilePrefix, mobileNumber, uid);
                } else {
                    //更新moonbank_uid
                    metaPartnerUserService.updatePartnerUser(metaPartnerUser.getId(), uid);
                }
                //设置用户信息
                bodyJson = setUserInfo(uid, jsonObject);
                return bodyJson;
            } else {
                String message = apiResponse.getMessage();
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
        }

    }

    private JSONObject setUserInfo(String uid, JSONObject jsonObject) {
        JSONObject json = new JSONObject();
        MetaCardUserInfo metaCardUserInfo = metaCardUserInfoService.findUid(uid, PhysicalCardType.EuroCard.getType());
        if (metaCardUserInfo == null) {
            metaCardUserInfo = new MetaCardUserInfo();
        }

        CardUserInfoRequest cardUserInfoRequest = new CardUserInfoRequest();
        String firstNameEnglish = jsonObject.getString("firstName");
        cardUserInfoRequest.setFirstName(firstNameEnglish);
        cardUserInfoRequest.setFirstNameEnglish(firstNameEnglish);
        metaCardUserInfo.setFirstName(firstNameEnglish);
        metaCardUserInfo.setFirstNameEnglish(firstNameEnglish);

        String lastNameEnglish = jsonObject.getString("lastName");
        cardUserInfoRequest.setLastName(lastNameEnglish);
        cardUserInfoRequest.setLastNameEnglish(lastNameEnglish);
        metaCardUserInfo.setLastName(lastNameEnglish);
        metaCardUserInfo.setLastNameEnglish(lastNameEnglish);

        String dateOfBirth = jsonObject.getString("dateOfBirth");
        cardUserInfoRequest.setDateOfBirth(dateOfBirth);
        metaCardUserInfo.setDateOfBirth(dateOfBirth);

        String nationality = jsonObject.getString("nationality");
        cardUserInfoRequest.setNationality(nationality);
        metaCardUserInfo.setNationality(nationality);

        Address addressRequest = new Address();
        String addressLine = jsonObject.getString("addressLine");
        if (addressLine.length() > 70) {
            String firstPart = addressLine.substring(0, 70);
            String secondPart = addressLine.substring(70);
            System.out.println("第一部分（70个字符）：\n" + firstPart);
            System.out.println("第二部分（超过70个字符）：\n" + secondPart);
            addressRequest.setAddressLine1(firstPart);
            addressRequest.setAddressLine2(secondPart);
            metaCardUserInfo.setAddressLine1(firstPart);
            metaCardUserInfo.setAddressLine2(secondPart);
        } else {
            addressRequest.setAddressLine1(addressLine);
            metaCardUserInfo.setAddressLine1(addressLine);
        }


        String city = jsonObject.getString("city");
        addressRequest.setCity(city);
        metaCardUserInfo.setCity(city);

        String countryCode = jsonObject.getString("countryCode");
        addressRequest.setCountryCode(countryCode);
        metaCardUserInfo.setCountryCode(countryCode);

        String postCode = jsonObject.getString("postCode");
        addressRequest.setPostCode(postCode);
        metaCardUserInfo.setPostCode(postCode);

        Identification identificationRequest = new Identification();

        String identificationType = jsonObject.getString("identificationType");
        String identificationNumber = jsonObject.getString("identificationNumber");
        String identificationExpiryDate = jsonObject.getString("identificationExpiryDate");
        identificationRequest.setIdentificationType(identificationType);
        identificationRequest.setIdentificationNumber(identificationNumber);
        identificationRequest.setIdentificationExpiryDate(identificationExpiryDate);
        metaCardUserInfo.setIdentificationType(identificationType);
        metaCardUserInfo.setIdentificationNumber(identificationNumber);
        metaCardUserInfo.setIdentificationExpiryDate(identificationExpiryDate);

        //国籍为中国，需要上传工作签证
        String imgBase64 = "";
        if ("CN".equals(cardUserInfoRequest.getNationality())) {
            String visaNumber = jsonObject.getString("visaNumber");
            String visaExpiryDate = jsonObject.getString("visaExpiryDate");
            imgBase64 = jsonObject.getString("imgBase64");
            String message = "";

            if (StringUtils.isEmpty(visaNumber)) {
                message = "When nationality is CN, visaNumber cannot be empty";
                json.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return json;
            }
            if (StringUtils.isEmpty(visaExpiryDate)) {
                message = "When nationality is CN, visaExpiryDate cannot be empty";
                json.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return json;
            }
            if (StringUtils.isEmpty(imgBase64)) {
                message = "When nationality is CN, imgBase64 cannot be empty";
                json.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return json;
            }
            identificationRequest.setVisaNumber(visaNumber);
            identificationRequest.setVisaExpiryDate(visaExpiryDate);
            metaCardUserInfo.setVisaNumber(visaNumber);
            metaCardUserInfo.setVisaExpiryDate(visaExpiryDate);
        }

        cardUserInfoRequest.setAddress(addressRequest);
        cardUserInfoRequest.setIdentification(identificationRequest);
        ApiResponse<String> apiResponse = moonbankUtil.setUserInfo(uid, cardUserInfoRequest);
        if (apiResponse.isSuccess()) {
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
            System.out.println("setUserInfo encode result===>" + descStr);

            JSONObject j = JSON.parseObject(descStr);


            String walletId = j.getString("walletId");

            metaCardUserInfo.setId(new CardUserInfoKey(uid, PhysicalCardType.EuroCard.getType()));
            metaCardUserInfo.setWalletId(walletId);
            if ("CN".equals(metaCardUserInfo.getNationality())) {
                String email = jsonObject.getString("email");
                String picName = email + "_kyc.jpg";
                ResponseEntity<String> responseEntity = moonbankUtil.uploadVisaBase64(uid, imgBase64, picName);
                if (responseEntity.getStatusCode().is2xxSuccessful()) {
                    JSONObject jf = JSON.parseObject(responseEntity.getBody());
                    logger.info("visa  img encode result===>" + responseEntity.getBody());
                    if (jf.getBoolean("success")) {
                        if (StringUtils.isNotEmpty(jf.getString("result"))) {
                            String desc = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, jf.getString("result"));

                            logger.info("visa  img encode result2===>" + desc);
                            if (desc.contains("url")) {
                                JSONObject jf2 = JSON.parseObject(desc);
                                String url = jf2.getString("url");
                                metaCardUserInfo.setVisaUrl(url);
                                logger.info("CN  工作签证上传成功");
                            }

                        }

                    }


                }

            }

            metaCardUserInfoService.save(metaCardUserInfo);

            json.put("status", "SUBMIT");
            return json;

        } else {
            int code = apiResponse.getCode();
            String message = apiResponse.getMessage();

            json.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return json;
        }

    }

    /**
     * 进行 kyc
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject startKYC(JSONObject jsonObject, String sysId) {
        String email = jsonObject.getString("email");
        String idType = jsonObject.getString("idType");
        String country = jsonObject.getString("country");
        JSONObject bodyJson = new JSONObject();
        MetaPartnerUser metaPartnerUser = metaPartnerUserService.findEmailAndSysId(email, sysId);
        if (metaPartnerUser == null) {
            String message = "Please create kyc information first";
            bodyJson.put("error_message", message);
            logger.info("======kyc1error===== " + message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        } else {

            MetaCardUserInfo metaCardUserInfo = metaCardUserInfoService.findUid(metaPartnerUser.getUid(), PhysicalCardType.EuroCard.getType());
            if (metaCardUserInfo == null) {
                String message = "Please create kyc information first";
                bodyJson.put("error_message", message);
                logger.info("======kyc2error===== " + message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }


            if ("ID".equals(idType)) {
                idType = "ID_CARD";
            }
            UserKyccheckRequest request = new UserKyccheckRequest();
            request.setIdType(idType);
            request.setCountry(country);
            ApiResponse<String> apiResponse = moonbankUtil.userKyccheck(metaPartnerUser.getUid(), request);
            if (apiResponse.isSuccess()) {
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

                JSONObject j = JSON.parseObject(descStr);
                String gateway = j.getString("gateway");

                metaCardUserInfo.setKycCountry(request.getCountry());
                metaCardUserInfo.setKycIdType(request.getIdType());
                metaCardUserInfoService.save(metaCardUserInfo);
                bodyJson.put("gateway", gateway);
                return bodyJson;
            } else {
                String message = apiResponse.getMessage();

                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }

        }


    }

    /**
     * 激活卡
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject activateCard(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String email = jsonObject.getString("email");
        String cardNumber = jsonObject.getString("cardNumber");
        String mobileNumber = jsonObject.getString("mobileNumber");
        String mobilePrefix = jsonObject.getString("mobilePrefix");

        //判断用户是否已经存在
        MetaPartnerUser metaPartnerUser = metaPartnerUserService.findEmailAndSysId(email, sysId);
        if (metaPartnerUser != null && StringUtils.isNotEmpty(metaPartnerUser.getUid())) {
            //激活卡
            activeCard(sysId, bodyJson, email, cardNumber, metaPartnerUser);
            return bodyJson;
        } else {
            //不存在则注册
            if (StringUtils.isEmpty(mobilePrefix) || StringUtils.isEmpty(mobileNumber)) {
                String message = "Mobile phone number cannot be empty";
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
            ApiResponse<String> apiResponse = moonbankUtil.userRegister(mobilePrefix, mobileNumber, email);
            if (apiResponse.isSuccess()) {
                //注册获得uid
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
                logger.info("userRegister encode result===>" + descStr);
                JSONObject j = JSON.parseObject(descStr);
                String uid = j.getString("uid");
                if (metaPartnerUser == null) {
                    metaPartnerUser = savePartnerUser(sysId, email, mobilePrefix, mobileNumber, uid);
                } else {
                    metaPartnerUser.setUid(uid);
                    //更新moonbank_uid
                    metaPartnerUserService.updatePartnerUser(metaPartnerUser.getId(), metaPartnerUser.getUid());
                }

                //激活卡
                activeCard(sysId, bodyJson, email, cardNumber, metaPartnerUser);

                return bodyJson;
            } else {
                String message = apiResponse.getMessage();
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
        }


    }

    private JSONObject activeCard(String sysId, JSONObject bodyJson, String email, String cardNumber, MetaPartnerUser metaPartnerUser) {
        String cardBin = cardNumber.substring(0, 6);
        cardBin = "524604";
        CardConfig cc = cardConfigService.findCardPartnerConfig(sysId, cardBin, "99");
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartnerBySysId(sysId);
        if (cc == null) {
            String message = "Wrong card number";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;

        }

        if (metaPartnerUser == null) {
            String message = "Please create kyc information first";
            bodyJson.put("error_message", message);
            logger.info("======kyc1error===== " + message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }

        ActivatecardRequest activatecardRequest = new ActivatecardRequest();
        activatecardRequest.setCardNumber(cardNumber);


        activatecardRequest.setTemplateId(cc.getBankCardId());
        ApiResponse<String> apiResponse = moonbankUtil.activatecard(metaPartnerUser.getUid(), activatecardRequest);
        if (apiResponse.isSuccess()) {
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

            System.out.println("activatecard encode result===>" + descStr);
            JSONObject j = JSON.parseObject(descStr);
            String id = j.getString("cardId");
            String bankCardId = j.getString("userBankId");
            String cardStatus = j.getString("cardStatus");
            String cardId = creditCardService.generateCardId();
            bodyJson.put("cardID", cardId);
            bodyJson.put("status", cardStatus);
            newPhysicalCard(sysId, email, AESUtils.aesEncrypt(Constants.card_key, cardNumber), metaPartnerUser, cardBin, cc, metaPartnerAssetPool, id, cardId, cardStatus, bankCardId);

            return bodyJson;
        } else {
            String message = apiResponse.getMessage();

            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
    }

    //新增实体卡
    private void newPhysicalCard(String sysId, String email, String cardNumber, MetaPartnerUser metaPartnerUser, String cardBin, CardConfig cc, MetaPartnerAssetPool metaPartnerAssetPool, String id, String cardID, String cardStatus, String bankCardId) {
        //保存申请信息
        CreditCardApplication c = new CreditCardApplication();
        c.setRequestNo(id);
        c.setFirstName(email);
        c.setLastName(email);
        c.setMobile(metaPartnerUser.getMobilePrefix() + " " + metaPartnerUser.getMobileNumber());
        c.setEmail(email);
        c.setCardLevel(cc.getCardLevel());
        c.setCardType(cc.getCardType());
        c.setCardFee(cc.getCardFee());
        c.setCardBin(cardBin);
        c.setCardNo(cardNumber);
        c.setStatus("03");
        c.setCardId(cardID);
        c.setCreatedAt(LocalDateTime.now());
        c.setMoonbankUid(cc.getBankCardId());
        creditCardApplicationService.save(c);
        //保存卡信息
        CreditCard card = new CreditCard();
        card.setCardId(cardID);
        card.setCardNo(cardNumber);
        card.setCardLevel(cc.getCardLevel());
        card.setCardType(cc.getCardType());
        card.setCardFee(cc.getCardFee());
        card.setBalance(BigDecimal.ZERO);
        card.setUserId(metaPartnerAssetPool.getId().getPartnerId());
        card.setCreatedAt(LocalDateTime.now());
        card.setCardHolder(email);
        card.setCardStatus(cardStatus);
        card.setIsActive("N");
        card.setCountry("");
        card.setSource(cc.getChannel());
        card.setCardLabel(cc.getCardLabel());
        card.setCardCfgId(cc.getCardCfgId());
        card.setSysId(sysId);
        card.setCurrency(cc.getCurrency());
        card.setBankCardId(bankCardId);
        creditCardService.save(card);
    }

    /**
     * 查询kyc状态
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject queryKYC(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String email = jsonObject.getString("email");
        MetaPartnerUser metaPartnerUser = metaPartnerUserService.findEmailAndSysId(email, sysId);
        if (metaPartnerUser == null) {
            String message = "Please create kyc information first";
            bodyJson.put("error_message", message);
            logger.info("======kyc1error===== " + message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        } else {
            ApiResponse<String> apiResponse = moonbankUtil.userKycstatus(metaPartnerUser.getUid());

            if (apiResponse.isSuccess()) {
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

                System.out.println("userKycstatus encode result===>" + descStr);
                JSONObject j = JSON.parseObject(descStr);
                String kycStatus = j.getString("kycStatus");
                String reason = j.getString("reason");

                if (StringUtils.isNotEmpty(kycStatus)) {
                    metaCardUserInfoService.updataStaue(metaPartnerUser.getUid(), kycStatus, PhysicalCardType.EuroCard.getType());
                }
                bodyJson.put("reason", reason);
                bodyJson.put("kycStatus", kycStatus);
                return bodyJson;
            } else {
                String message = apiResponse.getMessage();

                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }

        }
    }

    /**
     * 关闭卡
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject closeCard(JSONObject jsonObject, String sysId) {

        JSONObject bodyJson = new JSONObject();
        String cardID = jsonObject.getString("cardID");
        //先判断卡id是否存在
        CreditCard c = creditCardService.findByCardIdAndSysId(cardID, sysId);
        CardConfig cardConfig = cardConfigService.findCardConfigByCardCfgId(c.getCardCfgId());
        if (c == null) {
            String message = "Card id " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        if (c.getSource().startsWith("moonbank")) {

            return creditCardService.closeCardB(sysId, bodyJson, c, cardConfig);
        } else if (c.getSource().equals("kun")) {
            JSONObject result = kunService.closeCard(new JSONObject(), c, cardConfig);
            BigDecimal balance = result.getBigDecimal("refundAmt");
            String currency = result.getString("currency");
            String txnId = result.getString("txnId");

            if (!currency.equals("USD")) {
                BigDecimal price = kunService.getPrice("USDT", c.getCurrency(), KunPriceSide.BUY.getValue(), balance);
                if (price.compareTo(new BigDecimal("1")) > 0) {
                    balance = balance.divide(price, 2, RoundingMode.CEILING);//充值coin转USD的值
                } else {
                    balance = balance.multiply(price).setScale(2, RoundingMode.CEILING);//充值coin转USD的值
                }
                result.put("refundAmt", balance);
                result.put("currency", "USD");
            }
            String status = result.getString("status");
            if (!status.equals(TxnStatus.DECLINED.getName())) {
                creditCardService.updateStatus(cardID, CardStatus.CLOSE.getValue());
                creditCardService.dealBalanceB(sysId, c, cardConfig, txnId, balance);
            }
            return result;
        } else {
            //错误
            String message = "The card does not support close";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
    }

    /**
     * 更新卡状态
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject updCardStatus(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String cardID = jsonObject.getString("cardID");
        String status = jsonObject.getString("status");
        //先判断卡id是否存在
        CreditCard c = creditCardService.findByCardIdAndSysId(cardID, sysId);
        if (c == null) {
            String message = "Card id " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        if (c.getSource().startsWith("moonbank")) {

            if (!"ACTIVE".equals(status) && !"INACTIVE".equals(status)) {
                String message = "Status parameter error";
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
            }
            boolean enable = false;
            if ("ACTIVE".equals(status)) {
                enable = true;
            } else if ("INACTIVE".equals(status)) {
                enable = false;
            }

            MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(c.getCardHolder(), sysId);
            ApiResponse<String> apiResponse = moonbankUtil.updateBankcardStatus(partnerUser.getUid(), Integer.valueOf(c.getBankCardId()), enable);
            logger.info("updCardStatus response Object:  " + apiResponse);
            if (apiResponse.isSuccess()) {
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
                logger.info("updCardStatus encode result===>" + descStr);
                JSONObject j = JSONObject.parseObject(descStr);

                bodyJson.put("cardID", cardID);
                String cs = "";
                ApiResponse<String> apiResponse2 = moonbankUtil.queryBankcardInfo(partnerUser.getUid(), Integer.valueOf(c.getBankCardId()));
                logger.info("updCardStatus response Object:  " + apiResponse2);
                if (apiResponse2.isSuccess()) {
                    String descStr2 = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse2.getResult());
                    logger.info("updCardStatus encode result===>" + descStr2);
                    JSONObject j2 = JSONObject.parseObject(descStr2);
                    String cardStatus = j2.getString("userBankCardStatus");
                    cs = cardStatus;
                } else {
                    if ("ACTIVE".equals(status)) {
                        cs = "ACTIVE";
                    } else if ("INACTIVE".equals(status)) {
                        cs = "INACTIVE";
                    }

                }
                bodyJson.put("status", cs);
                creditCardService.updateStatus(cardID, cs);
                return bodyJson;
            } else {
                //错误
                String message = apiResponse.getMessage();
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }

        } else if ("kun".equals(c.getSource())) {
            if ("89".equals(c.getCardType())) {
                //解锁
                kunService.cardUnlock(c, bodyJson);
                boolean enable = false;
                if ("ACTIVE".equals(status)) {
                    enable = true;
                } else if ("INACTIVE".equals(status)) {
                    enable = false;
                }
                //冻结/解冻
                kunService.cardfreeze(c, enable, bodyJson);

                MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(c.getCardHolder(), sysId);
                JSONObject result = kunService.getCardInfo(new JSONObject(), c, partnerUser);
                if (result != null) {
                    bodyJson.put("status", result.getString("userBankCardStatus"));
                } else {
                    bodyJson.put("status", "");
                }
                bodyJson.put("cardID", cardID);
                return bodyJson;
            }
        } else if (ChannelType.FORNAX.getValue().equals(c.getSource())) {
            if ("ACTIVE".equals(status)) {
                fornaxService.updateCard(c, status);
            } else if ("INACTIVE".equals(status)) {
                fornaxService.updateCard(c, status);
            } else {
                String message = "The status field is abnormal";
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }

            bodyJson.put("cardID", c.getCardId());
            bodyJson.put("status", c.getCardStatus());
            return bodyJson;

        }
        //错误
        String message = "The card does not support unfreezing or freezing";
        bodyJson.put("error_message", message);
        logger.info("======调用错误===== " + message);
        return bodyJson;

    }

    /**
     * 卡 pin
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject getCardPin(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String cardID = jsonObject.getString("cardID");
        //先判断卡id是否存在
        CreditCard c = creditCardService.findByCardIdAndSysId(cardID, sysId);
        if (c == null) {
            String message = "Card id " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        } else {
            if (!CardLevel.PHYSICAL.getValue().equals(c.getCardLevel())) {
                String message = "Card id " + cardID + " not a physical card";
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
            String pin = "";
            if (StringUtils.isNotEmpty(c.getPin())) {
                pin = AESUtils.aesDecrypt(Constants.card_key, c.getPin());
                bodyJson.put("cardID", cardID);
                bodyJson.put("cardPin", pin);
                return bodyJson;

            }

            MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(c.getCardHolder(), sysId);
            String message = "";
            for (int i = 0; i < 3; i++) {
                ApiResponse<String> apiResponse = moonbankUtil.getCardPin(partnerUser.getUid(), Integer.valueOf(c.getBankCardId()));
                if (apiResponse.isSuccess()) {
                    String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

                    System.out.println("getCardPin encode result===>  true");
                    JSONObject j = JSON.parseObject(descStr);
                    pin = j.getString("pin");
                    break;
                } else {
                    message = apiResponse.getMessage();
                }
            }
            if (StringUtils.isNotEmpty(pin)) {
                bodyJson.put("cardID", cardID);
                bodyJson.put("cardPin", pin);
                creditCardService.updateCardPin(c.getCardId(), AESUtils.aesEncrypt(Constants.card_key, pin));
                return bodyJson;
            } else {
                //错误
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
        }
    }

    /**
     * 查询开卡结果
     *
     * @param jsonObject
     * @param sysId
     * @param requestNo
     * @return
     */
    @Override
    public JSONObject createCardQuery(JSONObject jsonObject, String sysId, String requestNo) {
        JSONObject bodyJson = new JSONObject();
        String cardID = jsonObject.getString("cardID");
        //先判断卡id是否存在
        CreditCard c = creditCardService.findByCardIdAndSysId(cardID, sysId);
        if (c == null) {
            String message = "Card id " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        MetaOrder metaOrder = metaOrderService.findByCardId(KzOrderType.open.getValue(), cardID);
        if (metaOrder != null && KzOrderStatus.create.getValue().equals(metaOrder.getType())) {
            bodyJson.put("cardID", c.getCardId());
            bodyJson.put("status", "AUDITING");
            return bodyJson;
        }

        if ("kun".equals(c.getSource())) {
            CreditCardApplication cardApplication = creditCardApplicationService.findByCardId(c.getCardId());
            if (cardApplication == null) {
                String message = "Data does not exist";
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
            List<MetaPartnerTxnDtl> metaPartnerTxnDtlList = metaPartnerTxnDtlService.findCardIdAndTransactionId(c.getCardId(), cardApplication.getRequestNo(), new String[]{PartnerTxnType.CardApplication.getValue()});
            if (metaPartnerTxnDtlList.size() < 0) {
                String message = "Data does not exist";
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
            JSONObject result = kunService.queryOpenCard(c, cardApplication.getRequestNo());
            if (result != null) {
                return result;
            }

        } else if ("moonbank".equals(c.getSource())) {
            bodyJson.put("cardID", c.getCardId());
            bodyJson.put("status", "APPROVED");
            return bodyJson;

        } else if (ChannelType.FORNAX.getValue().equals(c.getSource())) {
            JSONObject result = fornaxService.queryOrderOpenCard(cardID);

            if (result != null) {
                return result;
            } else {
                if ("AUDITING".equals(c.getCardStatus())) {
                    bodyJson.put("cardID", cardID);
                    bodyJson.put("transactionStatus", TxnStatus.PENDING.getName());
                    return bodyJson;
                } else if ("AUDIT_NOT_PASS".equals(c.getCardStatus())) {
                    bodyJson.put("cardID", cardID);
                    bodyJson.put("transactionStatus", TxnStatus.DECLINED.getName());
                    return bodyJson;
                }

            }

        }

        String message = "kazepay internal error";
        bodyJson.put("error_message", message);
        logger.info("======调用错误===== " + message);
        return bodyJson;
    }


    @Override
    public JSONObject rechargeQuery(JSONObject jsonObject, String sysId, String requestNo) {
        JSONObject bodyJson = new JSONObject();
        String cardID = jsonObject.getString("cardID");
        String transactionId = jsonObject.getString("transactionId");
        //先判断卡id是否存在
        CreditCard c = creditCardService.findByCardIdAndSysId(cardID, sysId);
        if (c == null) {
            String message = "Card id " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        if (c.getSource().startsWith("moonbank")) {

            JSONObject res = mcardService.rechargeQuery(transactionId, c);
            if (res == null) {
                String message = "Data query failed";
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;

            } else {
                return res;
            }

        } else if ("kun".equals(c.getSource())) {
            logger.info("查询kun卡充值情况");


            //判断流水是否存在
            List<MetaPartnerTxnDtl> metaPartnerTxnDtlList = metaPartnerTxnDtlService.findCardIdAndTransactionId(c.getCardId(), transactionId, new String[]{PartnerTxnType.CardTopup.getValue()});
            if (metaPartnerTxnDtlList.size() < 0) {
                String message = "Transaction ID " + transactionId + " does not exist";
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
            JSONObject result = kunService.queryCharge(transactionId, c);
            if (result != null) {
                return result;
            } else {
                return kunService.returnError(bodyJson, result);
            }


        } else if (ChannelType.FORNAX.getValue().equals(c.getSource())) {
            JSONObject result = fornaxService.queryOrderRechargeCard(transactionId);
            if (result != null) {
                bodyJson.put("cardID", c.getCardId());
                bodyJson.put("transactionId", transactionId);
                bodyJson.put("transactionStatus", result.getString("transactionStatus"));
                return bodyJson;
            } else {
                String message = transactionId + " does not exist";
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }

        }

        String message = "kazepay internal error";
        bodyJson.put("error_message", message);
        logger.info("======调用错误===== " + message);
        return bodyJson;
    }

    @Override
    public JSONObject approve3dsAuth(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String cardID = jsonObject.getString("cardID");
        String authId = jsonObject.getString("authId");
        //先判断卡id是否存在
        CreditCard c = creditCardService.findByCardIdAndSysId(cardID, sysId);
        if (c == null) {
            String message = "Card id " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        } else {
            if (!CardLevel.PHYSICAL.getValue().equals(c.getCardLevel())) {
                String message = "Card id " + cardID + " not a physical card";
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
            MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(c.getCardHolder(), sysId);
            ApiResponse<String> apiResponse = moonbankUtil.approve3dsAuth(authId, partnerUser.getUid());
            if (apiResponse.isSuccess()) {
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

                System.out.println("approve3dsAuth encode result===>  true");
                JSONObject j = JSON.parseObject(descStr);
                String authIdStr = j.getString("authId");
                String authResult = j.getString("authResult");
                bodyJson.put("authId", authIdStr);
                bodyJson.put("authResult", authResult);
                return bodyJson;
            } else {
                //错误
                String message = apiResponse.getMessage();
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
        }
    }

    @Override
    public JSONObject reject3dsAuth(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String cardID = jsonObject.getString("cardID");
        String authId = jsonObject.getString("authId");
        //先判断卡id是否存在
        CreditCard c = creditCardService.findByCardIdAndSysId(cardID, sysId);
        if (c == null) {
            String message = "Card id " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        } else {
            if (!CardLevel.PHYSICAL.getValue().equals(c.getCardLevel())) {
                String message = "Card id " + cardID + " not a physical card";
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
            MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(c.getCardHolder(), sysId);
            ApiResponse<String> apiResponse = moonbankUtil.reject3dsAuth(authId, partnerUser.getUid());
            if (apiResponse.isSuccess()) {
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

                System.out.println("reject3dsAuth encode result===>  true");
                JSONObject j = JSON.parseObject(descStr);
                String authIdStr = j.getString("authId");
                String authResult = j.getString("authResult");
                bodyJson.put("authId", authIdStr);
                bodyJson.put("authResult", authResult);
                return bodyJson;
            } else {
                //错误
                String message = apiResponse.getMessage();
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
        }
    }

    @Override
    public JSONObject query3dsAuth(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String cardID = jsonObject.getString("cardID");
        String authId = jsonObject.getString("authId");
        //先判断卡id是否存在
        CreditCard c = creditCardService.findByCardIdAndSysId(cardID, sysId);
        if (c == null) {
            String message = "Card id " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        } else {
            if (!CardLevel.PHYSICAL.getValue().equals(c.getCardLevel())) {
                String message = "Card id " + cardID + " not a physical card";
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
            MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(c.getCardHolder(), sysId);
            ApiResponse<String> apiResponse = moonbankUtil.query3dsAuth(authId, partnerUser.getUid());
            if (apiResponse.isSuccess()) {
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

                System.out.println("query3dsAuth encode result===>  true");
                JSONObject j = JSON.parseObject(descStr);
                String authIdStr = j.getString("authId");

                String cardPan = j.getString("cardPan");
                String txnCurrency = j.getString("txnCurrency");
                String txnAmount = j.getString("txnAmount");
                String cardAcceptorLocationName = j.getString("cardAcceptorLocationName");
                String cardAcceptorLocationCountry = j.getString("cardAcceptorLocationCountry");
                String authResult = j.getString("authResult");

                String expiredTime = j.getString("expiredTime");
                String createdTime = j.getString("createdTime");
                String updatedTime = j.getString("updatedTime");

                bodyJson.put("authId", authIdStr);
                bodyJson.put("cardID", c.getCardId());
                bodyJson.put("cardPan", cardPan);
                bodyJson.put("txnCurrency", txnCurrency);
                bodyJson.put("txnAmount", txnAmount);
                bodyJson.put("cardAcceptorLocationName", cardAcceptorLocationName);
                bodyJson.put("cardAcceptorLocationCountry", cardAcceptorLocationCountry);
                bodyJson.put("authResult", authResult);

                bodyJson.put("expiredTime", expiredTime);
                bodyJson.put("createdTime", createdTime);
                bodyJson.put("updatedTime", updatedTime);
                return bodyJson;
            } else {
                //错误
                String message = apiResponse.getMessage();
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
        }
    }

    @Override
    public JSONObject getEURAmount(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        BigDecimal amount = jsonObject.getBigDecimal("amount");
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            bodyJson.put("amount", BigDecimal.ZERO);
            return bodyJson;
        }

        MetaPartnerUser partnerUser = metaPartnerUserService.findData();
        ApiResponse<String> apiResponse = moonbankUtil.getEURAmount(amount, partnerUser.getUid());
        if (apiResponse.isSuccess()) {
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

            System.out.println("getEURAmount encode result===>  true");
            JSONObject j = JSON.parseObject(descStr);
            bodyJson.put("amount", j.getBigDecimal("amount"));
            return bodyJson;
        } else {
            //错误
            String message = apiResponse.getMessage();
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
    }

    @Override
    public JSONObject walletAddress(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String network = jsonObject.getString("network");
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartnerBySysId(sysId);
        if (metaPartnerAssetPool == null) {
            String message = "kazepay internal error";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
        }
        List<AddressVo> list = new ArrayList<>();

        if (StringUtils.isEmpty(network)) {
            trc(metaPartnerAssetPool.getId().getPartnerId(), list);
            Bep(metaPartnerAssetPool.getId().getPartnerId(), list);
        } else if ("TRC20".equals(network)) {

            trc(metaPartnerAssetPool.getId().getPartnerId(), list);
        } else if ("BEP20".equals(network)) {
            Bep(metaPartnerAssetPool.getId().getPartnerId(), list);

        }

        bodyJson.put("data", list);
        return bodyJson;
    }

    /**
     * //获取trc20的钱包地址
     *
     * @param cstId
     * @param list
     */
    private void trc(Long cstId, List<AddressVo> list) {

        //读取kazepay的钱包
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        String sysId = String.valueOf(accountId);
        List<TrcCstaddress> trclist = trcCstaddressService.findByCstId(cstId, sysId);
        for (TrcCstaddress trcCstaddress : trclist) {
            AddressVo addressVo = new AddressVo();
            addressVo.setAddress(trcCstaddress.getCstAddress());
            addressVo.setNetwork("TRC20");
            list.add(addressVo);
        }
    }

    /**
     * //获取bep20的钱包地址
     *
     * @param cstId
     * @param list
     */
    private void Bep(Long cstId, List<AddressVo> list) {

        //读取kazepay的钱包
        Long accountId = Long.valueOf(configService.selectConfigByKey("company_account_id"));
        String sysId = String.valueOf(accountId);
        List<BepCstaddress> beplist = bepCstaddressService.findByCstId(cstId, sysId);
        for (BepCstaddress bepCstaddress : beplist) {
            AddressVo addressVo = new AddressVo();
            addressVo.setAddress(bepCstaddress.getCstAdress());
            addressVo.setNetwork("BEP20");
            list.add(addressVo);
        }
    }

    @Override
    public JSONObject setWebhookUrl(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String url = jsonObject.getString("url");
        metaPartnerAssetPoolService.updateNotifyUrl(url, sysId);
        bodyJson.put("url", url);
        return bodyJson;
    }

    @Override
    public JSONObject deleteWebhookUrl(JSONObject jsonObject, String sysId) {

        JSONObject bodyJson = new JSONObject();
        metaPartnerAssetPoolService.updateNotifyUrl("", sysId);
        bodyJson.put("url", "");
        return bodyJson;
    }

    @Override
    public JSONObject getCoinExchangeRate(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String from = jsonObject.getString("from");
        String to = jsonObject.getString("to");

        bodyJson.put("from", from);
        bodyJson.put("to", to);

        if (("USD".equals(from) && "EUR".equals(to)) || ("USDT".equals(from) && "EUR".equals(to))) {
            MetaPartnerUser partnerUser = metaPartnerUserService.findData();
            BigDecimal eur = creditCardService.getEUR(partnerUser.getUid(), new BigDecimal("1"));
            if (eur != null && eur.compareTo(BigDecimal.ZERO) > 0) {
                bodyJson.put("rate", eur);
                return bodyJson;
            }

        } else if (("USD".equals(from) && "HKD".equals(to)) || ("USDT".equals(from) && "HKD".equals(to))) {
            BigDecimal price = kunService.getPrice("USDT", "HKD", KunPriceSide.SELL.getValue(), new BigDecimal("100"));
            if (price != null && price.compareTo(BigDecimal.ZERO) > 0) {
                bodyJson.put("rate", price);
                return bodyJson;
            }
        } else if (("HKD".equals(from) && "USD".equals(to)) || ("HKD".equals(from) && "USDT".equals(to))) {
            BigDecimal price = kunService.getPrice("USDT", "HKD", KunPriceSide.BUY.getValue(), new BigDecimal("100"));
            if (price != null && price.compareTo(BigDecimal.ZERO) > 0) {
                bodyJson.put("rate", kunService.exchangeUSDT(price));
                return bodyJson;
            }
        }

        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 将 Date 对象格式化为字符串
        String time = sdf.format(date);
        ExchangeRate exchangeRate = exchangeRateService.findByRate(from, to, time);
        if (exchangeRate == null) {
            bodyJson.put("rate", "");
        } else {
            bodyJson.put("rate", exchangeRate.getRateVal());
        }

        return bodyJson;
    }

    @Override
    public JSONObject getWebhookUrl(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartnerBySysId(sysId);
        bodyJson.put("url", metaPartnerAssetPool.getApiUrl());
        return bodyJson;
    }

    /**
     * 设置U卡的kyc
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject setUcardKyc(JSONObject jsonObject, String sysId) {


        JSONObject bodyJson = new JSONObject();
        String email = jsonObject.getString("email");
        String mobileNumber = jsonObject.getString("mobileNumber");
        String mobilePrefix = jsonObject.getString("mobilePrefix");
        CardConfig cc = cardConfigService.findCardPartnerConfig(sysId, "********", CardLevel.PHYSICAL.getValue());
        if (cc == null) {
            String message = RespCode.INTERNAL_ERROR.getValue();
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            // 显式清除大对象引用
            jsonObject.clear();
            return bodyJson;
        }
        //判断用户是否已经存在
        MetaPartnerUser metaPartnerUser = metaPartnerUserService.findEmailAndSysId(email, sysId);
        if (metaPartnerUser != null && StringUtils.isNotEmpty(metaPartnerUser.getUid())) {
            //设置用户信息
            bodyJson = ucardService.setUcardKycInfo(metaPartnerUser.getUid(), jsonObject, Integer.valueOf(cc.getBankCardId()), sysId);
            // 显式清除大对象引用
            jsonObject.clear();
            return bodyJson;
        } else {
            //不存在则注册
            ApiResponse<String> apiResponse = moonbankUtil.userRegister(mobilePrefix, mobileNumber, email);
            if (apiResponse.isSuccess()) {
                //注册获得uid
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
                logger.info("userRegister encode result===>" + descStr);
                JSONObject j = JSON.parseObject(descStr);
                String uid = j.getString("uid");
                if (metaPartnerUser == null) {
                    saveUser(sysId, email, mobilePrefix, mobileNumber, uid);
                } else {
                    //更新moonbank_uid
                    metaPartnerUserService.updatePartnerUser(metaPartnerUser.getId(), uid);
                }
                //设置用户信息
                bodyJson = ucardService.setUcardKycInfo(uid, jsonObject, Integer.valueOf(cc.getBankCardId()), sysId);
                if (bodyJson != null) {
                    String status = bodyJson.getString("status");
                    if ("WAITING_KYC".equals(status)) {
                        JSONObject jo = new JSONObject();
                        jo.put("type", "kyc_delayed");
                        jo.put("cardType", PhysicalCardType.SingaporeUCard_10.getType());
                        jo.put("uid", uid);
                        jo.put("sysId", sysId);
                        jo.put("id", UniqueIDGenerator.generateID());
                        //发送消息到mq的队列

                        mqDlxutils.sendJson(jo, Constants.routing_key_dlx, 3);

                    }
                }
                // 显式清除大对象引用
                jsonObject.clear();
                return bodyJson;
            } else {
                String message = apiResponse.getMessage();
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                // 显式清除大对象引用
                jsonObject.clear();
                return bodyJson;
            }
        }
    }

    @Override
    public JSONObject setUcardKycNew(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String email = jsonObject.getString("email");
        String mobileNumber = jsonObject.getString("mobileNumber");
        String mobilePrefix = jsonObject.getString("mobilePrefix");
        CardConfig cc = cardConfigService.findCardPartnerConfig(sysId, "********", CardLevel.PHYSICAL.getValue());
        if (cc == null) {
            String message = RespCode.INTERNAL_ERROR.getValue();
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            // 显式清除大对象引用
            jsonObject.clear();
            return bodyJson;
        }
        //判断用户是否已经存在
        MetaPartnerUser metaPartnerUser = metaPartnerUserService.findEmailAndSysId(email, sysId);
        if (metaPartnerUser != null && StringUtils.isNotEmpty(metaPartnerUser.getUid())) {
            //设置用户信息
            bodyJson = ucardService.setUcardKycInfoNew(metaPartnerUser.getUid(), jsonObject, Integer.valueOf(cc.getBankCardId()), sysId);
            return bodyJson;
        } else {
            //不存在则注册
            ApiResponse<String> apiResponse = moonbankUtil.userRegister(mobilePrefix, mobileNumber, email);
            if (apiResponse.isSuccess()) {
                //注册获得uid
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
                logger.info("userRegister encode result===>" + descStr);
                JSONObject j = JSON.parseObject(descStr);
                String uid = j.getString("uid");
                if (metaPartnerUser == null) {
                    saveUser(sysId, email, mobilePrefix, mobileNumber, uid);
                } else {
                    //更新moonbank_uid
                    metaPartnerUserService.updatePartnerUser(metaPartnerUser.getId(), uid);
                }
                //设置用户信息
                bodyJson = ucardService.setUcardKycInfoNew(uid, jsonObject, Integer.valueOf(cc.getBankCardId()), sysId);
                if (bodyJson != null) {
                    String status = bodyJson.getString("status");
                    if ("WAITING_KYC".equals(status)) {
                        JSONObject jo = new JSONObject();
                        jo.put("type", "kyc_delayed");
                        jo.put("cardType", PhysicalCardType.SingaporeUCard_10.getType());
                        jo.put("uid", uid);
                        jo.put("sysId", sysId);
                        jo.put("id", UniqueIDGenerator.generateID());
                        //发送消息到mq的队列
                        logger.info("kyc的结果推送到mq");
                        mqDlxutils.sendJson(jo, Constants.routing_key_dlx, 3);

                    }
                }
                return bodyJson;
            } else {
                String message = apiResponse.getMessage();
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                // 显式清除大对象引用
                jsonObject.clear();
                return bodyJson;
            }
        }
    }

    /**
     * U卡kyc查询
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject queryUcardKycStatus(JSONObject jsonObject, String sysId) {

        return ucardService.getUcardKycStatue(jsonObject, sysId);
    }


    /**
     * U卡分配
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject ucardAssign(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String email = jsonObject.getString("email");
        String cardNumber = jsonObject.getString("cardNumber");
        String lastNo = cardNumber.substring(cardNumber.length() - 4, cardNumber.length());
        boolean autoActive = jsonObject.getBoolean("autoActive");

        //判断卡片是否入库，未入库不让进行激活
        MetaPhysicalCard physicalCard = metaPhysicalCardService.findByCardNoAndSysId(cardNumber, sysId);
        if (physicalCard == null) {
            String message = "Card number does not exist!!";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }

        //判断卡号是否已经有激活码
        String cardNo = AESUtils.aesEncrypt(Constants.card_key, cardNumber);
        MetaUcardCode code = metaUcardCodeService.findByNo(cardNo);
        if (code != null && StringUtils.isNotEmpty(code.getCode())) {
            String message = "This card number has already obtained an activation code";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        MetaPartnerUser metaPartnerUser = metaPartnerUserService.findEmailAndSysId(email, sysId);
        if (metaPartnerUser == null) {
            String message = "Please create kyc information first";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        } else {
            CardConfig cc = cardConfigService.findCardConfig(sysId, PhysicalCardType.SingaporeUCard_10.getType(), CardLevel.PHYSICAL.getValue());
            //判断用户的实体卡数量是否超过5
            List<CreditCard> list = creditCardService.findCardHolderAndCardType(email, cc.getCardType());
            if (list.size() >= 5) {
                String message = "A user can only have up to 5 physical cards";
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }

            MetaPartnerAssetPool metaPartnerAssetPool = metaPartnerAssetPoolService.selectPartnerBySysId(sysId);
            ApiResponse<String> apiResponse = moonbankUtil.ucardAssign(metaPartnerUser.getUid(), cardNumber, cc.getBankCardId(), autoActive);

            if (apiResponse.isSuccess()) {
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());

                logger.info("ucardAssign encode result===>" + descStr);
                JSONObject j = JSON.parseObject(descStr);
                String bankCardId = j.getString("userBankcardId");
                String cardId = creditCardService.generateCardId();
                if (!autoActive) {
                    MetaUcardCode metaUcardCode = new MetaUcardCode();
                    metaUcardCode.setCardNo(AESUtils.aesEncrypt(Constants.card_key, cardNumber));
                    metaUcardCode.setCardNoLast(lastNo);
                    metaUcardCode.setCreateTime(new Date());
                    metaUcardCode.setUid(metaPartnerUser.getUid());
                    metaUcardCode.setSysId(sysId);
                    metaUcardCode.setCardId(cardId);
                    metaUcardCode.setEmail(email);
                    metaUcardCodeService.saveMetaUcardCode(metaUcardCode);
                }

                bodyJson.put("cardID", cardId);

                String cardStatus = "TBA";
                //新增卡片
                newPhysicalCard(sysId, email, AESUtils.aesEncrypt(Constants.card_key, cardNumber), metaPartnerUser, cc.getCardBin(), cc, metaPartnerAssetPool, UniqueIDGenerator.generateID(), cardId, cardStatus, bankCardId);

                if (autoActive) {
                    //更新入库的卡为激活
                    metaPhysicalCardService.updateActive(cardNumber);
                }
                return bodyJson;

            } else {
                String message = apiResponse.getMessage();

                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
        }
    }

    /**
     * U卡激活
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject ucardActive(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String cardID = jsonObject.getString("cardID");
        String activationCode = jsonObject.getString("activationCode");

        //先判断卡id是否存在
        CreditCard c = creditCardService.findByCardIdAndSysId(cardID, sysId);
        if (c == null) {
            String message = "Card id " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }

        MetaUcardCode mc = metaUcardCodeService.fingByTemporaryCardID(cardID);
        if (mc == null) {
            String message = "Temporary Card ID " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }

        MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(mc.getEmail(), sysId);

        ApiResponse<String> apiResponse = moonbankUtil.ucardActive(partnerUser.getUid(), c.getBankCardId(), activationCode);
        if (apiResponse.isSuccess()) {
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
            logger.info("ucardActive encode result===>" + descStr);
            JSONObject j = JSONObject.parseObject(descStr);

            String cardStatus = j.getString("cardStatus");
            bodyJson.put("cardID", c.getCardId());
            bodyJson.put("status", cardStatus);

            //更新卡片状态
            creditCardService.updateStatus(c.getCardId(), cardStatus);
            //更新入库的卡为激活
            String cardNo = AESUtils.aesDecrypt(Constants.card_key, mc.getCardNo());
            metaPhysicalCardService.updateActive(cardNo);

            return bodyJson;
        } else {

            String message = apiResponse.getMessage();
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);

            return bodyJson;
        }
    }

    /**
     * 设置卡的ATM密码
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject cardSetPin(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String cardID = jsonObject.getString("cardID");
        String pin = jsonObject.getString("pin");

        //先判断卡id是否存在
        CreditCard c = creditCardService.findByCardIdAndSysId(cardID, sysId);
        if (c == null) {
            String message = "Card id " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        MetaPartnerUser partnerUser = metaPartnerUserService.findEmailAndSysId(c.getCardHolder(), sysId);
        ApiResponse<String> apiResponse = moonbankUtil.setBankcardPin(partnerUser.getUid(), Integer.valueOf(c.getBankCardId()), pin);
        if (apiResponse.isSuccess()) {
            String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
            logger.info("setBankcardPin encode result===>" + descStr);

            c.setPin(AESUtils.aesEncrypt(Constants.card_key, pin));
            creditCardService.updateCardPin(c.getCardId(), c.getPin());
            return bodyJson;


        }

        String message = apiResponse.getMessage();
        bodyJson.put("error_message", message);
        logger.info("======调用错误===== " + message);
        return bodyJson;

    }

    /**
     * 卡片解锁
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject cardUnlock(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String cardID = jsonObject.getString("cardID");

        //先判断卡id是否存在
        CreditCard c = creditCardService.findByCardIdAndSysId(cardID, sysId);
        if (c == null) {
            String message = "Card id " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        if ("89".equals(c.getCardType())) {
            JSONObject json = kunService.cardUnlock(c, bodyJson);
            return json;
        }
        String message = " The card does not support card Unlock.";
        bodyJson.put("error_message", message);
        logger.info("======调用错误===== " + message);
        return bodyJson;
    }


    /**
     * 创建用户钱包地址
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject createWalletAddress(JSONObject jsonObject, String sysId) {

        Long userId = jsonObject.getLong("userId");

        String netWork = jsonObject.getString("netWork");
        JSONObject bodyJson = new JSONObject();
        if ("TRC20".equals(netWork)) {
            List<TrcCstaddress> trcCstaddressList = trcCstaddressService.findByCstId(userId, sysId);
            if (trcCstaddressList.size() > 0) {
                bodyJson.put("address", trcCstaddressList.get(0).getCstAddress());
                return bodyJson;
            } else {
                String address = trcCstaddressService.createWallet(userId, sysId, WalletConfig.key);
                if (StringUtils.isNotEmpty(address)) {
                    bodyJson.put("address", address);
                    return bodyJson;
                }

            }
        } else if ("BEP20".equals(netWork)) {
            List<BepCstaddress> list = bepCstaddressService.findByCstId(userId, sysId);
            if (list.size() > 0) {
                bodyJson.put("address", list.get(0).getCstAdress());
                return bodyJson;
            }
            logger.info("找不到bep数据");

            String address = bepCstaddressService.createWallet(userId, "USDT", sysId, WalletConfig.key);
            if (StringUtils.isNotEmpty(address)) {
                bodyJson.put("address", address);
                return bodyJson;
            }
        }

        String message = "钱包服务失败";
        bodyJson.put("error_message", message);
        logger.info("======调用错误===== " + message);
        return bodyJson;
    }

    @Override
    public JSONObject collectWalletAddress(JSONObject jsonObject, String sysId) {
        String address = jsonObject.getString("address");
        String netWork = jsonObject.getString("netWork");
        String coinType = jsonObject.containsKey("coinType")
                ? jsonObject.getString("coinType")
                : "USDT";
        JSONObject bodyJson = new JSONObject();
        //trc20钱包归集调用地址
        String trcCollectUrl = configService.selectConfigByKey("meta_waller_url") + "user/guijiyuer";
        //bep20钱包归集调用地址
        String bepCollectUrl = configService.selectConfigByKey("web3j_bep20_url");

        if ("TRC20".equals(netWork)) {
            AjaxResult ajaxResult = trcCstaddressService.trcCollect(address, trcCollectUrl, coinType);
            if (ajaxResult.get("code").equals(200)) {
                bodyJson.put("result", "success");
                return bodyJson;
            }
        } else if ("BEP20".equals(netWork) || "ERC20_ARB".equals(netWork) || "ERC20_BASE".equals(netWork)) {
            AjaxResult ajaxResult = bepCstaddressService.bepCollect(WalletConfig.key, bepCollectUrl, address, netWork, coinType);
            if (ajaxResult.get("code").equals(200)) {
                bodyJson.put("result", "success");
                return bodyJson;
            }
        }


        String message = "钱包服务失败";
        bodyJson.put("error_message", message);
        logger.info("======调用错误===== " + message);
        return bodyJson;
    }

    /**
     * 4146旧卡换新卡的接口
     *
     * @param jsonObject
     * @param sysId
     * @return
     */
    @Override
    public JSONObject oldCardTransferNewCard(JSONObject jsonObject, String sysId) {
        String cardId = jsonObject.getString("cardID");
        //判断该时间段是否开放
        boolean b = creditCardService.checkShowExchange(cardId);
        JSONObject bodyJson = new JSONObject();
        if (!b) {
            String message = "Not open yet";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;

        }
        if (StringUtils.isNotEmpty(cardId)) {
            CreditCard c = creditCardService.findByCardId(cardId);
            if ("87".equals(c.getCardType())) {
                if (c.getOldBalance().compareTo(BigDecimal.ZERO) <= 0) {
                    String message = "No amount to transfer";
                    bodyJson.put("error_message", message);
                    logger.info("======调用错误===== " + message);
                    return bodyJson;
                }
                MetaCreditCardUold cardUold = metaCreditCardUoldService.findByOldId(cardId);
                if (cardUold != null) {
                    String newCardId = cardUold.getCardIdNew();

                    //判断旧卡是否状态正常
                    if (CardStatus.CLOSE.getValue().equals(c.getCardStatus())) {
                        //充值到新卡
                        if (exchangeToCard(cardId, bodyJson, c, newCardId)) return bodyJson;


                    } else {
                        JSONObject cardInfo = kunService.getCardInfo(new JSONObject(), c, null);
                        if (cardInfo != null) {
                            String userBankCardStatus = cardInfo.getString("userBankCardStatus");
                            if (CardStatus.CLOSE.getValue().equals(userBankCardStatus)) {
                                //充值到新卡
                                if (exchangeToCard(cardId, bodyJson, c, newCardId)) return bodyJson;
                            }
                        }
                    }
                }

            }
        }
        String message = "This card does not support";
        bodyJson.put("error_message", message);
        logger.info("======调用错误===== " + message);
        return bodyJson;
    }

    private boolean exchangeToCard(String cardId, JSONObject bodyJson, CreditCard c, String newCardId) {
        String kun_customerId = configService.selectConfigByKey("kun_customerId");
        String send_kun_ip = KunConfig.ip;
        String kun_environment = KunConfig.environment;
        String requestNoKun = UniqueIDGenerator.generateID();
        JSONObject jsonKun = new JSONObject();
        jsonKun.put("customerId", kun_customerId);
        jsonKun.put("requestNo", requestNoKun);
        CreditCard cardNew = creditCardService.findByCardId(newCardId);
        jsonKun.put("cardId", cardNew.getBankCardId());

        jsonKun.put("currency", "HKD");
        jsonKun.put("amount", c.getOldBalance());
        jsonKun.put("ip", send_kun_ip);
        String content = "旧卡Id:" + cardId + ",充值金额oldbalance:" + c.getOldBalance();
        logger.info(content);
        JSONObject result = kunUtils.result(kun_environment, jsonKun.toJSONString(), requestNoKun, KunMethod.kcard_recharge, "POST", cardNew.getBankCardId());
        if ("000000".equals(result.getString("code"))) {

            //新增流水Id
            String status = result.getString("status");
            if (StringUtils.isEmpty(status)) {
                status = KunOrderStatus.PROCESSING.getValue();
            }
            Character orderStatus = kunService.getOrderStatus(status);
            if (TxnStatus.PENDING.getValue() == orderStatus) {
                //将oldbalance的余额置为0
                creditCardService.updateOldCard(cardId, BigDecimal.ZERO, CardStatus.CLOSE.getValue());
                //处理中
                creditCardService.oldCardTransferNewCard(VccTxnType.card_deposit.getValue(), requestNoKun, "HKD", c.getOldBalance(), "HKD", c.getOldBalance(), newCardId, "processing", BigDecimal.ZERO, "HKD");

                bodyJson.put("cardID", cardId);
                bodyJson.put("transactionId", requestNoKun);
                bodyJson.put("transactionStatus", TxnStatus.PENDING.getName());
                return true;

            } else if (TxnStatus.SUCCESS.getValue() == orderStatus) {
                //将oldbalance的余额置为0
                creditCardService.updateOldCard(cardId, BigDecimal.ZERO, CardStatus.CLOSE.getValue());
                //保留流水Id
                creditCardService.oldCardTransferNewCard(VccTxnType.card_deposit.getValue(), requestNoKun, "HKD", c.getOldBalance(), "HKD", c.getOldBalance(), newCardId, "success", BigDecimal.ZERO, "HKD");
                //更新卡片信息
                baseCardCommon.updateCardKun(newCardId);
                bodyJson.put("cardID", cardId);
                bodyJson.put("transactionId", requestNoKun);
                bodyJson.put("transactionStatus", TxnStatus.APPROVED.getName());
                return true;
            } else if (TxnStatus.FAIL.getValue() == orderStatus) {
                creditCardService.oldCardTransferNewCard(VccTxnType.card_deposit.getValue(), requestNoKun, "HKD", c.getOldBalance(), "HKD", c.getOldBalance(), newCardId, "fail", BigDecimal.ZERO, "HKD");
                bodyJson.put("cardID", cardId);
                bodyJson.put("transactionId", requestNoKun);
                bodyJson.put("transactionStatus", TxnStatus.DECLINED.getName());
                return true;
            }


        } else {
            sendEmail.remindMail("旧卡金额充值新卡异常", content);
        }
        return false;
    }

    @Override
    public JSONObject uploadFile(String sysId, String email, String apicode, String mobilePrefix, String mobileNumber, MultipartFile file) {

        MetaPartnerUser metaPartnerUser = metaPartnerUserService.findEmailAndSysId(email, sysId);
        JSONObject bodyJson = new JSONObject();
        if (metaPartnerUser != null && StringUtils.isNotEmpty(metaPartnerUser.getUid())) {

            dealFile(metaPartnerUser.getUid(), file, bodyJson);
            return bodyJson;
        } else {
            //不存在则注册
            ApiResponse<String> apiResponse = moonbankUtil.userRegister(mobilePrefix, mobileNumber, email);
            if (apiResponse.isSuccess()) {
                //注册获得uid
                String descStr = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, apiResponse.getResult());
                logger.info("userRegister encode result===>" + descStr);
                JSONObject j = JSON.parseObject(descStr);
                String uid = j.getString("uid");
                if (metaPartnerUser == null) {
                    metaPartnerUser = savePartnerUser(sysId, email, mobilePrefix, mobileNumber, uid);
                } else {
                    metaPartnerUser.setUid(uid);
                    //更新moonbank_uid
                    metaPartnerUserService.updatePartnerUser(metaPartnerUser.getId(), metaPartnerUser.getUid());
                }
                dealFile(metaPartnerUser.getUid(), file, bodyJson);
                return bodyJson;
            } else {
                String message = apiResponse.getMessage();
                bodyJson.put("error_message", message);
                logger.info("======调用错误===== " + message);
                return bodyJson;
            }
        }
    }

    /**
     * 处理图片
     *
     * @param uid
     * @param file
     * @param bodyJson
     */
    private void dealFile(String uid, MultipartFile file, JSONObject bodyJson) {

        ResponseEntity<String> responseEntity = moonbankUtil.uCardUpload(uid, file);
        String fileId = "";
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            JSONObject jf = JSON.parseObject(responseEntity.getBody());
            logger.info("uplod uCardFile result===>" + responseEntity.getBody());
            if (jf.getBoolean("success")) {
                if (StringUtils.isNotEmpty(jf.getString("result"))) {
                    String desc = MoonbankEncryptUtil.decode(MoonbankConfig.appSecret, jf.getString("result"));

                    if (desc.contains("documentFileId")) {
                        JSONObject jf2 = JSON.parseObject(desc);
                        fileId = jf2.getString("documentFileId");

                    }

                }

            }

        } else {
            String message = responseEntity.getBody();
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);

        }

        if (StringUtils.isEmpty(fileId)) {
            bodyJson.put("error_message", "Internal system error");
        } else {
            bodyJson.put("fileId", fileId);
        }


    }

    @Override
    public JSONObject cardSetting(JSONObject jsonObject, String sysId) {

        JSONObject bodyJson = new JSONObject();
        String cardID = jsonObject.getString("cardID");

        //先判断卡id是否存在
        CreditCard c = creditCardService.findByCardIdAndSysId(cardID, sysId);
        if (c == null) {
            String message = "Card id " + cardID + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }

        if (!"89".equals(c.getCardType())) {
            String message = "The card does not support modification";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        String email = jsonObject.getString("email");
        String phoneNumber = jsonObject.getString("mobileNumber");
        String areaCode = jsonObject.getString("mobilePrefix");
        JSONObject json = kunService.cardSetting(cardID, bodyJson, email, phoneNumber, areaCode);
        if (json != null) {
            return json;
        } else {
            bodyJson.put("error_message", "Failed to modify card information");
            return bodyJson;
        }

    }

    @Override
    public JSONObject setcardholder(JSONObject jsonObject, String sysId) {
        JSONObject bodyJson = new JSONObject();
        String firstName = jsonObject.getString("firstName");
        String lastName = jsonObject.getString("lastName");
        String email = jsonObject.getString("email");
        String mobilePrefix = jsonObject.getString("mobilePrefix");
        String mobileNumber = jsonObject.getString("mobileNumber");
        String birthDate = jsonObject.getString("birthDate");
        String countryCode = jsonObject.getString("countryCode");
        String billingState = jsonObject.getString("billingState");
        String billingCity = jsonObject.getString("billingCity");
        String billingAddress = jsonObject.getString("billingAddress");
        String billingZipCode = jsonObject.getString("billingZipCode");

        MetaPartnerUser metaPartnerUser = metaPartnerUserService.findEmailAndSysId(email, sysId);
        if (metaPartnerUser == null) {
            savePartnerUser(sysId, email, mobilePrefix, mobileNumber, "");
        }

        MetaFxUserInfo metaFxUserInfo = new MetaFxUserInfo();
        metaFxUserInfo.setFirstName(firstName);
        metaFxUserInfo.setLastName(lastName);
        metaFxUserInfo.setEmail(email);
        metaFxUserInfo.setMobile(mobileNumber);
        metaFxUserInfo.setMobilePrefix(mobilePrefix);
        metaFxUserInfo.setBirthDate(birthDate);
        metaFxUserInfo.setCountryCode(countryCode);
        metaFxUserInfo.setBillingState(billingState);
        metaFxUserInfo.setBillingCity(billingCity);
        metaFxUserInfo.setBillingAddress(billingAddress);
        metaFxUserInfo.setBillingZipCode(billingZipCode);
        boolean save = metaFxUserInfoService.save(metaFxUserInfo);
        if (save) {
            MetaFxUserInfo newfx = metaFxUserInfoService.findByEmail(email);
            bodyJson.put("cardUserId", newfx.getFid());
            return bodyJson;
        } else {

            String message = "User information setting failed";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }

    }

    @Override
    public JSONObject getCardholder(JSONObject jsonObject, String sysId) {

        JSONObject bodyJson = new JSONObject();
        String email = jsonObject.getString("email");
        MetaPartnerUser metaPartnerUser = metaPartnerUserService.findEmailAndSysId(email, sysId);
        if (metaPartnerUser == null) {
            String message = email + " does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        }
        MetaFxUserInfo fxUserInfo = metaFxUserInfoService.findByEmail(email);
        if (fxUserInfo == null) {
            String message = "User information does not exist";
            bodyJson.put("error_message", message);
            logger.info("======调用错误===== " + message);
            return bodyJson;
        } else {
            bodyJson.put("firstName", fxUserInfo.getFirstName());
            bodyJson.put("lastName", fxUserInfo.getLastName());
            bodyJson.put("email", fxUserInfo.getEmail());
            bodyJson.put("mobilePrefix", fxUserInfo.getMobilePrefix());
            bodyJson.put("mobileNumber", fxUserInfo.getMobile());
            bodyJson.put("birthDate", fxUserInfo.getBirthDate());
            bodyJson.put("countryCode", fxUserInfo.getCountryCode());
            bodyJson.put("billingState", fxUserInfo.getBillingState());
            bodyJson.put("billingCity", fxUserInfo.getBillingCity());
            bodyJson.put("billingAddress", fxUserInfo.getBillingAddress());
            bodyJson.put("billingZipCode", fxUserInfo.getBillingZipCode());
            bodyJson.put("cardUserId", fxUserInfo.getFid());
            return bodyJson;
        }


    }
}
