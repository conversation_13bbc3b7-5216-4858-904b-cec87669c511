package com.meta.api.es.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meta.api.es.utils.MQDelayedUtils;
import com.meta.api.es.utils.MQutils;
import com.meta.common.constant.Constants;
import com.meta.common.utils.StringUtils;
import com.meta.system.domain.groupkey.MetaPartnerAssetPoolKey;
import com.meta.system.domain.partner.MetaPartnerUser;
import com.meta.system.domain.partner.MetaPushData;
import com.meta.system.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/03/24/2:34
 */
@Slf4j
@Component
public class MessageReceiver {

    @Autowired
    private UcardService ucardService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private MQDelayedUtils mqDelayedUtils;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private MQutils mQutils;

    @Autowired
    private MetaPushDataService metaPushDataService;

    @Autowired
    private MetaPartnerUserService metaPartnerUserService;

    @Autowired
    private MetaPartnerAssetPoolService metaPartnerAssetPoolService;

//    /**
//     * 延迟队列（需要mq能支持延迟队列的插件）
//     * @param message
//     * @throws Exception
//     */
//    @RabbitListener(queues = "queue.delayed")
//    @Transactional
//    public void receiveMessage(JSONObject message) throws Exception {
//
//        try {
//            log.info("Received delayed message: " + message);
//            String type = message.getString("type");
//            //kyc数据处理
//            if ("kyc_delayed".equals(type)) {
//                String cardType = message.getString("cardType");
//                String uid = message.getString("uid");
//                String sysId = message.getString("sysId");
//                JSONObject data = ucardService.getUcardKyc(cardType, uid);
//                if (data != null) {
//                    String status = data.getString("kycStatus");
//                    String waitKey = Constants.KYC_WAIT_COUNT + uid;
//                    String kycReason = data.getString("reason");
//                    if ("WAITING_KYC".equals(status)) {
//
//
//                        if (redisTemplate.hasKey(waitKey)) {
//                            //重新进入等待的次数
//                            Long num = Long.valueOf("3");
//                            Long count = redisTemplate.opsForValue().increment(waitKey);
//                            if (count > num) {
//                                redisTemplate.delete(waitKey);
//                                redisTemplate.delete(waitKey);
//                            } else {
//                                String kyc_check_time = sysConfigService.selectConfigByKey("kyc_check_time");
//                                int time = Integer.valueOf(kyc_check_time.replaceAll(" ", ""));
//                                mqDelayedUtils.sendDelayedMessage(Constants.exchange_name_delayed, Constants.routing_key_delayed, message, time * 60 * 1000, 3);
//                            }
//                        }
//                    } else {
//                        redisTemplate.delete(waitKey);
//                        redisTemplate.delete(waitKey);
//
//                        MetaPartnerUser partnerUser = metaPartnerUserService.findUid(uid);
//                        MetaPartnerAssetPoolKey metaPartnerAssetPool = metaPartnerAssetPoolService.selectBySysId(sysId);
//                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//
//                        JSONObject jsonData = new JSONObject();
//                        jsonData.put("email", partnerUser.getEmail());
//                        jsonData.put("time", simpleDateFormat.format(new Date()));
//                        jsonData.put("status", status);
//                        if (StringUtils.isNotEmpty(kycReason)) {
//                            jsonData.put("reason", kycReason);
//                        } else {
//                            jsonData.put("reason", "");
//                        }
//                        log.info("推送kyc的审核结果："+jsonData);
//                        //发送通知
//                        pushData("USER_KYC_STATUS_CHANGE", sysId, "", partnerUser.getEmail(), metaPartnerAssetPool.getApiUrl(), jsonData);
//
//                    }
//                }
//            }
//
//        } catch (Exception e) {
//            log.error(e.getMessage());
//            throw new Exception(e);
//        }
//    }

//    @Transactional
//    public void pushData(String type, String sysId, String cardId, String userEmail, String apiUrl, JSONObject json) {
//
//        MetaPushData metaPushData = metaPushDataService.creatPashData(sysId, cardId, userEmail, apiUrl, JSON.toJSONString(json), type);
//        metaPushDataService.save(metaPushData);
//        if (StringUtils.isNotEmpty(apiUrl)) {
//            // 发送消息（确保在事务提交后发送）
//            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
//                @Override
//                public void afterCommit() {
//                    String routingKey = "";
//                    if ("USER_KYC_STATUS_CHANGE".equals(type)) {
//                        routingKey = Constants.routing_key_card_kyc;
//                    }
//                    mQutils.send(metaPushData, routingKey, 3);
//                }
//            });
//        }
//
//    }

}
