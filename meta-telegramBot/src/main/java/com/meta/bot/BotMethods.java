package com.meta.bot;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.api.methods.send.SendPhoto;
import org.telegram.telegrambots.meta.api.methods.updatingmessages.EditMessageText;
import org.telegram.telegrambots.meta.api.objects.CallbackQuery;
import org.telegram.telegrambots.meta.api.objects.InputFile;
import org.telegram.telegrambots.meta.api.objects.Message;
import org.telegram.telegrambots.meta.api.objects.replykeyboard.InlineKeyboardMarkup;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;

/**
 * <AUTHOR>
 * @Date 2024/12/6
 */

@Service
public class BotMethods {

    private final BotSettings botSettings;
    private final ButtonSettings buttonSettings;

    public BotMethods(@Lazy BotSettings botSettings ,ButtonSettings buttonSettings) {
        this.botSettings = botSettings;
        this.buttonSettings = buttonSettings;
    }

    public void message(Message message) {
        Long chatId = message.getChatId();
        Long userId = message.getFrom().getId();
//        if (Template.CREATOR_ID.equals(userId.toString()))
//            adminMessage(message, chatId, userId);
//        else userMessage(message, chatId, userId);
        userMessage(message, chatId, userId);
    }

    public void callbackData(CallbackQuery callbackQuery) {
        Long userId = callbackQuery.getMessage().getChatId();
        String data = removeIcon(callbackQuery.getData());
        SendMessage sm = new SendMessage(userId.toString(), data);
        System.out.println("data: " + data);
//        if (Template.CREATOR_ID.equals(userId.toString()))
//            adminCallback(userId, data, sm, callbackQuery.getMessage().getMessageId(), callbackQuery.getId());
//        else userCallback(userId, data, sm, callbackQuery.getMessage().getMessageId());
        userCallback(userId, data, sm, callbackQuery.getMessage().getMessageId());
    }

    public void userMessage(Message message, Long chatId, Long userId) {
        if (message.hasText()) {
            SendMessage sm = new SendMessage(chatId.toString(), message.getText());
            String text = removeIcon(message.getText());
            switch (text) {
                case Template.START -> {
                    echoByStartCommand(sm);
                }
                case Template.MY -> {
                    echoByMyCommand(sm);
                }
                default -> {
                    sendMSG(sm, "Habaringiz adminga yuborildi.");
                    sm.setChatId(Template.CREATOR_ID);
                    sendMSG(sm, "yangi taklif qoldirildi\n\nFirstName: " + message.getFrom().getFirstName()
                            + "\nMessage: " + message.getText());
                }
            }
        }
    }

    public void userCallback(Long userId, String data, SendMessage sm, Integer messageId) {
//        switch (data) {
//            case Template.RECHARGE -> {
//                editCallbackByRecharge(userId, messageId, sm);
//                editCallbackQuery(userId, messageId, "Mavzuni tanlang",
//                        buttonSettings.getInlineMarkup(subjectService.getSubjectByScience(data)));
//            }
//        }
    }


    public void editCallbackQuery(Long chatId, Integer messageId, String text, InlineKeyboardMarkup newInlineKeyboard) {
        try {
            botSettings.execute(EditMessageText.builder()
                    .chatId(chatId.toString())
                    .messageId(messageId)
                    .text(text)
                    .replyMarkup(newInlineKeyboard)
                    .build());
        } catch (TelegramApiException e) {
            System.out.println(e.getMessage());
        }
    }


    private void echoByMyCommand(SendMessage sm) {
        sm.setReplyMarkup(buttonSettings.getInlineMarkup(Template.MY_INLINE_BUTTON));
        sm.setParseMode("HTML");
        String htmlMessage = """
                账户信息：
                
                账户ID： 5059707517
                用户名： xxxx
                邮箱：<EMAIL> (用户未绑定邮箱情况下，提示信息：立即绑定)
                卡片数量： 0 张
                剩余可开卡数量： 3 张  (要更多卡片,需联系客服申请)
                账户等级：\s
                
                账户余额：
                美金(USD)： 0.000   \s
                
                节点：
                邀请下级开卡返佣金额： 0  USD\s
                邀请下级卡充值返佣金额： 0  USD\s
                邀请下级人数统计： 0  人
                """;
        sendMSG(sm, htmlMessage);
    }

    private void echoByStartCommand(SendMessage sm) {
        sm.setReplyMarkup(buttonSettings.getKeyboardButton(Template.START_REPLY_BUTTON));
        sm.setReplyMarkup(buttonSettings.getInlineMarkup(Template.START_REPLY_BUTTON));
        sm.setParseMode("HTML");
        String htmlMessage = """
                    <b>欢迎使用 @KarismaBot</b>
                    
                    <b>自助开卡充值：</b>
                    无需人工协助，用户可自行免费注册，后台自助实现入金、开卡、充值、销卡、查询等操作，支持无限开卡、在线接码。
                    
                    <b>BIN号丰富：</b>
                    拥有全球多地区国家VISA、MASTER信用卡，多达26种卡bin供您选择，全系卡bin支持AVS验证。
                    
                    <b>全场景支持：</b>
                    店租（Amazon、Shopify、Shope等）、广告（Facebook、Google等）、云服务、游戏、开发者应用、海淘购物等全场景支付。
                    ✅支持 OpenAi 人工智能 chatGPT PLUS 开通
                    ✅支持绑定支付宝进行线上消费
                    ✅支持 开通Telegram飞机会员
                    ✅支持USDT和人民币充值
                    
                    
                    频道 <a href=""> @pikabao </a>
                    社群 <a href=""> @pikabao0 </a>
                    客服 <a href=""> @pikabao1 </a>
                    """;
        sendMSG(sm, htmlMessage);
//        sendPhoto(Long.valueOf(sm.getChatId()), "src/main/resources/static/img/1.jpg");
    }

    public void sendPhoto(Long chatId, String photoUrl) {
        SendPhoto sendPhoto = SendPhoto
                .builder()
                .chatId(chatId)
                .photo(new InputFile("https://png.pngtree.com/background/20230519/original/pngtree-this-is-a-picture-of-a-tiger-cub-that-looks-straight-picture-image_2660243.jpg"))
                .caption("This is a little cat :)")
                .build();
        try {
            botSettings.execute(sendPhoto);
        } catch (TelegramApiException e) {
            e.printStackTrace();
            System.out.println("not execute photo");
        }
    }


    public void sendMSG(SendMessage sendMessage, String text) {
        try {
            sendMessage.setText(text);
            botSettings.execute(sendMessage);
        } catch (TelegramApiException e) {
            System.out.println("not execute message");
        }
    }

    /**
     * 去掉ICON，只保留命令本身
     * @param text
     * @return
     */
    private String removeIcon(String text) {
        return "/" + text.replaceAll("[^\\x00-\\x7F]", "").replaceAll("/", "").trim();
    }
}
